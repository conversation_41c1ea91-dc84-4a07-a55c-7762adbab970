package com.guwave.onedata.dataware.source.agent.common.util;

import com.guwave.onedata.dataware.common.contant.ExceptionType;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ProcessLog;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ProcessLogThreadLocalUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProcessLogThreadLocalUtil.class);

    private static final ThreadLocal<ProcessLog> processLogLocal = new ThreadLocal<>();

    public static void setLocal(ProcessLog processLog) {
        processLogLocal.set(processLog);
    }

    public static void appendErrorMessage(String msg) {
        if (StringUtils.isNotEmpty(msg)) {
            ProcessLog processLog = processLogLocal.get();
            if (processLog != null) {
                try {
                    StringBuilder buffer = new StringBuilder();
                    String oldMsg = processLog.getErrorMessage();
                    if (StringUtils.isNotEmpty(oldMsg)) {
                        buffer
                                .append(oldMsg)
                                .append("\n");
                    }
                    buffer.append(msg);
                    processLog.setErrorMessage(buffer.toString());
                } catch (Exception e) {
                    LOGGER.info("异常：", e);
                }
            }
        }
    }

    public static void overrideErrorMessage(String msg) {
        ProcessLog processLog = processLogLocal.get();
        if (processLog != null) {
            processLog.setErrorMessage(msg == null ? "" : msg);
        }
    }

    public static void setExceptionType(ExceptionType exceptionType) {
        ProcessLog processLog = processLogLocal.get();
        if (processLog != null) {
            processLog.setExceptionType(exceptionType);
        }
    }

    public static void clearLocal() {
        processLogLocal.remove();
    }
}
