package com.guwave.onedata.dataware.source.agent.common.handler;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.*;
import com.guwave.onedata.dataware.parser.stdf.model.BinDefinition;
import com.guwave.onedata.dataware.parser.stdf.model.FileMainData;
import com.guwave.onedata.dataware.parser.stdf.visitor.Visitor;
import com.guwave.onedata.dataware.parser.stdf.model.ClearRule;
import com.guwave.onedata.dataware.source.agent.common.model.FileTestInfo;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import com.guwave.onedata.dataware.source.agent.common.sink.filesystem.impl.HdfsSink;
import com.guwave.onedata.dataware.source.agent.common.util.FileTestInfoUtil;
import com.guwave.onedata.dataware.source.agent.common.util.HdfsUtil;
import com.guwave.onedata.dataware.source.agent.common.visitor.MultiThreadVisitor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.fs.Path;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * Handler
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2021-10-19 16:48:37
 */
public interface DataParseHandler extends Handler {
    Logger LOGGER = LoggerFactory.getLogger(DataParseHandler.class);

    default void parseFile(SftpFileDetail sftpFileDetail, LotMetaDataDetail lotMetaDataDetail, File uncompressFile) {
        // 删除ods数据
        deleteOdsHdfs(lotMetaDataDetail, uncompressFile);
        try {
            // 解析并上传ods文件
            List<Visitor> visitors = parseAndSendData(lotMetaDataDetail, uncompressFile.getAbsolutePath());

            // 填充lotMetaDataDetail文件的测试项条数
            fillFileTestInfo(lotMetaDataDetail, visitors);
        } catch (Exception e) {
            // 删除ods数据
            deleteOdsHdfs(lotMetaDataDetail, uncompressFile);
            throw e;
        }
    }

    default void deleteOdsHdfs(LotMetaDataDetail lotMetaDataDetail, File uncompressFile) {
        String odsHdfsDirPathTypeTemplate = (getOdsHdfsTemplatePath().endsWith(SLASH) ? getOdsHdfsTemplatePath() : getOdsHdfsTemplatePath() + SLASH)
                .replace(FILE_CATEGORY, lotMetaDataDetail.getFileCategory().getCategory())
                .replace(CUSTOMER, lotMetaDataDetail.getCustomer())
                .replace(TEST_AREA, lotMetaDataDetail.getTestArea().getArea())
                .replace(FACTORY, lotMetaDataDetail.getFactory())
                .replace(DEVICE_ID, lotMetaDataDetail.getDeviceId())
                .replace(LOT_ID, lotMetaDataDetail.getLotId())
                .replace(WAFER_NO, lotMetaDataDetail.getWaferNo())
                .replace(TEST_STAGE, lotMetaDataDetail.getTestStage())
                .replace(LOT_TYPE, lotMetaDataDetail.getLotType().getType())
                .replace(FILE_ID, lotMetaDataDetail.getFileInfoId() + EMPTY);
        HdfsSink.SCHEMA_MAP.keySet().forEach(dataType -> {
            // 多线程的ods路径
            Path newOdsPath = new Path(odsHdfsDirPathTypeTemplate.replace(TYPE, dataType));
            // 单线程的废弃历史数据ods路径
            Path deprecatedOdsPath = new Path(newOdsPath.getParent(), uncompressFile.getName() + POINT + dataType.toLowerCase() + PARQUET_FILE_SUFFIX);
            Lists.newArrayList(deprecatedOdsPath, newOdsPath).forEach(odsPath -> getHdfsUtil().deleteFile(odsPath.toString(), true));
        });
    }

    default Map<StdfFieldType, List<ClearRule>> getDataClearRule(String customer, String factory, TestArea testArea, String deviceId) {
        Map<StdfFieldType, List<ClearRule>> resRules = new HashMap<>();
        List<DataClearRule> clearRules = getDataClearRuleRepository().findAllByCustomerAndFactoryAndTestArea(customer, factory, testArea);

        // 支持一家工厂相同field_type、rule_type规则配置不同的device和factory维度
        clearRules.stream()
                .collect(Collectors.groupingBy(rule -> rule.getFieldType() + UNDER_LINE + rule.getRuleType()))
                .forEach((k, value) -> {
                    Map<String, DataClearRule> deviceIdWithRuleMap = value.stream()
                            .collect(Collectors.groupingBy(DataClearRule::getDeviceId, Collectors.collectingAndThen(Collectors.toList(), list -> list.get(0))));

                    DataClearRule rule = deviceIdWithRuleMap.getOrDefault(deviceId, deviceIdWithRuleMap.get(EMPTY));
                    if (rule != null) {
                        StdfFieldType fieldType = rule.getFieldType();
                        List<ClearRule> ruleList = resRules.getOrDefault(fieldType, new ArrayList<>());
                        ruleList.add(new ClearRule(rule.getRuleType(), rule.getRule()));
                        resRules.put(fieldType, ruleList);
                    }
                });
        return resRules;
    }

    default List<BinDefinition> getBinDefinitions(LotMetaDataDetail lotMetaDataDetail) {
        if (TestArea.getCpMapDataSourceList().contains(lotMetaDataDetail.getTestArea()) || TestArea.getCpInklessMapDataDourceList().contains(lotMetaDataDetail.getTestArea())) {
            return new ArrayList<>();
        }
        if (lotMetaDataDetail.getFileCategory() == FileCategory.BIT_MEM) {
            return new ArrayList<>();
        }
        List<BinDefinition> binDefinitions = getTestProgramBinDefinitionRepository().findByCustomerAndSubCustomerAndTestProgram(
                        lotMetaDataDetail.getCustomer(), lotMetaDataDetail.getSubCustomer(), lotMetaDataDetail.getTestProgram()
                ).stream().filter(t -> t.getBinNum() != null)
                .map(t -> new BinDefinition(t.getBinType(), t.getBinNum(), t.getBinName(), t.getBinPf()))
                .collect(Collectors.toList());

        lotMetaDataDetail.setUseBinDefinitionFlag(CollectionUtils.isNotEmpty(binDefinitions));

        return binDefinitions;
    }

    default Map<String, String> getCustomerSettingMap(LotMetaDataDetail lotMetaDataDetail) {
        Map<String, String> customerSettingMap = getCustomerSettingRepository().findByCustomerAndSubCustomer(lotMetaDataDetail.getCustomer(), lotMetaDataDetail.getSubCustomer())
                .stream().collect(Collectors.toMap(t -> StringUtils.defaultString(t.getSettingCode()), t -> StringUtils.defaultString(t.getSettingValue()), (v1, v2) -> v2));
        LOGGER.info("{} customerSettingMap: {}", lotMetaDataDetail.getFileName(), JSON.toJSONString(customerSettingMap));
        return customerSettingMap;
    }

    default Consumer<FileMainData> generateFillFileMainDataConsumer(LotMetaDataDetail lotMetaDataDetail, List<BinDefinition> binDefinitions) {
        return fileMainData -> {
            fileMainData.setTestArea(lotMetaDataDetail.getTestArea());

            fileMainData.setCustomer(lotMetaDataDetail.getCustomer());
            fileMainData.setSubCustomer(lotMetaDataDetail.getSubCustomer());
            fileMainData.setFileCategory(lotMetaDataDetail.getFileCategory());
            fileMainData.setTestStage(lotMetaDataDetail.getTestStage());
            fileMainData.setFactory(lotMetaDataDetail.getFactory());
            fileMainData.setFactorySite(lotMetaDataDetail.getFactorySite());
            fileMainData.setLotType(lotMetaDataDetail.getLotType());
            fileMainData.setFileInfoId(lotMetaDataDetail.getFileInfoId());
            fileMainData.setFileName(lotMetaDataDetail.getFileName());
            fileMainData.setDeviceId(lotMetaDataDetail.getDeviceId());
            fileMainData.setLotId(lotMetaDataDetail.getLotId());
            fileMainData.setOriginWaferId(lotMetaDataDetail.getOriginWaferId());
            fileMainData.setWaferNo(lotMetaDataDetail.getWaferNo());
            fileMainData.setSblotId(lotMetaDataDetail.getSblotId());
            fileMainData.setTestProgram(lotMetaDataDetail.getTestProgram());
            fileMainData.setTestTemperature(lotMetaDataDetail.getTestTemperature());
            fileMainData.setTestProgramVersion(lotMetaDataDetail.getTestProgramVersion());
            fileMainData.setTesterName(lotMetaDataDetail.getTesterName());
            fileMainData.setTesterType(lotMetaDataDetail.getTesterType());
            fileMainData.setProbecardLoadboardId(lotMetaDataDetail.getProbecardLoadboardId());
            fileMainData.setStartT(lotMetaDataDetail.getStartT());
            fileMainData.setFinishT(lotMetaDataDetail.getFinishT());
            fileMainData.setTestCod(lotMetaDataDetail.getTestCod());
            fileMainData.setPkgTyp(lotMetaDataDetail.getPkgTyp());
            fileMainData.setFloorId(lotMetaDataDetail.getFloorId());
            fileMainData.setRetestBinNum(lotMetaDataDetail.getRetestBinNum());
            fileMainData.setPosX(lotMetaDataDetail.getPosX());
            fileMainData.setPosY(lotMetaDataDetail.getPosY());
            fileMainData.setNotch(lotMetaDataDetail.getNotch());
            fileMainData.setProcess(lotMetaDataDetail.getProcess());
            fileMainData.setCreateUser(SYSTEM);
            fileMainData.setUploadType(UploadType.AUTO);

            fileMainData.setOfflineRetest(lotMetaDataDetail.getOfflineRetest());
            fileMainData.setDupRetest(lotMetaDataDetail.getDupRetest());
            fileMainData.setBatchNum(lotMetaDataDetail.getBatchNum());
            fileMainData.setInterrupt(lotMetaDataDetail.getInterrupt());
            fileMainData.setOfflineRetestIgnoreTp(lotMetaDataDetail.getOfflineRetestIgnoreTp());
            fileMainData.setDupRetestIgnoreTp(lotMetaDataDetail.getDupRetestIgnoreTp());
            fileMainData.setBatchNumIgnoreTp(lotMetaDataDetail.getBatchNumIgnoreTp());
            fileMainData.setInterruptIgnoreTp(lotMetaDataDetail.getInterruptIgnoreTp());

            fileMainData.setConditionSet(lotMetaDataDetail.getConditionSet());
            fileMainData.setRealWaferId(lotMetaDataDetail.getRealWaferId());

            fileMainData.generateBinDefiintionMap(binDefinitions);
        };
    }

    default void fillFileTestInfo(LotMetaDataDetail lotMetaDataDetail, List<Visitor> visitors) {
        FileTestInfo fileTestInfo = FileTestInfoUtil.calculateFileTestInfo(visitors.stream().map(t -> (MultiThreadVisitor) t).collect(Collectors.toList()));

        lotMetaDataDetail
                .setDieDataCnt(fileTestInfo.getDieDataCnt().intValue())
                .setTestItemDataCnt(fileTestInfo.getTestItemDataCnt())
                .setMaxRecordTestItem(fileTestInfo.getTestItemCntMax() != null ? fileTestInfo.getTestItemCntMax().getKey() : EMPTY)
                .setMaxRecordTestItemCnt(fileTestInfo.getTestItemCntMax() != null ? fileTestInfo.getTestItemCntMax().getValue() : 0L)
                .setMinRecordTestItem(fileTestInfo.getTestItemCntMin() != null ? fileTestInfo.getTestItemCntMin().getKey() : EMPTY)
                .setMinRecordTestItemCnt(fileTestInfo.getTestItemCntMin() != null ? fileTestInfo.getTestItemCntMin().getValue() : 0L);
    }

    List<LotMetaDataDetail> getNeedParseLotMetaDetails();

    List<Visitor> parseAndSendData(LotMetaDataDetail lotMetaDataDetail, String absolutePath);

    DataClearRuleRepository getDataClearRuleRepository();

    EcidRuleRepository getEcidRuleRepository();

    UidRuleRepository getUidRuleRepository();

    LotRelationSyncRepository getLotRelationSyncRepository();

    TestProgramBinDefinitionRepository getTestProgramBinDefinitionRepository();

    CustomerSettingRepository getCustomerSettingRepository();

    HdfsUtil getHdfsUtil();

    String getOdsHdfsTemplatePath();

    Integer getBatchSize();

    Map<String, CkSink> getCkSinkMap();
}
