package com.guwave.onedata.dataware.source.agent.common.sink.ck.dft;

import com.guwave.onedata.dataware.common.model.dft.DwdDftDetail;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

import static com.guwave.onedata.dataware.common.contant.Constant.HBIN_NAME_PREFIX;
import static com.guwave.onedata.dataware.common.contant.Constant.SBIN_NAME_PREFIX;

/**
 * 2024/3/25 14:07
 * DwdDftDetailSink
 *
 * <AUTHOR>
 */
@Component
public class DwdDftDetailSink implements CkSink<DwdDftDetail> {

    @Value("${spring.sink.ck.dwd.dbName}")
    private String dbName;
    @Value("${spring.sink.ck.dftSinkFlag}")
    private boolean dftSinkFlag;

    @Override
    public String getTableName() {
        return "dwd_dft_detail_cluster";
    }

    @Value("${spring.lotBucketNum}")
    private Integer lotBucketNum;

    @Override
    public String getDbName() {
        return dbName;
    }

    @Override
    public boolean sinkFlag() {
        return dftSinkFlag;
    }

    @Override
    public void handle(PreparedStatement statement, List<DwdDftDetail> items) throws SQLException {
        for (DwdDftDetail item : items) {
            statement.setObject(1, item.getCustomer());
            statement.setObject(2, item.getSubCustomer());
            statement.setObject(3, item.getUploadType());
            statement.setObject(4, item.getFileId());
            statement.setObject(5, item.getFileName());
            statement.setObject(6, item.getFileType());
            statement.setObject(7, item.getDeviceId());
            statement.setObject(8, item.getFactory());
            statement.setObject(9, item.getFactorySite());
            statement.setObject(10, item.getFab());
            statement.setObject(11, item.getFabSite());
            statement.setObject(12, item.getLotType());
            statement.setObject(13, item.getLotId());
            statement.setObject(14, item.getProcess());
            statement.setObject(15, item.getSblotId());
            statement.setObject(16, item.getWaferLotId());
            statement.setObject(17, item.getTestArea());
            statement.setObject(18, item.getTestStage());
            statement.setObject(19, item.getOfflineRetest());
            statement.setObject(20, item.getOnlineRetest());
            statement.setObject(21, item.getInterrupt());
            statement.setObject(22, item.getDupRetest());
            statement.setObject(23, item.getBatchNum());
            statement.setObject(24, item.getNumTest());
            statement.setObject(25, item.getTestProgram());
            statement.setObject(26, item.getTestTemperature());
            statement.setObject(27, item.getTestProgramVersion());
            statement.setObject(28, item.getSpecNam());
            statement.setObject(29, item.getSpecVer());
            statement.setObject(30, item.getHbinNum());
            statement.setObject(31, item.getSbinNum());
            statement.setObject(32, item.getSbinPf());
            statement.setObject(33, item.getSbinNam());
            statement.setObject(34, item.getHbinPf());
            statement.setObject(35, item.getHbinNam());
            statement.setObject(36, buildBin(HBIN_NAME_PREFIX, item.getHbinNum(), item.getHbinNam()));
            statement.setObject(37, buildBin(SBIN_NAME_PREFIX, item.getSbinNum(), item.getSbinNam()));
            statement.setObject(38, item.getTestHead());
            statement.setObject(39, item.getTesterName());
            statement.setObject(40, item.getTesterType());
            statement.setObject(41, item.getOperatorName());
            statement.setObject(42, item.getProberHandlerTyp());
            statement.setObject(43, item.getProberHandlerId());
            statement.setObject(44, item.getProbecardLoadboardTyp());
            statement.setObject(45, item.getProbecardLoadboardId());
            statement.setObject(46, item.getPartFlg());
            statement.setObject(47, item.getPartId());
            statement.setObject(48, item.getcPartId());
            statement.setObject(49, item.getxCoord());
            statement.setObject(50, item.getyCoord());
            statement.setObject(51, item.getTestTime());
            statement.setObject(52, item.getPartTxt());
            statement.setObject(53, item.getPartFix());
            statement.setObject(54, item.getTouchDownId());
            statement.setObject(55, item.getSite());
            statement.setObject(56, item.getSiteGrp());
            statement.setObject(57, item.getSiteCnt());
            statement.setObject(58, item.getSiteNums());
            statement.setObject(59, this.toTimestamp(item.getStartTime()));
            statement.setObject(60, this.toTimestamp(item.getEndTime()));
            statement.setObject(61, item.getStartHourKey());
            statement.setObject(62, item.getStartDayKey());
            statement.setObject(63, item.getEndHourKey());
            statement.setObject(64, item.getEndDayKey());
            statement.setObject(65, item.getWaferId());
            statement.setObject(66, item.getWaferNo());
            statement.setObject(67, toBigDecimal(item.getWaferSize()));
            statement.setObject(68, toBigDecimal(item.getWaferMargin()));
            statement.setObject(69, toBigDecimal(item.getDieHeight()));
            statement.setObject(70, toBigDecimal(item.getDieWidth()));
            statement.setObject(71, item.getWfUnits());
            statement.setObject(72, item.getWfFlat());
            statement.setObject(73, item.getCenterX());
            statement.setObject(74, item.getCenterY());
            statement.setObject(75, item.getPosX());
            statement.setObject(76, item.getPosY());
            statement.setObject(77, item.getDieCnt());
            statement.setObject(78, item.getTestNum());
            statement.setObject(79, item.getPsrRef());
            statement.setObject(80, stringValue(item.getTestFlg()));
            statement.setObject(81, stringValue(item.getLogTyp()));
            statement.setObject(82, stringValue(item.getTestTxt()));
            statement.setObject(83, stringValue(item.getAlarmId()));
            statement.setObject(84, stringValue(item.getProgTxt()));
            statement.setObject(85, stringValue(item.getRsltTxt()));
            statement.setObject(86, item.getzVal());
            statement.setObject(87, stringValue(item.getFmuFlg()));
            statement.setObject(88, stringValue(item.getMaskMap()));
            statement.setObject(89, stringValue(item.getFalMap()));
            statement.setObject(90, item.getCycCnt());
            statement.setObject(91, item.getTotfCnt());
            statement.setObject(92, item.getTotlCnt());
            statement.setObject(93, item.getCycBase());
            statement.setObject(94, item.getBitBase());
            statement.setObject(95, item.getCondCnt());
            statement.setObject(96, item.getLimCnt());
            statement.setObject(97, item.getCycSize());
            statement.setObject(98, item.getPmrSize());
            statement.setObject(99, item.getChnSize());
            statement.setObject(100, item.getPatSize());
            statement.setObject(101, item.getBitSize());
            statement.setObject(102, item.getU1Size());
            statement.setObject(103, item.getU2Size());
            statement.setObject(104, item.getU3Size());
            statement.setObject(105, item.getUtxSize());
            statement.setObject(106, item.getCapBgn());
            statement.setObject(107, toArrayFromIntegerList(item.getLimIndx()));
            statement.setObject(108, toArrayFromLongList(item.getLimSpec()));
            statement.setObject(109, toArrayFromStringList(item.getCondLst()));
            statement.setObject(110, item.getCycoCnt());
            statement.setObject(111, toArrayFromLongList(item.getCycOfst()));
            statement.setObject(112, item.getPmrCnt());
            statement.setObject(113, toArrayFromLongList(item.getPmrIndx()));
            statement.setObject(114, item.getChnCnt());
            statement.setObject(115, toArrayFromLongList(item.getChnNum()));
            statement.setObject(116, item.getExpCnt());
            statement.setObject(117, toArrayFromStringList(item.getExpData()));
            statement.setObject(118, item.getCapCnt());
            statement.setObject(119, toArrayFromStringList(item.getCapData()));
            statement.setObject(120, item.getNewCnt());
            statement.setObject(121, toArrayFromStringList(item.getNewData()));
            statement.setObject(122, item.getPatCnt());
            statement.setObject(123, toArrayFromLongList(item.getPatNum()));
            statement.setObject(124, item.getBposCnt());
            statement.setObject(125, toArrayFromLongList(item.getBitPos()));
            statement.setObject(126, item.getUsr1Cnt());
            statement.setObject(127, toArrayFromLongList(item.getUsr1()));
            statement.setObject(128, item.getUsr2Cnt());
            statement.setObject(129, toArrayFromLongList(item.getUsr2()));
            statement.setObject(130, item.getUsr3Cnt());
            statement.setObject(131, toArrayFromLongList(item.getUsr3()));
            statement.setObject(132, item.getTxtCnt());
            statement.setObject(133, toArrayFromStringList(item.getUserTxt()));
            statement.setObject(134, stringValue(item.getPsrNam()));
            statement.setObject(135, stringValue(item.getOptFlg()));
            statement.setObject(136, item.getTotpCnt());
            statement.setObject(137, item.getLocpCnt());
            statement.setObject(138, toArrayFromLongList(item.getPatBgn()));
            statement.setObject(139, toArrayFromLongList(item.getPatEnd()));
            statement.setObject(140, toArrayFromStringList(item.getPatFile()));
            statement.setObject(141, toArrayFromStringList(item.getPatLbl()));
            statement.setObject(142, toArrayFromStringList(item.getFileUid()));
            statement.setObject(143, toArrayFromStringList(item.getAtpgDsc()));
            statement.setObject(144, toArrayFromStringList(item.getSrcId()));
            statement.setObject(145, toArrayFromStringList(item.getAtpgNam()));
            statement.setObject(146, item.getCreateHourKey());
            statement.setObject(147, item.getCreateDayKey());
            statement.setObject(148, this.toTimestamp(item.getCreateTime()));
            statement.setObject(149, item.getCreateUser());
            statement.setObject(150, this.toTimestamp(item.getUploadTime()));
            statement.setObject(151, item.getDataVersion());
            statement.setObject(152, getLotBucket(item.getLotId(), lotBucketNum));
            statement.setObject(153, 0);

            statement.addBatch();

        }
    }

    @Override
    public List<String> getColumns() {
        return Arrays.asList(
                "CUSTOMER",
                "SUB_CUSTOMER",
                "UPLOAD_TYPE",
                "FILE_ID",
                "FILE_NAME",
                "FILE_TYPE",
                "DEVICE_ID",
                "FACTORY",
                "FACTORY_SITE",
                "FAB",
                "FAB_SITE",
                "LOT_TYPE",
                "LOT_ID",
                "PROCESS",
                "SBLOT_ID",
                "WAFER_LOT_ID",
                "TEST_AREA",
                "TEST_STAGE",
                "OFFLINE_RETEST",
                "ONLINE_RETEST",
                "INTERRUPT",
                "DUP_RETEST",
                "BATCH_NUM",
                "NUM_TEST",
                "TEST_PROGRAM",
                "TEST_TEMPERATURE",
                "TEST_PROGRAM_VERSION",
                "SPEC_NAM",
                "SPEC_VER",
                "HBIN_NUM",
                "SBIN_NUM",
                "SBIN_PF",
                "SBIN_NAM",
                "HBIN_PF",
                "HBIN_NAM",
                "HBIN",
                "SBIN",
                "TEST_HEAD",
                "TESTER_NAME",
                "TESTER_TYPE",
                "OPERATOR_NAME",
                "PROBER_HANDLER_TYP",
                "PROBER_HANDLER_ID",
                "PROBECARD_LOADBOARD_TYP",
                "PROBECARD_LOADBOARD_ID",
                "PART_FLG",
                "PART_ID",
                "C_PART_ID",
                "X_COORD",
                "Y_COORD",
                "TEST_TIME",
                "PART_TXT",
                "PART_FIX",
                "TOUCH_DOWN_ID",
                "SITE",
                "SITE_GRP",
                "SITE_CNT",
                "SITE_NUMS",
                "START_TIME",
                "END_TIME",
                "START_HOUR_KEY",
                "START_DAY_KEY",
                "END_HOUR_KEY",
                "END_DAY_KEY",
                "WAFER_ID",
                "WAFER_NO",
                "WAFER_SIZE",
                "WAFER_MARGIN",
                "DIE_HEIGHT",
                "DIE_WIDTH",
                "WF_UNITS",
                "WF_FLAT",
                "CENTER_X",
                "CENTER_Y",
                "POS_X",
                "POS_Y",
                "DIE_CNT",
                "TEST_NUM",
                "PSR_REF",
                "TEST_FLG",
                "LOG_TYP",
                "TEST_TXT",
                "ALARM_ID",
                "PROG_TXT",
                "RSLT_TXT",
                "Z_VAL",
                "FMU_FLG",
                "MASK_MAP",
                "FAL_MAP",
                "CYC_CNT",
                "TOTF_CNT",
                "TOTL_CNT",
                "CYC_BASE",
                "BIT_BASE",
                "COND_CNT",
                "LIM_CNT",
                "CYC_SIZE",
                "PMR_SIZE",
                "CHN_SIZE",
                "PAT_SIZE",
                "BIT_SIZE",
                "U1_SIZE",
                "U2_SIZE",
                "U3_SIZE",
                "UTX_SIZE",
                "CAP_BGN",
                "LIM_INDX",
                "LIM_SPEC",
                "COND_LST",
                "CYCO_CNT",
                "CYC_OFST",
                "PMR_CNT",
                "PMR_INDX",
                "CHN_CNT",
                "CHN_NUM",
                "EXP_CNT",
                "EXP_DATA",
                "CAP_CNT",
                "CAP_DATA",
                "NEW_CNT",
                "NEW_DATA",
                "PAT_CNT",
                "PAT_NUM",
                "BPOS_CNT",
                "BIT_POS",
                "USR1_CNT",
                "USR1",
                "USR2_CNT",
                "USR2",
                "USR3_CNT",
                "USR3",
                "TXT_CNT",
                "USER_TXT",
                "PSR_NAM",
                "OPT_FLG",
                "TOTP_CNT",
                "LOCP_CNT",
                "PAT_BGN",
                "PAT_END",
                "PAT_FILE",
                "PAT_LBL",
                "FILE_UID",
                "ATPG_DSC",
                "SRC_ID",
                "ATPG_NAM",
                "CREATE_HOUR_KEY",
                "CREATE_DAY_KEY",
                "CREATE_TIME",
                "CREATE_USER",
                "UPLOAD_TIME",
                "DATA_VERSION",
                "LOT_BUCKET",
                "IS_DELETE"
        );
    }

}
