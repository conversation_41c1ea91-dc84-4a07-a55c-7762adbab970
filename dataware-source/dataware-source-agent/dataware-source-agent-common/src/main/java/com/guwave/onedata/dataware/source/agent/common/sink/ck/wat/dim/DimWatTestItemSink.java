package com.guwave.onedata.dataware.source.agent.common.sink.ck.wat.dim;

import com.guwave.onedata.dataware.common.model.wat.dim.DimWatTestItem;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

/**
 * Copyright (C), 2022, guwave
 * <p>
 * DimWatSiteSink
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2022-03-14 11:34:47
 */
@Component
public class DimWatTestItemSink implements CkSink<DimWatTestItem> {

    @Value("${spring.sink.ck.dim.dbName}")
    private String dbName;

    @Override
    public String getTableName() {
        return "dim_test_item_cluster";
    }

    @Override
    public String getPartitionExpr() {
        return "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{SUB_CUSTOMER}', '{DEVICE_ID}')";
    }

    @Override
    public void handle(PreparedStatement statement, List<DimWatTestItem> items) throws SQLException {
        for (DimWatTestItem item : items) {
            // CUSTOMER
            statement.setObject(1, item.getCustomer());
            // UPLOAD_TYPE
            statement.setObject(2, item.getUploadType());
            // DEVICE_ID
            statement.setObject(3, item.getDeviceId());
            // FACTORY
            statement.setObject(4, item.getFactory());
            // FACTORY_SITE
            statement.setObject(5, item.getFactorySite());
            // FAB
            statement.setObject(6, item.getFab());
            // FAB_SITE
            statement.setObject(7, item.getFabSite());
            // LOT_TYPE
            statement.setObject(8, item.getLotType());
            // LOT_ID
            statement.setObject(9, item.getLotId());
            // TEST_AREA
            statement.setObject(10, item.getTestArea());
            // WAFER_ID
            statement.setObject(11, item.getWaferId());
            // FABWF_ID
            statement.setObject(12, item.getFabwfId());
            // START_TIME
            statement.setObject(13, this.toTimestamp(item.getStartTime()));
            // END_TIME
            statement.setObject(14, this.toTimestamp(item.getEndTime()));
            // CREATE_TIME
            statement.setObject(15, new Timestamp(System.currentTimeMillis()));
            // CREATE_USER
            statement.setObject(16, item.getCreateUser());

            // TEST_PROGRAM
            statement.setObject(17, item.getTestProgram());
            // TEST_TXT
            statement.setObject(18, item.getTestTxt());
            // ORIGIN_UNITS
            statement.setObject(19, item.getOriginUnits());
            statement.setObject(20, item.getFileId());
            statement.setObject(21, item.getFileName());
            // TEST_NUM
            statement.setObject(22, item.getTestNum());
            // TEST_ITEM
            statement.setObject(23, item.getTestItem());
            // HI_LIMIT
            statement.setObject(24, this.toBigDecimal(item.getHiLimit()));
            // LO_LIMIT
            statement.setObject(25, this.toBigDecimal(item.getLoLimit()));
            // ORIGIN_HI_LIMIT
            statement.setObject(26, this.toBigDecimal(item.getOriginHiLimit()));
            // ORIGIN_LO_LIMIT
            statement.setObject(27, this.toBigDecimal(item.getOriginLoLimit()));
            // UNITS
            statement.setObject(28, item.getUnits());
            // WAFER_LOT_ID
            statement.setObject(29, item.getWaferLotId());
            // WAFER_NO
            statement.setObject(30, item.getWaferNo());
            // TEST_STAGE
            statement.setObject(31, item.getTestStage());
            // SUB_CUSTOMER
            statement.setObject(32, item.getSubCustomer());
            statement.setObject(33, item.getTestTemperature());
            statement.setObject(34, item.getTesterName());
            statement.setObject(35, item.getOperatorName());
            statement.setObject(36, item.getProcess());
            // version
            statement.setObject(37, System.currentTimeMillis());
            // wafer_id_key
            statement.setObject(38, item.getWaferId());
            // wafer_no_key
            statement.setObject(39, item.getWaferNo());
            statement.setObject(40, item.getSiteKey());
            statement.setObject(41, item.getHbinNumKey());
            statement.setObject(42, item.getSbinNumKey());
            statement.setObject(43, item.getConditionSet());
            statement.setObject(44, new Timestamp(item.getUploadTime()));
            statement.setObject(45, item.getTestitemType());
            statement.setObject(46, this.toBigDecimal(item.getTarget()));


            statement.addBatch();
        }
    }

    @Override
    public List<String> getColumns() {
        return Arrays.asList(
                "CUSTOMER",
                "UPLOAD_TYPE",
                "DEVICE_ID",
                "FACTORY",
                "FACTORY_SITE",
                "FAB",
                "FAB_SITE",
                "LOT_TYPE",
                "LOT_ID",
                "TEST_AREA",
                "WAFER_ID",
                "FABWF_ID",
                "START_TIME",
                "END_TIME",
                "CREATE_TIME",
                "CREATE_USER",
                "TEST_PROGRAM",
                "TEST_TXT",
                "ORIGIN_UNITS",
                "FILE_ID",
                "FILE_NAME",
                "TEST_NUM",
                "TEST_ITEM",
                "HI_LIMIT",
                "LO_LIMIT",
                "ORIGIN_HI_LIMIT",
                "ORIGIN_LO_LIMIT",
                "UNITS",
                "WAFER_LOT_ID",
                "WAFER_NO",
                "TEST_STAGE",
                "SUB_CUSTOMER",
                "TEST_TEMPERATURE",
                "TESTER_NAME",
                "OPERATOR_NAME",
                "PROCESS",
                "VERSION",
                "WAFER_ID_KEY",
                "WAFER_NO_KEY",
                "SITE_KEY",
                "HBIN_NUM_KEY",
                "SBIN_NUM_KEY",
                "CONDITION_SET",
                "UPLOAD_TIME",
                "TESTITEM_TYPE",
                "TARGET"
        );
    }

    @Override
    public String getDbName() {
        return dbName;
    }
}
