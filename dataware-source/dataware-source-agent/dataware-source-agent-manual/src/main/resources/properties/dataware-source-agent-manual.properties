module.name=dataware-source-agent-manual
# database config
database.address=mpp01:3306
database.name=onedata
database.username=bi
database.password=bi@guwave

# scheduling
task.scheduling.pool.size=2
scheduler.polling.milliseconds=5000

# kafka config
kafka.bootstrapServers=riot12.guwave.com:6667,riot13.guwave.com:6667,riot14.guwave.com:6667
kafka.consumer.consumeGroup=OnedataDatawareAgentManual
kafka.consumer.autoOffsetReset=earliest
kafka.consumer.autoCommitInterval=1000
kafka.consumer.maxPollRecords=10
kafka.listener.concurrency=1
kafka.producer.batchSize=104857600
kafka.producer.lingerMs=0
kafka.producer.bufferMemory=104857600
kafka.producer.retries=3
kafka.producer.properties.retry.backoff.ms=1000
kafka.sleepMilliseconds=0
kafka.manualTopic=t_dw_manual_warehousing
kafka.manualFinishTopic=t_dw_manual_warehousing_finish
kafka.loadEndFlagTopic=t_dw_load_end_flag

handler.file.lockExpireTime=86400
handler.file.unlockExpireTime=60
handler.file.errorPath=/home/<USER>/deploy/onedata/dataware/dataware-source-agent-manual/data/error/
handler.file.readPath=/home/<USER>/deploy/onedata/dataware/dataware-source-agent-manual/data/read/
# hdfs模式：
#           HA : 此模式时 gdp.file.hdfsUrl 需要配两个地址，用英文逗号隔开
#           STANDALONE : 此模式时 gdp.file.hdfsUrl 只需要配一个地址
handler.file.hdfsMode=HA
handler.file.hdfsUrl=hdfs://riot11.guwave.com:8020,hdfs://riot12.guwave.com:8020
handler.file.hdfsUser=glory
handler.stdfTypes=ATR,FAR,FTR,HBR,MIR,MRR,PCR,PGR,PIR,PMR,PRR,PTR,SBR,SDR,TSR,WCR,WIR,WRR,MPR,STR,PSR,NMR
handler.sinkTypes=hdfsSink
handler.sink.batchSize=20000
handler.sink.coreNum=8
handler.sink.maxTextDatSize=500

# ck config
data.clickhouse.address=****************************,****************************,****************************
data.clickhouse.username=admin
data.clickhouse.password=admin@ck@Guwave
data.clickhouse.cluster=cluster_3shards_1replicas

# sink config
sink.ck.ods.dbName=ods
sink.ck.dim.dbName=dim
sink.ck.dwd.dbName=dwd

# ods pathTemplate in hdfs
hdfs.odsHdfsTemplatePath=/user/glory/data/onedata/dataware/ods_manual/result/{FILE_CATEGORY}/{TYPE}/TEST_AREA={TEST_AREA}/CUSTOMER={CUSTOMER}/FILE_ID={FILE_ID}/

# run mode
runMode.standaloneThreshold=0

# python安装路径
adapter.python.install.path=/usr/bin/python3

maxNoCPartIdCnt=1000000

maxRawDataThreadCnt=2

lotBucketNum=6

sink.ck.dftSinkFlag=false

# minio配置
minio.endpoint=http://minio.guwave.com:19000
minio.accessKey=report-center
minio.secretKey=report@guwave
minio.bucket=dataware

# 本地存储路径
ods.local.prefix=/home/<USER>/deploy/onedata/dataware/ods/data/

# 定义smt8 testTxt: PTR.TEST_TXT、TSR.TEST_NAM,多个用:分隔，PTR.TEST_TXT:TSR.TEST_NAM
smt8.testTxt.rule=
