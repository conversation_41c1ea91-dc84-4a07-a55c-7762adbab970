package com.guwave.onedata.dataware.collectx.core;

import com.guwave.onedata.dataware.collectx.entity.FTPFileAttr;
import com.guwave.onedata.dataware.collectx.manager.thread.SftpCollectRuleThreadManager;
import com.guwave.onedata.dataware.collectx.manager.thread.SftpConnectConfThreadManager;
import com.guwave.onedata.dataware.collectx.manager.thread.SftpLogThreadLocalUtil;
import com.guwave.onedata.dataware.collectx.util.FileUtils;
import com.guwave.onedata.dataware.common.contant.FTPServerType;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.model.collectx.SftpConnectConf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.commons.net.ftp.FTPSClient;

import javax.net.ssl.SSLSession;
import javax.net.ssl.SSLSessionContext;
import javax.net.ssl.SSLSocket;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.Socket;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Slf4j
public class FTPSyncCore extends AbstractFTPSyncCore implements IFTPSyncCore {

    static class ResumeSessionFtpClient extends FTPSClient {
        public ResumeSessionFtpClient(boolean isImplicit) {
            super(isImplicit);
            System.setProperty("jdk.tls.useExtendedMasterSecret", "false");
        }

        @Override
        protected void _prepareDataSocket_(Socket socket) throws IOException {
            if (socket instanceof SSLSocket) {
                final SSLSession session = ((SSLSocket) _socket_).getSession();
                final SSLSessionContext context = session.getSessionContext();
                try {
                    final Field sessionHostPortCache = context.getClass().getDeclaredField("sessionHostPortCache");
                    sessionHostPortCache.setAccessible(true);
                    final Object cache = sessionHostPortCache.get(context);
                    final Method putMethod = cache.getClass().getDeclaredMethod("put", Object.class, Object.class);
                    putMethod.setAccessible(true);
                    final Method getHostMethod = socket.getClass().getDeclaredMethod("getHost");
                    getHostMethod.setAccessible(true);
                    Object host = getHostMethod.invoke(socket);
                    final String key = String.format("%s:%s", host, String.valueOf(socket.getPort())).toLowerCase(Locale.ROOT);
                    putMethod.invoke(cache, key, session);
                } catch (Exception e) {
                    throw new IOException(e);
                }
            }
        }
    }

    private FTPClient ftpClient = null;

    public FTPSyncCore() {
        this(FTPServerType.NORMAL);
    }

    public FTPSyncCore(FTPServerType type) {
        setServerType(type);
    }

    public void connectServer() throws Exception {
        SftpConnectConf sftpConnectConf = SftpConnectConfThreadManager.getSftpConnectConf(getServerType());

        if (ftpClient == null) {
            boolean resumeSessionFlag = false;
            String resumeSession = sftpConnectConf.findExtProp("resumeSession");
            if ((StringUtils.isNotBlank(resumeSession) && "true".equalsIgnoreCase(resumeSession))) {
                log.info("使用resumeSession");
                resumeSessionFlag = true;
            }
            if (!Objects.equals(sftpConnectConf.getUseTls(), 0)) {
                String isImplicit = sftpConnectConf.findExtProp("isImplicit");
                if (Objects.equals(sftpConnectConf.getUseTls(), 2) || (StringUtils.isNotBlank(isImplicit) && "true".equalsIgnoreCase(isImplicit))) {
                    ftpClient = resumeSessionFlag ? new ResumeSessionFtpClient(true) : new FTPSClient(true);
                } else {
                    ftpClient = resumeSessionFlag ? new ResumeSessionFtpClient(false) : new FTPSClient();
                }
            } else {
                ftpClient = new FTPClient();
            }
        }
        if (!ftpClient.isAvailable()) {
            // 设置中文编码集，防止中文乱码
            ftpClient.setControlEncoding("UTF-8");
            //设置默认超时时间,30秒
            ftpClient.setDefaultTimeout(sftpConnectConf.getTimeout() * 1000);
            // 设置连接超时时间
            ftpClient.setConnectTimeout(sftpConnectConf.getTimeout() * 1000);
            //设置读取数据超时时间（每次socket传输，不是指整个下载）
            ftpClient.setDataTimeout(sftpConnectConf.getTimeout() * 1000);

            // 连接FPT服务器,设置IP及端口
            ftpClient.connect(sftpConnectConf.getServer(), sftpConnectConf.getPort());
            // 设置用户名和密码
            ftpClient.login(sftpConnectConf.getUsername(), sftpConnectConf.getPassword());

            if (!Objects.equals(sftpConnectConf.getUseTls(), 0)) {
                ((FTPSClient) ftpClient).execPBSZ(0);
                ((FTPSClient) ftpClient).execPROT("P");
            }

            ftpClient.setBufferSize(1024 * 1024 * 10);
            // 设置ftp在上传或下载大文件时，保持ftp的control的socket连接keepalive
            ftpClient.setControlKeepAliveTimeout(sftpConnectConf.getTimeout());
            ftpClient.setControlKeepAliveReplyTimeout(sftpConnectConf.getTimeout() * 1000);
            String activeMode = sftpConnectConf.findExtProp("activeMode");
            if ((StringUtils.isNotBlank(activeMode) && "true".equalsIgnoreCase(activeMode))) {
                log.info("使用activeMode");
                ftpClient.enterLocalActiveMode();
            } else {
                //设置被动模式，文件传输端口设置
                ftpClient.enterLocalPassiveMode();
            }
            //设置为二进制格式，解决zip，gz及xls等文件load问题
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            int replyCode = ftpClient.getReplyCode();
            if (!FTPReply.isPositiveCompletion(replyCode)) {
                log.error("FTP重新连接失败 {}", replyCode);
                ftpClient.disconnect();
                throw new RuntimeException("未连接到FTP，用户名或密码错误");
            }
        }
        if (ftpClient.isAvailable()) {
            log.info("FTP重新连接成功");
        } else {
            throw new RuntimeException("FTP重新连接失败");
        }
    }

    public String loadFile(String remoteFileName, String localPath) throws Exception {
        FTPFile ftpFile = new FTPFile();
        ftpFile.setName(remoteFileName);
        String fileName = FileUtils.getFileName(remoteFileName);
        String downFileFullName = FileUtils.getFileFullName(localPath, fileName);
        boolean flag = downloadFile(downFileFullName, remoteFileName, 1);
        if (!flag) {
            throw new Exception("文件下载失败！");
        }
        return downFileFullName;
    }

    private boolean downloadFile(String localFilePath, String remoteFilePath, int retryCount) {
        SftpConnectConf sftpConnectConf = SftpConnectConfThreadManager.getSftpConnectConf(getServerType());

        File localFile = new File(localFilePath);
        if (localFile.exists()) {
            org.apache.commons.io.FileUtils.deleteQuietly(localFile);
        }
        boolean flag = false;
        try (OutputStream out = new FileOutputStream(localFilePath)) {
            connectServer();
            flag = ftpClient.retrieveFile(remoteFilePath, out);
            out.flush();
            if (flag) {
                log.info("ftp file:{} is loaded and saved as local file:{}.", remoteFilePath, localFilePath);
            } else {
                log.error("ftp file:{} can't be loaded into local file:{}, fail reason: {}", remoteFilePath, localFilePath, ftpClient.getReplyString());
            }
        } catch (Exception e) {
            log.error("从 {} 下载到 {} 异常！", remoteFilePath, localFilePath, e);
            flag = false;
        }
        if (!flag && retryCount <= sftpConnectConf.getReconnectCount()) {
            // 下载失败 且未到最大重试次数   重试
            log.info("downloadFile {} 第{}次尝试重新连接......", remoteFilePath, retryCount);
            this.closeConnect();
            try {
                Thread.sleep(retryCount * 5000L);
            } catch (InterruptedException e) {
                //
            }
            flag = downloadFile(localFilePath, remoteFilePath, ++retryCount);
        }
        return flag;
    }

    public void listFTPFileInfo(String absoluteDir, List<FTPFileAttr> fileInfoList) throws Exception {
        SftpConnectConf sftpConnectConf = SftpConnectConfThreadManager.getSftpConnectConf(getServerType());
        log.debug("listFTPFileInfo>>> FTP dir: " + absoluteDir);
        FTPFile[] ftpFiles = listFiles(absoluteDir, 1);
        log.info("listFTPFileInfo>>> FTP dir: " + absoluteDir + " " + ftpFiles.length);
        SftpLogThreadLocalUtil.appendLogTxt(String.format("listFTPFileInfo>>> FTP dir: %s %s", absoluteDir, ftpFiles.length));
        for (FTPFile ftpFile : ftpFiles) {
            String fileName = FileUtils.getFileName(ftpFile.getName());

            if (ftpFile.isDirectory() && !(fileName.equals(".") || fileName.equals(".."))) {
                String subDir = FileUtils.getFileFullPath(absoluteDir, fileName);
                //根据黑名单目录配置优化
                if (isBlackRemotePath(subDir, sftpConnectConf.getBlackFileRemoteFullNameRegex())) {
                    continue;
                }

                listFTPFileInfo(subDir, fileInfoList);

            } else {
                if (fileName.equals(".") || fileName.equals("..")) {
                    continue;
                }
                String fullName = FileUtils.getFileFullName(absoluteDir, fileName);
                String fileSuffix = "";
                if (!ftpFile.isDirectory()) {
                    if (fileName.lastIndexOf(".") + 1 < fileName.length()) {
                        fileSuffix = fileName.substring(fileName.lastIndexOf(".") + 1);
                    } else {
                        fileSuffix = "";
                    }
                }
                FTPFileAttr ftpFileAttr = new FTPFileAttr();
                ftpFileAttr.setFileName(fileName);
                ftpFileAttr.setFilePath(absoluteDir);
                ftpFileAttr.setFullName(fullName);
                ftpFileAttr.setGroup(ftpFile.getGroup());
                ftpFileAttr.setUser(ftpFile.getUser());
                try {
                    ftpFileAttr.setGid(Integer.parseInt(ftpFile.getGroup()));
                    ftpFileAttr.setUid(Integer.parseInt(ftpFile.getUser()));
                } catch (Exception e) {
//                        log.error("Group and user id is set with error:{}", e.toString());
                }
                ftpFileAttr.setSize(ftpFile.getSize());
                //目前只能精确到分钟，并且时区是0时区时间，需要变更到+8时区时间
                long mTime = ftpFile.getTimestamp().getTimeInMillis() + sftpConnectConf.getTimeDifference() * 1000L;
                ftpFileAttr.setMTime(mTime);
                ftpFileAttr.setDir(ftpFile.isDirectory());
                ftpFileAttr.setFileSuffix(fileSuffix);
                ftpFileAttr.setRawListingString(ftpFile.getRawListing());

                if (mTime < sftpConnectConf.getFileModifiedRealStartDate().getTime()
                        || mTime > sftpConnectConf.getFileModifiedRealEndDate().getTime()) {
                    continue;
                }

                if (getServerType() == FTPServerType.NORMAL) {
                    FileCategory fileCategory = matchFileCategoryWithRemoteFileFullName(ftpFileAttr.getFullName(), SftpCollectRuleThreadManager.getSftpCollectRule());
                    if (!NOT_NEED_PULL_FILE_CATEGORY_LIST.contains(fileCategory)) {
                        ftpFileAttr.setFileCategory(fileCategory);
                    } else {
                        SftpLogThreadLocalUtil.appendLogTxt("文件名：" + ftpFileAttr.getFullName() + "匹配的fileCategory: " + fileCategory + " 不进行下载！");
                        continue;
                    }
                } else {
                    ftpFileAttr.setFileCategory(FileCategory.OTHER);
                }

                fileInfoList.add(ftpFileAttr);
            }
        }
    }

    @Override
    public FTPFileAttr ftpFileInfo(String absoluteDir, String sourceFileName) throws Exception {
        SftpConnectConf sftpConnectConf = SftpConnectConfThreadManager.getSftpConnectConf(getServerType());
        log.debug("fTPFileInfo>>> FTP dir: " + absoluteDir);
        FTPFile[] ftpFiles = listFiles(absoluteDir, 1);
        log.info("fTPFileInfo>>> FTP dir: " + absoluteDir + SPACE + ftpFiles.length);

        for (FTPFile ftpFile : ftpFiles) {
            String fileName = FileUtils.getFileName(ftpFile.getName());

            if (ftpFile.isDirectory() && !(fileName.equals(POINT) || fileName.equals(".."))) {
                String subDir = FileUtils.getFileFullPath(absoluteDir, fileName);
                //根据黑名单目录配置优化
                if (isBlackRemotePath(subDir, sftpConnectConf.getBlackFileRemoteFullNameRegex())) {
                    continue;
                }

                ftpFileInfo(subDir, fileName);

            } else {
                if (fileName.equals(POINT) || fileName.equals("..") || !fileName.equals(sourceFileName)) {
                    continue;
                }
                String fullName = FileUtils.getFileFullName(absoluteDir, fileName);
                String fileSuffix = EMPTY;
                if (!ftpFile.isDirectory()) {
                    if (fileName.lastIndexOf(POINT) + 1 < fileName.length()) {
                        fileSuffix = fileName.substring(fileName.lastIndexOf(POINT) + 1);
                    } else {
                        fileSuffix = EMPTY;
                    }
                }
                FTPFileAttr ftpFileAttr = new FTPFileAttr();
                ftpFileAttr.setFileName(fileName);
                ftpFileAttr.setFilePath(absoluteDir);
                ftpFileAttr.setFullName(fullName);
                ftpFileAttr.setGroup(ftpFile.getGroup());
                ftpFileAttr.setUser(ftpFile.getUser());
                try {
                    ftpFileAttr.setGid(Integer.parseInt(ftpFile.getGroup()));
                    ftpFileAttr.setUid(Integer.parseInt(ftpFile.getUser()));
                } catch (Exception e) {
//                        log.error("Group and user id is set with error:{}", e.toString());
                }
                ftpFileAttr.setSize(ftpFile.getSize());
                //目前只能精确到分钟，并且时区是0时区时间，需要变更到+8时区时间
                long mTime = ftpFile.getTimestamp().getTimeInMillis() + sftpConnectConf.getTimeDifference() * 1000L;
                ftpFileAttr.setMTime(mTime);
                ftpFileAttr.setDir(ftpFile.isDirectory());
                ftpFileAttr.setFileSuffix(fileSuffix);
                ftpFileAttr.setRawListingString(ftpFile.getRawListing());

                ftpFileAttr.setFileCategory(FileCategory.OTHER);
                return ftpFileAttr;
            }
        }
        return null;
    }

    private FTPFile[] listFiles(String absoluteDir, int retryCount) throws Exception {
        try {
            connectServer();
            return ftpClient.listFiles(absoluteDir);
        } catch (Exception e) {
            if (retryCount > SftpCollectRuleThreadManager.getSftpCollectRule().getReconnectCount()) {
                throw e;
            } else {
                log.info("listFiles {} 第{}次尝试重新连接......{}", absoluteDir, retryCount, e.getMessage());
                this.closeConnect();
                try {
                    Thread.sleep(retryCount * 5000L);
                } catch (InterruptedException ex) {
                    //
                }
                return listFiles(absoluteDir, ++retryCount);
            }
        }
    }

    public void closeConnect() {
        try {
            if (ftpClient != null) {
                ftpClient.logout();
                ftpClient.disconnect();
            }
        } catch (Exception e) {
            log.error("logout exception: ", e);
        } finally {
            try {
                if (ftpClient != null && ftpClient.isConnected()) {
                    ftpClient.disconnect();
                }
            } catch (Exception e) {
                log.error("disConnect exception: ", e);
            }
        }
    }

    @Override
    public boolean moveFile(String remoteSourcePath, String remoteTargetPath) throws Exception {
        connectServer();
        return ftpClient.rename(remoteSourcePath, remoteTargetPath);
    }

    @Override
    public void mkdir(String remotePath) throws Exception {
        connectServer();
        String[] dirs = remotePath.split(SLASH);
        String dir = "";
        for (String s : dirs) {
            if (StringUtils.isNotEmpty(s)) {
                dir += SLASH + s;
                log.info("创建目录 {} ", dir);
                ftpClient.makeDirectory(dir);
            }
        }
    }

    @Override
    public boolean deleteFile(String sourceFilePath) throws Exception {
        connectServer();
        if (ftpClient.listFiles(sourceFilePath).length > 0) {
            ftpClient.deleteFile(sourceFilePath);
        }
        return true;
    }

    @Override
    public boolean uploadFile(String localFilePath, String targetFilePath) throws Exception {
        connectServer();
        mkdir(targetFilePath);
        String localFileShortName = FileUtils.getFileName(localFilePath);
        String uploadFileFullName = FileUtils.getFileFullName(targetFilePath, localFileShortName);
        if (ftpClient.listFiles(uploadFileFullName).length > 0) {
            ftpClient.deleteFile(uploadFileFullName);
        }
        try (FileInputStream in = new FileInputStream(localFilePath)) {
            return ftpClient.storeFile(uploadFileFullName, in);
        } catch (Exception e) {
            log.error("上传 {} 到 {} 异常！", localFilePath, uploadFileFullName, e);
            throw e;
        }
    }

    /**
     * 判断是否连接
     */
    @Override
    public boolean isConnected() {
        return ftpClient != null;
    }

}