package com.guwave.onedata.dataware.source.agent.manual.model;

import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.contant.UploadType;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;

public class DwdShmooVminVmaxDetail {
    /**
     * dwd_shmoo_vmin_vmax_cluster.FILE_ID
     */
    private Long fileId;

    /**
     * dwd_shmoo_vmin_vmax_cluster.FILE_NAME
     */
    private String fileName;

    /**
     * dwd_shmoo_vmin_vmax_cluster.CUSTOMER
     */
    private String customer;

    /**
     * dwd_shmoo_vmin_vmax_cluster.UPLOAD_TYPE
     */
    private UploadType uploadType;

    /**
     * dwd_shmoo_vmin_vmax_cluster.ECID
     */
    private String ecid = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.TEST_PROGRAM
     */
    private String testProgram = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.TESTER_NAME
     */
    private String testerName = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.TEST_AREA
     */
    private TestArea testArea;

    /**
     * dwd_shmoo_vmin_vmax_cluster.DEVICE_ID
     */
    private String deviceId = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.LOT_ID
     */
    private String lotId = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.PROBER_HANDLER_ID
     */
    private String proberHandlerId = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.PROBER_HANDLER_TYP
     */
    private String proberHandlerTyp = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.TEST_ITEM
     */
    private String testItem = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.SITE_NUMBER
     */
    private Integer siteNumber;

    /**
     * dwd_shmoo_vmin_vmax_cluster.VMIN_RESULT
     */
    private BigDecimal vminResult;

    /**
     * dwd_shmoo_vmin_vmax_cluster.VMAX_RESULT
     */
    private BigDecimal vmaxResult;

    /**
     * dwd_shmoo_vmin_vmax_cluster.PROCESS
     */
    private String process = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.TEST_TEMPERATURE
     */
    private String testTemperature = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.CREATE_TIME
     */
    private Long createTime;

    /**
     * dwd_shmoo_vmin_vmax_cluster.CREATE_USER
     */
    private String createUser;

    /**
     * dwd_shmoo_vmin_vmax_cluster.UPLOAD_TIME
     */
    private Long uploadTime;

    /**
     * dwd_shmoo_vmin_vmax_cluster.PART_ID
     */
    private String partId = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.C_PART_ID
     */
    private Long cPartId;

    /**
     * dwd_shmoo_vmin_vmax_cluster.TEST_SUITE
     */
    private String testSuite = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.TEST_PATTERN
     */
    private String testPattern = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.SHMOO_NAME
     */
    private String shmooName = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.TEST_ORDER
     */
    private Long testOrder;

    /**
     * dwd_shmoo_vmin_vmax_cluster.EXECUTION_TYPE
     */
    private String executionType = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.EXECUTION_LABEL
     */
    private String executionLabel = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.EXECUTION_UNIT
     */
    private String executionUnit = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.EXECUTION_VALUE
     */
    private BigDecimal executionValue;

    /**
     * dwd_shmoo_vmin_vmax_cluster.SHMOO_RESULT
     */
    private Short shmooResult;

    /**
     * dwd_shmoo_vmin_vmax_cluster.SHMOO_RESULT_NAME
     */
    private String shmooResultName = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.SPECIFICATION
     */
    private String specification = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.IS_HOLE_FLAG
     */
    private Short isHoleFlag;

    /**
     * dwd_shmoo_vmin_vmax_cluster.SHMOO_JUDGE_RESULT
     */
    private String shmooJudgeResult = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.SUBLOOP
     */
    private String subloop = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.OPERATING_SEQUENC
     */
    private String operatingSequenc = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.TESTMETHOD
     */
    private String testmethod = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.AXISNUM
     */
    private Integer axisnum;

    /**
     * dwd_shmoo_vmin_vmax_cluster.ERRCNTFLAG
     */
    private Integer errcntflag;

    /**
     * dwd_shmoo_vmin_vmax_cluster.STDF_PART_TXT
     */
    private String stdfPartTxt = EMPTY;

    /**
     * dwd_shmoo_vmin_vmax_cluster.CHAR_START_EVENTID
     */
    private Integer charStartEventid;

    /**
     * dwd_shmoo_vmin_vmax_cluster.START_TIME
     */
    private Long startTime;

    /**
     * dwd_shmoo_vmin_vmax_cluster.END_TIME
     */
    private Long endTime;

    /**
     * dwd_shmoo_vmin_vmax_cluster.LOT_BUCKET
     */
    private Integer lotBucket;

    /**
     * DATA_TYPE
     */
    private String dataType = EMPTY;
    /**
     * LOT_TYPE
     */
    private LotType lotType = LotType.EMPTY;
    /**
     * WAFER_ID
     */
    private String waferId = EMPTY;
    /**
     * EXTRA_INFO
     */
    private Map<String, String>  extraInfo = new HashMap<>();


    public Long getFileId() {
        return fileId;
    }

    public DwdShmooVminVmaxDetail setFileId(Long fileId) {
        this.fileId = fileId;
        return this;
    }

    public String getFileName() {
        return fileName;
    }

    public DwdShmooVminVmaxDetail setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public String getCustomer() {
        return customer;
    }

    public DwdShmooVminVmaxDetail setCustomer(String customer) {
        this.customer = customer;
        return this;
    }

    public UploadType getUploadType() {
        return uploadType;
    }

    public DwdShmooVminVmaxDetail setUploadType(UploadType uploadType) {
        this.uploadType = uploadType;
        return this;
    }

    public String getEcid() {
        return ecid;
    }

    public DwdShmooVminVmaxDetail setEcid(String ecid) {
        this.ecid = ecid;
        return this;
    }

    public String getTestProgram() {
        return testProgram;
    }

    public DwdShmooVminVmaxDetail setTestProgram(String testProgram) {
        this.testProgram = testProgram;
        return this;
    }

    public String getTesterName() {
        return testerName;
    }

    public DwdShmooVminVmaxDetail setTesterName(String testerName) {
        this.testerName = testerName;
        return this;
    }

    public TestArea getTestArea() {
        return testArea;
    }

    public DwdShmooVminVmaxDetail setTestArea(TestArea testArea) {
        this.testArea = testArea;
        return this;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public DwdShmooVminVmaxDetail setDeviceId(String deviceId) {
        this.deviceId = deviceId;
        return this;
    }

    public String getLotId() {
        return lotId;
    }

    public DwdShmooVminVmaxDetail setLotId(String lotId) {
        this.lotId = lotId;
        return this;
    }

    public String getProberHandlerId() {
        return proberHandlerId;
    }

    public DwdShmooVminVmaxDetail setProberHandlerId(String proberHandlerId) {
        this.proberHandlerId = proberHandlerId;
        return this;
    }

    public String getProberHandlerTyp() {
        return proberHandlerTyp;
    }

    public DwdShmooVminVmaxDetail setProberHandlerTyp(String proberHandlerTyp) {
        this.proberHandlerTyp = proberHandlerTyp;
        return this;
    }

    public String getTestItem() {
        return testItem;
    }

    public DwdShmooVminVmaxDetail setTestItem(String testItem) {
        this.testItem = testItem;
        return this;
    }

    public Integer getSiteNumber() {
        return siteNumber;
    }

    public DwdShmooVminVmaxDetail setSiteNumber(Integer siteNumber) {
        this.siteNumber = siteNumber;
        return this;
    }

    public BigDecimal getVminResult() {
        return vminResult;
    }

    public DwdShmooVminVmaxDetail setVminResult(BigDecimal vminResult) {
        this.vminResult = vminResult;
        return this;
    }

    public BigDecimal getVmaxResult() {
        return vmaxResult;
    }

    public DwdShmooVminVmaxDetail setVmaxResult(BigDecimal vmaxResult) {
        this.vmaxResult = vmaxResult;
        return this;
    }

    public String getProcess() {
        return process;
    }

    public DwdShmooVminVmaxDetail setProcess(String process) {
        this.process = process;
        return this;
    }

    public String getTestTemperature() {
        return testTemperature;
    }

    public DwdShmooVminVmaxDetail setTestTemperature(String testTemperature) {
        this.testTemperature = testTemperature;
        return this;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public DwdShmooVminVmaxDetail setCreateTime(Long createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public DwdShmooVminVmaxDetail setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public Long getUploadTime() {
        return uploadTime;
    }

    public DwdShmooVminVmaxDetail setUploadTime(Long uploadTime) {
        this.uploadTime = uploadTime;
        return this;
    }

    public String getPartId() {
        return partId;
    }

    public DwdShmooVminVmaxDetail setPartId(String partId) {
        this.partId = partId;
        return this;
    }

    public Long getcPartId() {
        return cPartId;
    }

    public DwdShmooVminVmaxDetail setcPartId(Long cPartId) {
        this.cPartId = cPartId;
        return this;
    }

    public String getTestSuite() {
        return testSuite;
    }

    public DwdShmooVminVmaxDetail setTestSuite(String testSuite) {
        this.testSuite = testSuite;
        return this;
    }

    public String getTestPattern() {
        return testPattern;
    }

    public DwdShmooVminVmaxDetail setTestPattern(String testPattern) {
        this.testPattern = testPattern;
        return this;
    }

    public String getShmooName() {
        return shmooName;
    }

    public DwdShmooVminVmaxDetail setShmooName(String shmooName) {
        this.shmooName = shmooName;
        return this;
    }

    public Long getTestOrder() {
        return testOrder;
    }

    public DwdShmooVminVmaxDetail setTestOrder(Long testOrder) {
        this.testOrder = testOrder;
        return this;
    }

    public String getExecutionType() {
        return executionType;
    }

    public DwdShmooVminVmaxDetail setExecutionType(String executionType) {
        this.executionType = executionType;
        return this;
    }

    public String getExecutionLabel() {
        return executionLabel;
    }

    public DwdShmooVminVmaxDetail setExecutionLabel(String executionLabel) {
        this.executionLabel = executionLabel;
        return this;
    }

    public String getExecutionUnit() {
        return executionUnit;
    }

    public DwdShmooVminVmaxDetail setExecutionUnit(String executionUnit) {
        this.executionUnit = executionUnit;
        return this;
    }

    public BigDecimal getExecutionValue() {
        return executionValue;
    }

    public DwdShmooVminVmaxDetail setExecutionValue(BigDecimal executionValue) {
        this.executionValue = executionValue;
        return this;
    }

    public Short getShmooResult() {
        return shmooResult;
    }

    public DwdShmooVminVmaxDetail setShmooResult(Short shmooResult) {
        this.shmooResult = shmooResult;
        return this;
    }

    public String getShmooResultName() {
        return shmooResultName;
    }

    public DwdShmooVminVmaxDetail setShmooResultName(String shmooResultName) {
        this.shmooResultName = shmooResultName;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public DwdShmooVminVmaxDetail setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public Short getIsHoleFlag() {
        return isHoleFlag;
    }

    public DwdShmooVminVmaxDetail setIsHoleFlag(Short isHoleFlag) {
        this.isHoleFlag = isHoleFlag;
        return this;
    }

    public String getShmooJudgeResult() {
        return shmooJudgeResult;
    }

    public DwdShmooVminVmaxDetail setShmooJudgeResult(String shmooJudgeResult) {
        this.shmooJudgeResult = shmooJudgeResult;
        return this;
    }

    public String getSubloop() {
        return subloop;
    }

    public DwdShmooVminVmaxDetail setSubloop(String subloop) {
        this.subloop = subloop;
        return this;
    }

    public String getOperatingSequenc() {
        return operatingSequenc;
    }

    public DwdShmooVminVmaxDetail setOperatingSequenc(String operatingSequenc) {
        this.operatingSequenc = operatingSequenc;
        return this;
    }

    public String getTestmethod() {
        return testmethod;
    }

    public DwdShmooVminVmaxDetail setTestmethod(String testmethod) {
        this.testmethod = testmethod;
        return this;
    }

    public Integer getAxisnum() {
        return axisnum;
    }

    public DwdShmooVminVmaxDetail setAxisnum(Integer axisnum) {
        this.axisnum = axisnum;
        return this;
    }

    public Integer getErrcntflag() {
        return errcntflag;
    }

    public DwdShmooVminVmaxDetail setErrcntflag(Integer errcntflag) {
        this.errcntflag = errcntflag;
        return this;
    }

    public String getStdfPartTxt() {
        return stdfPartTxt;
    }

    public DwdShmooVminVmaxDetail setStdfPartTxt(String stdfPartTxt) {
        this.stdfPartTxt = stdfPartTxt;
        return this;
    }

    public Integer getCharStartEventid() {
        return charStartEventid;
    }

    public DwdShmooVminVmaxDetail setCharStartEventid(Integer charStartEventid) {
        this.charStartEventid = charStartEventid;
        return this;
    }

    public Long getStartTime() {
        return startTime;
    }

    public DwdShmooVminVmaxDetail setStartTime(Long startTime) {
        this.startTime = startTime;
        return this;
    }

    public Long getEndTime() {
        return endTime;
    }

    public DwdShmooVminVmaxDetail setEndTime(Long endTime) {
        this.endTime = endTime;
        return this;
    }

    public Integer getLotBucket() {
        return lotBucket;
    }

    public DwdShmooVminVmaxDetail setLotBucket(Integer lotBucket) {
        this.lotBucket = lotBucket;
        return this;
    }

    public String getDataType() {
        return dataType;
    }

    public DwdShmooVminVmaxDetail setDataType(String dataType) {
        this.dataType = dataType;
        return this;
    }

    public LotType getLotType() {
        return lotType;
    }

    public DwdShmooVminVmaxDetail setLotType(LotType lotType) {
        this.lotType = lotType;
        return this;
    }

    public String getWaferId() {
        return waferId;
    }

    public DwdShmooVminVmaxDetail setWaferId(String waferId) {
        this.waferId = waferId;
        return this;
    }

    public Map<String, String> getExtraInfo() {
        return extraInfo;
    }

    public DwdShmooVminVmaxDetail setExtraInfo(Map<String, String> extraInfo) {
        this.extraInfo = extraInfo;
        return this;
    }
}