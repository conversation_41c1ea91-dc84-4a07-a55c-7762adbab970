package com.guwave.onedata.dataware.scheduler.model.key;

import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;

import java.io.Serializable;
import java.util.Objects;

public class MesRequestKey implements Serializable {
    private String customer;
    private String subCustomer;
    private String factory;
    private String factorySite;
    private LotType lotType;
    private TestArea testArea;
    private String lotId;
    private String sblotId;
    private String deviceId;
    private String waferNo;
    private String testStage;

    public MesRequestKey() {
    }

    public MesRequestKey(String customer, String subCustomer, String factory, String factorySite, LotType lotType, TestArea testArea, String lotId, String sblotId, String deviceId, String waferNo, String testStage) {
        this.customer = customer;
        this.subCustomer = subCustomer;
        this.factory = factory;
        this.factorySite = factorySite;
        this.lotType = lotType;
        this.testArea = testArea;
        this.lotId = lotId;
        this.sblotId = sblotId;
        this.deviceId = deviceId;
        this.waferNo = waferNo;
        this.testStage = testStage;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getSubCustomer() {
        return subCustomer;
    }

    public void setSubCustomer(String subCustomer) {
        this.subCustomer = subCustomer;
    }

    public String getFactory() {
        return factory;
    }

    public void setFactory(String factory) {
        this.factory = factory;
    }

    public String getFactorySite() {
        return factorySite;
    }

    public void setFactorySite(String factorySite) {
        this.factorySite = factorySite;
    }

    public LotType getLotType() {
        return lotType;
    }

    public void setLotType(LotType lotType) {
        this.lotType = lotType;
    }

    public TestArea getTestArea() {
        return testArea;
    }

    public void setTestArea(TestArea testArea) {
        this.testArea = testArea;
    }

    public String getLotId() {
        return lotId;
    }

    public void setLotId(String lotId) {
        this.lotId = lotId;
    }

    public String getSblotId() {
        return sblotId;
    }

    public void setSblotId(String sblotId) {
        this.sblotId = sblotId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getWaferNo() {
        return waferNo;
    }

    public void setWaferNo(String waferNo) {
        this.waferNo = waferNo;
    }

    public String getTestStage() {
        return testStage;
    }

    public void setTestStage(String testStage) {
        this.testStage = testStage;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MesRequestKey that = (MesRequestKey) o;
        return Objects.equals(customer, that.customer) && Objects.equals(subCustomer, that.subCustomer) && Objects.equals(factory, that.factory) && Objects.equals(factorySite, that.factorySite) && lotType == that.lotType && testArea == that.testArea && Objects.equals(lotId, that.lotId) && Objects.equals(sblotId, that.sblotId) && Objects.equals(deviceId, that.deviceId) && Objects.equals(waferNo, that.waferNo) && Objects.equals(testStage, that.testStage);
    }

    @Override
    public int hashCode() {
        return Objects.hash(customer, subCustomer, factory, factorySite, lotType, testArea, lotId, sblotId, deviceId, waferNo, testStage);
    }
}
