package com.guwave.onedata.dataware.source.agent.manual.util;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.model.raw.TestItemData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;

@Component
public class RawDataCommonUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(RawDataCommonUtil.class);

    @Value("${spring.maxRawDataThreadCnt}")
    private Integer maxRawDataThreadCnt;

    private static ThreadPoolExecutor THREAD_POOL_EXECUTOR;
    private final static String THREAD_PREFIX = "raw_data";

    private final static AtomicInteger threadActiveCount = new AtomicInteger(0);


    @PostConstruct
    public void init() {
        THREAD_POOL_EXECUTOR = ThreadPoolUtils.getNewThreadPoolExecutor(
                THREAD_PREFIX, maxRawDataThreadCnt, maxRawDataThreadCnt, maxRawDataThreadCnt
        );
    }

    public static boolean canRunTask() {
        if (threadActiveCount.get() >= THREAD_POOL_EXECUTOR.getCorePoolSize()) {
            LOGGER.info("线程池已达最大处理数！");
            return false;
        }
        return true;
    }

    public static void runTask(Runnable runnable) {
        threadActiveCount.incrementAndGet();
        THREAD_POOL_EXECUTOR.execute(() -> {
            try {
                runnable.run();
            } finally {
                threadActiveCount.decrementAndGet();
            }
        });
    }

    public static final Map<String, BiConsumer<String, TestItemData>> RAW_DATA_COMMON_FIELD_CONSUMER_MAP = new HashMap<String, BiConsumer<String, TestItemData>>() {{
        put("batchNum", (str, rawData) -> rawData.setBatchNum(getIntegerValue(str)));
        put("batchNumIgnoreTp", (str, rawData) -> rawData.setBatchNumIgnoreTp(getIntegerValue(str)));
        put("deviceId", (str, rawData) -> rawData.setDeviceId(str));
        put("dupRetest", (str, rawData) -> rawData.setDupRetest(getIntegerValue(str)));
        put("dupRetestIgnoreTp", (str, rawData) -> rawData.setDupRetestIgnoreTp(getIntegerValue(str)));
        put("ecid", (str, rawData) -> rawData.setEcid(str));
        put("ecidExt", (str, rawData) -> rawData.setEcidExt(str));
        put("ecidExtra", (str, rawData) -> rawData.setEcidExtra(getMapValue(str)));
        put("endTime", (str, rawData) -> rawData.setEndTime(getTimeStamp(str)));
        put("factory", (str, rawData) -> rawData.setFactory(str));
        put("factorySite", (str, rawData) -> rawData.setFactorySite(str));
        put("flowId", (str, rawData) -> rawData.setFlowId(str));
        put("hbinNam", (str, rawData) -> rawData.setHbinNam(str));
        put("hbinNum", (str, rawData) -> rawData.setHbinNum(getLongValue(str)));
        put("hbinPf", (str, rawData) -> rawData.setHbinPf(str));
        put("hiLimit", (str, rawData) -> rawData.setHiLimit(getDoubleValue(str)));
        put("interrupt", (str, rawData) -> rawData.setInterrupt(getIntegerValue(str)));
        put("interruptIgnoreTp", (str, rawData) -> rawData.setInterruptIgnoreTp(getIntegerValue(str)));
        put("loLimit", (str, rawData) -> rawData.setLoLimit(getDoubleValue(str)));
        put("lotId", (str, rawData) -> rawData.setLotId(str));
        put("offlineRetest", (str, rawData) -> rawData.setOfflineRetest(getIntegerValue(str)));
        put("offlineRetestIgnoreTp", (str, rawData) -> rawData.setOfflineRetestIgnoreTp(getIntegerValue(str)));
        put("onlineRetest", (str, rawData) -> rawData.setOnlineRetest(getIntegerValue(str)));
        put("operatorName", (str, rawData) -> rawData.setOperatorName(str));
        put("partId", (str, rawData) -> rawData.setPartId(str));
        put("proberHandlerId", (str, rawData) -> rawData.setProberHandlerId(str));
        put("process", (str, rawData) -> rawData.setProcess(str));
        put("sbinNam", (str, rawData) -> rawData.setSbinNam(str));
        put("sbinNum", (str, rawData) -> rawData.setSbinNum(getLongValue(str)));
        put("sbinPf", (str, rawData) -> rawData.setSbinPf(str));
        put("sblotId", (str, rawData) -> rawData.setSblotId(str));
        put("site", (str, rawData) -> rawData.setSite(getLongValue(str)));
        put("startTime", (str, rawData) -> rawData.setStartTime(getTimeStamp(str)));
        put("testArea", (str, rawData) -> rawData.setTestArea(TestArea.of(str) == null ? null : TestArea.of(str).getArea()));
        put("testerName", (str, rawData) -> rawData.setTesterName(str));
        put("testitemType", (str, rawData) -> rawData.setTestitemType(str));
        put("testNum", (str, rawData) -> rawData.setTestNum(getLongValue(str)));
        put("testProgram", (str, rawData) -> rawData.setTestProgram(str));
        put("testProgramVersion", (str, rawData) -> rawData.setTestProgramVersion(str));
        put("testResult", (str, rawData) -> rawData.setTestResult(getIntegerValue(str)));
        put("testStage", (str, rawData) -> rawData.setTestStage(str));
        put("testSuite", (str, rawData) -> rawData.setTestSuite(str));
        put("testTemperature", (str, rawData) -> rawData.setTestTemperature(str));
        put("testTxt", (str, rawData) -> rawData.setTestTxt(str));
        put("testValue", (str, rawData) -> rawData.setTestValue(getDoubleValue(str)));
        put("uid", (str, rawData) -> rawData.setUid(str));
        put("units", (str, rawData) -> rawData.setUnits(str));
        put("waferId", (str, rawData) -> rawData.setWaferId(str));
        put("waferNo", (str, rawData) -> rawData.setWaferNo(str));
        put("wfFlat", (str, rawData) -> rawData.setWfFlat(str));
        put("xCoord", (str, rawData) -> rawData.setxCoord(getIntegerValue(str)));
        put("yCoord", (str, rawData) -> rawData.setyCoord(getIntegerValue(str)));
        put("lotType", (str, rawData) -> rawData.setLotType(LotType.of(str) == null ? null : LotType.of(str).getType()));


        put("cPartId", (str, rawData) -> rawData.setcPartId(getIntegerValue(str)));
        put("touchDownId", (str, rawData) -> rawData.setTouchDownId(getIntegerValue(str)));


        put("serial", (str, rawData) -> rawData.setSerial(str));
        put("textDat", (str, rawData) -> rawData.setTextDat(str));
        put("proberHandlerTyp", (str, rawData) -> rawData.setProberHandlerTyp(str));
        put("probecardLoadboardId", (str, rawData) -> rawData.setProbecardLoadboardId(str));
        put("probecardLoadboardTyp", (str, rawData) -> rawData.setProbecardLoadboardTyp(str));
        put("waferLotId", (str, rawData) -> rawData.setWaferLotId(str));
        put("testFlg", (str, rawData) -> rawData.setTestFlg(str));
        put("parmFlg", (str, rawData) -> rawData.setParmFlg(str));
        put("alarmId", (str, rawData) -> rawData.setAlarmId(str));
        put("optFlg", (str, rawData) -> rawData.setOptFlg(str));
        put("resScal", (str, rawData) -> rawData.setResScal(getIntegerValue(str)));
        put("numTest", (str, rawData) -> rawData.setNumTest(getIntegerValue(str)));
        put("llmScal", (str, rawData) -> rawData.setLlmScal(getIntegerValue(str)));
        put("hlmScal", (str, rawData) -> rawData.setHlmScal(getIntegerValue(str)));
        put("cResfmt", (str, rawData) -> rawData.setcResfmt(str));
        put("cLlmfmt", (str, rawData) -> rawData.setcLlmfmt(str));
        put("cHlmfmt", (str, rawData) -> rawData.setcHlmfmt(str));
        put("loSpec", (str, rawData) -> rawData.setLoSpec(getDoubleValue(str)));
        put("hiSpec", (str, rawData) -> rawData.setHiSpec(getDoubleValue(str)));
        put("specNam", (str, rawData) -> rawData.setSpecNam(str));
        put("specVer", (str, rawData) -> rawData.setSpecVer(str));
        put("testHead", (str, rawData) -> rawData.setTestHead(getLongValue(str)));
        put("testerType", (str, rawData) -> rawData.setTesterType(str));
        put("partFlg", (str, rawData) -> rawData.setPartFlg(str));
        put("testTime", (str, rawData) -> rawData.setTestTime(getLongValue(str)));
        put("partTxt", (str, rawData) -> rawData.setPartTxt(str));
        put("partFix", (str, rawData) -> rawData.setPartFix(str));
        put("siteGrp", (str, rawData) -> rawData.setSiteGrp(getLongValue(str)));
        put("siteCnt", (str, rawData) -> rawData.setSiteCnt(getLongValue(str)));
        put("siteNums", (str, rawData) -> rawData.setSiteNums(str));
        put("partCnt", (str, rawData) -> rawData.setPartCnt(getLongValue(str)));
        put("abrtCnt", (str, rawData) -> rawData.setAbrtCnt(getLongValue(str)));
        put("goodCnt", (str, rawData) -> rawData.setGoodCnt(getLongValue(str)));
        put("funcCnt", (str, rawData) -> rawData.setFuncCnt(getLongValue(str)));
        put("fabwfId", (str, rawData) -> rawData.setFabwfId(str));
        put("frameId", (str, rawData) -> rawData.setFrameId(str));
        put("maskId", (str, rawData) -> rawData.setMaskId(str));
        put("waferUsrDesc", (str, rawData) -> rawData.setWaferUsrDesc(str));
        put("waferExcDesc", (str, rawData) -> rawData.setWaferExcDesc(str));
        put("setupT", (str, rawData) -> rawData.setSetupT(getTimeStamp(str)));
        put("statNum", (str, rawData) -> rawData.setStatNum(getLongValue(str)));
        put("modeCod", (str, rawData) -> rawData.setModeCod(str));
        put("protCod", (str, rawData) -> rawData.setProtCod(str));
        put("burnTim", (str, rawData) -> rawData.setBurnTim(getLongValue(str)));
        put("cmodCod", (str, rawData) -> rawData.setCmodCod(str));
        put("execTyp", (str, rawData) -> rawData.setExecTyp(str));
        put("execVer", (str, rawData) -> rawData.setExecVer(str));
        put("userTxt", (str, rawData) -> rawData.setUserTxt(str));
        put("auxFile", (str, rawData) -> rawData.setAuxFile(str));
        put("pkgTyp", (str, rawData) -> rawData.setPkgTyp(str));
        put("famlyId", (str, rawData) -> rawData.setFamlyId(str));
        put("dateCod", (str, rawData) -> rawData.setDateCod(str));
        put("facilId", (str, rawData) -> rawData.setFacilId(str));
        put("floorId", (str, rawData) -> rawData.setFloorId(str));
        put("procId", (str, rawData) -> rawData.setProcId(str));
        put("operFrq", (str, rawData) -> rawData.setOperFrq(str));
        put("setupId", (str, rawData) -> rawData.setSetupId(str));
        put("dsgnRev", (str, rawData) -> rawData.setDsgnRev(str));
        put("engId", (str, rawData) -> rawData.setEngId(str));
        put("romCod", (str, rawData) -> rawData.setRomCod(str));
        put("serlNum", (str, rawData) -> rawData.setSerlNum(str));
        put("suprNam", (str, rawData) -> rawData.setSuprNam(str));
        put("dispCod", (str, rawData) -> rawData.setDispCod(str));
        put("lotUsrDesc", (str, rawData) -> rawData.setLotUsrDesc(str));
        put("lotExcDesc", (str, rawData) -> rawData.setLotExcDesc(str));
        put("dibTyp", (str, rawData) -> rawData.setDibTyp(str));
        put("dibId", (str, rawData) -> rawData.setDibId(str));
        put("cablTyp", (str, rawData) -> rawData.setCablTyp(str));
        put("cablId", (str, rawData) -> rawData.setCablId(str));
        put("contTyp", (str, rawData) -> rawData.setContTyp(str));
        put("contId", (str, rawData) -> rawData.setContId(str));
        put("lasrTyp", (str, rawData) -> rawData.setLasrTyp(str));
        put("lasrId", (str, rawData) -> rawData.setLasrId(str));
        put("extrTyp", (str, rawData) -> rawData.setExtrTyp(str));
        put("extrId", (str, rawData) -> rawData.setExtrId(str));
        put("retestBinNum", (str, rawData) -> rawData.setRetestBinNum(str));
        put("vectNam", (str, rawData) -> rawData.setVectNam(str));
        put("timeSet", (str, rawData) -> rawData.setTimeSet(str));
        put("numFail", (str, rawData) -> rawData.setNumFail(getLongValue(str)));
        put("failPin", (str, rawData) -> rawData.setFailPin(str));
        put("cyclCnt", (str, rawData) -> rawData.setCyclCnt(getLongValue(str)));
        put("reptCnt", (str, rawData) -> rawData.setReptCnt(getLongValue(str)));
        put("posX", (str, rawData) -> rawData.setPosX(str));
        put("posY", (str, rawData) -> rawData.setPosY(str));
        put("rtstCod", (str, rawData) -> rawData.setRtstCod(str));
        put("testCod", (str, rawData) -> rawData.setTestCod(str));
    }};

    public static Long getLongValue(String str) {
        return str.isEmpty() ? null : Double.valueOf(str).longValue();
    }

    public static Integer getIntegerValue(String str) {
        return str.isEmpty() ? null : Double.valueOf(str).intValue();
    }

    public static Double getDoubleValue(String str) {
        return str.isEmpty() ? null : Double.valueOf(str);
    }

    public static final String[] DATE_FORMAT_ALL = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy年MM月dd日 HH:mm:ss",
            "yyyyMMdd HH:mm:ss",
            "yyyy-MM-dd HH:mm",
            "yyyy/MM/dd HH:mm",
            "yyyy年MM月dd日 HH:mm",
            "yyyyMMdd HH:mm",
            "yyyy-MM-dd HH",
            "yyyy/MM/dd HH",
            "yyyy年MM月dd日 HH",
            "yyyyMMdd HH",
            "yyyy-MM-dd",
            "yyyy/MM/dd",
            "yyyy年MM月dd日",
            "yyyyMMdd"
    };

    public static Long getTimeStamp(String str) {
        if (str.isEmpty()) {
            return null;
        }
        try {
            Date date = org.apache.commons.lang.time.DateUtils.parseDate(str, DATE_FORMAT_ALL);
            return date.getTime() / 1000;
        } catch (Exception e) {
            throw new RuntimeException("时间格式解析失败：" + str, e);
        }
    }

    public static Map<String, String> getMapValue(String str) {
        Map<String, String> result = new HashMap<>();
        if (str.isEmpty()) {
            return result;
        }
        try {
            Map map = JSON.parseObject(str, Map.class);
            for (Object key : map.keySet()) {
                result.put(key.toString(), map.get(key).toString());
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException("json格式解析失败：" + str, e);
        }
    }


}
