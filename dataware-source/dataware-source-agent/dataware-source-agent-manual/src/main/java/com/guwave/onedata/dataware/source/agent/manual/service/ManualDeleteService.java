package com.guwave.onedata.dataware.source.agent.manual.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.manual.ManualDeleteVo;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingFinishMessage;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualDeleteTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualMessage;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualDeleteTaskRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualFileInfoRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualMessageRepository;
import com.guwave.onedata.dataware.source.agent.common.sink.impl.KafkaSink;
import com.guwave.onedata.dataware.source.agent.manual.util.CkUtil;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Component
public class ManualDeleteService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ManualDeleteService.class);

    @Value("${spring.kafka.manualFinishTopic}")
    private String manualFinishTopic;

    @Autowired
    private ManualDeleteTaskRepository manualDeleteTaskRepository;
    @Autowired
    private ManualMessageRepository manualMessageRepository;
    @Autowired
    private KafkaSink kafkaSink;
    @Autowired
    private CkUtil ckUtil;
    @Autowired
    private ManualFileInfoRepository manualFileInfoRepository;

    @ScheduleSwitch
    @Scheduled(fixedDelayString = "${spring.scheduler.polling.milliseconds}")
    public void deleteFile() {
        List<ManualDeleteTask> manualDeleteTasks = manualDeleteTaskRepository.findAllByProcessStatusOrderByIdAsc(ProcessStatus.CREATE, Pageable.ofSize(1));
        if (manualDeleteTasks.isEmpty()) {
            LOGGER.info("不存在需要处理的删除任务");
            return;
        }

        ManualDeleteTask manualDeleteTask = manualDeleteTasks.get(0);
        int updateCnt = manualDeleteTaskRepository.updateProcessStatusProcessingFromCreate(manualDeleteTask.getId(), new Date());
        if (updateCnt == 0) {
            LOGGER.info("{} 已经被处理", manualDeleteTask.getId());
            return;
        }

        LOGGER.info("deleteFile开始执行 manualDeleteTaskId：{}", manualDeleteTask.getId());
        ManualMessage manualMessage = manualMessageRepository.findById(manualDeleteTask.getMessageId()).get();
        List<Long> deletefileIds = Arrays.stream(manualDeleteTask.getDeleteFileIds().split(COMMA)).map(Long::parseLong).collect(Collectors.toList());
        try {
            // 删除ck数据
            ckUtil.deleteCkFromFileIds(deletefileIds);

            List<ManualFileInfo> manualFileInfos = manualFileInfoRepository.findAllByFileIdIn(deletefileIds);
            manualFileInfos.forEach(t -> t.setDeleteFlag(1));
            manualFileInfoRepository.saveAll(manualFileInfos);

            manualMessage.setProcessStatus(ProcessStatus.SUCCESS);
            LOGGER.info("deleteFile执行成功 manualDeleteTaskId：{}", manualDeleteTask.getId());
        } catch (Exception e) {
            LOGGER.error("deleteFile执行失败 manualDeleteTaskId：{}", manualDeleteTask.getId(), e);
            FileLoadException exception = e instanceof FileLoadException ? (FileLoadException) e : new FileLoadException(FileLoadExceptionInfo.OTHER_EXCEPTION, ExceptionUtils.getStackTrace(e), null).updateExceptionMessage(e.getMessage());
            manualMessage
                    .setProcessStatus(ProcessStatus.FAIL)
                    .setExceptionType(exception.getExceptionType())
                    .setExceptionMessage(exception.getExceptionMessage())
                    .setErrorMessage(ExceptionUtils.getStackTrace(exception));
        } finally {
            ManualWarehousingFinishMessage manualWarehousingFinishMessage = new ManualWarehousingFinishMessage()
                    .setManualType(manualMessage.getManualType())
                    .setManualDeleteVo(new ManualDeleteVo().setFileIds(deletefileIds))
                    .setProcessStatus(manualMessage.getProcessStatus())
                    .setExceptionType(manualMessage.getExceptionType())
                    .setExceptionMessage(manualMessage.getExceptionMessage());
            String manualFinishMessageStr = JSON.toJSONString(manualWarehousingFinishMessage);
            Date date = new Date();
            manualMessage
                    .setManualFinishMessage(manualFinishMessageStr)
                    .setUpdateTime(date);
            manualDeleteTask
                    .setProcessStatus(manualMessage.getProcessStatus())
                    .setUpdateTime(date);

            manualDeleteTaskRepository.save(manualDeleteTask);
            manualMessageRepository.save(manualMessage);
            // 发送结束消息
            kafkaSink.send(manualFinishTopic, manualMessage.getManualType().getType(), manualMessage.getManualFinishMessage());
        }


    }

}
