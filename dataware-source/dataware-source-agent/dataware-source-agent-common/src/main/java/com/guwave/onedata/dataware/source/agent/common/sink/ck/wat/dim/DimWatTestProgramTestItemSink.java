package com.guwave.onedata.dataware.source.agent.common.sink.ck.wat.dim;

import com.guwave.onedata.dataware.common.model.wat.dim.DimWatTestProgramTestItem;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import com.guwave.onedata.dataware.source.agent.common.util.DateUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/13 20:58
 * @description DimWatTestProgramTestItemSink
 */
@Component
public class DimWatTestProgramTestItemSink implements CkSink<DimWatTestProgramTestItem> {
    @Value("${spring.sink.ck.dim.dbName}")
    private String dbName;

    @Override
    public String getTableName() {
        return "dim_test_program_test_item_local";
    }

    @Override
    public void handle(PreparedStatement statement, List<DimWatTestProgramTestItem> items) throws SQLException {
        for (DimWatTestProgramTestItem item : items) {
            statement.setObject(1, item.getCustomer());
            statement.setObject(2, item.getUploadType());
            statement.setObject(3, item.getFactory());
            statement.setObject(4, item.getFactorySite());
            statement.setObject(5, item.getFab());
            statement.setObject(6, item.getFabSite());
            statement.setObject(7, item.getTestArea());
            statement.setObject(8, item.getTestStage());
            statement.setObject(9, item.getDeviceId());
            statement.setObject(10, item.getTestProgram());
            statement.setObject(11, item.getTestProgramVersion());
            statement.setObject(12, item.getTestNum());
            statement.setObject(13, item.getTestTxt());
            statement.setObject(14, item.getTestItem());
            statement.setObject(15, item.getTestitemType());
            statement.setObject(16, this.toBigDecimal(item.getOriginHiLimit()));
            statement.setObject(17, this.toBigDecimal(item.getOriginLoLimit()));
            statement.setObject(18, item.getOriginUnits());
            statement.setObject(19, this.toBigDecimal(item.getLoLimit()));
            statement.setObject(20, this.toBigDecimal(item.getHiLimit()));
            statement.setObject(21, item.getUnits());
            // CREATE_HOUR_KEY
            statement.setObject(22, DateUtil.getDayHour(System.currentTimeMillis()));
            // CREATE_DAY_KEY
            statement.setObject(23, DateUtil.getDate(System.currentTimeMillis()));
            // CREATE_TIME
            statement.setObject(24, new Timestamp(System.currentTimeMillis()));
            // CREATE_USER
            statement.setObject(25, item.getCreateUser());
            statement.setObject(26, item.getVersion());
            statement.setObject(27, item.getSubCustomer());
            statement.setObject(28, item.getTestTemperature());
            statement.setObject(29, item.getConditionSet());
            statement.setObject(30, item.getTestNumKey());
            statement.setObject(31, new Timestamp(item.getUploadTime()));
            statement.setObject(32, item.getFileId());
            statement.setObject(33, stringValue(toBigDecimal(item.getHiLimit())));
            statement.setObject(34, stringValue(toBigDecimal(item.getLoLimit())));
            statement.setObject(35, stringValue(toBigDecimal(item.getOriginHiLimit())));
            statement.setObject(36, stringValue(toBigDecimal(item.getOriginLoLimit())));
            statement.setObject(37, this.toBigDecimal(item.getTarget()));

            statement.addBatch();
        }

    }


    @Override
    public List<String> getColumns() {
        return Arrays.asList(
                "CUSTOMER",
                "UPLOAD_TYPE",
                "FACTORY",
                "FACTORY_SITE",
                "FAB",
                "FAB_SITE",
                "TEST_AREA",
                "TEST_STAGE",
                "DEVICE_ID",
                "TEST_PROGRAM",
                "TEST_PROGRAM_VERSION",
                "TEST_NUM",
                "TEST_TXT",
                "TEST_ITEM",
                "TESTITEM_TYPE",
                "ORIGIN_HI_LIMIT",
                "ORIGIN_LO_LIMIT",
                "ORIGIN_UNITS",
                "LO_LIMIT",
                "HI_LIMIT",
                "UNITS",
                "CREATE_HOUR_KEY",
                "CREATE_DAY_KEY",
                "CREATE_TIME",
                "CREATE_USER",
                "VERSION",
                "SUB_CUSTOMER",
                "TEST_TEMPERATURE",
                "CONDITION_SET",
                "TEST_NUM_KEY",
                "UPLOAD_TIME",
                "FILE_ID",
                "HI_LIMIT_KEY",
                "LO_LIMIT_KEY",
                "ORIGIN_HI_LIMIT_KEY",
                "ORIGIN_LO_LIMIT_KEY",
                "TARGET"
        );
    }

    @Override
    public String getDbName() {
        return dbName;
    }
}
