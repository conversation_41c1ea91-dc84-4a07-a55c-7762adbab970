package com.guwave.onedata.dataware.scheduler.handler;

import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LayerCalculatePool;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotStockingDetail;
import com.guwave.onedata.dataware.dao.mysql.manager.SftpFileDetailManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LayerCalculatePoolRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LotStockingDetailRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;


public interface OdsLayerHandler extends Handler {
    Logger LOGGER = LoggerFactory.getLogger(OdsLayerHandler.class);

    @Override
    default boolean isSupport(CalculateEndFlag endFlag) {
        return endFlag.getDwLayer() == DwLayer.ODS
                && endFlag.getExecuteMode() == ExecuteMode.AUTO
                && endFlag.getPlatform() == Platform.GDP
                && endFlag.getProcessStatus() == ProcessStatus.SUCCESS;
    }

    @Override
    default void doHandle(CalculateEndFlag endFlag) {

        LayerCalculatePool layerCalculatePool = getLayerCalculatePoolRepository().findByCustomerAndTestAreaAndFactoryAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoAndFileCategoryAndDwLayerAndDelayFlagAndProcessStatus(
                endFlag.getCustomer(),
                endFlag.getTestArea(),
                endFlag.getFactory(),
                endFlag.getDeviceId(),
                endFlag.getLotType(),
                endFlag.getTestStage(),
                endFlag.getLotId(),
                endFlag.getWaferNo(),
                endFlag.getFileCategory(),
                DwLayer.ODS,
                0,
                ProcessStatus.CREATE
        );

        if (layerCalculatePool == null) {
            // 新增数据
            layerCalculatePool = buildLayerCalculatePool(endFlag, 0);
        } else {
            // 已经存在的数据 则partition_num往上加1
            layerCalculatePool.setPartitionNum(layerCalculatePool.getPartitionNum() + 1);
            // 替换原有的优先级
            layerCalculatePool.setPriority(endFlag.getPriority());
        }

        SftpFileDetailManager.RunTaskFilesContext runTaskFilesInfo = getSftpFileDetailManager().getRunTaskFilesInfo(layerCalculatePool, false);
        if (layerCalculatePool.getRunMode() == null) {
            layerCalculatePool.setRunMode(this.runModeStrategy(runTaskFilesInfo));
        }
        layerCalculatePool.setCleanupTaskIds(runTaskFilesInfo.getCleanupTaskIdList());

        if (layerCalculatePool.getPartitionNum() >= getTestAreaMaxPartition(endFlag.getTestArea())) {
            // partition_num 已经达到达到最大
            // 新增一条待转换成dwd的ods延时记录
            LayerCalculatePool delayLayerCalculatePool = new LayerCalculatePool();
            BeanUtils.copyProperties(layerCalculatePool, delayLayerCalculatePool);
            Date now = new Date();
            delayLayerCalculatePool
                    .setId(null)
                    .setProcessStatus(ProcessStatus.CREATE)
                    .setCreateTime(now)
                    .setUpdateTime(now)
                    .setDelayFlag(1);
            getLayerCalculatePoolRepository().save(delayLayerCalculatePool);

            layerCalculatePool.setProcessStatus(ProcessStatus.SUCCESS);
        }
        layerCalculatePool.setUpdateTime(new Date());
        getLayerCalculatePoolRepository().save(layerCalculatePool);
    }

    SftpFileDetailManager getSftpFileDetailManager();

    /**
     * 根据当前计算的测项总条数决策使用的RunMode
     *
     * @return RunMode
     */
    default RunMode runModeStrategy(SftpFileDetailManager.RunTaskFilesContext runTaskFilesInfo) {
        if (runTaskFilesInfo.getTestItemCnt() > this.getManualThreshold()) {
            return RunMode.MANUAL;
        }
        return runTaskFilesInfo.getTestItemCnt() < this.getStandaloneThreshold() ? RunMode.STANDALONE : RunMode.DISTRIBUTED;
    }

    default LayerCalculatePool buildLayerCalculatePool(CalculateEndFlag endFlag, Integer delayFlag) {
        Date date = new Date();
        return new LayerCalculatePool()
                .setCustomer(endFlag.getCustomer())
                .setSubCustomer(endFlag.getSubCustomer())
                .setFactory(endFlag.getFactory())
                .setFactorySite(endFlag.getFactorySite())
                .setTestArea(endFlag.getTestArea())
                .setLotType(endFlag.getLotType())
                .setDeviceId(endFlag.getDeviceId())
                .setTestStage(endFlag.getTestStage())
                .setLotId(endFlag.getLotId())
                .setWaferNo(endFlag.getWaferNo())
                .setFileCategory(endFlag.getFileCategory())
                .setDwLayer(endFlag.getDwLayer())
                .setNextDwLayer(endFlag.getNextDwLayer())
                .setPartitionNum(1)
                .setDelayFlag(delayFlag)
                .setPriority(endFlag.getPriority())
                .setWarehousingMode(endFlag.getWarehousingMode())
                .setRepairLotWaferId(endFlag.getRepairLotWaferId())
                .setCleanupTaskIds(endFlag.getCleanupTaskIds())
                .setProcessStatus(ProcessStatus.CREATE)
                .setStopNextFlag(0)
                .setCreateTime(date)
                .setUpdateTime(date)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateUser(Constant.SYSTEM)
                ;
    }

    default Long getTestAreaMaxPartition(TestArea testArea) {
        return 1L;
    }

    LayerCalculatePoolRepository getLayerCalculatePoolRepository();

    LotStockingDetailRepository getLotStockingDetailRepository();

    Long getStandaloneThreshold();

    Long getManualThreshold();
}
