package com.guwave.onedata.dataware.source.agent.manual.model;

import com.guwave.onedata.dataware.common.contant.ManualConvertFileType;

/**
 * 2023/11/16 11:51
 * ManualConvertFileInfo
 * <AUTHOR>
 */
public class ManualConvertFileInfo {
    private String fileFullPath;
    private String fileName;
    private ManualConvertFileType fileType;

    public String getFileFullPath() {
        return fileFullPath;
    }

    public ManualConvertFileInfo setFileFullPath(String fileFullPath) {
        this.fileFullPath = fileFullPath;
        return this;
    }

    public String getFileName() {
        return fileName;
    }

    public ManualConvertFileInfo setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public ManualConvertFileType getFileType() {
        return fileType;
    }

    public ManualConvertFileInfo setFileType(ManualConvertFileType fileType) {
        this.fileType = fileType;
        return this;
    }
}
