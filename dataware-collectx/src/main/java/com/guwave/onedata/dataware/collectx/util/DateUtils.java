package com.guwave.onedata.dataware.collectx.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * Copyright (C), 2021, guwave
 *
 * <AUTHOR> <PERSON>
 * @version 0.0.1
 * @Title: DateUtils.java
 * @summary
 * @date 2021-12-14
 */
@Slf4j
public class DateUtils {

    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static Date parse(String str, String format) {
        if (StringUtils.isBlank(str) || StringUtils.isBlank(format)) {
            return null;
        }

        try {
            return org.apache.commons.lang3.time.DateUtils.parseDateStrictly(str, format);
//            SimpleDateFormat sdf = new SimpleDateFormat(format);
//            sdf.setLenient(false);
//            Date result = sdf.parse(str);
//            log.debug("日期解析成功: str:{}, format:{}", str, format);
//            return result;
        } catch (ParseException e) {
            log.debug("日期解析失败: str:{}, format:{}", str, format);
            return null;
        }
    }

    public static String parseDateToStr(final String format, final Date date) {
        if (null == date) {
            return "";
        }
        return new SimpleDateFormat(format).format(date);
    }


    public static void dayBegin(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
    }

    public static void dayEnd(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
    }

    public static String getNextNDayString(String dateStr, int nDay, String datePattern) {
        try {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat(datePattern);
            Date date = null;
            date = sdf.parse(dateStr);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, nDay);
            return sdf.format(calendar.getTime());
        } catch (ParseException e) {
            log.error(e.toString(), e);
            return null;
        } catch (Exception e) {
            log.error(e.toString(), e);
            return null;
        }

    }

    public static Date getNextNDay(String dateStr, int nDay, String datePattern) {
        try {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat(datePattern);
            Date date = null;
            date = sdf.parse(dateStr);
            calendar.setTime(date);
            calendar.add(Calendar.DATE, nDay);
            return calendar.getTime();
        } catch (ParseException e) {
            log.error(e.toString(), e);
            return null;
        }
    }

    public static Date getNextNDay(Date date, int nDay) {
        if (Objects.isNull(date)) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, nDay);
        return calendar.getTime();
    }

    public static void main(String[] args) throws ParseException {
        System.out.println(parse("2022", "MMyyyy"));
        System.out.println(parseDateToStr("yyyy-MM-dd", parse("01.14RTB0000.", "MM.yyyy")));
    }
}
