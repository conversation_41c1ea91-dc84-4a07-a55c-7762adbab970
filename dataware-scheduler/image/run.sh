#!/bin/bash

echo 'export JAVA_OPTS='\"$JAVA_OPTS\" >> /etc/profile.d/java.sh
source /etc/profile.d/java.sh

groupadd -g $GROUP_ID $ENVIRON_GROUP
useradd -M -u $USER_ID -g $GROUP_ID $ENVIRON_USER
chown -R $ENVIRON_USER:$ENVIRON_GROUP /home/<USER>

sed -i 's/session\t\tinclude\t\tsystem-auth/session\t\toptional\tsystem-auth/g' /etc/pam.d/su
sed -i 's/session\t\tinclude\t\tpostlogin/session\t\toptional\tpostlogin/g' /etc/pam.d/su
su $ENVIRON_USER -l -s /bin/bash -c 'export LANG="en_US.UTF-8" ; export LC_ALL="en_US.UTF-8" ; LANG=en_US.UTF-8; LC_ALL=en_US.UTF-8; cd /home/<USER>/deploy2/onedata/dataware/dataware-scheduler; tar xf /home/<USER>/deploy2/onedata/dataware/dataware-scheduler/dataware-scheduler-'$VERSION'.tar -k; cd /home/<USER>/deploy2/onedata/dataware/dataware-scheduler/dataware-scheduler-'$VERSION' ; bin/dataware-scheduler'
