package com.guwave.onedata.dataware.collectx.service.impl;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.collectx.manager.redis.SftpFileRedisManager;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpCollectRule;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpCollectRuleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class PushSftpTaskService {

    @Autowired
    private SftpCollectRuleRepository sftpCollectRuleRepository;

    public void pushTask() {
        boolean lockPushTask = SftpFileRedisManager.lockPushCollectTask();
        if (!lockPushTask) {
            log.info("pushTask 正在被处理");
            return;
        }

        try {
            List<String> cacheSftpCollectRules = SftpFileRedisManager.getCacheSftpCollectRuleList();
            if (cacheSftpCollectRules == null) {
                cacheSftpCollectRules = new ArrayList<>();
            }

            List<SftpCollectRule> allCollectRules = sftpCollectRuleRepository.findAllByStatusAndDeleteFlagOrderByUpdateTimeDesc(1, 0);

            List<String> needCacheSftpCollectRules = new ArrayList<>();
            for (SftpCollectRule sftpCollectRule : allCollectRules) {
                if (!cacheSftpCollectRules.contains(sftpCollectRule.getId().toString())) {
                    needCacheSftpCollectRules.add(sftpCollectRule.getId().toString());
                }
            }

            log.info("push task {}", JSON.toJSONString(needCacheSftpCollectRules));
            SftpFileRedisManager.pushCollectRuleList(needCacheSftpCollectRules);
        } finally {
            SftpFileRedisManager.unLockPushCollectTask();
        }
    }
}
