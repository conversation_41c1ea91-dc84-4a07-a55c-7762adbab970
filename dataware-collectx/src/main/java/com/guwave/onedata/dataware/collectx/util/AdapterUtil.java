package com.guwave.onedata.dataware.collectx.util;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.ConvertStatus;
import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.contant.RawDataConvertFailType;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.FutureTask;
import java.util.function.Consumer;

import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;

/**
 * Copyright (C), 2022, guwave
 * <p>
 * adapter工具类
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2022-08-10 10:17:04
 */
@Component
public class AdapterUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(AdapterUtil.class);

    private static final String SYSTEM_CODE = "SYSTEM CODE:";
    private static String pythonPath = null;

    @Value("${spring.adapter.python.install.path}")
    private String python;


    @PostConstruct
    public void init() {
        pythonPath = python;
    }

    /**
     * 执行python adapter脚本
     *
     * @param pyPath 脚本路径
     * @param args   脚本传入参数
     * @return <是否执行成功,附带信息Map>
     */
    public static Pair<Boolean, HashMap<Object, Object>> executePython(String pyPath, List<String> args, Consumer<String> logConsumer) throws Exception {
        boolean status = false;
        HashMap<Object, Object> resultMap = new HashMap<>();
        StringBuilder errorStringBuilder = new StringBuilder();

        args.add(0, pyPath);
        args.add(0, pythonPath);
        LOGGER.info("开始执行脚本: {}", args);
        StopWatch sw = new StopWatch();
        sw.start();
        Process proc = null;
        try {
            proc = Runtime.getRuntime().exec(args.toArray(new String[0]));

            Process finalProc = proc;
            FutureTask<List<String>> inputTask = new FutureTask<>(() -> readInput(args, finalProc, logConsumer));
            FutureTask<List<String>> errorTask = new FutureTask<>(() -> readError(args, finalProc, logConsumer));
            new Thread(inputTask).start();
            new Thread(errorTask).start();
            List<String> inputList = inputTask.get();
            List<String> errorList = errorTask.get();

            String lastInputline = "";
            if (CollectionUtils.isNotEmpty(inputList)) {
                for (String line : inputList) {
                    LOGGER.info("执行脚本 {}, 输出: {}", args, line);
                    if (line.startsWith(ConvertStatus.WARNING_MESSAGE_PREFIX.getStatus())) {
                        String resultStr = line.replace(ConvertStatus.WARNING_MESSAGE_PREFIX.getStatus(), EMPTY).trim();
                        resultMap.put(ConvertStatus.WARNING_MESSAGE_PREFIX.getStatus(), resultStr);
                    }
                }
                lastInputline = inputList.get(inputList.size() - 1);
                LOGGER.info("执行脚本 {}, lastInputline: {}", args, lastInputline);
                if (lastInputline.startsWith(ConvertStatus.TO_ONE_SUCCESS_PREFIX.getStatus())) {
                    status = true;
                    String resultStr = lastInputline.replace(ConvertStatus.TO_ONE_SUCCESS_PREFIX.getStatus(), EMPTY).trim();
                    if (StringUtils.isNotEmpty(resultStr)) {
                        resultMap = JSONObject.parseObject(resultStr, HashMap.class);
                    }
                } else if (lastInputline.startsWith(ConvertStatus.TO_MANY_SUCCESS_PREFIX.getStatus())) {
                    status = true;
                    String resultStr = lastInputline.replace(ConvertStatus.TO_MANY_SUCCESS_PREFIX.getStatus(), EMPTY).trim();
                    resultMap.put(ConvertStatus.TO_MANY_SUCCESS_PREFIX.getStatus(), resultStr);
                }
            }

            if (lastInputline.startsWith(SYSTEM_CODE)) {
                errorStringBuilder.append(lastInputline).append("\n");
            }
            if (CollectionUtils.isNotEmpty(errorList)) {
                errorList.forEach(errorLine -> errorStringBuilder.append(errorLine).append("\n"));
            }

            proc.waitFor();
        } catch (Exception e) {
            LOGGER.error("执行脚本 {} 异常 ", args, e);
            throw e;
        } finally {
            if (proc != null) {
                try {
                    proc.getInputStream().close();
                } catch (Exception ignored) {
                }
                try {
                    proc.getErrorStream().close();
                } catch (Exception ignored) {
                }
                try {
                    proc.getOutputStream().close();
                } catch (Exception ignored) {
                }
            }
            sw.stop();
            LOGGER.info("执行 {} 耗时: {}ms", args, sw.getTotalTimeMillis());
        }
        if (status) {
            LOGGER.info("执行脚本 {} 转换成功", args);
            return new Pair<>(status, resultMap);
        } else {
            LOGGER.error("执行脚本 {} 转换失败", args);
            errorStringBuilder.insert(0, " 转换失败！\n").insert(0, args).insert(0, "执行脚本 : ");
            FileLoadExceptionInfo fileLoadExceptionInfo = getFileLoadExceptionInfo(errorStringBuilder.toString());
            if (fileLoadExceptionInfo != null) {
                throw new FileLoadException(fileLoadExceptionInfo, errorStringBuilder.toString(), null);
            }
            throw new FileLoadException(FileLoadExceptionInfo.PYTHON_SCRIPT_EXECUTE_FAIL_EXCEPTION, errorStringBuilder.toString(), null);
        }
    }

    private static List<String> readInput(List<String> args, Process proc, Consumer<String> logConsumer) {
        try (InputStream inputStream = proc.getInputStream();
             InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            return read(args, bufferedReader, logConsumer);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static List<String> readError(List<String> args, Process proc, Consumer<String> logConsumer) {
        try (InputStream errorStream = proc.getErrorStream();
             InputStreamReader inputStreamReader = new InputStreamReader(errorStream, StandardCharsets.UTF_8);
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            return read(args, bufferedReader, logConsumer);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static List<String> read(List<String> args, BufferedReader reader, Consumer<String> logConsumer) throws Exception {
        List<String> resultList = Lists.newArrayList();
        String res;
        while ((res = reader.readLine()) != null) {
            LOGGER.info("执行脚本 {} 返回: {}", args, res);
            resultList.add(res);
            if (logConsumer != null) {
                try {
                    logConsumer.accept(res);
                } catch (Exception e) {
                    LOGGER.info("{} log日志消费异常", res, e);
                }
            }
        }
        return resultList;
    }

    private static FileLoadExceptionInfo getFileLoadExceptionInfo(String errorString) {
        if (StringUtils.isBlank(errorString)) {
            return null;
        }

        return RawDataConvertFailType.getExceptionInfoMap().entrySet().stream()
                .filter(entry -> errorString.contains(entry.getKey()))
                .findFirst()
                .map(Map.Entry::getValue)
                .orElse(null);
    }

    public static void main(String[] args) throws Exception {
        pythonPath = "C:\\1_D\\soft\\anaconda\\envs\\py39\\python.exe";

        Pair<Boolean, HashMap<Object, Object>> booleanHashMapPair = executePython("C:\\home\\qa\\deploy\\onedata\\dataware\\dataware-collectx\\GUWAVE\\rawData\\need_convert\\K45675.7z\\dosilicon_smic_cp_7z_asc_auto\\dosilicon_smic_cp_7z_asc_auto.py", Lists.newArrayList("C:\\home\\qa\\deploy\\onedata\\dataware\\dataware-collectx\\GUWAVE\\rawData\\need_convert\\K45675.7z\\K45675.7z", "C:\\home\\qa\\deploy\\onedata\\dataware\\dataware-collectx\\GUWAVE\\rawData\\convert\\K45675.7z.rawdata", "K45675.7z"), null);
        System.out.println(booleanHashMapPair);
    }
}
