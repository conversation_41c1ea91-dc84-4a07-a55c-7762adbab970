package com.guwave.onedata.dataware.source.common.serialization.protostuff.reference;

import com.guwave.onedata.dataware.common.model.wat.dim.DimWatTestItem;
import com.guwave.onedata.dataware.common.model.wat.dim.DimWatTestProgramBin;
import com.guwave.onedata.dataware.common.model.wat.dim.DimWatTestProgramSite;
import com.guwave.onedata.dataware.common.model.wat.dim.DimWatTestProgramTestItem;
import com.guwave.onedata.dataware.common.model.wat.dim.DimWatWafer;
import com.guwave.onedata.dataware.common.model.wat.dim.DimWatWaferBin;
import com.guwave.onedata.dataware.common.model.wat.dwd.DwdWatDieDetail;
import com.guwave.onedata.dataware.common.model.wat.dwd.DwdWatTestItemDetail;
import com.guwave.onedata.dataware.common.model.wat.ods.OdsWat;
import com.guwave.onedata.dataware.source.common.serialization.protostuff.TypeReference;

import java.util.List;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * WatReference
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2022-05-19 10:31:26
 */
public class WatReference {

    /**
     * 根据类型获取reference
     *
     * @param type type
     * @return TypeReference
     */
    public static TypeReference getReference(String type) {
        switch (type) {
            case ODS_WAT:
                return new TypeReference<List<OdsWat>>() {
                    private static final long serialVersionUID = -3695004137202498652L;
                };
            case DWD_DIE_DETAIL:
                return new TypeReference<List<DwdWatDieDetail>>() {
                    private static final long serialVersionUID = 5099130064848804767L;
                };
            case DWD_TEST_ITEM_DETAIL:
                return new TypeReference<List<DwdWatTestItemDetail>>() {
                    private static final long serialVersionUID = 4958117251149060770L;
                };
            case DIM_TEST_ITEM:
                return new TypeReference<List<DimWatTestItem>>() {
                    private static final long serialVersionUID = 2113946202988857254L;
                };
            case DIM_LOT_WAFER:
                return new TypeReference<List<DimWatWafer>>() {
                    private static final long serialVersionUID = 2115843907234355952L;
                };
            case DIM_LOT_WAFER_BIN:
                return new TypeReference<List<DimWatWaferBin>>() {
                    private static final long serialVersionUID = -5287150009739049431L;
                };
            case DIM_TEST_PROGRAM_SITE:
                return new TypeReference<List<DimWatTestProgramSite>>() {
                    private static final long serialVersionUID = 830317985678023569L;
                };
            case DIM_TEST_PROGRAM_BIN:
                return new TypeReference<List<DimWatTestProgramBin>>() {
                    private static final long serialVersionUID = 1904433721607077057L;
                };
            case DIM_TEST_PROGRAM_TEST_ITEM:
                return new TypeReference<List<DimWatTestProgramTestItem>>() {
                    private static final long serialVersionUID = 5309541808696085324L;
                };
            default:
                return null;
        }
    }
}
