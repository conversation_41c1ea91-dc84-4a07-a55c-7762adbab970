package com.guwave.onedata.dataware.collectx.manager.thread;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileRenameRule;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class FileRenameRuleThreadLocalUtil {

    private static final ThreadLocal<List<FileRenameRule>> FILE_RENAME_THREAD_LOCAL = new ThreadLocal<>();

    public static List<FileRenameRule> getFileRenameRules(){
        return FILE_RENAME_THREAD_LOCAL.get();
    }

    public static void setStdfFileRenameRules(List<FileRenameRule> fileRenameRules) {
        if(CollectionUtils.isNotEmpty(fileRenameRules)){
            FILE_RENAME_THREAD_LOCAL.set(fileRenameRules);
        }
        else{
            FILE_RENAME_THREAD_LOCAL.set(new ArrayList<>());
        }
    }

    public static void clear(){FILE_RENAME_THREAD_LOCAL.remove();}
}
