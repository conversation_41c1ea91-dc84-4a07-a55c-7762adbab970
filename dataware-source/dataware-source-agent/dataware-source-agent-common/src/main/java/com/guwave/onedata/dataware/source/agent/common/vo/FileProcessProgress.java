package com.guwave.onedata.dataware.source.agent.common.vo;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;

import java.io.Serializable;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * FileProcessProgress
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-14 11:06:43
 */
public class FileProcessProgress implements Serializable {

    private static final long serialVersionUID = 2019554795738117782L;

    // 文件名
    private String name;
    // 文件处理状态
    private ProcessStatus status;
    // 执行次数
    private Integer execNum = 0;
    // 时间戳
    private Long ts;

    public String getName() {
        return name;
    }

    public FileProcessProgress setName(String name) {
        this.name = name;
        return this;
    }

    public ProcessStatus getStatus() {
        return status;
    }

    public FileProcessProgress setStatus(ProcessStatus status) {
        this.status = status;
        return this;
    }

    public Integer getExecNum() {
        return execNum;
    }

    public FileProcessProgress setExecNum(Integer execNum) {
        this.execNum = execNum;
        return this;
    }

    public Long getTs() {
        return ts;
    }

    public FileProcessProgress setTs(Long ts) {
        this.ts = ts;
        return this;
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder()
                .append("name").append(Constant.WHITE_SPACE).append(this.getName()).append(Constant.WHITE_SPACE)
                .append("execNum").append(Constant.WHITE_SPACE).append(this.getExecNum()).append(Constant.WHITE_SPACE);

        if (this.getStatus() == ProcessStatus.PROCESSING) {
            builder.append("Is Processing");
        } else if (this.getStatus() == ProcessStatus.SUCCESS) {
            builder.append("Is Succeed");
        } else if (this.getStatus() == ProcessStatus.FAIL) {
            builder.append("Is Failed");
        } else {
            builder.append("Is Accessible");
        }
        return builder.toString();
    }
}
