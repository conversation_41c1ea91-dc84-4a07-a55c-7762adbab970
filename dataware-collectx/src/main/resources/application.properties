spring.module.name=${module.name}
spring.main.web-application-type=none
spring.main.allow-circular-references=true
# jpa config
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=jdbc:mysql://${database.address}/${database.name}?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&failOverReadOnly=false&useSSL=false&allowPublicKeyRetrieval=true&useTimezone=true&serverTimezone=Asia/Shanghai&allowMultiQueries=true&rewriteBatchedStatements=true
spring.datasource.username=${database.username}
spring.datasource.password=${database.password}
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.read-only=false
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=HikariCP
spring.datasource.hikari.max-lifetime=60000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1

spring.data.jpa.repositories.enabled=true
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.generate-ddl=false
spring.jpa.database=MYSQL
spring.jpa.show-sql=false
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.jdbc.batch_size=10000
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect

spring.task.scheduling.pool.size=${task.scheduling.pool.size}
spring.scheduler.timer.pushSftpCollectTask=${scheduler.timer.pushSftpCollectTask}
spring.scheduler.timer.pullSftpCollectTask=${scheduler.timer.pullSftpCollectTask}
spring.pullTask.convertCorePoolSize=${pullTask.convertCorePoolSize}
spring.immediatelyRunTask.convertCorePoolSize=${immediatelyRunTask.convertCorePoolSize}
spring.scheduler.timer.convertData=${scheduler.timer.convertData}
spring.convert.convertCorePoolSize=${convert.convertCorePoolSize}
spring.scheduler.timer.pushSftpBackupTask=${scheduler.timer.pushSftpBackupTask}
spring.scheduler.timer.pullSftpBackupTask=${scheduler.timer.pullSftpBackupTask}
spring.pullBackupTask.backupCorePoolSize=${pullBackupTask.backupCorePoolSize}
spring.redis.host=${redis.host}
spring.redis.port=${redis.port}
spring.redis.database=${redis.database}
spring.redis.password=${redis.password}
spring.redis.timeout=1000
spring.redis.jedis.pool.min-idle=1
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.max-active=8
spring.redis.jedis.pool.max-wait=1000ms
spring.data.redis.repositories.enabled=false
spring.handler.lockExpireTime=${handler.lockExpireTime}
spring.gdp.file.hdfsMode=${gdp.file.hdfsMode}
spring.gdp.file.hdfsUrl=${gdp.file.hdfsUrl}
spring.gdp.file.hdfsUser=${gdp.file.hdfsUser}
spring.gdp.file.hdfsRootPath=${gdp.file.hdfsRootPath}
spring.sftp.localPath=${sftp.localRootPath}ftp/original/
spring.sftp.localZipCompressPath=${sftp.localRootPath}ftp/hdfs/
spring.adapter.python.install.path=${adapter.python.install.path}
spring.rawData.convert.needConvertLocalPath=${sftp.localRootPath}rawData/need_convert/
spring.rawData.convert.convertLocalPath=${sftp.localRootPath}rawData/convert/
spring.summary.convert.needConvertLocalPath=${sftp.localRootPath}summary/need_convert/
spring.summary.convert.convertLocalPath=${sftp.localRootPath}summary/convert/
spring.lotRelation.convert.needConvertLocalPath=${sftp.localRootPath}lotRelation/need_convert/
spring.lotRelation.convert.convertLocalPath=${sftp.localRootPath}lotRelation/convert/
spring.log.convert.needConvertLocalPath=${sftp.localRootPath}log/need_convert/
spring.log.convert.convertLocalPath=${sftp.localRootPath}log/convert/
spring.map.convert.needConvertLocalPath=${sftp.localRootPath}map/need_convert/
spring.map.convert.convertLocalPath=${sftp.localRootPath}map/convert/
spring.logMapMerge.convert.needConvertLocalPath=${sftp.localRootPath}log_map_merge/need_convert/
spring.logMapMerge.convert.convertLocalPath=${sftp.localRootPath}log_map_merge/convert/
spring.zipSplit.convert.needConvertLocalPath=${sftp.localRootPath}zip_split/need_convert/
spring.zipSplit.convert.convertLocalPath=${sftp.localRootPath}zip_split/convert/
spring.wip.convert.needConvertLocalPath=${sftp.localRootPath}wip/need_convert/
spring.wip.convert.convertLocalPath=${sftp.localRootPath}wip/convert/
spring.bitMem.convert.needConvertLocalPath=${sftp.localRootPath}bitMem/need_convert/
spring.bitMem.convert.convertLocalPath=${sftp.localRootPath}bitMem/convert/
spring.zipSplit.convert.zipSplitFileMaxWaitMillion=${zipSplit.convert.zipSplitFileMaxWaitMillion}
spring.wat.convert.needConvertLocalPath=${sftp.localRootPath}wat/need_convert/
spring.wat.convert.convertLocalPath=${sftp.localRootPath}wat/convert/
spring.rar.tmpDirectory=${rar.tmpDirectory}

# kafka
spring.kafka.bootstrap-servers=${kafka.bootstrapServers}
spring.kafka.producer.retries=${kafka.producer.retries}
spring.kafka.producer.properties.retry.backoff.ms=${kafka.producer.properties.retry.backoff.ms}
spring.kafka.producer.acks=1
spring.kafka.producer.batch-size=${kafka.producer.batchSize}
spring.kafka.producer.properties.linger.ms=${kafka.producer.lingerMs}
spring.kafka.producer.buffer-memory=${kafka.producer.bufferMemory}
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.ByteArraySerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.ByteArraySerializer
spring.kafka.properties.max.request.size=104857600
spring.kafka.loadEndFlagTopic=${kafka.loadEndFlagTopic}
spring.kafka.sleepMilliseconds=${kafka.sleepMilliseconds}

# License config
license.baseUrl=${licenseBaseUrl}

spring.backup.maxFailCnt=${backup.maxFailCnt}

# dubbo config
dubbo.application.name=${module.name}
dubbo.application.serialize-check-status=WARN
dubbo.application.qos-enable=false
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.registry.protocol=zookeeper
dubbo.registry.address=zookeeper://${zookeeper.address}
dubbo.provider.timeout=${rpc.timeout}
dubbo.provider.retries=0
dubbo.provider.group=${environment.group}
dubbo.provider.filter=customerProviderFilter
dubbo.consumer.check=false
dubbo.consumer.timeout=${rpc.timeout}
dubbo.consumer.retries=0
dubbo.consumer.group=${environment.group}