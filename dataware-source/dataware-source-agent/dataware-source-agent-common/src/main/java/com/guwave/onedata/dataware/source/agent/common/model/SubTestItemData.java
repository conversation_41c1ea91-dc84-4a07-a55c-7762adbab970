package com.guwave.onedata.dataware.source.agent.common.model;

import java.util.HashMap;
import java.util.Map;

/**
 * 写到 ods parquet 的 test_item_data 内容
 * file_key + die_data + test_data
 */
public class SubTestItemData {
    // file_key

    // dc的mysql记录id
    private Long fileId;
    // 文件名
    private String fileName;


    // die_data

    // ONLINE_RETEST
    private Integer onlineRetest;
    // NUM_TEST
    private Integer numTest;
    // HBIN_NUM
    private Long hbinNum;
    // SBIN_NUM
    private Long sbinNum;
    // TEST_HEAD
    private Long testHead;
    // PART_FLG
    private String partFlg;
    // PART_ID
    private String partId;
    // 需计算补充的字段
    private Integer cPartId;
    // WAFER_LOT_ID
    private String waferLotId;
    // waferNo
    private String waferNo;
    // waferId
    private String waferId;
    // realWaferId
    private String realWaferId;
    // X_COORD
    private Integer xCoord;
    // Y_COORD
    private Integer yCoord;
    // TEST_TIME
    private Long testTime;
    // PART_TXT
    private String partTxt;
    // PART_FIX
    private String partFix;
    // SITE
    private Long site;
    // SBIN_NAM
    private String sbinNam;
    // SBIN_PF
    private String sbinPf;
    // HBIN_NAM
    private String hbinNam;
    // HBIN_PF
    private String hbinPf;
    private Integer touchDownId;
    private Integer maxOnlineRetest;
    private String ecid;
    private String ecidExt;
    private Map<String, String> ecidExtra = new HashMap<>();
    private String uid;


    // test_data

    // TEST_NUM
    private Long testNum;
    // UNITS
    private String units;
    // LO_LIMIT
    private Double loLimit;
    // HI_LIMIT
    private Double hiLimit;
    // TEST_TXT
    private String testTxt;
    // TEST_ITEM
    private String testItem;
    // TESTITEM_TYPE
    private String testitemType;
    // TEST_FLG
    private String testFlg;
    // PARM_FLG
    private String parmFlg;
    // ALARM_ID
    private String alarmId;
    // OPT_FLG
    private String optFlg;
    // RESULT：PTR/FTP/MPT的RESULT值
    private Double result;
    // RES_SCAL
    private Integer resScal;
    // LLM_SCAL
    private Integer llmScal;
    // HLM_SCAL
    private Integer hlmScal;
    // C_RESFMT
    private String cResfmt;
    // C_LLMFMT
    private String cLlmfmt;
    // C_HLMFMT
    private String cHlmfmt;
    // LO_SPEC
    private Double loSpec;
    // HI_SPEC
    private Double hiSpec;
    // VECT_NAM
    private String vectNam;
    // TIME_SET
    private String timeSet;
    // NUM_FAIL
    private Long numFail;
    // FAIL_PIN
    private String failPin;
    // CYCL_CNT
    private Long cyclCnt;
    // REPT_CNT
    private Long reptCnt;
    private Long testOrder;
    // TEST_RESULT
    private Integer testResult;
    // TEST_VALUE
    private Double testValue;
    // CONDITION_SET
    private Map<String, String> conditionSet = new HashMap<>();
    // TEXT_DAT
    private String textDat;


    public Long getFileId() {
        return fileId;
    }

    public SubTestItemData setFileId(Long fileId) {
        this.fileId = fileId;
        return this;
    }

    public String getFileName() {
        return fileName;
    }

    public SubTestItemData setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public Integer getOnlineRetest() {
        return onlineRetest;
    }

    public SubTestItemData setOnlineRetest(Integer onlineRetest) {
        this.onlineRetest = onlineRetest;
        return this;
    }

    public Integer getNumTest() {
        return numTest;
    }

    public SubTestItemData setNumTest(Integer numTest) {
        this.numTest = numTest;
        return this;
    }

    public Long getHbinNum() {
        return hbinNum;
    }

    public SubTestItemData setHbinNum(Long hbinNum) {
        this.hbinNum = hbinNum;
        return this;
    }

    public Long getSbinNum() {
        return sbinNum;
    }

    public SubTestItemData setSbinNum(Long sbinNum) {
        this.sbinNum = sbinNum;
        return this;
    }

    public Long getTestHead() {
        return testHead;
    }

    public SubTestItemData setTestHead(Long testHead) {
        this.testHead = testHead;
        return this;
    }

    public String getPartFlg() {
        return partFlg;
    }

    public SubTestItemData setPartFlg(String partFlg) {
        this.partFlg = partFlg;
        return this;
    }

    public String getPartId() {
        return partId;
    }

    public SubTestItemData setPartId(String partId) {
        this.partId = partId;
        return this;
    }

    public Integer getcPartId() {
        return cPartId;
    }

    public SubTestItemData setcPartId(Integer cPartId) {
        this.cPartId = cPartId;
        return this;
    }

    public Integer getxCoord() {
        return xCoord;
    }

    public SubTestItemData setxCoord(Integer xCoord) {
        this.xCoord = xCoord;
        return this;
    }

    public Integer getyCoord() {
        return yCoord;
    }

    public SubTestItemData setyCoord(Integer yCoord) {
        this.yCoord = yCoord;
        return this;
    }

    public Long getTestTime() {
        return testTime;
    }

    public SubTestItemData setTestTime(Long testTime) {
        this.testTime = testTime;
        return this;
    }

    public String getPartTxt() {
        return partTxt;
    }

    public SubTestItemData setPartTxt(String partTxt) {
        this.partTxt = partTxt;
        return this;
    }

    public String getPartFix() {
        return partFix;
    }

    public SubTestItemData setPartFix(String partFix) {
        this.partFix = partFix;
        return this;
    }

    public Long getSite() {
        return site;
    }

    public SubTestItemData setSite(Long site) {
        this.site = site;
        return this;
    }

    public String getSbinNam() {
        return sbinNam;
    }

    public SubTestItemData setSbinNam(String sbinNam) {
        this.sbinNam = sbinNam;
        return this;
    }

    public String getSbinPf() {
        return sbinPf;
    }

    public SubTestItemData setSbinPf(String sbinPf) {
        this.sbinPf = sbinPf;
        return this;
    }

    public String getHbinNam() {
        return hbinNam;
    }

    public SubTestItemData setHbinNam(String hbinNam) {
        this.hbinNam = hbinNam;
        return this;
    }

    public String getHbinPf() {
        return hbinPf;
    }

    public SubTestItemData setHbinPf(String hbinPf) {
        this.hbinPf = hbinPf;
        return this;
    }

    public Integer getTouchDownId() {
        return touchDownId;
    }

    public SubTestItemData setTouchDownId(Integer touchDownId) {
        this.touchDownId = touchDownId;
        return this;
    }

    public Integer getMaxOnlineRetest() {
        return maxOnlineRetest;
    }

    public SubTestItemData setMaxOnlineRetest(Integer maxOnlineRetest) {
        this.maxOnlineRetest = maxOnlineRetest;
        return this;
    }

    public String getEcid() {
        return ecid;
    }

    public SubTestItemData setEcid(String ecid) {
        this.ecid = ecid;
        return this;
    }

    public String getEcidExt() {
        return ecidExt;
    }

    public SubTestItemData setEcidExt(String ecidExt) {
        this.ecidExt = ecidExt;
        return this;
    }

    public Map<String, String> getEcidExtra() {
        return ecidExtra;
    }

    public SubTestItemData setEcidExtra(Map<String, String> ecidExtra) {
        this.ecidExtra = ecidExtra;
        return this;
    }

    public String getTextDat() {
        return textDat;
    }

    public SubTestItemData setTextDat(String textDat) {
        this.textDat = textDat;
        return this;
    }

    public String getUid() {
        return uid;
    }

    public SubTestItemData setUid(String uid) {
        this.uid = uid;
        return this;
    }

    public Long getTestNum() {
        return testNum;
    }

    public SubTestItemData setTestNum(Long testNum) {
        this.testNum = testNum;
        return this;
    }

    public String getUnits() {
        return units;
    }

    public SubTestItemData setUnits(String units) {
        this.units = units;
        return this;
    }

    public Double getLoLimit() {
        return loLimit;
    }

    public SubTestItemData setLoLimit(Double loLimit) {
        this.loLimit = loLimit;
        return this;
    }

    public Double getHiLimit() {
        return hiLimit;
    }

    public SubTestItemData setHiLimit(Double hiLimit) {
        this.hiLimit = hiLimit;
        return this;
    }

    public String getTestTxt() {
        return testTxt;
    }

    public SubTestItemData setTestTxt(String testTxt) {
        this.testTxt = testTxt;
        return this;
    }

    public String getTestItem() {
        return testItem;
    }

    public SubTestItemData setTestItem(String testItem) {
        this.testItem = testItem;
        return this;
    }

    public String getTestitemType() {
        return testitemType;
    }

    public SubTestItemData setTestitemType(String testitemType) {
        this.testitemType = testitemType;
        return this;
    }

    public String getTestFlg() {
        return testFlg;
    }

    public SubTestItemData setTestFlg(String testFlg) {
        this.testFlg = testFlg;
        return this;
    }

    public String getParmFlg() {
        return parmFlg;
    }

    public SubTestItemData setParmFlg(String parmFlg) {
        this.parmFlg = parmFlg;
        return this;
    }

    public String getAlarmId() {
        return alarmId;
    }

    public SubTestItemData setAlarmId(String alarmId) {
        this.alarmId = alarmId;
        return this;
    }

    public String getOptFlg() {
        return optFlg;
    }

    public SubTestItemData setOptFlg(String optFlg) {
        this.optFlg = optFlg;
        return this;
    }

    public Double getResult() {
        return result;
    }

    public SubTestItemData setResult(Double result) {
        this.result = result;
        return this;
    }

    public Integer getResScal() {
        return resScal;
    }

    public SubTestItemData setResScal(Integer resScal) {
        this.resScal = resScal;
        return this;
    }

    public Integer getLlmScal() {
        return llmScal;
    }

    public SubTestItemData setLlmScal(Integer llmScal) {
        this.llmScal = llmScal;
        return this;
    }

    public Integer getHlmScal() {
        return hlmScal;
    }

    public SubTestItemData setHlmScal(Integer hlmScal) {
        this.hlmScal = hlmScal;
        return this;
    }

    public String getcResfmt() {
        return cResfmt;
    }

    public SubTestItemData setcResfmt(String cResfmt) {
        this.cResfmt = cResfmt;
        return this;
    }

    public String getcLlmfmt() {
        return cLlmfmt;
    }

    public SubTestItemData setcLlmfmt(String cLlmfmt) {
        this.cLlmfmt = cLlmfmt;
        return this;
    }

    public String getcHlmfmt() {
        return cHlmfmt;
    }

    public SubTestItemData setcHlmfmt(String cHlmfmt) {
        this.cHlmfmt = cHlmfmt;
        return this;
    }

    public Double getLoSpec() {
        return loSpec;
    }

    public SubTestItemData setLoSpec(Double loSpec) {
        this.loSpec = loSpec;
        return this;
    }

    public Double getHiSpec() {
        return hiSpec;
    }

    public SubTestItemData setHiSpec(Double hiSpec) {
        this.hiSpec = hiSpec;
        return this;
    }

    public String getVectNam() {
        return vectNam;
    }

    public SubTestItemData setVectNam(String vectNam) {
        this.vectNam = vectNam;
        return this;
    }

    public String getTimeSet() {
        return timeSet;
    }

    public SubTestItemData setTimeSet(String timeSet) {
        this.timeSet = timeSet;
        return this;
    }

    public Long getNumFail() {
        return numFail;
    }

    public SubTestItemData setNumFail(Long numFail) {
        this.numFail = numFail;
        return this;
    }

    public String getFailPin() {
        return failPin;
    }

    public SubTestItemData setFailPin(String failPin) {
        this.failPin = failPin;
        return this;
    }

    public Long getCyclCnt() {
        return cyclCnt;
    }

    public SubTestItemData setCyclCnt(Long cyclCnt) {
        this.cyclCnt = cyclCnt;
        return this;
    }

    public Long getReptCnt() {
        return reptCnt;
    }

    public SubTestItemData setReptCnt(Long reptCnt) {
        this.reptCnt = reptCnt;
        return this;
    }

    public Long getTestOrder() {
        return testOrder;
    }

    public SubTestItemData setTestOrder(Long testOrder) {
        this.testOrder = testOrder;
        return this;
    }

    public Integer getTestResult() {
        return testResult;
    }

    public SubTestItemData setTestResult(Integer testResult) {
        this.testResult = testResult;
        return this;
    }

    public Double getTestValue() {
        return testValue;
    }

    public SubTestItemData setTestValue(Double testValue) {
        this.testValue = testValue;
        return this;
    }

    public Map<String, String> getConditionSet() {
        return conditionSet;
    }

    public SubTestItemData setConditionSet(Map<String, String> conditionSet) {
        this.conditionSet = conditionSet;
        return this;
    }

    public String getWaferLotId() {
        return waferLotId;
    }

    public SubTestItemData setWaferLotId(String waferLotId) {
        this.waferLotId = waferLotId;
        return this;
    }

    public String getWaferNo() {
        return waferNo;
    }

    public SubTestItemData setWaferNo(String waferNo) {
        this.waferNo = waferNo;
        return this;
    }

    public String getWaferId() {
        return waferId;
    }

    public SubTestItemData setWaferId(String waferId) {
        this.waferId = waferId;
        return this;
    }

    public String getRealWaferId() {
        return realWaferId;
    }

    public SubTestItemData setRealWaferId(String realWaferId) {
        this.realWaferId = realWaferId;
        return this;
    }
}
