package com.guwave.onedata.dataware.source.agent.manual.sink;

import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.source.agent.manual.model.DwdShmooVminVmaxDetail;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

/**
 * 2023/11/16 18:53
 * DwdFileShmooVminVmaxSink
 *
 * <AUTHOR>
 */
@Component
public class DwdShmooVminVmaxDetailSink implements CkSink<DwdShmooVminVmaxDetail> {

    @Value("${spring.sink.ck.dwd.dbName}")
    private String dbName;

    @Override
    public String getTableName() {
        return "dwd_shmoo_vmin_vmax_detail_cluster";
    }

    @Override
    public String getDbName() {
        return dbName;
    }

    @Value("${spring.lotBucketNum}")
    private Integer lotBucketNum;

    @Override
    public void handle(PreparedStatement statement, List<DwdShmooVminVmaxDetail> items) throws SQLException {
        for (DwdShmooVminVmaxDetail item : items) {
            statement.setObject(1, item.getFileId());
            statement.setObject(2, item.getFileName());
            statement.setObject(3, item.getCustomer());
            statement.setObject(4, item.getUploadType());
            statement.setObject(5, item.getEcid());
            statement.setObject(6, item.getTestProgram());
            statement.setObject(7, item.getTesterName());
            statement.setObject(8, item.getTestArea().getArea());
            statement.setObject(9, item.getDeviceId());
            statement.setObject(10, item.getLotId());
            statement.setObject(11, item.getProberHandlerId());
            statement.setObject(12, item.getProberHandlerTyp());
            statement.setObject(13, item.getTestItem());
            statement.setObject(14, item.getSiteNumber());
            statement.setObject(15, item.getVminResult());
            statement.setObject(16, item.getVmaxResult());
            statement.setObject(17, item.getProcess());
            statement.setObject(18, item.getTestTemperature());
            statement.setObject(19, this.toTimestamp(item.getCreateTime()));
            statement.setObject(20, item.getCreateUser());
            statement.setObject(21, this.toTimestamp(item.getUploadTime()));
            statement.setObject(22, item.getPartId());
            statement.setObject(23, item.getcPartId());
            statement.setObject(24, item.getTestSuite());
            statement.setObject(25, item.getTestPattern());
            statement.setObject(26, item.getShmooName());
            statement.setObject(27, item.getTestOrder());
            statement.setObject(28, item.getExecutionType());
            statement.setObject(29, item.getExecutionLabel());
            statement.setObject(30, item.getExecutionUnit());
            statement.setObject(31, item.getExecutionValue());
            statement.setObject(32, item.getShmooResult());
            statement.setObject(33, item.getShmooResultName());
            statement.setObject(34, item.getSpecification());
            statement.setObject(35, item.getIsHoleFlag());
            statement.setObject(36, item.getShmooJudgeResult());
            statement.setObject(37, item.getSubloop());
            statement.setObject(38, item.getOperatingSequenc());
            statement.setObject(39, item.getTestmethod());
            statement.setObject(40, item.getAxisnum());
            statement.setObject(41, item.getErrcntflag());
            statement.setObject(42, item.getStdfPartTxt());
            statement.setObject(43, item.getCharStartEventid());
            statement.setObject(44, this.toTimestamp(item.getStartTime()));
            statement.setObject(45, this.toTimestamp(item.getEndTime()));
            statement.setObject(46, getLotBucket(item.getLotId(), lotBucketNum));
            statement.setObject(47, item.getDataType());
            statement.setObject(48, item.getLotType() == null ? LotType.EMPTY.getType() : item.getLotType().getType());
            statement.setObject(49, item.getWaferId());
            statement.setObject(50, item.getExtraInfo());

            statement.addBatch();
        }
    }

    @Override
    public List<String> getColumns() {
        return Arrays.asList(
                "FILE_ID",
                "FILE_NAME",
                "CUSTOMER",
                "UPLOAD_TYPE",
                "ECID",
                "TEST_PROGRAM",
                "TESTER_NAME",
                "TEST_AREA",
                "DEVICE_ID",
                "LOT_ID",
                "PROBER_HANDLER_ID",
                "PROBER_HANDLER_TYP",
                "TEST_ITEM",
                "SITE_NUMBER",
                "VMIN_RESULT",
                "VMAX_RESULT",
                "PROCESS",
                "TEST_TEMPERATURE",
                "CREATE_TIME",
                "CREATE_USER",
                "UPLOAD_TIME",
                "PART_ID",
                "C_PART_ID",
                "TEST_SUITE",
                "TEST_PATTERN",
                "SHMOO_NAME",
                "TEST_ORDER",
                "EXECUTION_TYPE",
                "EXECUTION_LABEL",
                "EXECUTION_UNIT",
                "EXECUTION_VALUE",
                "SHMOO_RESULT",
                "SHMOO_RESULT_NAME",
                "SPECIFICATION",
                "IS_HOLE_FLAG",
                "SHMOO_JUDGE_RESULT",
                "SUBLOOP",
                "OPERATING_SEQUENC",
                "TESTMETHOD",
                "AXISNUM",
                "ERRCNTFLAG",
                "STDF_PART_TXT",
                "CHAR_START_EVENTID",
                "START_TIME",
                "END_TIME",
                "LOT_BUCKET",
                "DATA_TYPE",
                "LOT_TYPE",
                "WAFER_ID",
                "EXTRA_INFO"
        );
    }

}
