package com.guwave.onedata.dataware.source.common.serialization.protostuff;

import java.util.StringTokenizer;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * TypeTokenizer
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-18 12:15:22
 */
public class TypeTokenizer extends StringTokenizer {

    private final String _input;
    private int _index;
    private String _pushbackToken;

    public TypeTokenizer(String str) {
        super(str, "<,>", true);
        _input = str;
    }

    @Override
    public boolean hasMoreTokens() {
        return (_pushbackToken != null) || super.hasMoreTokens();
    }

    @Override
    public String nextToken() {
        String token;
        if (_pushbackToken != null) {
            token = _pushbackToken;
            _pushbackToken = null;
        } else {
            token = super.nextToken();
            _index += token.length();
            token = token.trim();
        }
        return token;
    }

    public void pushBack(String token) {
        _pushbackToken = token;
        // let's NOT change index for now, since token may have been trim()ed
    }

    public String getAllInput() {
        return _input;
    }

    public String getUsedInput() {
        return _input.substring(0, _index);
    }

    public String getRemainingInput() {
        return _input.substring(_index);
    }
}
