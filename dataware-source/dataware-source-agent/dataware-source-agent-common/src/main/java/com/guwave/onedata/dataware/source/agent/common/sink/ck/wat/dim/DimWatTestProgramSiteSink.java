package com.guwave.onedata.dataware.source.agent.common.sink.ck.wat.dim;

import com.guwave.onedata.dataware.common.model.wat.dim.DimWatTestProgramSite;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import com.guwave.onedata.dataware.source.agent.common.util.DateUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/13 20:57
 * @description DimWatTestProgramSiteSink
 */
@Component
public class DimWatTestProgramSiteSink implements CkSink<DimWatTestProgramSite> {
    @Value("${spring.sink.ck.dim.dbName}")
    private String dbName;

    @Override
    public String getTableName() {
        return "dim_test_program_site_local";
    }

    @Override
    public void handle(PreparedStatement statement, List<DimWatTestProgramSite> items) throws SQLException {
        for (DimWatTestProgramSite item : items) {
            statement.setObject(1, item.getCustomer());
            statement.setObject(2, item.getUploadType());
            statement.setObject(3, item.getFactory());
            statement.setObject(4, item.getFactorySite());
            statement.setObject(5, item.getFab());
            statement.setObject(6, item.getFabSite());
            statement.setObject(7, item.getTestArea());
            statement.setObject(8, item.getTestStage());
            statement.setObject(9, item.getDeviceId());
            statement.setObject(10, item.getTestProgram());
            statement.setObject(11, item.getTestProgramVersion());
            statement.setObject(12, item.getSite());
            // CREATE_HOUR_KEY
            statement.setObject(13, DateUtil.getDayHour(System.currentTimeMillis()));
            // CREATE_DAY_KEY
            statement.setObject(14, DateUtil.getDate(System.currentTimeMillis()));
            // CREATE_TIME
            statement.setObject(15, new Timestamp(System.currentTimeMillis()));
            // CREATE_USER
            statement.setObject(16, item.getCreateUser());
            statement.setObject(17, item.getVersion());
            statement.setObject(18, item.getSubCustomer());
            statement.setObject(19, item.getTestTemperature());
            statement.setObject(20, item.getSiteKey());
            statement.setObject(21, new Timestamp(item.getUploadTime()));
            statement.setObject(22, item.getFileId());

            statement.addBatch();
        }

    }

    @Override
    public List<String> getColumns() {
        return Arrays.asList(
                "CUSTOMER",
                "UPLOAD_TYPE",
                "FACTORY",
                "FACTORY_SITE",
                "FAB",
                "FAB_SITE",
                "TEST_AREA",
                "TEST_STAGE",
                "DEVICE_ID",
                "TEST_PROGRAM",
                "TEST_PROGRAM_VERSION",
                "SITE",
                "CREATE_HOUR_KEY",
                "CREATE_DAY_KEY",
                "CREATE_TIME",
                "CREATE_USER",
                "VERSION",
                "SUB_CUSTOMER",
                "TEST_TEMPERATURE",
                "SITE_KEY",
                "UPLOAD_TIME",
                "FILE_ID"
        );
    }

    @Override
    public String getDbName() {
        return dbName;
    }
}
