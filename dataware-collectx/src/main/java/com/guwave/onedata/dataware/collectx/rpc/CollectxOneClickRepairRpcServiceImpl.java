package com.guwave.onedata.dataware.collectx.rpc;

import com.guwave.onedata.dataware.bridge.api.iface.ICollectxOneClickRepairRpcService;
import com.guwave.onedata.dataware.collectx.manager.redis.FileConvertRedisManager;
import com.guwave.onedata.dataware.collectx.manager.redis.SftpFileRedisManager;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@DubboService
@Component
public class CollectxOneClickRepairRpcServiceImpl implements ICollectxOneClickRepairRpcService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CollectxOneClickRepairRpcServiceImpl.class);

    @Override
    public void oneClickRepair() {
        SftpFileRedisManager.clearLockCollectRuleId();
        FileConvertRedisManager.clearLockZipSplitConvert();
    }
}