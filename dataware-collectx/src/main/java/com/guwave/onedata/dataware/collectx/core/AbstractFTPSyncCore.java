package com.guwave.onedata.dataware.collectx.core;

import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.FTPServerType;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpCollectRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.regex.Pattern;

@Slf4j
public class AbstractFTPSyncCore {

    private FTPServerType serverType = null;

    public static final List<FileCategory> NOT_NEED_PULL_FILE_CATEGORY_LIST = Lists.newArrayList(
            FileCategory.BLACKLIST,
            FileCategory.OTHER
    );

    public static final List<Pair<FileCategory, Function<SftpCollectRule, String>>> remoteFileFullNameFileCategoryFunctions = new ArrayList<Pair<FileCategory, Function<SftpCollectRule, String>>>() {{
        add(Pair.of(FileCategory.BLACKLIST, SftpCollectRule::getBlackFileRemoteFullNameRegex));
        add(Pair.of(FileCategory.STDF, SftpCollectRule::getStdfFileRemoteFullNameRegex));
        add(Pair.of(FileCategory.RAW_DATA, SftpCollectRule::getRawDataFileRemoteFullNameRegex));
        add(Pair.of(FileCategory.WAT, SftpCollectRule::getWatFileRemoteFullNameRegex));
        add(Pair.of(FileCategory.SUMMARY, SftpCollectRule::getSummaryFileRemoteFullNameRegex));
        add(Pair.of(FileCategory.LOT_RELATION, SftpCollectRule::getLotRelationFileRemoteFullNameRegex));
        add(Pair.of(FileCategory.LOG, SftpCollectRule::getLogFileRemoteFullNameRegex));
        add(Pair.of(FileCategory.MAP, SftpCollectRule::getMapFileRemoteFullNameRegex));
        add(Pair.of(FileCategory.ZIP_SPLIT, SftpCollectRule::getZipSplitFileRemoteFullNameRegex));
        add(Pair.of(FileCategory.WIP, SftpCollectRule::getWipFileRemoteFullNameRegex));
        add(Pair.of(FileCategory.BIT_MEM, SftpCollectRule::getBitMemFileRemoteFullNameRegex));
    }};

    public static final List<Pair<FileCategory, Function<SftpCollectRule, String>>> fileNameFileCategoryFunctions = new ArrayList<Pair<FileCategory, Function<SftpCollectRule, String>>>() {{
        add(Pair.of(FileCategory.STDF, SftpCollectRule::getStdfFileRegex));
        add(Pair.of(FileCategory.RAW_DATA, SftpCollectRule::getRawDataFileRegex));
        add(Pair.of(FileCategory.WAT, SftpCollectRule::getWatFileRegex));
        add(Pair.of(FileCategory.SUMMARY, SftpCollectRule::getSummaryFileRegex));
        add(Pair.of(FileCategory.LOT_RELATION, SftpCollectRule::getLotRelationFileRegex));
        add(Pair.of(FileCategory.LOG, SftpCollectRule::getLogFileRegex));
        add(Pair.of(FileCategory.MAP, SftpCollectRule::getMapFileRegex));
        add(Pair.of(FileCategory.ZIP_SPLIT, SftpCollectRule::getZipSplitFileRegex));
        add(Pair.of(FileCategory.WIP, SftpCollectRule::getWipFileRegex));
        add(Pair.of(FileCategory.BIT_MEM, SftpCollectRule::getBitMemFileRegex));
    }};

    public static boolean isBlackRemotePath(String remotePath, String  blackFileRemoteFullNameRegex) {
        return StringUtils.hasLength(blackFileRemoteFullNameRegex) && Pattern.compile(blackFileRemoteFullNameRegex, Pattern.CASE_INSENSITIVE).matcher(remotePath).find();
    }


    public static FileCategory matchFileCategoryWithRemoteFileFullName(String remoteFileFullName, SftpCollectRule sftpCollectRule) {
        FileCategory fileCategory = FileCategory.OTHER;
        for (Pair<FileCategory, Function<SftpCollectRule, String>> remoteFileFullNameFileCategoryFunction : remoteFileFullNameFileCategoryFunctions) {
            String regex = remoteFileFullNameFileCategoryFunction.getRight().apply(sftpCollectRule);
            if (StringUtils.hasLength(regex) && Pattern.compile(regex, Pattern.CASE_INSENSITIVE).matcher(remoteFileFullName).find()) {
                fileCategory = remoteFileFullNameFileCategoryFunction.getLeft();
                break;
            }
        }
        log.info("{} {}", remoteFileFullName, fileCategory);
        return fileCategory;
    }

    public static FileCategory matchFileCategoryWithFileName(String fileName, SftpCollectRule sftpCollectRule) {
        FileCategory fileCategory = FileCategory.OTHER;
        for (Pair<FileCategory, Function<SftpCollectRule, String>> fileNameFileCategoryFunction : fileNameFileCategoryFunctions) {
            String regex = fileNameFileCategoryFunction.getRight().apply(sftpCollectRule);
            if (StringUtils.hasLength(regex) && Pattern.compile(regex, Pattern.CASE_INSENSITIVE).matcher(fileName).find()) {
                fileCategory = fileNameFileCategoryFunction.getLeft();
                break;
            }
        }
        log.info("{} {}", fileName, fileCategory);
        return fileCategory;
    }

    public FTPServerType getServerType() {
        return serverType;
    }

    public void setServerType(FTPServerType serverType) {
        this.serverType = serverType;
    }
}
