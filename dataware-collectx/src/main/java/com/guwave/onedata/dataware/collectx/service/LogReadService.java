package com.guwave.onedata.dataware.collectx.service;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileCategory;

public interface LogReadService extends SingleSftpFileReadService {
    default FileCategory getConvertResultFileCategory() {
        return FileCategory.LOG;
    }

    default String getPythonTypeArg() {
        return Constant.PYTHON_ARG_TYPE_LOG;
    }
}
