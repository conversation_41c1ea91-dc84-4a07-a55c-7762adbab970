package com.guwave.onedata.dataware.collectx.job;

import com.guwave.onedata.dataware.collectx.service.DataConvertService;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SummaryConvertJob {

    @Autowired
    private DataConvertService summaryConvertServiceImpl;

    @ScheduleSwitch
    @Scheduled(cron = "${spring.scheduler.timer.convertData}")
    public void schedulerSummaryConvert() {
        try {
            summaryConvertServiceImpl.convertDataAsync();
        } catch (Exception e) {
            log.error(e.toString(), e);
        }
    }
}
