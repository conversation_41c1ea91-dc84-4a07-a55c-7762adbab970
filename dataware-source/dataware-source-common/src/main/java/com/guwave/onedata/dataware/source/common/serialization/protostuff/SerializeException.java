package com.guwave.onedata.dataware.source.common.serialization.protostuff;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * SerializeException
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-18 12:10:14
 */
public class SerializeException extends RuntimeException {
    private static final long serialVersionUID = 5014485877547725109L;

    public SerializeException(String message) {
        super(message);
    }

    public SerializeException(String message, Throwable cause) {
        super(message, cause);
    }

    public SerializeException(Throwable cause) {
        super(cause);
    }

    protected SerializeException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
