package com.guwave.onedata.dataware.source.agent.common.fetcher;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.contant.FileParseStatus;
import com.guwave.onedata.dataware.common.contant.FileStorageType;
import com.guwave.onedata.dataware.common.contant.FileType;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.UploadType;
import com.guwave.onedata.dataware.common.contant.WarehousingMode;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.dao.mysql.domain.dc.FileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileLoadingLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ProcessLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.RepairRecord;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileDetail;
import com.guwave.onedata.dataware.dao.mysql.manager.FileWarehousingRecordManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dc.FileInfoRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LotWaferWarehousingRecordRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ProcessLogRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.RepairRecordRepository;
import com.guwave.onedata.dataware.repair.common.service.DataRepairService;
import com.guwave.onedata.dataware.source.agent.common.sink.impl.KafkaSink;
import com.guwave.onedata.dataware.source.agent.common.util.FileUtil;
import com.guwave.onedata.dataware.source.agent.common.util.HdfsUtil;
import com.guwave.onedata.dataware.source.agent.common.util.ProcessLogThreadLocalUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * Copyright (C), 2022, guwave
 * <p>
 * CommonFetcher
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2022-04-27 15:41:06
 */
public interface CommonFetcher {
    Logger LOGGER = LoggerFactory.getLogger(CommonFetcher.class);

    String CRC_FILE_SUFFIX = "crc";

    default void downloadAndUncompressHdfsFile(SftpFileDetail sftpFileDetail) throws IOException {
        String localcompressFilePath = getReadPath() + sftpFileDetail.getLocalFileName();
        LOGGER.info("从hdfs 下载不删除原文件: {} --> {}", sftpFileDetail.getHdfsFilePath(), localcompressFilePath);
        // 此处下载不删除源文件
        clean(getReadPath(), sftpFileDetail.getLocalFileName(), getErrorPath(), false);
        getHdfsUtil().downloadToLocal(false, sftpFileDetail.getHdfsFilePath(), localcompressFilePath);
        // 解压下载的文件
        getHdfsUtil().unCompress(getReadPath(), localcompressFilePath);
    }


    /**
     * 清理处理完成的文件
     *
     * @param readPath  文件路径
     * @param fileName  文件名
     * @param errorPath 保存错误文件的路径
     * @param move      true-移到错误路径  false-直接删除
     */
    default void clean(String readPath, String fileName, String errorPath, boolean move) {
        if (!readPath.endsWith(Constant.SLASH)) {
            readPath = readPath + Constant.SLASH;
        }
        if (!errorPath.endsWith(Constant.SLASH)) {
            errorPath = errorPath + Constant.SLASH;
        }

        // 清理文件
        String filePath = readPath + fileName;
        File file = new File(filePath);
        if (file.exists()) {

            try {
                if (move) {
                    // 移到错误路径
                    String errorFilePath = errorPath + fileName;
                    LOGGER.info("移动当前文件到错误路径：{} --> {}", filePath, errorFilePath);
                    File errorFile = new File(errorFilePath);
                    if (errorFile.exists()) {
                        FileUtils.deleteQuietly(errorFile);
                    }
                    FileUtils.moveToDirectory(file, new File(errorPath), true);
                } else {
                    // 删除文件
                    LOGGER.info("删除当前文件：{} ", filePath);
                    FileUtils.deleteQuietly(file);
                }
            } catch (Exception e) {
                LOGGER.info("文件异常", e);
            }
        }

        // 清理.crc文件
        String crcFilePath = readPath + Constant.POINT + fileName + Constant.POINT + CRC_FILE_SUFFIX;
        File crcFile = new File(crcFilePath);
        if (crcFile.exists()) {
            LOGGER.info("删除当前文件：{} ", crcFilePath);
            try {
                // 删除文件
                FileUtils.deleteQuietly(crcFile);
                LOGGER.info("删除文件成功");
            } catch (Exception e) {
                LOGGER.info("删除文件异常", e);
            }
        }

        deleteUnCompressFile(readPath, fileName, FileType.ZIP, errorPath, move);
    }

    /**
     * 删除解压缩文件
     *
     * @param readPath  被压缩的原文件路径
     * @param fileName  被压缩的文件名
     * @param fileType  压缩形式
     * @param errorPath 保存错误文件的路径
     * @param move      true-移到错误路径  false-直接删除
     */
    default void deleteUnCompressFile(String readPath, String fileName, FileType fileType, String errorPath, boolean move) {
        if (!readPath.endsWith(Constant.SLASH)) {
            readPath = readPath + Constant.SLASH;
        }
        if (!errorPath.endsWith(Constant.SLASH)) {
            errorPath = errorPath + Constant.SLASH;
        }
        fileName = FileUtil.removeFileSuffix(fileName, fileType);
        // 清理解压缩文件
        String unCompressFilePath = readPath + fileName;
        File unCompressFile = new File(unCompressFilePath);
        if (unCompressFile.exists()) {
            try {
                if (move) {
                    // 移到错误路径
                    String errorUnCompressFilePath = errorPath + fileName;
                    LOGGER.info("移动当前文件到错误路径：{} --> {}", unCompressFilePath, errorUnCompressFilePath);
                    File errorUnCompressFile = new File(errorUnCompressFilePath);
                    if (errorUnCompressFile.exists()) {
                        FileUtils.deleteQuietly(errorUnCompressFile);
                    }
                    FileUtils.moveToDirectory(unCompressFile, new File(errorPath), true);
                } else {
                    // 删除文件
                    LOGGER.info("删除当前文件：{} ", unCompressFilePath);
                    FileUtils.deleteQuietly(unCompressFile);
                }
            } catch (Exception e) {
                LOGGER.info("文件异常", e);
            }
        }

        // 清理crc文件
        String crcUnCompressFilePath = readPath + Constant.POINT + fileName + Constant.POINT + CRC_FILE_SUFFIX;
        File crcUnCompressFile = new File(crcUnCompressFilePath);
        if (crcUnCompressFile.exists()) {
            LOGGER.info("删除当前文件：{} ", crcUnCompressFilePath);
            try {
                // 删除文件
                FileUtils.deleteQuietly(crcUnCompressFile);
                LOGGER.info("删除文件成功");
            } catch (Exception e) {
                LOGGER.info("删除文件异常", e);
            }
        }
    }

    default ProcessLog generateProcessLog(SftpFileDetail sftpFileDetail) {
        Date date = new Date();
        ProcessLog processLog = new ProcessLog()
                .setCustomer(sftpFileDetail.getCustomer())
                .setSubCustomer(sftpFileDetail.getSubCustomer())
                .setFactory(sftpFileDetail.getFactory())
                .setFactorySite(sftpFileDetail.getFactorySite())
                .setFab(sftpFileDetail.getFab())
                .setFabSite(sftpFileDetail.getFabSite())
                .setTestArea(sftpFileDetail.getTestArea())
                .setFileType(sftpFileDetail.getFileType())
                .setFileCategory(sftpFileDetail.getFileCategory())
                .setFileName(sftpFileDetail.getLocalFileName())
                .setProcessCnt(1)
                .setProcessStatus(ProcessStatus.PROCESSING)
                .setProcessTime(date)
                .setCreateTime(date)
                .setUpdateTime(date)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateUser(Constant.SYSTEM);
        getProcessLogRepository().save(processLog);
        return processLog;
    }

    default void setProcessLogStatus(ProcessLog processLog, ProcessStatus status, String errorMsg) {
        Date now = new Date();
        processLog
                .setUpdateTime(now)
                .setProcessStatus(status);
        if (ProcessStatus.SUCCESS == status) {
            processLog
                    .setFinishTime(now);
        } else if (ProcessStatus.FAIL == status) {
            processLog.setFailTime(now);
        }
        if (StringUtils.isNotEmpty(errorMsg)) {
            ProcessLogThreadLocalUtil.appendErrorMessage(errorMsg);
        }
    }

    default FileInfo generateFileInfo(SftpFileDetail sftpFileDetail) {
        Date date = new Date();
        String localFileName = sftpFileDetail.getLocalFileName();
        String unCompressFileName = FileUtil.removeFileSuffix(localFileName, FileType.ZIP);
        String[] fileNameSplits = unCompressFileName.split(Constant.SPLIT_POINT);
        String suffix = fileNameSplits[fileNameSplits.length - 1];
        FileInfo fileInfo = new FileInfo()
                .setCustomer(sftpFileDetail.getCustomer())
                .setSubCustomer(sftpFileDetail.getSubCustomer())
                .setTestArea(sftpFileDetail.getTestArea())
                .setFactory(sftpFileDetail.getFactory())
                .setFactorySite(sftpFileDetail.getFactorySite())
                .setFab(sftpFileDetail.getFab())
                .setFabSite(sftpFileDetail.getFabSite())
                .setFileName(localFileName)
                .setName(localFileName)
                .setFileSize(sftpFileDetail.getFileSize() == null ? 0L : sftpFileDetail.getFileSize())
                .setFileCategory(sftpFileDetail.getFileCategory())
                .setFileSuffix(FileType.ZIP.getType())
                .setFileStorageType(FileStorageType.HDFS)
                .setFileFullPath(sftpFileDetail.getHdfsFilePath())
                .setOriginalFileName(unCompressFileName)
                .setOriginalFileType(suffix)
                .setCompressFile(Boolean.TRUE)
                .setPrivate(Boolean.FALSE)
                .setFileParseStatus(FileParseStatus.PROCESSING)
                .setDeleteFlag(Boolean.FALSE)
                .setUploadType(UploadType.AUTO)
                .setCreateTime(date)
                .setUpdateTime(date)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateUser(Constant.SYSTEM);

        getFileInfoRepository().save(fileInfo);
        return fileInfo;
    }

    default void afterOneFileDeal(ProcessLog processLog, FileInfo fileInfo, ProcessStatus processStatus, String fileName, long startTime) {
        Date updateTime = new Date();
        // 更新fileInfo
        if (fileInfo != null) {
            fileInfo.setUpdateTime(updateTime);
            switch (processStatus) {
                case CREATE:
                    fileInfo
                            .setFileParseStatus(FileParseStatus.CREATE)
                            .setFileParseMessage(null);
                    getFileInfoRepository().save(fileInfo);
                    break;
                case SUCCESS:
                    fileInfo
                            .setFileParseStatus(FileParseStatus.SUCCESS)
                            .setFileParseMessage(null);
                    getFileInfoRepository().save(fileInfo);
                    break;
                case FAIL:
                    fileInfo
                            .setFileParseStatus(FileParseStatus.FAIL)
                            .setFileParseMessage(processLog == null ? null : processLog.getErrorMessage());
                    getFileInfoRepository().save(fileInfo);
                    break;
            }
        }

        // 更新processLog
        if (processLog != null) {
            switch (processStatus) {
                case CREATE:
                    processLog.setProcessStatus(ProcessStatus.SUCCESS);
                    break;
                case SUCCESS:
                    setProcessLogStatus(processLog, ProcessStatus.SUCCESS, null);
                    break;
                case FAIL:
                    setProcessLogStatus(processLog, ProcessStatus.FAIL, null);
                    break;
            }
            processLog
                    .setUpdateTime(updateTime)
                    .setProcessConsume(System.currentTimeMillis() - startTime);
            getProcessLogRepository().save(processLog);
        }

        // 删除本地的文件和压缩文件
        clean(getReadPath(), fileName, getErrorPath(), false);
    }

    default void updateFileLoadingLogStatusAfterDealException(FileLoadingLog fileLoadingLog, Exception e) {
        FileLoadException fileLoadException = e instanceof FileLoadException ? ((FileLoadException) e) : new FileLoadException(FileLoadExceptionInfo.PARSE_FILE_EXCEPTION, ExceptionUtils.getStackTrace(e), null);

        fileLoadingLog
                .setFailedType(fileLoadException.getFailedType())
                .setFailedFields(fileLoadException.getFailedFields())
                .setExceptionType(fileLoadException.getExceptionType())
                .setExceptionMessage(fileLoadException.getExceptionMessage())
                .setProcessStatus(ProcessStatus.FAIL)
                .setErrorMessage(ExceptionUtils.getStackTrace(e));
    }

    default void updateLotWaferWarehousingRecordAfterDealFail(RepairRecordRepository repairRecordRepository, LotWaferWarehousingRecordRepository lotWaferWarehousingRecordRepository, DataRepairService dataRepairService, FileLoadingLog fileLoadingLog, Exception e) {
        // 任务失败，更新LotWaferWarehousing
        lotWaferWarehousingRecordRepository.updateProcessStatusForDie(
                fileLoadingLog.getCustomer(),
                fileLoadingLog.getTestArea(),
                fileLoadingLog.getFactory(),
                fileLoadingLog.getDeviceId(),
                fileLoadingLog.getLotType(),
                fileLoadingLog.getTestStage(),
                fileLoadingLog.getLotId(),
                fileLoadingLog.getWaferNo(),
                fileLoadingLog.getFileCategory(),
                ProcessStatus.FAIL,
                new Date(),
                ProcessStatus.FAIL
        );

        if (fileLoadingLog.getWarehousingMode() == WarehousingMode.REPAIR) {
            String fileName = fileLoadingLog.getFileName();
            List<RepairRecord> repairRecords = repairRecordRepository.findAllByRepairLotWaferIdAndFileName(fileLoadingLog.getRepairLotWaferId(), fileName);
            if (CollectionUtils.isEmpty(repairRecords)) {
                LOGGER.info("没有待修复的文件, {}", fileName);
                return;
            }
            RepairRecord repairRecord = repairRecords.get(0);

            FileLoadException fileLoadException = e instanceof FileLoadException ? ((FileLoadException) e) : new FileLoadException(FileLoadExceptionInfo.PARSE_FILE_EXCEPTION, ExceptionUtils.getStackTrace(e), null);
            // 修复结束
            dataRepairService.repairFinish(repairRecord.getRepairLotWaferId(), Collections.singletonList(fileName), ProcessStatus.FAIL, fileLoadException.getExceptionType(), fileLoadException.getExceptionMessage(), ExceptionUtils.getStackTrace(e));
        }
    }

    String getReadPath();

    String getErrorPath();

    HdfsUtil getHdfsUtil();

    FileInfoRepository getFileInfoRepository();

    ProcessLogRepository getProcessLogRepository();

    KafkaSink getKafKaSink();

    FileWarehousingRecordManager getFileWarehousingRecordManager();

}
