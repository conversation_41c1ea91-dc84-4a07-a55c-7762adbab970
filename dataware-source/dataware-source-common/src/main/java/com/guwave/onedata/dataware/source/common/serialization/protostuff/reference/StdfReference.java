package com.guwave.onedata.dataware.source.common.serialization.protostuff.reference;

import com.guwave.onedata.dataware.common.model.raw.DieData;
import com.guwave.onedata.dataware.common.model.raw.TestItemData;
import com.guwave.onedata.dataware.common.model.stdf.*;
import com.guwave.onedata.dataware.source.common.serialization.protostuff.TypeReference;

import java.util.List;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * StdfReference
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-20 17:31:26
 */
public class StdfReference {

    /**
     * 根据类型获取reference
     *
     * @param type type
     * @return TypeReference
     */
    public static TypeReference getReference(String type) {
        switch (type) {
            case SEND_LOG:
                return new TypeReference<List<SendLog>>() {
                    private static final long serialVersionUID = 6758782486453499244L;
                };
            case TEST_ITEM_DATA:
                return new TypeReference<List<TestItemData>>() {
                    private static final long serialVersionUID = -6545488651162219883L;
                };
            case DIE_DATA:
                return new TypeReference<List<DieData>>() {
                    private static final long serialVersionUID = 8177882728440228649L;
                };
            case ATR:
                return new TypeReference<List<Atr>>() {
                    private static final long serialVersionUID = 8177882728440228649L;
                };
            case FAR:
                return new TypeReference<List<Far>>() {
                    private static final long serialVersionUID = -3006281806653495903L;
                };
            case FTR:
                return new TypeReference<List<Ftr>>() {
                    private static final long serialVersionUID = 1641187334196914514L;
                };
            case HBR:
                return new TypeReference<List<Hbr>>() {
                    private static final long serialVersionUID = -1640191333115529021L;
                };
            case MIR:
                return new TypeReference<List<Mir>>() {
                    private static final long serialVersionUID = 7875856410982427221L;
                };
            case MRR:
                return new TypeReference<List<Mrr>>() {
                    private static final long serialVersionUID = 8667366419706555694L;
                };
            case PCR:
                return new TypeReference<List<Pcr>>() {
                    private static final long serialVersionUID = 4085622619887359948L;
                };
            case PGR:
                return new TypeReference<List<Pgr>>() {
                    private static final long serialVersionUID = -2325771971228794419L;
                };
            case PIR:
                return new TypeReference<List<Pir>>() {
                    private static final long serialVersionUID = 6073651881106820952L;
                };
            case PMR:
                return new TypeReference<List<Pmr>>() {
                    private static final long serialVersionUID = -6848182592616777202L;
                };
            case PRR:
                return new TypeReference<List<Prr>>() {
                    private static final long serialVersionUID = 2009262609652343055L;
                };
            case PTR:
                return new TypeReference<List<Ptr>>() {
                    private static final long serialVersionUID = 2195446761374966667L;
                };
            case SDR:
                return new TypeReference<List<Sdr>>() {
                    private static final long serialVersionUID = 688736948184780211L;
                };
            case SBR:
                return new TypeReference<List<Sbr>>() {
                    private static final long serialVersionUID = -7733099713464819498L;
                };
            case TSR:
                return new TypeReference<List<Tsr>>() {
                    private static final long serialVersionUID = 3128972478814240660L;
                };
            case WCR:
                return new TypeReference<List<Wcr>>() {
                    private static final long serialVersionUID = 261039718250493652L;
                };
            case WIR:
                return new TypeReference<List<Wir>>() {
                    private static final long serialVersionUID = -126242098734539575L;
                };
            case WRR:
                return new TypeReference<List<Wrr>>() {
                    private static final long serialVersionUID = 5663513035420262282L;
                };
            case MPR:
                return new TypeReference<List<Mpr>>() {
                    private static final long serialVersionUID = 7280869843095072745L;
                };
            default:
                return null;
        }
    }
}
