package com.guwave.onedata.dataware.source.agent.common.sink.filesystem.impl;

import com.guwave.onedata.dataware.common.model.biemem.DieBitMemData;
import com.guwave.onedata.dataware.common.model.biemem.TestItemBitMemData;
import com.guwave.onedata.dataware.common.model.raw.DieData;
import com.guwave.onedata.dataware.common.model.raw.EcidData;
import com.guwave.onedata.dataware.common.model.raw.UidData;
import com.guwave.onedata.dataware.source.agent.common.model.SubTestItemData;
import com.guwave.onedata.dataware.source.agent.common.sink.filesystem.FileSystemSink;
import com.guwave.onedata.dataware.source.agent.common.util.FileSystemUtil;
import com.guwave.onedata.dataware.source.agent.common.util.LocalfsUtil;
import org.apache.avro.Schema;
import org.apache.avro.reflect.ReflectData;
import org.apache.hadoop.fs.Path;
import org.apache.parquet.avro.AvroParquetWriter;
import org.apache.parquet.hadoop.ParquetFileWriter;
import org.apache.parquet.hadoop.ParquetWriter;
import org.apache.parquet.hadoop.metadata.CompressionCodecName;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import static com.guwave.onedata.dataware.common.contant.Constant.*;
import static com.guwave.onedata.dataware.common.contant.Constant.DIE_BIT_MEM_DATA;

public class LocalftSink implements FileSystemSink {

    public static final Map<String, Schema> SCHEMA_MAP = new HashMap<String, Schema>() {{
        put(TEST_ITEM_DATA, ReflectData.AllowNull.get().getSchema(SubTestItemData.class));
        put(DIE_DATA, ReflectData.AllowNull.get().getSchema(DieData.class));
        put(ECID_DATA, ReflectData.AllowNull.get().getSchema(EcidData.class));
        put(UID_DATA, ReflectData.AllowNull.get().getSchema(UidData.class));
        put(TEST_ITEM_BIT_MEM_DATA, ReflectData.AllowNull.get().getSchema(TestItemBitMemData.class));
        put(DIE_BIT_MEM_DATA, ReflectData.AllowNull.get().getSchema(DieBitMemData.class));
    }};


    private final int threadIndex;
    private LocalfsUtil localfsUtil;
    private final String odsHdfsTemplateForSchemaTypePath;
    private final File sourceFile;
    private final Integer allThreadCnt;
    private final Map<String, ParquetWriter> PARQUET_WRITE_MAP = new HashMap<>();
    private final Map<String, String> PARQUET_LOCAL_PATH = new HashMap<>();
    private final Map<String, Long> TYPE_VALUE_CNT = new HashMap<>();


    public LocalftSink(int threadIndex, FileSystemUtil localfsUtil, String odsHdfsTemplateForSchemaTypePath, File sourceFile, Integer allThreadCnt) {
        this.threadIndex = threadIndex;
        this.localfsUtil = (LocalfsUtil) localfsUtil;
        this.odsHdfsTemplateForSchemaTypePath = odsHdfsTemplateForSchemaTypePath;
        this.sourceFile = sourceFile;
        this.allThreadCnt = allThreadCnt;
        SCHEMA_MAP.forEach((type, schema) -> {
            String parquetLocalPath = getParquetLocalPath(type);
            this.PARQUET_LOCAL_PATH.put(type, parquetLocalPath);
            this.TYPE_VALUE_CNT.put(type, 0L);
            try {
                this.PARQUET_WRITE_MAP.put(type, getParquetWrite(schema, parquetLocalPath));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public Map<String, Long> getTypeValueCnt() {
        return TYPE_VALUE_CNT;
    }

    @Override
    public Map<String, ParquetWriter> getParquetWriteMap() {
        return PARQUET_WRITE_MAP;
    }

    @Override
    public File getSourceFile() {
        return sourceFile;
    }

    @Override
    public void afterWrite() {
        closeParquet();
        PARQUET_LOCAL_PATH.forEach((type, path) -> {
            if (this.TYPE_VALUE_CNT.get(type) > 0L) {
                // 生成的parquet文件上传hdfs
                localfsUtil.upload(
                        path.replace(LOCAL_FILE_PREFIX, EMPTY),
                        odsHdfsTemplateForSchemaTypePath
                                .replace(TYPE, type)
                                .replace(SMALL_LETTER_ODS_DATA_TYPE, type.toLowerCase()),
                        1
                );
            }
        });
    }

    @Override
    public Map<String, String> getParquetLocalPaths() {
        return PARQUET_LOCAL_PATH;
    }

    private ParquetWriter getParquetWrite(Schema schema, String parquetLocalPath) throws Exception {
        return AvroParquetWriter.builder(new Path(parquetLocalPath))
                .withSchema(schema)
                .withDataModel(new ReflectData.AllowNull())
                .withDictionaryEncoding(true)
                .withConf(localfsUtil.getSourceAgentCommonConfiguration().generateLocalConfig())
                .withWriteMode(ParquetFileWriter.Mode.OVERWRITE)
                .withRowGroupSize(calculateRowGroupSize())
                .withCompressionCodec(CompressionCodecName.ZSTD)
                .build();
    }

    @Override
    public Integer getThreadIndex() {
        return threadIndex;
    }


}
