package com.guwave.onedata.dataware.source.agent.common.util;

import com.guwave.onedata.dataware.common.contant.ManualFileSystem;
import com.guwave.onedata.dataware.source.agent.common.configuration.SourceAgentCommonConfiguration;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * HdfsUtil
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-26 10:17:18
 */
@Component
public class HdfsUtil implements FileSystemUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(HdfsUtil.class);

    private static final int allRetryCount = 5;

    private final FileSystem fileSystem;

    @Autowired
    private SourceAgentCommonConfiguration sourceAgentCommonConfiguration;

    public HdfsUtil(FileSystem fileSystem) {
        this.fileSystem = fileSystem;
    }

    public void upload(String filePath, String targetPath, int retryCount) {
        uploadToHdfs(filePath, targetPath, retryCount);
    }

    /**
     * 上传至hdfs
     *
     * @param filePath filePath
     * @param hdfsPath hdfsPath
     */
    private void uploadToHdfs(String filePath, String hdfsPath, int retryCount) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                LOGGER.info("文件不存在: {}", filePath);
                throw new RuntimeException(filePath + "文件不存在");
            }
            Path dest = new Path(hdfsPath);
            if (!this.fileSystem.exists(dest)) {
                LOGGER.info("目录不存在, 创建目录");
                this.fileSystem.mkdirs(dest);
            }
            LOGGER.info("{} 上传到 {}", filePath, hdfsPath);
            this.fileSystem.copyFromLocalFile(true, true, new Path(filePath), dest);
        } catch (Exception e) {
            LOGGER.info("{} -> {} 上传至hdfs失败", filePath, hdfsPath, e);
            if (retryCount > allRetryCount) {
                throw new RuntimeException(e);
            } else {
                LOGGER.info("uploadToHDFS 第{}次尝试重新连接......{}", retryCount, e.getMessage());
                try {
                    Thread.sleep(retryCount * 5000L);
                } catch (InterruptedException ex) {
                    //
                }
                uploadToHdfs(filePath, hdfsPath, ++retryCount);
            }
        }
    }

    /**
     * get hdfs file
     *
     * @param delSrc   是否删除原始文件
     * @param targetPath hdfs路径
     * @param filePath 本地路径
     * @throws IOException
     */
    public void downloadToLocal(boolean delSrc, String targetPath, String filePath) throws IOException {
        try {
            LOGGER.info("从hdfs下载文件到本地, hdfsPath: {},localPath: {}", targetPath, filePath);
            this.fileSystem.copyToLocalFile(delSrc, new Path(targetPath), new Path(filePath));
        } catch (IOException e) {
            LOGGER.error("从hdfs下载文件到本地失败, hdfsPath: {},localPath: {}", targetPath, filePath, e);
            throw e;
        }
    }

    public void deleteFile(String targetPath, Boolean recursive) {
        deleteHdfsFile(targetPath, recursive);
    }

    @Override
    public ManualFileSystem getManualFileSystem() {
        return ManualFileSystem.HDFS;
    }

    public void deleteHdfsFile(String hdfsPath, Boolean recursive) {
        try {
            LOGGER.info("从hdfs删除文件, hdfsPath: {}", hdfsPath);
            this.fileSystem.delete(new Path(hdfsPath), recursive);
        } catch (Exception e) {
            LOGGER.error("从hdfs删除文件失败, hdfsPath: {}", hdfsPath, e);
            throw new RuntimeException(e);
        }
    }

    public SourceAgentCommonConfiguration getSourceAgentCommonConfiguration() {
        return sourceAgentCommonConfiguration;
    }
}
