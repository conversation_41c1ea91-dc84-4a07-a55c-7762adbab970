package com.guwave.onedata.dataware.collectx.service;

import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.collectx.util.AdapterUtil;
import com.guwave.onedata.dataware.common.contant.ConvertStatus;
import com.guwave.onedata.dataware.common.contant.ExceptionType;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileLoadingLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileDetail;
import org.apache.commons.math3.util.Pair;

import java.util.Date;
import java.util.HashMap;

public interface SingleSftpFileConvertService extends DataConvertService {


    default int updateStatusBeforeDeal(SftpFileDetail needConvertFile) {
        // 更新处理状态
        needConvertFile
                .setProcessStatus(ProcessStatus.PROCESSING)
                .setUpdateTime(new Date());
        return getSftpFileDetailRepository().updateProcessStatusProcessingFromCreate(needConvertFile.getId(), needConvertFile.getUpdateTime());
    }

    default Boolean getNeedCompressAndUploadResultFileFlag() {
        return Boolean.TRUE;
    }

    default Pair<Boolean, HashMap<Object, Object>> convertFile(SftpFileDetail needConvertFile, String originalFilePath, String convertFilePath, String executeScriptPath, FileLoadingLog fileLoadingLog,FileLoadingLog sourceFileLoadingLog) throws Exception {
        log.info("convert file : {}  ----->  {}", originalFilePath, convertFilePath);
        //转换前文件名
        Date date = new Date();
        String fileName = needConvertFile.getLocalFileName();
        fileLoadingLog.setSourceFileNames(fileName)
                .setCreateTime(date)
                .setStepStartTime(date)
                .setProcessStatus(ProcessStatus.PROCESSING)
        ;

        Pair<Boolean, HashMap<Object, Object>> resultMap = AdapterUtil.executePython(executeScriptPath, Lists.newArrayList(originalFilePath, convertFilePath, needConvertFile.getOriginFileName()), getLogConsumer(fileLoadingLog,sourceFileLoadingLog));
        if (resultMap.getValue() != null && resultMap.getValue().containsKey(ConvertStatus.TO_MANY_SUCCESS_PREFIX.getStatus())) {
            String convertFileNames = String.valueOf(resultMap.getValue().get(ConvertStatus.TO_MANY_SUCCESS_PREFIX.getStatus()));
            log.info("原始文件：{} 转换后生成多个文件: {}", fileName, convertFileNames);
            saveSftpFileConvertRecord(convertFileNames, fileName);
        }
        return resultMap;
    }

    default void readFile(SftpFileDetail needConvertFile, String originalFilePath, String executeScriptPath, FileLoadingLog fileLoadingLog) {
        // do nothing
    }

    default void updateStatusAfterDealSuccess(SftpFileDetail needConvertFile) {
        // 设置处理状态
        needConvertFile.setProcessStatus(ProcessStatus.SUCCESS);
    }

    default void updateFileLoadingLogStatusAfterDealSuccess(FileLoadingLog fileLoadingLog) {
        fileLoadingLog.setProcessStatus(ProcessStatus.SUCCESS);
    }

    default void updateStatusAfterDealException(SftpFileDetail needConvertFile, Exception e) {
        log.error("转换文件异常：", e);
        // 设置处理状态
        needConvertFile.setProcessStatus(ProcessStatus.FAIL);
    }

    default void updateStatusFinally(SftpFileDetail needConvertFile) {
        // 更新处理状态
        needConvertFile.setUpdateTime(new Date());
        getSftpFileDetailRepository().save(needConvertFile);
    }

    default void updateFileLoadingLogStatusFinally(FileLoadingLog fileLoadingLog) {
        fileLoadingLog.setStepEndTime(new Date())
                .setUpdateTime(new Date());
        getFileLoadingLogRepository().save(fileLoadingLog);
    }

}
