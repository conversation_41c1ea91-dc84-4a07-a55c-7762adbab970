package com.guwave.onedata.dataware.source.agent.common.sink.impl;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import com.guwave.onedata.dataware.common.model.stdf.StdfCommon;
import com.guwave.onedata.dataware.common.model.stdf.StdfData;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DatawareFailMessageRecord;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileLoadingLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotMetaDataDetail;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.DatawareFailMessageRecordRepository;
import com.guwave.onedata.dataware.source.agent.common.sink.Sink;
import com.guwave.onedata.dataware.source.common.serialization.avro.StdfDataSerialization;
import com.guwave.onedata.dataware.source.common.serialization.protostuff.TypeReference;
import com.guwave.onedata.dataware.source.common.serialization.protostuff.reference.StdfReference;
import com.guwave.onedata.dataware.source.common.util.ProtostuffUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.guwave.onedata.dataware.common.contant.Constant.SEND_LOG;
import static com.guwave.onedata.dataware.common.contant.Constant.SYSTEM;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * KafkaSink
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2021-10-18 15:41:29
 */
@Component
public class KafkaSink implements Sink {

    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaSink.class);

    private final KafkaTemplate<byte[], byte[]> kafkaTemplate;

    private final DatawareFailMessageRecordRepository datawareFailMessageRecordRepository;

    @Value("${spring.kafka.sleepMilliseconds}")
    private Long sleepMilliseconds;
    @Value("${spring.kafka.loadEndFlagTopic}")
    private String loadEndFlagTopic;

    public KafkaSink(KafkaTemplate<byte[], byte[]> kafkaTemplate, DatawareFailMessageRecordRepository datawareFailMessageRecordRepository) {
        this.kafkaTemplate = kafkaTemplate;
        this.datawareFailMessageRecordRepository = datawareFailMessageRecordRepository;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void send(String topic, String type, List<StdfCommon> values, LotMetaDataDetail lotMetaDataDetail, Long ts) {

    }

    @Override
    public void afterSend() {
        // do nothing
    }

    /**
     * 发送消息
     *
     * @param topic topic
     * @param value value
     */
    public void send(String topic, String value) {
        byte[] message = value.getBytes(StandardCharsets.UTF_8);
        sleep();
        this.kafkaTemplate.send(topic, message).addCallback(success -> {
            // 消息发送到的topic
            assert success != null;
            // 消息发送到的分区
            int partition = success.getRecordMetadata().partition();
            // 消息在分区内的offset
            long offset = success.getRecordMetadata().offset();
            LOGGER.info("消息发送成功, topic: {}, partition: {}, offset: {}, message: {}", topic, partition, offset, value);
        }, fail -> {
            DatawareFailMessageRecord datawareFailMessageRecord = new DatawareFailMessageRecord()
                    .setProject(ProjectEnum.DATAWARE)
                    .setModule(ModuleEnum.DATAWARE_SOURCE_AGENT)
                    .setTopic(topic)
                    .setKey(null)
                    .setValue(value)
                    .setProcessStatus(ProcessStatus.FAIL)
                    .setDeleteFlag(0)
                    .setCreateUser(SYSTEM)
                    .setUpdateUser(SYSTEM)
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date());

            datawareFailMessageRecordRepository.save(datawareFailMessageRecord);
            LOGGER.info("发送消息失败，将该条消息记入dw_dataware_fail_message_record", fail);
        });
    }

    /**
     * 发送消息
     *
     * @param topic topic
     * @param value value
     */
    public void send(String topic, String key, String value) {
        LOGGER.info("发送消息 {}", value);
        byte[] message = value.getBytes(StandardCharsets.UTF_8);
        sleep();
        this.kafkaTemplate.send(topic, key.getBytes(StandardCharsets.UTF_8), message).addCallback(success -> {
            // 消息发送到的topic
            assert success != null;
            // 消息发送到的分区
            int partition = success.getRecordMetadata().partition();
            // 消息在分区内的offset
            long offset = success.getRecordMetadata().offset();
            LOGGER.info("消息发送成功, topic: {}, partition: {}, offset: {}, {}", topic, partition, offset, value);
        }, fail -> {
            DatawareFailMessageRecord datawareFailMessageRecord = new DatawareFailMessageRecord()
                    .setProject(ProjectEnum.DATAWARE)
                    .setModule(ModuleEnum.DATAWARE_SOURCE_AGENT)
                    .setTopic(topic)
                    .setKey(null)
                    .setValue(value)
                    .setProcessStatus(ProcessStatus.FAIL)
                    .setDeleteFlag(0)
                    .setCreateUser(SYSTEM)
                    .setUpdateUser(SYSTEM)
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date());

            datawareFailMessageRecordRepository.save(datawareFailMessageRecord);
            LOGGER.error("发送消息失败，将该条消息记入dw_dataware_fail_message_record {} ", value, fail);
        });
    }

    public void sendLoadEndFlagMessage(FileLoadingLog fileLoadingLog) {
        // 发送文件处理结束通知
        LOGGER.info("发送文件处理结束通知, fileName: {}", fileLoadingLog.getFileName());

        List<Long> cleanupTaskIds = StringUtils.isBlank(fileLoadingLog.getCleanupTaskIds()) ? null : Stream.of(fileLoadingLog.getCleanupTaskIds().split(Constant.COMMA)).flatMap(t -> Stream.of(t.split(Constant.COMMA)).map(s -> Long.parseLong(s.trim())))
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        CalculateEndFlag calculateEndFlagMessage = new CalculateEndFlag()
                .setCustomer(fileLoadingLog.getCustomer())
                .setSubCustomer(fileLoadingLog.getSubCustomer())
                .setFactory(fileLoadingLog.getFactory())
                .setFactorySite(fileLoadingLog.getFactorySite())
                .setTestArea(fileLoadingLog.getTestArea())
                .setFileCategory(fileLoadingLog.getFileCategory())
                .setWarehousingMode(fileLoadingLog.getWarehousingMode())
                .setRepairLotWaferId(fileLoadingLog.getRepairLotWaferId())
                .setCleanupTaskIds(cleanupTaskIds)
                .setPlatform(Platform.CK)
                .setProcessStatus(fileLoadingLog.getProcessStatus())
                .setExceptionType(fileLoadingLog.getExceptionType())
                .setExceptionMessage(fileLoadingLog.getExceptionMessage())
                .setErrorMessage(fileLoadingLog.getProcessStatus() == ProcessStatus.SUCCESS ? null : fileLoadingLog.getFileName() + Constant.ENTER + fileLoadingLog.getErrorMessage())
                .setTs(System.currentTimeMillis());
        this.send(loadEndFlagTopic, JSON.toJSONString(calculateEndFlagMessage));
    }


    @Override
    public void close() {
        // do nothing
    }

    @Override
    public void cleanTmpFile() {
        // do nothing
    }

    private void sleep() {
        try {
            // kafka busy networkException ， try sleep
            Thread.sleep(sleepMilliseconds);
        } catch (InterruptedException e) {
            LOGGER.info("sleep 异常", e);
        }
    }
}
