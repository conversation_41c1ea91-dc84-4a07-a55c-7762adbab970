package com.guwave.onedata.dataware.source.agent.common.sink.ck.wat.dwd;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.model.wat.dwd.DwdWatTestItemDetail;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

/**
 * Copyright (C), 2022, guwave
 * <p>
 * DwdWatTestItemDetailSink
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2022-03-14 11:34:47
 */
@Component
public class DwdWatTestItemDetailSink implements CkSink<DwdWatTestItemDetail> {

    @Value("${spring.sink.ck.dwd.dbName}")
    private String dbName;

    @Override
    public String getTableName() {
        return "dwd_test_item_detail_cluster";
    }

    @Override
    public String getPartitionExpr() {
        return "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{SUB_CUSTOMER}', '{DEVICE_ID}', {LOT_BUCKET})";
    }

    @Override
    public void handle(PreparedStatement statement, List<DwdWatTestItemDetail> items) throws SQLException {
        for (DwdWatTestItemDetail item : items) {
            // CUSTOMER
            statement.setObject(1, item.getCustomer());
            // UPLOAD_TYPE
            statement.setObject(2, item.getUploadType());
            // FILE_ID
            statement.setObject(3, item.getFileId());
            // FILE_NAME
            statement.setObject(4, item.getFileName());
            // FILE_TYPE
            statement.setObject(5, item.getFileType());
            // DEVICE_ID
            statement.setObject(6, item.getDeviceId());
            // FACTORY
            statement.setObject(7, item.getFactory());
            // FACTORY_SITE
            statement.setObject(8, item.getFactorySite());
            // FAB
            statement.setObject(9, item.getFab());
            // FAB_SITE
            statement.setObject(10, item.getFabSite());
            // LOT_TYPE
            statement.setObject(11, item.getLotType());
            // LOT_ID
            statement.setObject(12, item.getLotId());
            // TEST_AREA
            statement.setObject(13, item.getTestArea());
            // WAFER_ID
            statement.setObject(14, item.getWaferId());
            // START_TIME
            statement.setObject(15, this.toTimestamp(item.getStartTime()));
            // END_TIME
            statement.setObject(16, this.toTimestamp(item.getEndTime()));
            // CREATE_TIME
            statement.setObject(17, new Timestamp(System.currentTimeMillis()));
            // CREATE_USER
            statement.setObject(18, item.getCreateUser());

            // SITE_ID
            statement.setObject(19, item.getSiteId() == null ? Constant.EMPTY_STR : item.getSiteId());
            // RETICLE_X
            statement.setObject(20, item.getReticleX());
            // RETICLE_Y
            statement.setObject(21, item.getReticleY());
            // X_COORD
            statement.setObject(22, item.getxCoord());
            // Y_COORD
            statement.setObject(23, item.getyCoord());
            // WF_FLAT
            statement.setObject(24, item.getWfFlat());
            // TEST_PROGRAM
            statement.setObject(25, item.getTestProgram());
            // TEST_TXT
            statement.setObject(26, item.getTestTxt());
            // LO_LIMIT
            statement.setObject(27, this.toBigDecimal(item.getLoLimit()));
            // HI_LIMIT
            statement.setObject(28, this.toBigDecimal(item.getHiLimit()));
            // ORIGIN_UNITS
            statement.setObject(29, item.getOriginUnits());
            // ORIGIN_TEST_VALUE
            statement.setObject(30, this.toBigDecimal(item.getOriginTestValue()));
            // ID
            statement.setObject(31, item.getId());
            // WAFER_MARGIN
            statement.setObject(32, item.getWaferMargin());
            // RETICLE_ROW
            statement.setObject(33, item.getReticleRow());
            // RETICLE_COLUMN
            statement.setObject(34, item.getReticleColumn());
            // RETICLE_ROW_CENTER_OFFSET
            statement.setObject(35, item.getReticleRowCenterOffset());
            // RETICLE_COLUMN_CENTER_OFFSET
            statement.setObject(36, item.getReticleColumnCenterOffset());
            // CENTER_OFFSET_X
            statement.setObject(37, item.getCenterOffsetX());
            // CENTER_OFFSET_Y
            statement.setObject(38, item.getCenterOffsetY());
            // CENTER_X
            statement.setObject(39, item.getCenterX());
            // CENTER_Y
            statement.setObject(40, item.getCenterY());
            // WF_UNITS
            statement.setObject(41, item.getWfUnits());
            // DIE_HEIGHT
            statement.setObject(42, item.getDieHeight());
            // DIE_WIDTH
            statement.setObject(43, item.getDieWidth());
            // WAFER_SIZE
            statement.setObject(44, item.getWaferSize());
            // DIE_CNT
            statement.setObject(45, item.getDieCnt());
            // POS_X
            statement.setObject(46, item.getPosX());
            // POS_Y
            statement.setObject(47, item.getPosY());
            // DIE_X
            statement.setObject(48, item.getDieX());
            // DIE_Y
            statement.setObject(49, item.getDieY());
            // RETICLE_T_X
            statement.setObject(50, item.getReticleTX());
            // RETICLE_T_Y
            statement.setObject(51, item.getReticleTY());
            // TEST_NUM
            statement.setObject(52, item.getTestNum());
            // TEST_ITEM
            statement.setObject(53, item.getTestItem());
            // WAFER_NO
            statement.setObject(54, item.getWaferNo());
            // IS_FIRST_TEST
            statement.setObject(55, item.getIsFirstTest());
            // IS_FINAL_TEST
            statement.setObject(56, item.getIsFinalTest());
            // IS_DUP_FIRST_TEST
            statement.setObject(57, item.getIsDupFirstTest());
            // IS_DUP_FINAL_TEST
            statement.setObject(58, item.getIsDupFinalTest());
            // TEST_STAGE
            statement.setObject(59, item.getTestStage());
            // TEST_VALUE
            statement.setObject(60, this.toBigDecimal(item.getTestValue()));
            // PROC_ID
            statement.setObject(61, item.getProcess());
            // SPEC_NAM
//            statement.setObject(62, item.getSpecNam());
            statement.setObject(62, item.getTestTemperature());
            // TESTITEM_TYPE
            statement.setObject(63, item.getTestitemType());
            // ORIGIN_HI_LIMIT
            statement.setObject(64, this.toBigDecimal(item.getOriginHiLimit()));
            // ORIGIN_LO_LIMIT
            statement.setObject(65, this.toBigDecimal(item.getOriginLoLimit()));
            // UNITS
            statement.setObject(66, item.getUnits());
            // HI_SPEC
            statement.setObject(67, this.toBigDecimal(item.getHiSpec()));
            // LO_SPEC
            statement.setObject(68, this.toBigDecimal(item.getLoSpec()));
            // TEST_RESULT
            statement.setObject(69, item.getTestResult());
            // WAFER_LOT_ID
            statement.setObject(70, item.getWaferLotId());
            // CENTER_RETICLE_X
            statement.setObject(71, item.getCenterReticleX());
            // CENTER_RETICLE_Y
            statement.setObject(72, item.getCenterReticleY());
            // CENTER_RETICLE_OFFSET_X
            statement.setObject(73, item.getCenterReticleOffsetX());
            // CENTER_RETICLE_OFFSET_Y
            statement.setObject(74, item.getCenterReticleOffsetY());
            // ORIGINAL_WF_FLAT
            statement.setObject(75, item.getOriginalWfFlat());
            // ORIGINAL_POS_X
            statement.setObject(76, item.getOriginalPosX());
            // ORIGINAL_POS_Y
            statement.setObject(77, item.getOriginalPosY());
            // ORIGINAL_DIE_WIDTH
            statement.setObject(78, item.getOriginalDieWidth());
            // ORIGINAL_DIE_HEIGHT
            statement.setObject(79, item.getOriginalDieHeight());
            // ORIGINAL_RETICLE_ROW
            statement.setObject(80, item.getOriginalReticleRow());
            // ORIGINAL_RETICLE_COLUMN
            statement.setObject(81, item.getOriginalReticleColumn());
            // ORIGINAL_RETICLE_ROW_CENTER_OFFSET
            statement.setObject(82, item.getOriginalReticleRowCenterOffset());
            // ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET
            statement.setObject(83, item.getOriginalReticleColumnCenterOffset());
            // ORIGINAL_CENTER_X
            statement.setObject(84, item.getOriginalCenterX());
            // ORIGINAL_CENTER_Y
            statement.setObject(85, item.getOriginalCenterY());
            // ORIGINAL_CENTER_RETICLE_X
            statement.setObject(86, item.getOriginalCenterReticleX());
            // ORIGINAL_CENTER_RETICLE_Y
            statement.setObject(87, item.getOriginalCenterReticleY());
            // ORIGINAL_CENTER_OFFSET_X
            statement.setObject(88, item.getOriginalCenterOffsetX());
            // ORIGINAL_CENTER_OFFSET_Y
            statement.setObject(89, item.getOriginalCenterOffsetY());
            // ORIGINAL_CENTER_RETICLE_OFFSET_X
            statement.setObject(90, item.getOriginalCenterReticleOffsetX());
            // ORIGINAL_CENTER_RETICLE_OFFSET_Y
            statement.setObject(91, item.getOriginalCenterReticleOffsetY());
            // ORIGINAL_WAFER_SIZE
            statement.setObject(92, item.getOriginalWaferSize());
            // ORIGINAL_WAFER_MARGIN
            statement.setObject(93, item.getOriginalWaferMargin());
            // ORIGINAL_WF_UNITS
            statement.setObject(94, item.getOriginalWfUnits());
            statement.setObject(95, item.getSubCustomer());
            statement.setObject(96, item.getTesterName());
            statement.setObject(97, item.getOperatorName());
            statement.setObject(98, item.getConditionSet());
            statement.setObject(99, new Timestamp(item.getUploadTime()));
            statement.setObject(100, item.getDataVersion());
            statement.setObject(101, item.getFabwfId());
            statement.setObject(102, this.toBigDecimal(item.getTarget()));
            statement.addBatch();
        }
    }

    @Override
    public List<String> getColumns() {
        return Arrays.asList(
                "CUSTOMER",
                "UPLOAD_TYPE",
                "FILE_ID",
                "FILE_NAME",
                "FILE_TYPE",
                "DEVICE_ID",
                "FACTORY",
                "FACTORY_SITE",
                "FAB",
                "FAB_SITE",
                "LOT_TYPE",
                "LOT_ID",
                "TEST_AREA",
                "WAFER_ID",
                "START_TIME",
                "END_TIME",
                "CREATE_TIME",
                "CREATE_USER",
                "SITE_ID",
                "RETICLE_X",
                "RETICLE_Y",
                "X_COORD",
                "Y_COORD",
                "WF_FLAT",
                "TEST_PROGRAM",
                "TEST_TXT",
                "LO_LIMIT",
                "HI_LIMIT",
                "ORIGIN_UNITS",
                "ORIGIN_TEST_VALUE",
                "ID",
                "WAFER_MARGIN",
                "RETICLE_ROW",
                "RETICLE_COLUMN",
                "RETICLE_ROW_CENTER_OFFSET",
                "RETICLE_COLUMN_CENTER_OFFSET",
                "CENTER_OFFSET_X",
                "CENTER_OFFSET_Y",
                "CENTER_X",
                "CENTER_Y",
                "WF_UNITS",
                "DIE_HEIGHT",
                "DIE_WIDTH",
                "WAFER_SIZE",
                "DIE_CNT",
                "POS_X",
                "POS_Y",
                "DIE_X",
                "DIE_Y",
                "RETICLE_T_X",
                "RETICLE_T_Y",
                "TEST_NUM",
                "TEST_ITEM",
                "WAFER_NO",
                "IS_FIRST_TEST",
                "IS_FINAL_TEST",
                "IS_DUP_FIRST_TEST",
                "IS_DUP_FINAL_TEST",
                "TEST_STAGE",
                "TEST_VALUE",
                "PROCESS",
                "TEST_TEMPERATURE",
                "TESTITEM_TYPE",
                "ORIGIN_HI_LIMIT",
                "ORIGIN_LO_LIMIT",
                "UNITS",
                "HI_SPEC",
                "LO_SPEC",
                "TEST_RESULT",
                "WAFER_LOT_ID",
                "CENTER_RETICLE_X",
                "CENTER_RETICLE_Y",
                "CENTER_RETICLE_OFFSET_X",
                "CENTER_RETICLE_OFFSET_Y",
                "ORIGINAL_WF_FLAT",
                "ORIGINAL_POS_X",
                "ORIGINAL_POS_Y",
                "ORIGINAL_DIE_WIDTH",
                "ORIGINAL_DIE_HEIGHT",
                "ORIGINAL_RETICLE_ROW",
                "ORIGINAL_RETICLE_COLUMN",
                "ORIGINAL_RETICLE_ROW_CENTER_OFFSET",
                "ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET",
                "ORIGINAL_CENTER_X",
                "ORIGINAL_CENTER_Y",
                "ORIGINAL_CENTER_RETICLE_X",
                "ORIGINAL_CENTER_RETICLE_Y",
                "ORIGINAL_CENTER_OFFSET_X",
                "ORIGINAL_CENTER_OFFSET_Y",
                "ORIGINAL_CENTER_RETICLE_OFFSET_X",
                "ORIGINAL_CENTER_RETICLE_OFFSET_Y",
                "ORIGINAL_WAFER_SIZE",
                "ORIGINAL_WAFER_MARGIN",
                "ORIGINAL_WF_UNITS",
                "SUB_CUSTOMER",
                "TESTER_NAME",
                "OPERATOR_NAME",
                "CONDITION_SET",
                "UPLOAD_TIME",
                "DATA_VERSION",
                "FABWF_ID",
                "TARGET"
        );
    }

    @Override
    public String getDbName() {
        return dbName;
    }
}
