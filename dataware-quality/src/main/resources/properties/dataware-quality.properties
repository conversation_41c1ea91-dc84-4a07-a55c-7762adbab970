module.name=dataware-quality

# database config
database.address=riot43:3306
database.name=onedata
database.username=bi
database.password=bi@guwave

# 定时任务个数
task.scheduling.pool.size=2

# 每天一次的任务的cron
scheduler.timer.dailyTask=0 0 1 * * ?
# 半小时一次的任务的cron
scheduler.timer.realtimeTask=0 0/30 * * * ?
# 半小时每分钟一次的任务的cron
scheduler.timer.minuteTask=0 0/1 * * * ?

# ck config
data.clickhouse.address=****************************************,****************************************,****************************************
data.clickhouse.username=admin
data.clickhouse.password=admin@ck@Guwave
data.clickhouse.cluster=cluster_3shards_1replicas
data.clickhouse.batchInsertSize=10000

# dubbo config
zookeeper.address=riot12.guwave.com:2181,riot13.guwave.com:2181,riot14.guwave.com:2181
# qa deploy fill with   qa    ,otherwise fill with    prod
environment.group=prod
# rpc timeout (ms)
rpc.timeout=60000
