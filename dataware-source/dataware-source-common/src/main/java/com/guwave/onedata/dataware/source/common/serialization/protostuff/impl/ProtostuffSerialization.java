package com.guwave.onedata.dataware.source.common.serialization.protostuff.impl;

import com.guwave.onedata.dataware.source.common.serialization.protostuff.Serialization;
import com.guwave.onedata.dataware.source.common.serialization.protostuff.TypeReference;
import com.guwave.onedata.dataware.source.common.util.ProtostuffUtil;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * ProtostuffSerialization
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-18 15:16:05
 */
public class ProtostuffSerialization implements Serialization {

    @Override
    public <T> byte[] serializer(T obj, TypeReference<T> typeReference) {
        return ProtostuffUtil.serializer(obj, typeReference);
    }

    @Override
    public <T> T deserializer(byte[] bytes, TypeReference<T> typeReference) {
        return ProtostuffUtil.deserializer(bytes, typeReference);
    }
}
