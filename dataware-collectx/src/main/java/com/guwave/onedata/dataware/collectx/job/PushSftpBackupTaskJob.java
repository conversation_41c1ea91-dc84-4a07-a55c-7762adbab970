package com.guwave.onedata.dataware.collectx.job;

import com.guwave.onedata.dataware.collectx.service.impl.PushSftpBackupTaskService;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PushSftpBackupTaskJob {

    @Autowired
    private PushSftpBackupTaskService pushSftpBackupTaskService;

    @ScheduleSwitch
    @Scheduled(cron = "${spring.scheduler.timer.pushSftpBackupTask}")
    public void pushTask() {
        try {
            pushSftpBackupTaskService.pushTask();
        } catch (Exception e) {
            log.error(e.toString(), e);
        }
    }

}

