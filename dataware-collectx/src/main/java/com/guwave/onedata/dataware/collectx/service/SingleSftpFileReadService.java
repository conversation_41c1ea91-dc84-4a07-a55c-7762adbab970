package com.guwave.onedata.dataware.collectx.service;

import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.collectx.util.AdapterUtil;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.ConvertStatus;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileLoadingLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LogMapDetail;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileDetail;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LogMapDetailRepository;
import org.apache.commons.math3.util.Pair;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

public interface SingleSftpFileReadService extends DataConvertService {
    Map<String, BiConsumer<LogMapDetail, String>> FILE_CONTENT_CONSUMER_MAP = new HashMap<String, BiConsumer<LogMapDetail, String>>() {{
        put("deviceId", LogMapDetail::setDeviceId);
        put("lotId", LogMapDetail::setLotId);
        put("waferNo", LogMapDetail::setWaferNo);
        put("testStage", LogMapDetail::setTestStage);
        put("flowId", LogMapDetail::setFlowId);
    }};


    default void readFile(SftpFileDetail needConvertFile, String originalFilePath, String executeScriptPath, FileLoadingLog fileLoadingLog) throws Exception {
        log.info("read file : {}", originalFilePath);
        Pair<Boolean, HashMap<Object, Object>> resultPair = AdapterUtil.executePython(executeScriptPath, Lists.newArrayList(getPythonTypeArg(), originalFilePath, needConvertFile.getOriginFileName()), null);
        Boolean status = resultPair.getKey();
        HashMap<Object, Object> resultMap = resultPair.getValue();
        if (!status || resultMap == null || resultMap.isEmpty()) {
            throw new RuntimeException(originalFilePath + "读取失败！");
        }

        Date date = new Date();
        LogMapDetail logMapDetail = new LogMapDetail()
                .setCustomer(needConvertFile.getCustomer())
                .setSubCustomer(needConvertFile.getSubCustomer())
                .setFtpIp(needConvertFile.getFtpIp())
                .setTestArea(needConvertFile.getTestArea())
                .setFactory(needConvertFile.getFactory())
                .setFactorySite(needConvertFile.getFactorySite())
                .setFileCategory(getConvertResultFileCategory())
                .setSftpFileId(needConvertFile.getId())
                .setProcessStatus(ProcessStatus.CREATE)
                .setCreateTime(date)
                .setUpdateTime(date)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateUser(Constant.SYSTEM);

        resultMap.forEach((key, value) -> {
            if (key != null && value != null) {
                String result = value.toString().trim();
                BiConsumer<LogMapDetail, String> consumer = FILE_CONTENT_CONSUMER_MAP.get(key.toString());
                if (consumer != null) {
                    consumer.accept(logMapDetail, result);
                }
            }
        });

        //解析出基本字段
        fileLoadingLog
                .setDeviceId(logMapDetail.getDeviceId())
                .setLotId(logMapDetail.getLotId())
                .setWaferNo(logMapDetail.getWaferNo())
                .setTestStage(logMapDetail.getTestStage());
        if (resultMap.containsKey(ConvertStatus.WARNING_MESSAGE_PREFIX.getStatus())) {
            String warningMessage = String.valueOf(resultMap.get(ConvertStatus.WARNING_MESSAGE_PREFIX.getStatus()));
            fileLoadingLog.setWarningMessage(warningMessage);
            log.info("文件：{} 转换后有告警信息: {}", fileLoadingLog.getFileName(), warningMessage);
        }
        getLogMapDetailRepository().save(logMapDetail);
    }

    default Pair<Boolean, HashMap<Object, Object>> convertFile(SftpFileDetail needConvertFile, String originalFilePath, String convertFilePath, String executeScriptPath, FileLoadingLog fileLoadingLog,FileLoadingLog sourceFileLoadingLog) {
        // do nothing
        return new Pair<>(false, new HashMap<>());
    }

    LogMapDetailRepository getLogMapDetailRepository();

    String getPythonTypeArg();

    default Boolean getNeedCompressAndUploadResultFileFlag() {
        return Boolean.FALSE;
    }

    default int updateStatusBeforeDeal(SftpFileDetail needConvertFile) {
        // 更新处理状态
        needConvertFile
                .setProcessStatus(ProcessStatus.PROCESSING)
                .setUpdateTime(new Date());
        return getSftpFileDetailRepository().updateProcessStatusProcessingFromCreate(needConvertFile.getId(), needConvertFile.getUpdateTime());
    }

    default void updateStatusAfterDealSuccess(SftpFileDetail needConvertFile) {
        // 设置处理状态
        needConvertFile.setProcessStatus(ProcessStatus.SUCCESS);
    }

    default void updateFileLoadingLogStatusAfterDealSuccess(FileLoadingLog fileLoadingLog) {
        fileLoadingLog.setProcessStatus(ProcessStatus.SUCCESS);
    }

    default void updateStatusAfterDealException(SftpFileDetail needConvertFile, Exception e) {
        log.error("转换文件异常：", e);
        // 设置处理状态
        needConvertFile.setProcessStatus(ProcessStatus.FAIL);
    }

    default void updateStatusFinally(SftpFileDetail needConvertFile) {
        // 更新处理状态
        needConvertFile.setUpdateTime(new Date());
        getSftpFileDetailRepository().save(needConvertFile);
    }

    default void updateFileLoadingLogStatusFinally(FileLoadingLog fileLoadingLog) {
        fileLoadingLog.setStepEndTime(new Date())
                .setUpdateTime(new Date());
        getFileLoadingLogRepository().save(fileLoadingLog);
    }
}
