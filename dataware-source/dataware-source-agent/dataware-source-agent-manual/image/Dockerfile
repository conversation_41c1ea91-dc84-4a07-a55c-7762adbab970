FROM common/openjdk-8u262-python3.8.13-centos7.8

<NAME_EMAIL>

ARG version

ENV VERSION $version

RUN mkdir -p /home/<USER>/deploy/onedata/dataware/dataware-source-agent-manual
COPY dataware-source-agent-manual-$version.tar /home/<USER>/deploy/onedata/dataware/dataware-source-agent-manual

COPY run.sh /run.sh
RUN chmod +x /run.sh

WORKDIR /home/<USER>/deploy/onedata/dataware/dataware-source-agent-manual

CMD ["/run.sh"]
