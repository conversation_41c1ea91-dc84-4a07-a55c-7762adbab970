package com.guwave.onedata.dataware.scheduler.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.guwave.onedata.dataware.dao.ck.connection.ClickHouseConnection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.*;
import java.util.function.Function;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

/**
 * 2023/7/5 16:12
 * CkProvider
 * <AUTHOR>
 */
@Component
public class CkProvider {

    private static final Logger LOGGER = LoggerFactory.getLogger(CkProvider.class);


    public <T> List<T> readCk(String sql, Class<T> clazz) {
        LOGGER.info("读取ck开始");
        long start = System.currentTimeMillis();
        // 创建connection
        Connection connection = null;
        PreparedStatement statement = null;
        try {
            connection = ClickHouseConnection.getConnection();
            assert connection != null;
            statement = connection.prepareStatement(sql);
            ResultSet resultSet = statement.executeQuery();
            List<T> data = resultSetToList(resultSet, clazz);
            LOGGER.info("读取ck完成，耗时：{}, dataSize: {}", System.currentTimeMillis() - start, data.size());
            return data;
        } catch (SQLException e) {
            // 处理SQLException
            LOGGER.error("SQL执行出错", e);
            return new ArrayList<>();
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    LOGGER.info("关闭statement失败");
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    LOGGER.info("关闭connection失败");
                }
            }
        }
    }

    public Long count(String sql) {
        LOGGER.info("读取ck开始");
        long start = System.currentTimeMillis();
        // 创建connection
        Connection connection = null;
        PreparedStatement statement = null;
        try {
            connection = ClickHouseConnection.getConnection();
            assert connection != null;
            statement = connection.prepareStatement(sql);
            ResultSet resultSet = statement.executeQuery();
            Long count = resultSet != null && resultSet.next() ? resultSet.getLong(1) : null;
            LOGGER.info("读取ck完成，耗时：{}, count: {}", System.currentTimeMillis() - start, count);
            return count;
        } catch (SQLException e) {
            // 处理SQLException
            LOGGER.error("SQL执行出错", e);
            return null;
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    LOGGER.info("关闭statement失败");
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    LOGGER.info("关闭connection失败");
                }
            }
        }
    }

    private <T> List<T> resultSetToList(ResultSet resultSet, Class<T> clazz) throws SQLException {
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();

        List<T> buffer = new ArrayList<>();
        while (resultSet.next()) {
            JSONObject row = new JSONObject();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                Object value = resultSet.getObject(i);
                row.put(columnName, value);
            }
            buffer.add(JSON.parseObject(row.toJSONString(), clazz));
        }
        return buffer;
    }


    public void executeSqls(List<String> sqls) {
        String allSql = String.join(SEMICOLON, sqls);
        LOGGER.info("执行sql：{}", allSql);
        long start = System.currentTimeMillis();
        executeConnect(connection -> {
            sqls.forEach(sql -> executeStatement(connection, statement -> {
                try {
                    LOGGER.info("执行sql 开始：{}", sql);
                    statement.execute(sql);
                    LOGGER.info("执行sql 结束：{}", sql);
                } catch (Exception e) {
                    LOGGER.error("执行sql 异常：{}", sql, e);
                    throw new RuntimeException(e);
                }
                return null;
            }));
            return null;
        });
        LOGGER.info("执行sql {} 完成，耗时：{}", allSql, System.currentTimeMillis() - start);
    }

    private static <R> R executeStatement(Connection connection, Function<Statement, R> function) {
        R res = null;
        Statement statement = null;
        try {
            statement = connection.createStatement();
            res = function.apply(statement);
        } catch (Exception e) {
            LOGGER.error("executeStatement 异常：", e);
            throw new RuntimeException(e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (Exception e) {
                    LOGGER.info("close statement failed", e);
                }
            }
        }
        return res;
    }

    private static <R> R executeConnect(Function<Connection, R> function) {
        R res = null;
        Connection connection = null;
        try {
            connection = ClickHouseConnection.getConnection();
            res = function.apply(connection);
        } catch (Exception e) {
            LOGGER.error("executeConnect 异常：", e);
            throw new RuntimeException(e);
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (Exception e) {
                    LOGGER.info("close connection failed", e);
                }
            }
        }
        return res;
    }

    public Map<String, String> getTablePartitionKeyMap(Set<String> dbNames, Set<String> tableNames) {
        Map<String, String> partitionMap = new HashMap<>();
        String sqlTemplate = "select name, partition_key from system.tables where database in ({DB_NAME}) and name in ({TABLE_NAME}) and partition_key is not null and partition_key != ''";
        String sql = sqlTemplate.replace(DB_NAME, String.join(COMMA, dbNames))
                .replace(TABLE_NAME, String.join(COMMA, tableNames));
        long start = System.currentTimeMillis();
        executeConnect(connection -> {
            executeStatement(connection, statement -> {
                try {
                    LOGGER.info("执行sql 开始：{}", sql);
                    ResultSet resultSet = statement.executeQuery(sql);
                    LOGGER.info("执行sql 结束：{}", sql);
                    while (resultSet.next()) {
                        String name = resultSet.getString("name");
                        String partitionKey = resultSet.getString("partition_key");
                        partitionMap.put(name, partitionKey);
                    }
                } catch (Exception e) {
                    LOGGER.error("执行sql 异常：{}", sql, e);
                    throw new RuntimeException(e);
                }
                return null;
            });
            return null;
        });
        LOGGER.info("执行sql {} 完成，耗时：{}", sql, System.currentTimeMillis() - start);
        return partitionMap;
    }

}
