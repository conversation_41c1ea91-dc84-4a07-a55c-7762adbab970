package com.guwave.onedata.dataware.collectx.job;

import com.guwave.onedata.dataware.collectx.service.DataConvertService;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/5/27
 * @description BitMemConvertJob
 */
@Slf4j
@Component
public class BitMemConvertJob {

    @Autowired
    private DataConvertService bitMemConvertServiceImpl;

        @ScheduleSwitch
    @Scheduled(cron = "${spring.scheduler.timer.convertData}")
    public void schedulerWipConvert() {
        try {
            bitMemConvertServiceImpl.convertDataAsync();
        } catch (Exception e) {
            log.error(e.toString(), e);
        }
    }
}
