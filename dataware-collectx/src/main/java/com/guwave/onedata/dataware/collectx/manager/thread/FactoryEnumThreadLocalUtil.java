package com.guwave.onedata.dataware.collectx.manager.thread;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.FactoryEnum;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class FactoryEnumThreadLocalUtil {

    private static final ThreadLocal<Set<String>> FACTORY_ENUM_THREAD_LOCAL = new ThreadLocal<>();

    public static void setAllFactoy(List<FactoryEnum> factoryEnums) {
        if (CollectionUtils.isNotEmpty(factoryEnums)) {
            Set<String> factorys = factoryEnums.stream().map(FactoryEnum::getFactory).collect(Collectors.toSet());
            FACTORY_ENUM_THREAD_LOCAL.set(factorys);
        } else {
            FACTORY_ENUM_THREAD_LOCAL.set(new HashSet<>());
        }
    }

    public static boolean containFactory(String factory) {
        Set<String> factorys = FACTORY_ENUM_THREAD_LOCAL.get();
        if (factorys != null) {
            return factorys.contains(factory);
        } else {
            return false;
        }
    }

    public static void clear() {
        FACTORY_ENUM_THREAD_LOCAL.remove();
    }
}
