package com.guwave.onedata.dataware.collectx.configuration.feign;


import feign.Feign;
import feign.Request;
import feign.RequestInterceptor;
import feign.Retryer;
import feign.codec.Encoder;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

@Data
public class FeignClientFactoryBean implements FactoryBean<Object>, BeanFactoryAware {

    private Class<?> type;

    private String name;

    private String url;

    private String contextId;

    private String path;

    private boolean decode404;

    private boolean inheritParentContext = true;

    private ApplicationContext applicationContext;

    private DefaultListableBeanFactory beanFactory;

    private Class<?> fallback = void.class;

    private Class<?> fallbackFactory = void.class;

    private int readTimeoutMillis = new Request.Options().readTimeoutMillis();

    private int connectTimeoutMillis = new Request.Options().connectTimeoutMillis();

    private boolean followRedirects = new Request.Options().isFollowRedirects();

    private boolean refreshableClient = false;
    
    private Encoder encoder = new JacksonEncoder();
    
    private Set<String> interceptorNames;
        
    @Override
    public Object getObject() {
        Map<String, RequestInterceptor> interceptorMap = beanFactory.getBeansOfType(RequestInterceptor.class);
        Collection<RequestInterceptor> requestInterceptors = null != interceptorMap && CollectionUtils.isNotEmpty(interceptorNames) ? 
                interceptorMap.entrySet().stream().filter(entry -> interceptorNames.contains(entry.getKey())).map(Entry::getValue).collect(Collectors.toList()) : Collections.emptyList();
        return Feign.builder()
                .encoder(encoder)
                .decoder(new JacksonDecoder())
//                .client(new ApacheHttpClient())
                .options(new Request.Options(connectTimeoutMillis, readTimeoutMillis))
//                .retryer(new Retryer.Default(10000, 10000, 3))
                .retryer(Retryer.NEVER_RETRY)
                .requestInterceptors(requestInterceptors)
                .target(type, url);
    }


    @Override
    public Class<?> getObjectType() {
        return type;
    }
    
    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = (DefaultListableBeanFactory)beanFactory;
    }

}
