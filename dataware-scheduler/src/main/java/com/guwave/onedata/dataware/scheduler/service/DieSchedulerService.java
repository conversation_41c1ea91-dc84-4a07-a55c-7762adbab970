package com.guwave.onedata.dataware.scheduler.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import com.guwave.onedata.dataware.dao.mysql.manager.FileWarehousingRecordManager;
import com.guwave.onedata.dataware.dao.mysql.manager.SftpFileDetailManager;
import com.guwave.onedata.dataware.dao.mysql.manager.WarehousingTaskRecordManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.*;
import com.guwave.onedata.dataware.repair.common.service.DataRepairService;
import com.guwave.onedata.dataware.scheduler.util.ThreadPoolUtils;
import com.guwave.onedata.next.compute.api.iface.IComputeRpcService;
import com.guwave.onedata.next.compute.api.vo.response.ComputeResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.guwave.gdp.common.constant.Constant.MIDDLE_LINE;
import static com.guwave.onedata.dataware.common.contant.Constant.DEFAULT_TIME_FORMAT;
import static com.guwave.onedata.dataware.common.contant.Constant.SYSTEM;

@Component
public class DieSchedulerService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DieSchedulerService.class);

    private Executor executor;
    private final AtomicInteger threadActiveCount = new AtomicInteger(0);
    private final int poolCoreSize = 20;
    private final static Set<FileCategory> REQUEST_MES_FILE_CATEGORY_SET = Sets.newHashSet(FileCategory.STDF, FileCategory.RAW_DATA);


    @Value("${spring.spark.buffer.pool.allMaxRunningSize}")
    private Long allMaxRunningSize;
    @Value("${spring.module.name}")
    private String moduleName;

    @Autowired
    private LayerCalculatePoolRepository layerCalculatePoolRepository;
    @Autowired
    private ExpireDeleteRecordRepository expireDeleteRecordRepository;
    @Autowired
    private FileLoadingLogRepository fileLoadingLogRepository;
    @Autowired
    private LotWaferCalRecordRepository lotWaferCalRecordRepository;
    @Autowired
    private LotWaferCalStatusRepository lotWaferCalStatusRepository;
    @Autowired
    private SparkAppConfigRepository sparkAppConfigRepository;
    @Autowired
    private RedisService redisService;
    @Autowired
    private DataRepairService dataRepairService;
    @Autowired
    private SftpFileDetailManager sftpFileDetailManager;
    @Autowired
    private FileWarehousingRecordManager fileWarehousingRecordManager;
    @Autowired
    private WarehousingTaskRecordManager warehousingTaskRecordManager;
    @DubboReference
    private IComputeRpcService computeRpcService;
    @Autowired
    private MesRequestService mesRequestService;

    @PostConstruct
    public void init() {
        executor = ThreadPoolUtils.getNewThreadPoolExecutor(this.getClass().getName(), poolCoreSize, poolCoreSize, poolCoreSize);
    }

    @ScheduleSwitch
    @Scheduled(fixedDelayString = "${spring.scheduler.polling.milliseconds}")
    public void runDwDieTask() {
        if (threadActiveCount.get() >= poolCoreSize) {
            LOGGER.info("达到线程处理上限");
            return;
        }
        Long allProcessingTaskCnt = layerCalculatePoolRepository.countAllByRunModeInAndProcessStatusIn(Collections.singletonList(RunMode.DISTRIBUTED), Collections.singletonList(ProcessStatus.PROCESSING));
        long canSubmitTaskCnt = allMaxRunningSize - allProcessingTaskCnt;
        if (canSubmitTaskCnt <= 0) {
            LOGGER.info("运行任务达到总上限！");
            return;
        }

        int querySize = (int) Math.min(canSubmitTaskCnt, poolCoreSize - threadActiveCount.get());
        List<LayerCalculatePool> layerCalculatePools = layerCalculatePoolRepository.findAllByDwLayerAndProcessStatusAndRunModeOrderByPriorityDescUpdateTimeAsc(
                DwLayer.DWD, ProcessStatus.CREATE, RunMode.DISTRIBUTED, Pageable.ofSize(querySize)
        );

        layerCalculatePools.forEach(task -> {
            threadActiveCount.incrementAndGet();
            executor.execute(() -> {
                        try {
                            submitTask(task);
                        } finally {
                            threadActiveCount.decrementAndGet();
                        }
                    }
            );
        });

    }

    public void submitTask(LayerCalculatePool task) {
        String taskJsonStr = JSON.toJSONString(task);
        boolean lockDwDieTask = redisService.lockDwDieTask(
                task.getFileCategory() == FileCategory.BIT_MEM ? Lists.newArrayList(FileCategory.BIT_MEM) : Lists.newArrayList(FileCategory.OTHER),
                task.getCustomer(), task.getTestArea(), task.getFactory(), task.getDeviceId(), task.getLotType(), task.getTestStage(), task.getLotId(), task.getWaferNo(),
                RedisService.lockDwDieValue
        );
        if (!lockDwDieTask) {
            LOGGER.info("{} 存在同维度的DwDie任务正在运行", taskJsonStr);
            layerCalculatePoolRepository.updateUpdateTime(task.getId(), new Date());
            return;
        }

        boolean submitSuccess = false;
        try {
            if (ProcessStatus.CREATE != layerCalculatePoolRepository.findById(task.getId()).get().getProcessStatus()) {
                LOGGER.info("{} 已被处理", taskJsonStr);
                return;
            }
            LOGGER.info("{} 开始处理", taskJsonStr);

            // 查询文件信息
            SftpFileDetailManager.RunTaskFilesContext runTaskFilesInfo = sftpFileDetailManager.getRunTaskFilesInfo(task, true);
            task
                    .setCalculateDwTestItem(runTaskFilesInfo.getCalculateDwTestItem())
                    .setProcessStatus(ProcessStatus.PROCESSING)
                    .setDataVersion(System.currentTimeMillis())
                    .setUpdateTime(new Date())
                    .setUniqueId(null)
                    .setExceptionType(null)
                    .setExceptionMessage(null)
                    .setErrorMessage(null)
                    .setAppId(null)
                    .setVersion(null)
                    .setSinkType(null)
                    .setCleanupTaskIds(runTaskFilesInfo.getCleanupTaskIdList());
            if (task.getUseCustomSetting() == 0) {
                task
                        .setNumExecutors(null)
                        .setExecutorCores(null)
                        .setExecutorMemory(null)
                        .setDriverMemory(null)
                        .setParallelism(null)
                        .setTestItemDetailResultPartition(null)
                        .setExtraConf(null);
            }
            LOGGER.info("查询文件信息结束");

            RLock updateDwTaskTableLock = redisService.getUpdateDwTaskTableLock(task.getFileCategory(), task.getCustomer(), task.getTestArea(), task.getFactory(), task.getDeviceId(), task.getLotType(), task.getTestStage(), task.getLotId(), task.getWaferNo());
            updateDwTaskTableLock.lock();
            try {
                // 更新lotWaferCalStatus
                LOGGER.info("更新lotWaferCalStatus");
                saveLotWaferCalStatus(task, runTaskFilesInfo);
                // 更新lotWaferCalRecord
                LOGGER.info("更新lotWaferCalRecord");
                saveLotWaferCalRecord(runTaskFilesInfo);
                // 记录fileLoadingLog 和 fileWarehousingRecord
                LOGGER.info("记录fileLoadingLog 和 fileWarehousingRecord");
                deleteAndUpdateFileLoadingLogs(runTaskFilesInfo.getFileNames());
                // 更新warehousingTaskRecord
                LOGGER.info("更新warehousingTaskRecord");
                warehousingTaskRecordManager.updateWarehousingTaskRecord(task);
                // 更新LotWaferWarehousingRecord
                LOGGER.info("更新LotWaferWarehousingRecord");
                dataRepairService.saveNormalLotWaferWarehousingRecord(task, ProcessStatus.PROCESSING, runTaskFilesInfo.getFileIds(), moduleName);
                // 请求mes信息
                requestMes(task, runTaskFilesInfo);
            } finally {
                updateDwTaskTableLock.unlock();
            }

            try {
                // 更新layerCalculatePool
                LOGGER.info("保存layerCalculatePool: {}", JSON.toJSONString(task));
                layerCalculatePoolRepository.save(task);

                SparkAppConfig config = sparkAppConfigRepository.findByFileCategoryAndUploadTypeAndTestAreaAndDwLayer(
                        task.getFileCategory(),
                        UploadType.AUTO,
                        TestArea.of(task.getTestArea().getTestScope()),
                        task.getDwLayer()
                );
                Map<String, String> params = new HashMap<String, String>() {{
                    put("customer", task.getCustomer());
                    put("subCustomer", task.getSubCustomer());
                    put("factory", task.getFactory());
                    put("factorySite", task.getFactorySite());
                    put("testArea", task.getTestArea().getArea());
                    put("deviceId", task.getDeviceId());
                    put("lotId", task.getLotId());
                    put("waferNo", task.getWaferNo());
                    put("lotType", task.getLotType().getType());
                    put("testStage", task.getTestStage());
                    put("fileCategory", task.getFileCategory().getCategory());
                    put("uploadTime", runTaskFilesInfo.getUploadTime().toString());
                    put("dataVersion", task.getDataVersion().toString());
                    put("executeMode", ExecuteMode.AUTO.getMode());
                    put("dieCount", runTaskFilesInfo.getDieCnt().toString());
                    put("newDataFlag", String.valueOf(true));
                }};
                fillTaskParams(params, task, runTaskFilesInfo);
                ComputeResponse computeResponse = computeRpcService.submit(config.getMainClass(), generateAppName(config.getMainClass(), task), runTaskFilesInfo.getDieCnt(), runTaskFilesInfo.getDieCnt(), params);
                if (!ComputeResponse.SUCCESS.equals(computeResponse.getCode())) {
                    throw new RuntimeException(JSON.toJSONString(computeResponse));
                }
                task.setUniqueId(computeResponse.getUniqueId()).setUpdateTime(new Date());
                layerCalculatePoolRepository.save(task);
                submitSuccess = true;
            } catch (Exception e) {
                LOGGER.error("{} 提交spark任务异常：", taskJsonStr, e);
                // 改成 CREATE，等待重试
                task.setUniqueId(null).setProcessStatus(ProcessStatus.CREATE).setUpdateTime(new Date());
                layerCalculatePoolRepository.save(task);
            }
        } finally {
            if (!submitSuccess) {
                // 未运行任务立马解锁
                redisService.unlockDwDieTask(
                        task.getFileCategory() == FileCategory.BIT_MEM ? Lists.newArrayList(FileCategory.BIT_MEM) : Lists.newArrayList(FileCategory.OTHER),
                        task.getCustomer(), task.getTestArea(), task.getFactory(), task.getDeviceId(), task.getLotType(), task.getTestStage(), task.getLotId(), task.getWaferNo(),
                        RedisService.lockDwDieValue
                );
            } else {
                // 任务结束后才解锁
            }
        }
    }

    private void fillTaskParams(Map<String, String> params, LayerCalculatePool task, SftpFileDetailManager.RunTaskFilesContext runTaskFilesInfo) {

    }

    private void requestMes(LayerCalculatePool task, SftpFileDetailManager.RunTaskFilesContext runTaskFilesInfo) {
        if (TestArea.of(task.getTestArea().getTestScope()) == TestArea.FT && REQUEST_MES_FILE_CATEGORY_SET.contains(task.getFileCategory())) {
            try {
                // 请求接口
                mesRequestService.requestMes(task, runTaskFilesInfo);
            } catch (Exception e) {
                LOGGER.info("requestMes 异常：", e);
            }
        }
    }

    private void saveLotWaferCalStatus(LayerCalculatePool task, SftpFileDetailManager.RunTaskFilesContext runTaskFilesInfo) {
        Date date = new Date();
        LotWaferCalStatus lotWaferCalStatus = lotWaferCalStatusRepository.findFirstByCustomerAndTestAreaAndFileCategoryAndFactoryAndDeviceIdAndLotIdAndWaferNoAndTestStageAndLotTypeOrderByUpdateTimeDesc(
                task.getCustomer(), task.getTestArea(), task.getFileCategory(), task.getFactory(), task.getDeviceId(), task.getLotId(), task.getWaferNo(), task.getTestStage(), task.getLotType()
        );
        if (lotWaferCalStatus == null) {
            lotWaferCalStatus = new LotWaferCalStatus()
                    .setCreateTime(date)
                    .setCreateUser(SYSTEM);
        }
        lotWaferCalStatus
                .setCustomer(task.getCustomer())
                .setSubCustomer(task.getSubCustomer())
                .setFactory(task.getFactory())
                .setFactorySite(task.getFactorySite())
                .setTestArea(task.getTestArea())
                .setLotType(task.getLotType())
                .setDeviceId(task.getDeviceId())
                .setLotId(task.getLotId())
                .setWaferNo(task.getWaferNo())
                .setTestStage(task.getTestStage())
                .setFileCategory(task.getFileCategory())
                .setDieTotalCnt(runTaskFilesInfo.getDieCnt())
                .setTestItemTotalCnt(runTaskFilesInfo.getTestItemCnt())
                .setWarehousingMode(task.getWarehousingMode())
                .setRepairLotWaferId(task.getRepairLotWaferId())
                .setCleanupTaskIds(task.getCleanupTaskIds())
                .setDataVersion(task.getDataVersion())
                .setCalculateDwTestItem(runTaskFilesInfo.getCalculateDwTestItem())
                .setCalculateYmsTestItem(runTaskFilesInfo.getCalculateYmsTestItem())
                .setDieStatus(ProcessStatus.PROCESSING)
                .setDieStartTime(date)
                .setDieEndTime(null)
                .setDieCalTime(null)
                .setTestItemStatus(null)
                .setTestItemStartTime(null)
                .setTestItemEndTime(null)
                .setTestItemCalTime(null)
                .setUpdateTime(date)
                .setUpdateUser(SYSTEM);

        lotWaferCalStatusRepository.save(lotWaferCalStatus);
    }

    private void saveLotWaferCalRecord(SftpFileDetailManager.RunTaskFilesContext runTaskFilesInfo) {
        Date updateTime = new Date();
        LayerCalculatePool task = runTaskFilesInfo.getLayerCalculatePool();
        LotWaferCalRecord oldLotWaferCalRecord = lotWaferCalRecordRepository.findFirstByCustomerAndFactoryAndTestAreaAndFileCategoryAndDeviceIdAndLotIdAndWaferNoAndTestStageAndLotTypeAndLatestFlagOrderByUpdateTimeDesc(
                task.getCustomer(), task.getFactory(), task.getTestArea(), task.getFileCategory(), task.getDeviceId(), task.getLotId(), task.getWaferNo(), task.getTestStage(), task.getLotType(), 1
        );

        List<LotWaferCalRecord> lotWaferCalRecords = new ArrayList<>();
        if (oldLotWaferCalRecord == null || oldLotWaferCalRecord.getDieStatus() != ProcessStatus.PROCESSING) {
            int repairFlag = runTaskFilesInfo.getLotStockingDetails().stream().anyMatch(t -> Objects.equals(t.getDieFinishFlag(), 0) && t.getRepairLotWaferId() != null) ? 1 : 0;
            int replayFlag = runTaskFilesInfo.getLotStockingDetails().stream().anyMatch(t -> Objects.equals(t.getDieFinishFlag(), 0) && StringUtils.isNotBlank(t.getCleanupTaskIds())) ? 1 : 0;

            List<FileLoadingLog> allFileLoadingLogs = runTaskFilesInfo.getSourceFileLoadingMap().values().stream().flatMap(t -> t.values().stream()).collect(Collectors.toList());
            allFileLoadingLogs.addAll(runTaskFilesInfo.getFileLoadingMap().values().stream().flatMap(t -> t.values().stream()).collect(Collectors.toList()));
            List<Date> allStepStartTimes = allFileLoadingLogs.stream().map(FileLoadingLog::getStepStartTime).filter(Objects::nonNull).sorted().collect(Collectors.toList());

            List<FileLoadingLog> stepDownLoadFileLoadingLogs = new ArrayList<>(runTaskFilesInfo.getSourceFileLoadingMap().getOrDefault(StepType.STEP_TYPE_1000.getStep(), new HashMap<>()).values());
            stepDownLoadFileLoadingLogs.addAll(runTaskFilesInfo.getFileLoadingMap().getOrDefault(StepType.STEP_TYPE_1000.getStep(), new HashMap<>()).values());

            List<Date> allFtpTimes = stepDownLoadFileLoadingLogs.stream().map(FileLoadingLog::getRemoteFileMtime).filter(Objects::nonNull).sorted().collect(Collectors.toList());

            List<Pair<Date, Date>> fileDownLoadTimes = stepDownLoadFileLoadingLogs.stream().filter(t -> t.getStepStartTime() != null && t.getStepEndTime() != null)
                    .map(t -> Pair.of(t.getStepStartTime(), t.getStepEndTime())).sorted(Map.Entry.comparingByKey()).collect(Collectors.toList());

            List<Pair<Date, Date>> fileConvertTimes = runTaskFilesInfo.getSourceFileLoadingMap().values().stream().flatMap(t -> t.values().stream())
                    .filter(t -> !Objects.equals(t.getStep(), StepType.STEP_TYPE_1000.getStep()) && t.getStepStartTime() != null && t.getStepEndTime() != null)
                    .map(t -> Pair.of(t.getStepStartTime(), t.getStepEndTime())).sorted(Map.Entry.comparingByKey()).collect(Collectors.toList());

            List<Pair<Date, Date>> filePreParseTimes = runTaskFilesInfo.getFileLoadingMap().getOrDefault(StepType.STEP_TYPE_3100.getStep(), new HashMap<>())
                    .values().stream().filter(t -> t.getStepStartTime() != null && t.getStepEndTime() != null)
                    .map(t -> Pair.of(t.getStepStartTime(), t.getStepEndTime())).sorted(Map.Entry.comparingByKey()).collect(Collectors.toList());

            List<Pair<Date, Date>> fileParseTimes = runTaskFilesInfo.getFileLoadingMap().getOrDefault(StepType.STEP_TYPE_4100.getStep(), new HashMap<>())
                    .values().stream().filter(t -> t.getStepStartTime() != null && t.getStepEndTime() != null)
                    .map(t -> Pair.of(t.getStepStartTime(), t.getStepEndTime())).sorted(Map.Entry.comparingByKey()).collect(Collectors.toList());


            LotWaferCalRecord newLotWaferCalRecord = new LotWaferCalRecord()
                    .setCustomer(task.getCustomer())
                    .setSubCustomer(task.getSubCustomer())
                    .setFactory(task.getFactory())
                    .setFactorySite(task.getFactorySite())
                    .setTestArea(task.getTestArea())
                    .setLotType(task.getLotType())
                    .setDeviceId(task.getDeviceId())
                    .setLotId(task.getLotId())
                    .setWaferNo(task.getWaferNo())
                    .setTestStage(task.getTestStage())
                    .setFileCategory(task.getFileCategory())
                    .setLatestFlag(1)
                    .setCalCnt((oldLotWaferCalRecord == null ? 0 : oldLotWaferCalRecord.getCalCnt()) + 1)
                    .setRepairCnt((oldLotWaferCalRecord == null ? 0 : oldLotWaferCalRecord.getRepairCnt()) + repairFlag)
                    .setReplayCnt((oldLotWaferCalRecord == null ? 0 : oldLotWaferCalRecord.getReplayCnt()) + replayFlag)
                    .setFileCnt(runTaskFilesInfo.getFileNames().size())
                    .setFileTotalSize(runTaskFilesInfo.getSftpFileDetails().stream().mapToLong(SftpFileDetail::getOriginFileSize).sum())
                    .setCalculateDwTestItem(runTaskFilesInfo.getCalculateDwTestItem())
                    .setCalculateYmsTestItem(runTaskFilesInfo.getCalculateYmsTestItem())
                    .setFirstStartTime(allStepStartTimes.stream().findFirst().orElse(null))
                    .setMinFtpMtime(allFtpTimes.stream().findFirst().orElse(null))
                    .setMaxFtpMtime(CollectionUtils.isEmpty(allFtpTimes) ? null : allFtpTimes.get(allFtpTimes.size() - 1))
                    .setFileFtpMtimes(allFtpTimes.stream().map(t -> DateFormatUtils.format(t, DEFAULT_TIME_FORMAT)).collect(Collectors.joining(Constant.COMMA)))
                    .setFileDownloadTimes(fileDownLoadTimes.stream().map(t -> DateFormatUtils.format(t.getLeft(), DEFAULT_TIME_FORMAT) + Constant.WAVY_LINE + DateFormatUtils.format(t.getRight(), DEFAULT_TIME_FORMAT)).collect(Collectors.joining(Constant.COMMA)))
                    .setFileDownloadConsume(fileDownLoadTimes.stream().map(t -> (t.getRight().getTime() - t.getLeft().getTime()) / 1000L + Constant.EMPTY).collect(Collectors.joining(Constant.COMMA)))
                    .setFileDownloadTotalConsume(fileDownLoadTimes.stream().mapToLong(t -> (t.getRight().getTime() - t.getLeft().getTime()) / 1000L).sum())
                    .setFileConvertTimes(fileConvertTimes.stream().map(t -> DateFormatUtils.format(t.getLeft(), DEFAULT_TIME_FORMAT) + Constant.WAVY_LINE + DateFormatUtils.format(t.getRight(), DEFAULT_TIME_FORMAT)).collect(Collectors.joining(Constant.COMMA)))
                    .setFileConvertConsume(fileConvertTimes.stream().map(t -> (t.getRight().getTime() - t.getLeft().getTime()) / 1000L + Constant.EMPTY).collect(Collectors.joining(Constant.COMMA)))
                    .setFileConvertTotalConsume(fileConvertTimes.stream().mapToLong(t -> (t.getRight().getTime() - t.getLeft().getTime()) / 1000L).sum())
                    .setFilePreParseTimes(filePreParseTimes.stream().map(t -> DateFormatUtils.format(t.getLeft(), DEFAULT_TIME_FORMAT) + Constant.WAVY_LINE + DateFormatUtils.format(t.getRight(), DEFAULT_TIME_FORMAT)).collect(Collectors.joining(Constant.COMMA)))
                    .setFilePreParseConsume(filePreParseTimes.stream().map(t -> (t.getRight().getTime() - t.getLeft().getTime()) / 1000L + Constant.EMPTY).collect(Collectors.joining(Constant.COMMA)))
                    .setFilePreParseTotalConsume(filePreParseTimes.stream().mapToLong(t -> (t.getRight().getTime() - t.getLeft().getTime()) / 1000L).sum())
                    .setFileParseTimes(fileParseTimes.stream().map(t -> DateFormatUtils.format(t.getLeft(), DEFAULT_TIME_FORMAT) + Constant.WAVY_LINE + DateFormatUtils.format(t.getRight(), DEFAULT_TIME_FORMAT)).collect(Collectors.joining(Constant.COMMA)))
                    .setFileParseConsume(fileParseTimes.stream().map(t -> (t.getRight().getTime() - t.getLeft().getTime()) / 1000L + Constant.EMPTY).collect(Collectors.joining(Constant.COMMA)))
                    .setFileParseTotalConsume(fileParseTimes.stream().mapToLong(t -> (t.getRight().getTime() - t.getLeft().getTime()) / 1000L).sum())
                    .setDieStatus(ProcessStatus.PROCESSING)
                    .setDieTotalCnt(runTaskFilesInfo.getDieCnt())
                    .setRuleStatus(ProcessStatus.PROCESSING)
                    .setTestItemTotalCnt(runTaskFilesInfo.getTestItemCnt())
                    .setCreateTime(updateTime)
                    .setCreateUser(SYSTEM)
                    .setUpdateTime(updateTime)
                    .setUpdateUser(SYSTEM);

            lotWaferCalRecords.add(newLotWaferCalRecord);
            if (oldLotWaferCalRecord != null) {
                oldLotWaferCalRecord.setLatestFlag(0);
            }
        }

        if (oldLotWaferCalRecord != null) {
            oldLotWaferCalRecord
                    .setUpdateTime(updateTime)
                    .setUpdateUser(SYSTEM);
            lotWaferCalRecords.add(oldLotWaferCalRecord);
        }

        lotWaferCalRecordRepository.saveAll(lotWaferCalRecords);
    }

    /**
     * 在loading表中，删除此阶段往后的数据，更新此阶段的数据
     */
    public void deleteAndUpdateFileLoadingLogs(List<String> fileNames) {
        Date date = new Date();
        // 删除loading表中所有大于等于该阶段的数据
        try {
            fileLoadingLogRepository.deleteAllByFileNameInAndStepGreaterThanEqual(fileNames, StepType.STEP_TYPE_5100.getStep());
        } catch (Exception e) {
            LOGGER.info("5100阶段及往后阶段不存在数据\n" + e);
        }

        // 获取上阶段的数据
        List<FileLoadingLog> fileLoadingLogs = fileLoadingLogRepository.findAllByFileNameInAndStep(fileNames, StepType.STEP_TYPE_4100.getStep());

        // 构造该阶段的数据
        List<FileLoadingLog> stageFileLoadingLogs = fileLoadingLogs.stream().map(fileLoadingLog -> {
            FileLoadingLog subFileLoadingLog = new FileLoadingLog();
            BeanUtils.copyProperties(fileLoadingLog, subFileLoadingLog);
            subFileLoadingLog
                    .setId(null)
                    .setStep(StepType.STEP_TYPE_5100.getStep())
                    .setStepStartTime(date)
                    .setStepEndTime(null)
                    .setProcessStatus(ProcessStatus.PROCESSING)
                    .setFailedType(null)
                    .setFailedFields(null)
                    .setExceptionType(null)
                    .setExceptionMessage(null)
                    .setErrorMessage(null)
                    .setCreateTime(date)
                    .setUpdateTime(date)
                    .setCreateUser(SYSTEM)
                    .setUpdateUser(SYSTEM);
            return subFileLoadingLog;
        }).collect(Collectors.toList());

        fileLoadingLogRepository.saveAll(stageFileLoadingLogs);
        fileWarehousingRecordManager.updateFileWarehousingStatus(stageFileLoadingLogs);
    }

    public String generateAppName(String prefix, LayerCalculatePool task) {
        StringBuilder builder = new StringBuilder(prefix + MIDDLE_LINE)
                .append(task.getFileCategory().getCategory())
                .append("/TEST_AREA=").append(task.getTestArea().getArea())
                .append("/CUSTOMER=").append(task.getCustomer())
                .append("/FACTORY=").append(task.getFactory())
                .append("/DEVICE_ID=").append(task.getDeviceId())
                .append("/LOT_ID=").append(task.getLotId());
        if (StringUtils.isNotBlank(task.getWaferNo())) {
            builder.append("/WAFER_NO=").append(task.getWaferNo());
        }
        builder
                .append("/TEST_STAGE=").append(task.getTestStage())
                .append("/LOT_TYPE=").append(task.getLotType().getType());
        return builder.toString();
    }

}
