package com.guwave.onedata.dataware.source.agent.common.handler;

import com.guwave.onedata.dataware.common.contant.DataClearRuleType;
import com.guwave.onedata.dataware.common.contant.StdfFieldType;
import com.guwave.onedata.dataware.common.contant.TesterType;
import com.guwave.onedata.dataware.common.exception.WrongValidHeadException;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotMetaDataDetail;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.DtrParseRuleRepository;
import com.guwave.onedata.dataware.parser.stdf.model.BinDefinition;
import com.guwave.onedata.dataware.parser.stdf.model.ClearRule;
import com.guwave.onedata.dataware.parser.stdf.util.StdfMultiThreadParseUtil;
import com.guwave.onedata.dataware.parser.stdf.visitor.Visitor;
import com.guwave.onedata.dataware.source.agent.common.visitor.MultiThreadVisitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface StdfDataParseHandler extends DataParseHandler {
    Logger LOGGER = LoggerFactory.getLogger(StdfDataParseHandler.class);

    default List<Visitor> parseAndSendData(LotMetaDataDetail lotMetaDataDetail, String localStdfPath) {
        Map<StdfFieldType, List<ClearRule>> rules = getDataClearRule(lotMetaDataDetail.getCustomer(), lotMetaDataDetail.getFactory(), lotMetaDataDetail.getTestArea(), lotMetaDataDetail.getDeviceId());
        List<BinDefinition> binDefinitions = getBinDefinitions(lotMetaDataDetail);
        Map<String, String> customerSettingMap = getCustomerSettingMap(lotMetaDataDetail);
        // 解析stdf
        File localFile = new File(localStdfPath);
        int allThreadCnt = StdfMultiThreadParseUtil.calculateThredCnt(localFile);
        List<Visitor> visitors;
        try {
            visitors = parseStdfFile(lotMetaDataDetail, rules, localFile, allThreadCnt, binDefinitions, customerSettingMap);
        } catch (WrongValidHeadException wrongValidHeadException) {
            LOGGER.info("{} 分 {} 片解析,head校验不正确,尝试使用1个线程解析", localStdfPath, allThreadCnt, wrongValidHeadException);
            allThreadCnt = 1;
            visitors = parseStdfFile(lotMetaDataDetail, rules, localFile, allThreadCnt, binDefinitions, customerSettingMap);
        }
        return visitors;
    }

    default List<Visitor> parseStdfFile(LotMetaDataDetail lotMetaDataDetail, Map<StdfFieldType, List<ClearRule>> rules, File localFile, int allThreadCnt, List<BinDefinition> binDefinitions, Map<String, String> customerSettingMap) {
        List<Visitor> visitors = new ArrayList<>();
        for (int i = 1; i <= allThreadCnt; i++) {
            visitors.add(new MultiThreadVisitor(i, getHdfsUtil(), getOdsHdfsTemplatePath(), localFile, generateFillFileMainDataConsumer(lotMetaDataDetail, binDefinitions), allThreadCnt, getBatchSize(), getCkSinkMap(), getEcidRuleRepository(), getUidRuleRepository(), getLotRelationSyncRepository(), null, null, null));
        }

        try {
            StdfMultiThreadParseUtil.MetaData multiThreadMetaData = new StdfMultiThreadParseUtil.MetaData()
                    .setFile(localFile)
                    .setVisitors(visitors)
                    .setTestAreaFunction((mir, prrs) -> lotMetaDataDetail.getTestArea())
                    .setNeedMergeTestTxt(TesterType.J750.getType().equals(lotMetaDataDetail.getTesterType()) && rules.getOrDefault(StdfFieldType.PTR, new ArrayList<>()).stream().anyMatch(t -> t.getRuleType() == DataClearRuleType.INTERSECTION))
                    .setNotNeedLoopTestTxt(false)
                    .setSiteNumFilterFlag(getSiteNumFilterFlag())
                    .setClearRules(rules)
                    .setMaxTextDatSize(getMaxTextDatSize())
                    .setIgnoreDamagedTouchdownFlag(getIgnoreDamagedTouchdown(lotMetaDataDetail))
                    .setSmt8TestTxtRule(getSmt8TestTxtRule());
            StdfMultiThreadParseUtil.parse(multiThreadMetaData);
        } finally {
            visitors.forEach(t -> {
                MultiThreadVisitor visitor = (MultiThreadVisitor) t;
                visitor.close();
            });
        }

        return visitors;
    }

    Boolean getSiteNumFilterFlag();

    Boolean getIgnoreDamagedTouchdown(LotMetaDataDetail lotMetaDataDetail);

    String getSmt8TestTxtRule();

    Integer getMaxTextDatSize();

    DtrParseRuleRepository getDtrParseRuleRepository();
}
