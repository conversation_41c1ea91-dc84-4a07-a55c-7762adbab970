//package com.guwave.onedata.dataware.scheduler.util;
//
//import org.apache.hadoop.fs.FileSystem;
//import org.apache.hadoop.fs.Path;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
//import java.io.*;
//
///**
// * Copyright (C), 2021, guwave
// * <p>
// * HdfsUtil
// *
// * <AUTHOR>
// * @version 0.0.1
// * 2021-10-26 10:17:18
// */
//@Component
//public class HdfsUtil {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(HdfsUtil.class);
//
//    private final FileSystem fileSystem;
//
//    public HdfsUtil(FileSystem fileSystem) {
//        this.fileSystem = fileSystem;
//    }
//
//    public boolean isPathExists(String hdfsPath){
//        try {
//            Path path = new Path(hdfsPath);
//            return fileSystem.exists(path);
//        } catch (Exception e) {
//            LOGGER.info("查询hdfs文件是否存在异常：",e);
//            return false;
//        }
//    }
//}
