package com.guwave.onedata.dataware.source.agent.manual.listener;

import com.guwave.onedata.dataware.source.agent.manual.service.ManualMessageService;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Component
public class KafkaConsumer {

    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaConsumer.class);

    @Autowired
    private ManualMessageService manualMessageService;

    @KafkaListener(topics = "${spring.kafka.manualTopic}")
    public void consumer(ConsumerRecord<byte[], byte[]> record) {
        String jsonStr = new String(record.value());
        try {
            LOGGER.info("开始处理kafka数据, topic: {}, message: {}", record.topic(), jsonStr);
            manualMessageService.distributeManualMessage(jsonStr);
            LOGGER.info("处理kafka数据结束, topic: {}, message: {}", record.topic(), jsonStr);
        } catch (Exception e) {
            LOGGER.error("处理kafka数据异常, topic: {}, message: {}", record.topic(), jsonStr, e);
        }
    }
}
