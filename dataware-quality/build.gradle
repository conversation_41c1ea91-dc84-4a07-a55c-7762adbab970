plugins {
  id 'application'
}

description = 'dataware quality'

dependencies {
  implementation project(':dataware-common')
  api project(':dataware-dao:dataware-dao-ck')
  implementation project(':dataware-dao:dataware-dao-mysql')
  implementation enforcedPlatform("org.springframework.boot:spring-boot-dependencies:$springBootVersion")

  implementation group: 'org.springframework.boot', name: 'spring-boot-starter'

  implementation group: 'com.alibaba', name: 'fastjson', version: fastJsonVersion
  implementation group: 'commons-collections', name: 'commons-collections', version: commonsCollectionsVersion

  implementation group: 'mysql', name: 'mysql-connector-java', version: mysqlVersion

  testImplementation group: 'org.springframework.boot', name: 'spring-boot-starter-test'
  implementation group: 'org.apache.commons', name: 'commons-lang3', version: commonsLang3Version

  implementation group: 'org.apache.dubbo', name: 'dubbo-spring-boot-starter', version: dubboVersion
  implementation group: 'org.apache.curator', name: 'curator-framework', version: curatorVersion
  implementation group: 'org.apache.curator', name: 'curator-x-discovery-server', version: curatorVersion

  implementation project(':dataware-bridge:dataware-bridge-api')

  implementation group: 'com.guwave.onedata', name: 'next-compute-api', version: nextComputeVersion
  implementation group: 'com.guwave.bigbrother', name: 'skyeye-driver-logback', version: skyeyeVersion
}

configurations {
  compile.exclude group: 'log4j', module: 'log4j'
  compile.exclude group: 'org.hibernate.validator', module: 'hibernate-validator'
  compile.exclude group: 'org.apache.tomcat.embed', module: 'tomcat-embed-websocket'
  compile.exclude group: 'org.slf4j', module: 'slf4j-log4j12'
}

compileJava {
  options.compilerArgs = ["-parameters"]
}

jar {
  enabled true
  manifest.attributes 'Main-Class': 'com.guwave.onedata.dataware.quality.Application'
}

application {
  mainClassName = 'com.guwave.onedata.dataware.quality.Application'
  applicationDistribution.from('src/main/resources/properties').into('properties')
}

distributions {
  main {
    contents {
      from('src/main/resources/shell/startup.sh') {
        into '.'
      }
    }
  }
}

startScripts {
  doLast {
    unixScript.text = unixScript.text.replaceAll(":\\\$APP_HOME/lib/(.*)\n", ":\\\$APP_HOME/lib/\\*\n")
  }
}
