package com.guwave.onedata.dataware.source.agent.common.util;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileType;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2022, guwave
 * <p>
 * FileUtil
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2022-03-02 12:00:14
 */
public class FileUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileUtil.class);

    /**
     * 读取文件
     *
     * @param file 文件路径
     * @return List
     */
    public static List<String> read(String file) {
        List<String> lines = new ArrayList<>();
        BufferedReader br = null;
        try {
            br = new BufferedReader(new FileReader(file));
            String line;
            while ((line = br.readLine()) != null) {
                lines.add(line);
            }
        } catch (FileNotFoundException e) {
            LOGGER.info("文件不存在", e);
        } catch (IOException e) {
            LOGGER.info("读文件报错", e);
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    LOGGER.info("关闭资源报错 ", e);
                }
            }
        }
        return lines;
    }

    public static String removeFileSuffix(String fileName, FileType fileType) {
        String suffix = Constant.POINT + fileType.getType();
        if (fileName.endsWith(suffix)) {
            fileName = fileName.substring(0, fileName.length() - suffix.length());
        }
        return fileName;
    }

    public static void moveFile(String sourcePath, String targetPath) throws Exception {
        org.apache.commons.io.FileUtils.deleteQuietly(new File(targetPath));
        org.apache.commons.io.FileUtils.moveFile(new File(sourcePath), new File(targetPath));
    }

    public static void mkdir(String targetPath) throws IOException {
        File file = new File(targetPath);
        if (!file.exists() || !file.isDirectory()) {
            FileUtils.forceMkdir(file);
        }
    }

    public static void copyFile(String targetPath, String sourcePath) throws IOException {
        org.apache.commons.io.FileUtils.copyFile(new File(targetPath), new File(sourcePath));
    }

    public static void deleteFile(String filePath, Boolean recursive) {
        File file = new File(filePath);
        if (file.isDirectory() && !recursive) {
            throw new RuntimeException("delete {} failed, it is a directory and recursive is false");
        }
        org.apache.commons.io.FileUtils.deleteQuietly(file);
    }
}
