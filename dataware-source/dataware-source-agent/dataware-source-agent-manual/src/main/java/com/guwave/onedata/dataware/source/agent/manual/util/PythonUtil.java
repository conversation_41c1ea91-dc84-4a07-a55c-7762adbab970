package com.guwave.onedata.dataware.source.agent.manual.util;

import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.math3.util.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.FutureTask;
import java.util.function.Consumer;
import java.util.function.Function;


@Component
public class PythonUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(PythonUtil.class);

    private static String pythonPath = null;

    @Value("${spring.adapter.python.install.path}")
    private String python;


    @PostConstruct
    public void init() {
        pythonPath = python;
    }

    /**
     * 执行python adapter脚本
     *
     * @param pyPath 脚本路径
     * @param args   脚本传入参数
     */
    public static <T> T executePython(String pyPath, List<String> args, Consumer<String> logConsumer, Function<List<String>, T> resultFunction) throws Exception {
        T result = null;
        StringBuilder errorStringBuilder = new StringBuilder();

        args.add(0, pyPath);
        args.add(0, pythonPath);
        LOGGER.info("开始执行脚本: {}", args);
        StopWatch sw = new StopWatch();
        sw.start();
        Process proc = null;
        try {
            proc = Runtime.getRuntime().exec(args.toArray(new String[0]));

            Process finalProc = proc;
            FutureTask<List<String>> inputTask = new FutureTask<>(() -> readInput(args, finalProc, logConsumer));
            FutureTask<List<String>> errorTask = new FutureTask<>(() -> readError(args, finalProc, logConsumer));
            new Thread(inputTask).start();
            new Thread(errorTask).start();
            List<String> inputList = inputTask.get();
            List<String> errorList = errorTask.get();

            if (resultFunction != null) {
                result = resultFunction.apply(inputList);
            }

            if (result == null) {
                errorList.forEach(errorLine -> errorStringBuilder.append(errorLine).append("\n"));
                LOGGER.error("执行脚本 {} 转换失败", args);
                errorStringBuilder.insert(0, " 转换失败！\n").insert(0, args).insert(0, "执行脚本 : ");
                String errorMessage = errorStringBuilder.toString();
                throw new FileLoadException(FileLoadExceptionInfo.PYTHON_SCRIPT_EXECUTE_FAIL_EXCEPTION, errorMessage, null).updateExceptionMessage(errorMessage);
            }

            proc.waitFor();
        } catch (Exception e) {
            LOGGER.error("执行脚本 {} 异常 ", args, e);
            throw e;
        } finally {
            if (proc != null) {
                try {
                    proc.getInputStream().close();
                } catch (Exception ignored) {
                }
                try {
                    proc.getErrorStream().close();
                } catch (Exception ignored) {
                }
                try {
                    proc.getOutputStream().close();
                } catch (Exception ignored) {
                }
            }
            sw.stop();
            LOGGER.info("执行 {} 耗时: {}ms", args, sw.getTotalTimeMillis());
        }
        LOGGER.info("执行脚本 {} 转换成功", args);
        return result;
    }

    private static List<String> readInput(List<String> args, Process proc, Consumer<String> logConsumer) {
        try (InputStream inputStream = proc.getInputStream();
             InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            return read(args, bufferedReader, logConsumer);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static List<String> readError(List<String> args, Process proc, Consumer<String> logConsumer) {
        try (InputStream errorStream = proc.getErrorStream();
             InputStreamReader inputStreamReader = new InputStreamReader(errorStream, StandardCharsets.UTF_8);
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            return read(args, bufferedReader, logConsumer);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static List<String> read(List<String> args, BufferedReader reader, Consumer<String> logConsumer) throws Exception {
        List<String> resultList = Lists.newArrayList();
        String res;
        while ((res = reader.readLine()) != null) {
            LOGGER.info("执行脚本 {} 返回: {}", args, res);
            resultList.add(res);
            if (logConsumer != null) {
                try {
                    logConsumer.accept(res);
                } catch (Exception e) {
                    LOGGER.info("{} log日志消费异常", res, e);
                }
            }
        }
        return resultList;
    }

    public static void main(String[] args) throws Exception {
        pythonPath = "C:\\1_D\\soft\\anaconda\\envs\\py39\\python.exe";

        Pair<Boolean, HashMap<Object, Object>> booleanHashMapPair = executePython("C:\\home\\qa\\deploy\\onedata\\dataware\\dataware-collectx\\GUWAVE\\rawData\\need_convert\\K45675.7z\\dosilicon_smic_cp_7z_asc_auto\\dosilicon_smic_cp_7z_asc_auto.py", Lists.newArrayList("C:\\home\\qa\\deploy\\onedata\\dataware\\dataware-collectx\\GUWAVE\\rawData\\need_convert\\K45675.7z\\K45675.7z", "C:\\home\\qa\\deploy\\onedata\\dataware\\dataware-collectx\\GUWAVE\\rawData\\convert\\K45675.7z.rawdata", "K45675.7z"), null, null);
        System.out.println(booleanHashMapPair);
    }
}
