package com.guwave.onedata.dataware.source.agent.common.fetcher;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.ExceptionType;
import com.guwave.onedata.dataware.common.contant.FileType;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.StepType;
import com.guwave.onedata.dataware.common.contant.UploadType;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.dao.mysql.domain.dc.FileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileLoadingLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ProcessLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileDetail;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.FileLoadingLogRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpFileDetailRepository;
import com.guwave.onedata.dataware.source.agent.common.exception.FailException;
import com.guwave.onedata.dataware.source.agent.common.exception.NotFailException;
import com.guwave.onedata.dataware.source.agent.common.handler.SingleParseHandler;
import com.guwave.onedata.dataware.source.agent.common.service.FileProcessProgressService;
import com.guwave.onedata.dataware.source.agent.common.util.FileUtil;
import com.guwave.onedata.dataware.source.agent.common.util.ProcessLogThreadLocalUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;

/**
 * Copyright (C), 2022, guwave
 * <p>
 * CommonFetcher
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2022-04-27 15:41:06
 */
public interface SingleFileFetcher extends CommonFetcher {
    Logger LOGGER = LoggerFactory.getLogger(SingleFileFetcher.class);

    default void fetch(SingleParseHandler singleParseHandler) {

        // 查询待解析的文件
        List<SftpFileDetail> files = singleParseHandler.getNeedParseSftpFiles();

        if (CollectionUtils.isEmpty(files)) {
            LOGGER.info("没有待处理的文件");
            return;
        }

        SftpFileDetail sftpFileDetail = files.get(0);
        LOGGER.info("find {} to process", sftpFileDetail.getLocalFileName());
        // 先把更新时间更新
        sftpFileDetail
                .setUpdateTime(new Date())
                .setUpdateUser(Constant.SYSTEM);
        getSftpFileDetailRepository().updateUpdateTime(sftpFileDetail.getId(), sftpFileDetail.getUpdateTime(), sftpFileDetail.getUpdateUser(), sftpFileDetail.getProcessStatus());

        // 上锁原始文件名
        boolean lockSingleFileFetcherFile = getFileProcessProgressService().lockSingleFileFetcherFile(sftpFileDetail.getOriginFileName());
        if (!lockSingleFileFetcherFile) {
            // 上锁失败
            return;
        }

        // 上锁成功
        long startTime = System.currentTimeMillis();
        ProcessLog processLog = null;
        FileInfo fileInfo = null;
        Date stepStartTime = new Date();
        FileLoadingLog fileLoadingLog = new FileLoadingLog();
        try {
            sftpFileDetail = getSftpFileDetailRepository().findById(sftpFileDetail.getId()).orElse(sftpFileDetail);
            if (sftpFileDetail.getProcessStatus() != ProcessStatus.CREATE) {
                LOGGER.info(" {} 已被处理", sftpFileDetail.getLocalFileName());
                return;
            }

            // 更新文件处理状态
            sftpFileDetail
                    .setProcessStatus(ProcessStatus.PROCESSING)
                    .setUpdateTime(new Date());
            getSftpFileDetailRepository().save(sftpFileDetail);

            // 生成处理日志
            processLog = generateProcessLog(sftpFileDetail);
            ProcessLogThreadLocalUtil.setLocal(processLog);
            fileInfo = getFileInfoRepository().findByFileNameAndUploadTypeAndDeleteFlag(sftpFileDetail.getLocalFileName(), UploadType.AUTO, Boolean.FALSE);
            if (fileInfo == null) {
                // 生成数据中心处理文件
                fileInfo = generateFileInfo(sftpFileDetail);
            }

            // 下载并解压文件
            downloadAndUncompressHdfsFile(sftpFileDetail);
            // 获取解压后的文件
            File uncompressFile = new File(getReadPath(), FileUtil.removeFileSuffix(sftpFileDetail.getLocalFileName(), FileType.ZIP));
            if (!uncompressFile.exists()) {
                LOGGER.error("找不到解压后的文件 : {}", uncompressFile.getAbsolutePath());
                throw new FailException("文件解压失败！,找不到解压后的文件");
            }

            // 解析文件
            singleParseHandler.dealFile(uncompressFile, sftpFileDetail, fileInfo, processLog, fileLoadingLog);

            // 设置文件处理状态 -->  成功
            sftpFileDetail.setProcessStatus(ProcessStatus.SUCCESS);

            // 生成 file_loading_log
            insertFileLoadingLog(sftpFileDetail, stepStartTime, ProcessStatus.SUCCESS, fileLoadingLog);
            getFileWarehousingRecordManager().updateFileWarehousingStatus(Collections.singletonList(fileLoadingLog));
        } catch (Exception e) {
            if (e instanceof NotFailException) {
                LOGGER.info("{} ：{}", sftpFileDetail.getLocalFileName(), e.getMessage());
                ProcessLogThreadLocalUtil.appendErrorMessage(e.getMessage());
            } else {
                LOGGER.error("处理文件{}异常：", sftpFileDetail.getLocalFileName(), e);
                // 设置文件处理状态 -->  失败
                sftpFileDetail.setProcessStatus(ProcessStatus.FAIL);
                if (processLog != null) {
                    processLog.setExceptionType(e instanceof FileLoadException ? ((FileLoadException) e).getExceptionType() : ExceptionType.OTHER_EXCEPTION);
                }
                ProcessLogThreadLocalUtil.appendErrorMessage(ExceptionUtils.getStackTrace(e));

                // 生成 file_loading_log
                updateFileLoadingLogStatusAfterDealException(fileLoadingLog, e);
                insertFileLoadingLog(sftpFileDetail, stepStartTime, ProcessStatus.FAIL, fileLoadingLog);
                getFileWarehousingRecordManager().updateFileWarehousingStatus(Collections.singletonList(fileLoadingLog));
            }
        } finally {
            // 清除threadLocal
            ProcessLogThreadLocalUtil.clearLocal();

            Date updateTime = new Date();

            // 更新sftpFileDetail
            if (sftpFileDetail.getProcessStatus() == ProcessStatus.PROCESSING) {
                //  状态还是processing 时， 设置文件的状态 --> CREATE 等待下次重试
                sftpFileDetail.setProcessStatus(ProcessStatus.CREATE);
            }
            sftpFileDetail.setUpdateTime(updateTime);
            getSftpFileDetailRepository().save(sftpFileDetail);

            // 更新process_log和fileInfo
            afterOneFileDeal(processLog, fileInfo, sftpFileDetail.getProcessStatus(), sftpFileDetail.getLocalFileName(), startTime);

            // 文件处理结束
            getKafKaSink().sendLoadEndFlagMessage(fileLoadingLog);
            // 解锁文件
            getFileProcessProgressService().unlockSingleFileFetcherFile(sftpFileDetail.getOriginFileName());
        }
    }

    FileProcessProgressService getFileProcessProgressService();

    SftpFileDetailRepository getSftpFileDetailRepository();

    FileLoadingLogRepository getFileLoadingLogRepository();

    default void insertFileLoadingLog(SftpFileDetail sftpFileDetail, Date stepStartTime, ProcessStatus processStatus, FileLoadingLog fileLoadingLog) {
        Date date = new Date();
        fileLoadingLog
                .setCustomer(sftpFileDetail.getCustomer())
                .setSubCustomer(sftpFileDetail.getSubCustomer())
                .setTestArea(sftpFileDetail.getTestArea())
                .setFactory(sftpFileDetail.getFactory())
                .setFactorySite(sftpFileDetail.getFactorySite())
                .setFab(sftpFileDetail.getFab())
                .setFabSite(sftpFileDetail.getFabSite())
                .setFileCategory(sftpFileDetail.getFileCategory())
                .setFileName(sftpFileDetail.getLocalFileName()) //
                .setOriginFileName(sftpFileDetail.getOriginFileName())
                .setConvertFlag(sftpFileDetail.getConvertFlag())
                .setHdfsPath(sftpFileDetail.getHdfsFilePath())
                .setRemoteFileMtime(sftpFileDetail.getRemoteFileMtime())
                .setStep(StepType.STEP_TYPE_7100.getStep())
                .setStepStartTime(stepStartTime)
                .setFileSize(sftpFileDetail.getFileSize())
                .setOriginFileSize(sftpFileDetail.getOriginFileSize())
                .setFtpIp(sftpFileDetail.getFtpIp())
                .setStepEndTime(new Date())
                .setWarehousingMode(sftpFileDetail.getWarehousingMode())
                .setRepairLotWaferId(sftpFileDetail.getRepairLotWaferId())
                .setCleanupTaskIds(sftpFileDetail.getCleanupTaskIds())
                .setProcessStatus(processStatus)
                .setCreateTime(stepStartTime)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateTime(date)
                .setUpdateUser(Constant.SYSTEM);

        // 查询上一step数据，补充内容
        List<FileLoadingLog> lastStepFileLoadingLogs = getFileLoadingLogRepository().findAllByFileNameAndStep(
                fileLoadingLog.getFileName(),
                StepType.STEP_TYPE_1000.getStep()
        );
        if (CollectionUtils.isNotEmpty(lastStepFileLoadingLogs)) {
            Set<String> uniqueWarningMessages = new HashSet<>();
            for (FileLoadingLog log : lastStepFileLoadingLogs) {
                String warningMessage = log.getWarningMessage();
                if (warningMessage != null && !warningMessage.isEmpty()) {
                    uniqueWarningMessages.add(warningMessage);
                }
            }
            String concatenatedWarningMessages = String.join(Constant.COMMA, uniqueWarningMessages);
            fileLoadingLog
                    .setSourceFileNames(lastStepFileLoadingLogs.get(0).getSourceFileNames()) //
                    .setFtpPath(lastStepFileLoadingLogs.get(0).getFtpPath())
                    .setDataIntegrityFileComment(lastStepFileLoadingLogs.get(0).getDataIntegrityFileComment())
                    .setDataIntegrityFileLabel(lastStepFileLoadingLogs.get(0).getDataIntegrityFileLabel())
                    .setWarningMessage(concatenatedWarningMessages)
            ;
        }

        lastStepFileLoadingLogs.forEach(t -> {
            t.setLotId(fileLoadingLog.getLotId());
            t.setSblotId(fileLoadingLog.getSblotId());
            t.setDeviceId(fileLoadingLog.getDeviceId());
            t.setWaferId(fileLoadingLog.getWaferId());
            t.setWaferNo(fileLoadingLog.getWaferNo());
            t.setLotType(fileLoadingLog.getLotType());
            t.setTesterName(fileLoadingLog.getTesterName());
            t.setTestStage(fileLoadingLog.getTestStage());
            t.setTesterType(fileLoadingLog.getTesterType());
            t.setTestProgram(fileLoadingLog.getTestProgram());
            t.setTestProgramVersion(fileLoadingLog.getTestProgramVersion());
            t.setStartT(fileLoadingLog.getStartT());
            t.setFinishT(fileLoadingLog.getFinishT());
            t.setUpdateTime(date);
        });

        lastStepFileLoadingLogs.add(fileLoadingLog);

        //插入前先删除 当前以及以后的所有行
        getFileLoadingLogRepository().deleteByFileNameAndStep(fileLoadingLog.getFileName(), fileLoadingLog.getStep());

        getFileLoadingLogRepository().saveAll(lastStepFileLoadingLogs);
    }

}
