plugins {
  id 'application'
}

description = 'dataware collectx'

dependencies {
  api (group: 'org.apache.hadoop', name: 'hadoop-common', version: hadoopVersion) {
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    exclude group: 'tomcat', module: 'jasper-runtime'
    exclude group: 'tomcat', module: 'jasper-compiler'
    exclude group: 'com.jcraft', module: 'jsch'
  }
  api (group: 'org.apache.hadoop', name: 'hadoop-hdfs', version: hadoopVersion) {
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    exclude group: 'tomcat', module: 'jasper-runtime'
    exclude group: 'tomcat', module: 'jasper-compiler'
  }
  api (group: 'org.apache.hadoop', name: 'hadoop-client', version: hadoopVersion) {
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    exclude group: 'tomcat', module: 'jasper-runtime'
    exclude group: 'tomcat', module: 'jasper-compiler'
  }

  implementation project(':dataware-common')
  implementation project(':dataware-dao:dataware-dao-mysql')
  implementation project(':dataware-bridge:dataware-bridge-api')
  implementation enforcedPlatform("org.springframework.boot:spring-boot-dependencies:$springBootVersion")

  implementation group: 'org.springframework.boot', name: 'spring-boot-starter'
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter-validation'
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter-data-redis', version: springBootVersion
  implementation group: 'org.springframework', name: 'spring-web'
  implementation group: 'commons-codec', name: 'commons-codec', version: commonsCodecVersion
  implementation group: 'commons-io', name: 'commons-io', version: commonsIoVersion
  implementation group: 'org.apache.commons', name: 'commons-lang3', version: commonsLang3Version
  implementation group: 'mysql', name: 'mysql-connector-java', version: mysqlVersion
  implementation group: 'com.alibaba', name: 'fastjson', version: fastJsonVersion
  implementation group: 'org.apache.commons', name: 'commons-compress', version: commonsCompressVersion
  implementation group: 'org.tukaani', name: 'xz', version: tukaaniXzVersion
  implementation group: 'net.sf.sevenzipjbinding', name: 'sevenzipjbinding', version: sevenzipjbindingVersion
  implementation group: 'net.sf.sevenzipjbinding', name: 'sevenzipjbinding-all-platforms', version: sevenzipjbindingVersion
  implementation group: 'org.apache.tika', name: 'tika-core', version: tikaVersion
  compileOnly group: 'org.projectlombok', name: 'lombok', version: lombokVersion
  annotationProcessor group: 'org.projectlombok', name: 'lombok', version: lombokVersion
  implementation group: 'org.springframework.kafka', name: 'spring-kafka'

  testImplementation group: 'org.springframework.boot', name: 'spring-boot-starter-test'

  implementation group: 'io.github.openfeign.form', name: 'feign-form', version: feignFormVersion
  implementation group: 'io.github.openfeign', name: 'feign-core', version: feignVersion
  implementation group: 'io.github.openfeign', name: 'feign-httpclient', version: feignVersion
  implementation group: 'io.github.openfeign', name: 'feign-jackson', version: feignVersion

  implementation group: 'org.apache.dubbo', name: 'dubbo-spring-boot-starter', version: dubboVersion
  implementation group: 'org.apache.curator', name: 'curator-framework', version: curatorVersion
  implementation group: 'org.apache.curator', name: 'curator-x-discovery-server', version: curatorVersion
  implementation group: 'com.github.mwiede', name: 'jsch', version: jschVersion

  implementation group: 'com.guwave.bigbrother', name: 'skyeye-driver-logback', version: skyeyeVersion
}

configurations {
  compile.exclude group: 'log4j', module: 'log4j'
  compile.exclude group: 'org.hibernate.validator', module: 'hibernate-validator'
  compile.exclude group: 'org.apache.tomcat.embed', module: 'tomcat-embed-websocket'
  compile.exclude group: 'org.slf4j', module: 'slf4j-log4j12'
}

compileJava {
  options.compilerArgs = ["-parameters"]
}

jar {
  enabled true
  manifest.attributes 'Main-Class': 'com.guwave.onedata.dataware.collectx.Application'
}

application {
  mainClassName = 'com.guwave.onedata.dataware.collectx.Application'
  applicationDistribution.from('src/main/resources/properties').into('properties')
}

distributions {
  main {
    contents {
      from('src/main/resources/shell/startup.sh') {
        into '.'
      }
    }
  }
}

startScripts {
  doLast {
    unixScript.text = unixScript.text.replaceAll(":\\\$APP_HOME/lib/(.*)\n", ":\\\$APP_HOME/lib/\\*\n")
  }
}
