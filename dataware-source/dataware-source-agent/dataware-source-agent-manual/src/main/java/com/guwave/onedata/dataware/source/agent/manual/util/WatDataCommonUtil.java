package com.guwave.onedata.dataware.source.agent.manual.util;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.common.contant.DwLayer;
import com.guwave.onedata.dataware.common.contant.ExceptionType;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.model.manual.ManualCalculateFinishVo;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingFinishMessage;
import com.guwave.onedata.dataware.common.model.wat.dim.*;
import com.guwave.onedata.dataware.common.model.wat.dwd.DwdWatDieDetail;
import com.guwave.onedata.dataware.common.model.wat.dwd.DwdWatTestItemDetail;
import com.guwave.onedata.dataware.common.model.wat.ods.OdsWat;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.source.agent.common.sink.impl.KafkaSink;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import com.guwave.onedata.dataware.source.agent.common.util.WatCommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;

@Component
public class WatDataCommonUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(WatDataCommonUtil.class);

    public static final Map<String, BiConsumer<String, OdsWat>> WAT_DATA_COMMON_FIELD_CONSUMER_MAP = new HashMap<String, BiConsumer<String, OdsWat>>() {{
        put("lotId", (str, odsWat) -> odsWat.setLotId(str));
        put("waferNo", (str, odsWat) -> odsWat.setWaferNo(Integer.valueOf(str).toString()));
        put("testTxt", (str, odsWat) -> odsWat.setTestTxt(str));
        put("testValue", (str, odsWat) -> odsWat.setTestValue(getDoubleValue(str)));
        put("testNum", (str, odsWat) -> odsWat.setTestNum(getLongValue(str)));
        put("hiLimit", (str, odsWat) -> odsWat.setHiLimit(getDoubleValue(str)));
        put("loLimit", (str, odsWat) -> odsWat.setLoLimit(getDoubleValue(str)));
        put("target", (str, odsWat) -> odsWat.setTarget(getDoubleValue(str)));
        put("units", (str, odsWat) -> odsWat.setUnits(str));
        put("testResult", (str, odsWat) -> odsWat.setTestResult(getIntegerValue(str)));
        put("deviceId", (str, odsWat) -> odsWat.setDeviceId(str));
        put("startTime", (str, odsWat) -> odsWat.setStartTime(getTimeStamp(str)));
        put("endTime", (str, odsWat) -> odsWat.setEndTime(getTimeStamp(str)));
        put("testCod", (str, odsWat) -> odsWat.setTestCod(str));
//        put("testitemType", (str, odsWat) -> odsWat.setTestitemType(str));
        put("testProgram", (str, odsWat) -> odsWat.setTestProgram(str));
        put("waferId", (str, odsWat) -> odsWat.setWaferId(str));
        put("testStage", (str, odsWat) -> odsWat.setTestStage(str));
        put("reticleX", (str, odsWat) -> odsWat.setReticleX(getIntegerValue(str)));
        put("reticleY", (str, odsWat) -> odsWat.setReticleY(getIntegerValue(str)));
        put("siteId", (str, odsWat) -> odsWat.setSiteId(str));
        put("wfFlat", (str, odsWat) -> odsWat.setWfFlat(str));
        put("testTemperature", (str, odsWat) -> odsWat.setTestTemperature(str));
        put("testerName", (str, odsWat) -> odsWat.setTesterName(str));
        put("operatorName", (str, odsWat) -> odsWat.setOperatorName(str));
        put("process", (str, odsWat) -> odsWat.setProcess(str));
        put("hiSpec", (str, odsWat) -> odsWat.setHiSpec(getDoubleValue(str)));
        put("loSpec", (str, odsWat) -> odsWat.setLoSpec(getDoubleValue(str)));
        put("lotType", (str, odsWat) -> odsWat.setLotType(LotType.of(str) == null ? null : LotType.of(str).getType()));

        put("factory", (str, odsWat) -> odsWat.setFactory(str));
        put("factorySite", (str, odsWat) -> odsWat.setFactorySite(str));
        put("fab", (str, odsWat) -> odsWat.setFab(str));
        put("fabSite", (str, odsWat) -> odsWat.setFabSite(str));
    }};


    @Value("${spring.kafka.manualFinishTopic}")
    private String manualFinishTopic;

    @Autowired
    private Map<String, CkSink> sinks;
    @Autowired
    private KafkaSink kafkaSink;

    public void sinkCk(List<OdsWat> wats) {
        if (CollectionUtils.isEmpty(wats)) {
            throw new RuntimeException("没有解析到数据");
        }
        OdsWat odsWat = wats.get(0);
        List<DwdWatTestItemDetail> testItemDetails = WatCommonUtil.toDwdWatTestItemDetail(wats);
        LOGGER.info("解析dwd wat test item detail条数: {}", testItemDetails.size());
        this.sinks.get("dwdWatTestItemDetailSink").writeCk(testItemDetails);
        // 写入dwd wat die detail
        List<DwdWatDieDetail> dieDetails = WatCommonUtil.toDwdWatDieDetail(testItemDetails);
        LOGGER.info("解析dwd wat die detail条数: {}", dieDetails.size());
        this.sinks.get("dwdWatDieDetailSink").writeCk(dieDetails);
        // 写入dim wat test item
        List<DimWatTestItem> testItems = WatCommonUtil.toDimWatTestItem(testItemDetails);
        LOGGER.info("解析dim wat test item条数: {}", testItems.size());
        this.sinks.get("dimWatTestItemSink").writeCk(testItems);

        // 写入dim wat wafer
        List<DimWatWafer> wafers = WatCommonUtil.toDimWatWafer(testItemDetails);
        LOGGER.info("解析dim wat wafer条数: {}", wafers.size());
        this.sinks.get("dimWatWaferSink").writeCk(wafers);

        // 写入dim lot wafer bin
        List<DimWatWaferBin> watWaferBins = WatCommonUtil.toDimWatWaferBin(testItemDetails);
        LOGGER.info("解析dim wat lot wafer bin条数: {}", watWaferBins.size());
        this.sinks.get("dimWatWaferBinSink").writeCk(watWaferBins);

        // 写入dim wat site
        List<DimWatWaferSite> waferSites = WatCommonUtil.toDimWatWaferSite(testItemDetails);
        LOGGER.info("解析dim wat site 条数: {}", waferSites.size());
        this.sinks.get("dimWatWaferSiteSink").writeCk(waferSites);

        // 写入dim wat test program site local
        List<DimWatTestProgramSite> watTestProgramSites = WatCommonUtil.toDimWatTestProgramSite(testItemDetails);
        LOGGER.info("使用的watTestProgramSites {}", watTestProgramSites);
        LOGGER.info("解析dim wat test program site条数: {}", watTestProgramSites.size());
        DimWatTestProgramSite site = watTestProgramSites.get(0);
        this.sinks.get("dimWatTestProgramSiteSink").doHandle(watTestProgramSites, WatCommonUtil.getTestProgramPartition(site.getCustomer(), site.getTestArea(), site.getFactory()));
        // 写入dim wat test program bin local
        List<DimWatTestProgramBin> watTestProgramBins = WatCommonUtil.toDimWatTestProgramBin(testItemDetails);
        LOGGER.info("解析dim wat test program bin条数: {}", watTestProgramBins.size());
        DimWatTestProgramBin bin = watTestProgramBins.get(0);
        this.sinks.get("dimWatTestProgramBinSink").doHandle(watTestProgramBins, WatCommonUtil.getTestProgramPartition(bin.getCustomer(), bin.getTestArea(), bin.getFactory()));
        // 写入dim wat test program test local
        List<DimWatTestProgramTestItem> watTestProgramTestItems = WatCommonUtil.toDimWatTestProgramTestItem(testItemDetails);
        LOGGER.info("解析dim wat test program test item条数: {}", watTestProgramTestItems.size());
        DimWatTestProgramTestItem item = watTestProgramTestItems.get(0);
        this.sinks.get("dimWatTestProgramTestItemSink").doHandle(watTestProgramTestItems, WatCommonUtil.getTestProgramPartition(item.getCustomer(), item.getTestArea(), item.getFactory()));
    }

    public String sendManualWarehousingFinishMessage(ManualCalculateTask manualCalculateTask, DwLayer dwLayer, ExceptionType exceptionType, String exceptionMessage) {
        ManualCalculateFinishVo manualCalculateFinishVo = new ManualCalculateFinishVo()
                .setFileId(manualCalculateTask.getFileId())
                .setFileName(manualCalculateTask.getFileName())
                .setDwLayer(dwLayer);

        ManualWarehousingFinishMessage manualWarehousingFinishMessage = new ManualWarehousingFinishMessage()
                .setManualType(manualCalculateTask.getManualType())
                .setManualDeleteVo(null)
                .setManualCalculateFinishVo(manualCalculateFinishVo)
                .setProcessStatus(manualCalculateTask.getProcessStatus())
                .setExceptionType(exceptionType)
                .setExceptionMessage(exceptionMessage);

        String manualFinishMessageStr = JSON.toJSONString(manualWarehousingFinishMessage);
        kafkaSink.send(manualFinishTopic, manualCalculateTask.getFileId() + EMPTY, manualFinishMessageStr);

        return manualFinishMessageStr;
    }


    public static Long getLongValue(String str) {
        return str.isEmpty() ? null : Double.valueOf(str).longValue();
    }

    public static Integer getIntegerValue(String str) {
        return str.isEmpty() ? null : Double.valueOf(str).intValue();
    }

    public static Double getDoubleValue(String str) {
        return str.isEmpty() ? null : Double.valueOf(str);
    }

    public static final String[] DATE_FORMAT_ALL = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy年MM月dd日 HH:mm:ss",
            "yyyyMMdd HH:mm:ss",
            "yyyy-MM-dd HH:mm",
            "yyyy/MM/dd HH:mm",
            "yyyy年MM月dd日 HH:mm",
            "yyyyMMdd HH:mm",
            "yyyy-MM-dd HH",
            "yyyy/MM/dd HH",
            "yyyy年MM月dd日 HH",
            "yyyyMMdd HH",
            "yyyy-MM-dd",
            "yyyy/MM/dd",
            "yyyy年MM月dd日",
            "yyyyMMdd"
    };

    public static Long getTimeStamp(String str) {
        if (str.isEmpty()) {
            return null;
        }
        try {
            Date date = org.apache.commons.lang.time.DateUtils.parseDate(str, DATE_FORMAT_ALL);
            return date.getTime();
        } catch (Exception e) {
            throw new RuntimeException("时间格式解析失败：" + str, e);
        }
    }

    public static Map<String, String> getMapValue(String str) {
        Map<String, String> result = new HashMap<>();
        if (str.isEmpty()) {
            return result;
        }
        try {
            Map map = JSON.parseObject(str, Map.class);
            for (Object key : map.keySet()) {
                result.put(key.toString(), map.get(key).toString());
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException("json格式解析失败：" + str, e);
        }
    }


}
