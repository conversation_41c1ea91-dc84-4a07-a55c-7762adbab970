<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false">

  <property name="APP_NAME" value="dataware-source-agent-manual" />
  <property name="LOG_HOME" value="./logs/" />
  <contextName>${APP_NAME}</contextName>

  <!-- 控制台输出 -->
  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
      <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度 %msg：日志消息，%n是换行符 -->
      <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{96}[%line]: %msg%n</pattern>
    </encoder>
  </appender>

  <!-- 按照每天生成日志文件 -->
  <appender name="dailyInfoFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <prudent>false</prudent>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${LOG_HOME}/info/${APP_NAME}_%d{yyyy-MM-dd}.log.zip</fileNamePattern>
      <maxHistory>20</maxHistory>
    </rollingPolicy>
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>INFO</level>
    </filter>
    <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
    <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
      <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{96}[%line]: %msg%n</Pattern>
    </encoder>
  </appender>

  <appender name="dailyErrorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <prudent>false</prudent>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${LOG_HOME}/error/${APP_NAME}_%d{yyyy-MM-dd}.log.zip</fileNamePattern>
      <maxHistory>20</maxHistory>
    </rollingPolicy>
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>ERROR</level>
    </filter>
    <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符 -->
    <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
      <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{96}[%line]: %msg%n</Pattern>
    </encoder>
  </appender>

  <appender name="kafkaAppender" class="com.guwave.bigbrother.skyeye.driver.logback.appender.KafkaAppender">
    <encoder class="com.guwave.bigbrother.skyeye.driver.logback.encoder.KafkaLayoutEncoder">
      <layout class="ch.qos.logback.classic.PatternLayout">
        <pattern>%X{TRACKID};%d{yyyy-MM-dd HH:mm:ss.SSS};${CONTEXT_NAME};HOSTNAME;%thread;%-5level;%logger{96};%line;%msg%n</pattern>
      </layout>
    </encoder>
    <keyBuilder class="com.guwave.bigbrother.skyeye.driver.logback.builder.impl.AppHostKeyBuilder" />
  </appender>

  <appender name="asyncAppender" class="ch.qos.logback.classic.AsyncAppender">
    <neverBlock>true</neverBlock>
    <appender-ref ref="kafkaAppender" />
  </appender>

  <root level="INFO">
    <appender-ref ref="STDOUT" />
    <appender-ref ref="dailyInfoFile" />
    <appender-ref ref="dailyErrorFile" />
    <appender-ref ref="asyncAppender" />
  </root>

</configuration>
