package com.guwave.onedata.dataware.collectx.manager.thread;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpBackupRule;

public class SftpBackupRuleThreadManager {
    private static final ThreadLocal<SftpBackupRule> sftpBackupRuleThreadLocal = new ThreadLocal<>();

    public static SftpBackupRule getSftpBackupRule() {
        return sftpBackupRuleThreadLocal.get();
    }

    public static void setSftpBackupRule(SftpBackupRule sftpBackupRule) {
        sftpBackupRuleThreadLocal.set(sftpBackupRule);
    }

    public static void clear() {
        sftpBackupRuleThreadLocal.remove();
    }

}
