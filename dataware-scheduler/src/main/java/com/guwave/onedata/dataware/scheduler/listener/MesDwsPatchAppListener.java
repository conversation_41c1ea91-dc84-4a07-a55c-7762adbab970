package com.guwave.onedata.dataware.scheduler.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.MesDataLossRecord;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.MesParseLog;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.MesDataLossRecordRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.MesParseLogRepository;
import com.guwave.onedata.dataware.scheduler.service.RedisService;
import com.guwave.onedata.dataware.scheduler.util.ComputeUtil;
import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Date;
import java.util.List;

@Component
public class MesDwsPatchAppListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(MesDwsPatchAppListener.class);

    @Autowired
    private MesDataLossRecordRepository mesDataLossRecordRepository;
    @Autowired
    private MesParseLogRepository mesParseLogRepository;
    @Autowired
    private RedisService redisService;

    public void stateChanged(List<MesParseLog> mesParseLogs, ComputeResultMessage computeResultMessage) {
        MesParseLog needDealMesParseLog = mesParseLogs.get(0);
        try {
            ProcessStatus processStatus = ComputeUtil.convertProcessStatus(computeResultMessage.getProcessStatus());
            Date date = new Date();
            List<MesDataLossRecord> mesDataLossRecords = mesDataLossRecordRepository.findAllByCustomerAndFactoryAndFactorySiteAndTestAreaAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoAndProcessStatus(
                    needDealMesParseLog.getCustomer(), needDealMesParseLog.getFactory(), needDealMesParseLog.getFactorySite(), needDealMesParseLog.getTestArea(), needDealMesParseLog.getDeviceId(), needDealMesParseLog.getLotType(), needDealMesParseLog.getTestStage(), needDealMesParseLog.getLotId(), needDealMesParseLog.getWaferNo(), ProcessStatus.PROCESSING
            );
            LOGGER.info("MesDwsPatchAppListener 任务处理 {},{}", processStatus, JSON.toJSONString(needDealMesParseLog));
            mesParseLogs.forEach(t -> t.setProcessStatus(processStatus)
                    .setComment(t.getComment() + DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss") + "\tMesDwsPatch补充mes信息 " + (processStatus == ProcessStatus.SUCCESS ? "成功" : "失败") + "\n")
                    .setUpdateTime(date));
            mesParseLogRepository.saveAll(mesParseLogs);
            mesDataLossRecords.forEach(t -> t.setProcessStatus(processStatus)
                    .setUpdateTime(date));
            mesDataLossRecordRepository.saveAll(mesDataLossRecords);
        } finally {
            // 解锁die任务
            redisService.unlockDwDieTask(
                    Lists.newArrayList(FileCategory.OTHER),
                    needDealMesParseLog.getCustomer(), needDealMesParseLog.getTestArea(), needDealMesParseLog.getFactory(), needDealMesParseLog.getDeviceId(), needDealMesParseLog.getLotType(), needDealMesParseLog.getTestStage(), needDealMesParseLog.getLotId(), needDealMesParseLog.getWaferNo(),
                    RedisService.lockDwDieMesPatchValue
            );
        }
    }
}
