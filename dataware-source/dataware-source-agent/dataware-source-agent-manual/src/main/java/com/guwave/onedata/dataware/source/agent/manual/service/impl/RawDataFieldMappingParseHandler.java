package com.guwave.onedata.dataware.source.agent.manual.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.ManualFileSystem;
import com.guwave.onedata.dataware.common.contant.ParseRule;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.model.raw.TestItemData;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.EcidRuleRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LotRelationSyncRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.UidRuleRepository;
import com.guwave.onedata.dataware.parser.stdf.visitor.Visitor;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import com.guwave.onedata.dataware.source.agent.common.util.FileSystemUtil;
import com.guwave.onedata.dataware.source.agent.common.util.HdfsUtil;
import com.guwave.onedata.dataware.source.agent.common.visitor.MultiThreadVisitor;
import com.guwave.onedata.dataware.source.agent.manual.listener.ExcelLineListener;
import com.guwave.onedata.dataware.source.agent.manual.listener.RawDataExcelPreView;
import com.guwave.onedata.dataware.source.agent.manual.listener.RawDataExcelReader;
import com.guwave.onedata.dataware.source.agent.manual.service.ParseFileHandler;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

@Component
public class RawDataFieldMappingParseHandler implements ParseFileHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(RawDataFieldMappingParseHandler.class);

    public static final Map<String, BiConsumer<String, TestItemData>> OTHER_FIELD_CONSUMER_MAP = new HashMap<String, BiConsumer<String, TestItemData>>() {{
    }};
    public static final Map<String, BiConsumer<Pair<String, String>, TestItemData>> MAP_FIELD_CONSUMER_MAP = new HashMap<String, BiConsumer<Pair<String, String>, TestItemData>>() {{
        put("conditionSet", (pairStr, rawData) -> {
            if (rawData.getConditionSet() == null) {
                rawData.setConditionSet(new HashMap<>());
            }
            rawData.getConditionSet().put(pairStr.getKey(), pairStr.getValue());
        });
    }};

    @Value("${spring.hdfs.odsHdfsTemplatePath}")
    private String odsHdfsTemplatePath;
    @Value("${spring.maxNoCPartIdCnt}")
    private Long maxNoCPartIdCnt;
    @Value("${spring.handler.sink.batchSize}")
    private Integer batchSize;
    @Autowired
    private Map<String, FileSystemUtil> fileSystemUtilMap;
    @Autowired
    private Map<String, CkSink> ckSinkMap;
    @Autowired
    private EcidRuleRepository ecidRuleRepository;
    @Autowired
    private UidRuleRepository uidRuleRepository;
    @Autowired
    private LotRelationSyncRepository lotRelationSyncRepository;

    private static final Map<ManualFileSystem, String> MANUAL_FILE_SYSTEM_MAP = new HashMap<ManualFileSystem, String>() {{
        put(ManualFileSystem.HDFS, "hdfsUtil");
        put(ManualFileSystem.LOCAL, "localftUtil");
        put(ManualFileSystem.MINIO, "minioUtil");
    }};
    @Override
    public Boolean support(FileCategory fileCategory, ParseRule parseRule) {
        return fileCategory == FileCategory.RAW_DATA && parseRule == ParseRule.RAW_DATA_FIELD_MAPPING;
    }

    @Override
    public FileCategory calRealFileCategory(File localFile, File scriptFile, ManualFileInfo manualFileInfo) {
        if (manualFileInfo.getTestArea() == TestArea.WAT) {
            return FileCategory.WAT;
        }
        RawDataExcelPreView rawDataExcelPreView = new RawDataExcelPreView(manualFileInfo);
        ExcelReader excel = EasyExcel.read(localFile, new ExcelLineListener(manualFileInfo.getFieldMapping(), rawDataExcelPreView))
                .headRowNumber(1)
                .excelType(ExcelTypeEnum.CSV)
                .build();
        try {
            excel.read(new ReadSheet(0));
        } finally {
            excel.finish();
        }
        return rawDataExcelPreView.getRealFileCategory();
    }

    @Override
    public void dealFile(ManualCalculateTask manualCalculateTask, File localFile, ManualFileInfo manualFileInfo, File scriptFile) {
        // 读取文件到ods
        FileSystemUtil fileSystemUtil = fileSystemUtilMap.get(MANUAL_FILE_SYSTEM_MAP.get(manualFileInfo.getManualFileSystem()));
        List<Visitor> visitors = new ArrayList<>();
        int allThreadCnt = 1;
        for (int i = 1; i <= allThreadCnt; i++) {
            visitors.add(new MultiThreadVisitor(i, fileSystemUtil, odsHdfsTemplatePath, localFile, defaultFillFileMainDataConsumer(manualFileInfo), allThreadCnt, batchSize, ckSinkMap, ecidRuleRepository, uidRuleRepository, lotRelationSyncRepository, manualFileInfo.getParameterType(), manualFileInfo.getTestParameter(), manualFileInfo.getScript()));
        }
        try {
            Visitor visitor = visitors.get(0);
            RawDataExcelReader rawDataExcelReader = new RawDataExcelReader(visitor, firstRecordTestArea -> predictTestArea(manualCalculateTask, firstRecordTestArea, fileSystemUtil), maxNoCPartIdCnt, OTHER_FIELD_CONSUMER_MAP, MAP_FIELD_CONSUMER_MAP);
            ExcelReader excel = EasyExcel.read(localFile, new ExcelLineListener(manualFileInfo.getFieldMapping(), rawDataExcelReader))
                    .headRowNumber(1)
                    .excelType(ExcelTypeEnum.CSV)
                    .build();
            try {
                excel.read(new ReadSheet(0));
            } finally {
                excel.finish();
            }
            rawDataExcelReader.dealRemain();
        } finally {
            visitors.forEach(t -> {
                MultiThreadVisitor visitor = (MultiThreadVisitor) t;
                visitor.close();
            });
        }

        // 填充manual_file_info
        defaultFillManualFileInfo(manualFileInfo, visitors, localFile);
    }

    private TestArea predictTestArea(ManualCalculateTask manualCalculateTask, TestArea firstRecordTestArea, FileSystemUtil fileSystemUtil) {
        TestArea resultArea = manualCalculateTask.getTestArea() == null ? firstRecordTestArea : manualCalculateTask.getTestArea();

        // 删除此文件对应的ods数据
        deleteOdsFile(odsHdfsTemplatePath, manualCalculateTask, resultArea, fileSystemUtil);

        return resultArea;
    }
}
