package com.guwave.onedata.dataware.source.common.util;

import com.guwave.onedata.dataware.source.common.serialization.protostuff.TypeReference;
import com.guwave.onedata.dataware.source.common.serialization.protostuff.TypeUtil;
import io.protostuff.LinkedBuffer;
import io.protostuff.MessageCollectionSchema;
import io.protostuff.ProtostuffIOUtil;
import io.protostuff.Schema;
import io.protostuff.runtime.RuntimeSchema;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.*;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * ProtostuffUtil
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-18 12:11:01
 */
public class ProtostuffUtil {

    private static final Set<Class<?>> WRAPPER_SET = new HashSet<>();

    static {
        WRAPPER_SET.add(StringBuffer.class);
        WRAPPER_SET.add(StringBuilder.class);
        WRAPPER_SET.add(BigDecimal.class);
        WRAPPER_SET.add(Date.class);
        WRAPPER_SET.add(Calendar.class);
        WRAPPER_SET.add(Wrapper.class);
    }

    public static <T> byte[] serializer(T obj, TypeReference<T> typeReference) {
        Type type = typeReference.getType();
        return serializer(obj, type);
    }

    @SuppressWarnings("unchecked")
    public static <T> byte[] serializer(T obj, Type type) {
        LinkedBuffer buffer = LinkedBuffer.allocate(LinkedBuffer.DEFAULT_BUFFER_SIZE);
        try {
            if (null != obj && TypeUtil.isCollection(type)) {
                Class<?> subClazz = null;
                Type item = TypeUtil.getCollectionItemType(type);
                if (null != item) {
                    subClazz = TypeUtil.getClass(item);
                }
                if (null != subClazz && subClazz != Object.class) {
                    Schema<?> schema = RuntimeSchema.getSchema(subClazz);
                    Schema<T> collection = (Schema<T>) new MessageCollectionSchema<>(schema);
                    return ProtostuffIOUtil.toByteArray(obj, collection, buffer);
                }
            }

            if (obj == null || needWrapper(obj)) {
                Schema<Wrapper> schema = RuntimeSchema.getSchema(Wrapper.class);
                Wrapper<T> wrapper = new Wrapper<>(obj);
                return ProtostuffIOUtil.toByteArray(wrapper, schema, buffer);
            } else {
                Class<?> clazz = TypeUtil.getClass(type);
                Schema<T> schema = (Schema<T>) RuntimeSchema.getSchema(clazz);
                return ProtostuffIOUtil.toByteArray(obj, schema, buffer);
            }
        } finally {
            buffer.clear();
        }
    }

    /**
     * 反序列化
     *
     * @param bytes         序列化后的byte[]值
     * @param typeReference 反序列化后的对象
     * @param <T>           泛型
     * @return 返回的对象
     */
    public static <T> T deserializer(byte[] bytes, TypeReference<T> typeReference) {
        Type type = typeReference.getType();
        return deserializer(bytes, type);
    }

    /**
     * 反序列化
     *
     * @param bytes 序列化后的byte[]值
     * @param type  反序列化后的对象
     * @param <T>   泛型
     * @return 返回的对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T deserializer(byte[] bytes, Type type) {
        if (null == bytes) {
            return null;
        }

        T result = null;
        try {
            if (TypeUtil.isCollection(type)) {
                Class<?> subClazz = null;
                Type item = TypeUtil.getCollectionItemType(type);
                if (null != item) {
                    subClazz = TypeUtil.getClass(item);
                }
                if (null != subClazz && subClazz != Object.class) {
                    Schema<?> schema = RuntimeSchema.getSchema(subClazz);
                    Schema<T> collection = (Schema<T>) new MessageCollectionSchema<>(schema);
                    result = collection.newMessage();
                    ProtostuffIOUtil.mergeFrom(bytes, result, collection);
                    return result;
                }
            }

            Class<?> clazz = TypeUtil.getClass(type);
            if (needWrapper(clazz)) {
                @SuppressWarnings("rawtypes")
                Schema<Wrapper> schema = RuntimeSchema.getSchema(Wrapper.class);
                Wrapper<T> wrapper = schema.newMessage();
                ProtostuffIOUtil.mergeFrom(bytes, wrapper, schema);
                result = wrapper.getData();
            } else {
                Schema<T> schema = (Schema<T>) RuntimeSchema.getSchema(clazz);
                result = schema.newMessage();
                ProtostuffIOUtil.mergeFrom(bytes, result, schema);
            }
        } catch (Exception ignore) {
        }
        return result;
    }

    /**
     * Determine if the object needs wrap
     *
     * @param clazz object type
     * @return need wrap
     */
    public static boolean needWrapper(Class<?> clazz) {
        return WRAPPER_SET.contains(clazz) || clazz.isArray() || clazz.isEnum() || Collection.class.isAssignableFrom(clazz) || Map.class.isAssignableFrom(clazz);
    }

    /**
     * Determine if the object needs wrap
     *
     * @param obj object
     * @return need wrap
     */
    public static boolean needWrapper(Object obj) {
        return needWrapper(obj.getClass());
    }

    public static class Wrapper<T> {
        private final T data;

        public Wrapper(T data) {
            this.data = data;
        }

        public T getData() {
            return data;
        }
    }
}
