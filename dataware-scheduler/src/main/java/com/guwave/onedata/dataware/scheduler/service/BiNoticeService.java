package com.guwave.onedata.dataware.scheduler.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotStockingDetail;
import com.guwave.onedata.dataware.scheduler.model.key.BiRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.util.Map;

@Component
public class BiNoticeService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BiNoticeService.class);

    private static final MultiValueMap<String, String> headMap = new LinkedMultiValueMap<String, String>() {{
        add("Content-Type", "application/json;charset=UTF-8");
    }};

    @Value("${spring.noticeBi.open}")
    private Boolean open;
    @Value("${spring.noticeBi.url}")
    private String url;
    @Value("${spring.noticeBi.headers}")
    private String headers;

    @Autowired
    private RestTemplate restTemplate;

    @PostConstruct
    @SuppressWarnings("unchecked")
    public void init() {
        Map<String, String> map = JSONObject.parseObject(headers, Map.class);
        map.forEach(headMap::add);
    }

    public void requestBi(LotStockingDetail lotStockingDetail) {
        if (open) {
            BiRequest biRequest = new BiRequest()
                    .setCustomer(lotStockingDetail.getCustomer())
                    .setSubCustomer(lotStockingDetail.getSubCustomer())
                    .setDeviceId(lotStockingDetail.getDeviceId())
                    .setFactory(lotStockingDetail.getFactory())
                    .setFactorySite(lotStockingDetail.getFactorySite())
                    .setTestStage(lotStockingDetail.getTestStage())
                    .setWaferId(lotStockingDetail.getWaferId());
            HttpEntity<BiRequest> biRequestHttpEntity = new HttpEntity<>(biRequest, headMap);
            LOGGER.info("开始请求 : url: {}, headers: {}, request: {}", url, JSON.toJSONString(biRequestHttpEntity.getHeaders()), JSON.toJSONString(biRequestHttpEntity.getBody()));
            try {
                ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url, biRequestHttpEntity, JSONObject.class);
                JSONObject res = responseEntity.getBody();
                LOGGER.info("请求结束 : url: {}, headers: {}, request: {}, res: {}", url, JSON.toJSONString(biRequestHttpEntity.getHeaders()), JSON.toJSONString(biRequestHttpEntity.getBody()), res);
            } catch (Exception e) {
                LOGGER.info("请求异常 : url: {} ,", url, e);
            }
        }
    }
}
