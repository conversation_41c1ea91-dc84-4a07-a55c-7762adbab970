package com.guwave.onedata.dataware.scheduler.service;

import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.MesDataLossRecord;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.MesParseLog;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.MesDataLossRecordRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.MesParseLogRepository;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class MesDataLossExpireScheduleService {
    private static final Logger LOGGER = LoggerFactory.getLogger(MesDataLossExpireScheduleService.class);

    @Value("${spring.mesDataLossRecord.expireDay}")
    private Long mesDataLossRecordExpireDay;

    @Autowired
    private MesParseLogRepository mesParseLogRepository;
    @Autowired
    private MesDataLossRecordRepository mesDataLossRecordRepository;

    @ScheduleSwitch
    @Scheduled(fixedDelayString = "${spring.scheduler.polling.milliseconds}")
    public void runTask() {
        dealMesDataLossExpire();
    }

    private void dealMesDataLossExpire() {
        long expireTime = System.currentTimeMillis() - (mesDataLossRecordExpireDay * 24L * 60L * 60L * 1000L);
        Date expireDate = new Date(expireTime);
        List<MesDataLossRecord> mesDataLossRecords = mesDataLossRecordRepository.findAllByProcessStatusAndExpireDealFlagAndCreateTimeLessThan(
                ProcessStatus.CREATE, 0, expireDate, Pageable.ofSize(2000)
        );
        LOGGER.info("{} 过期 {} 条MesDataLossRecord", expireDate, mesDataLossRecords.size());

        if (CollectionUtils.isEmpty(mesDataLossRecords)) {
            return;
        }

        Date date = new Date();
        mesDataLossRecords.forEach(t -> t.setExpireDealFlag(1).setUpdateTime(date));
        mesDataLossRecordRepository.saveAll(mesDataLossRecords);

        // 记录 mes_parse_log
        List<MesParseLog> mesParseLogs = mesDataLossRecords.stream().map(t -> new MesParseLog()
                .setCustomer(t.getCustomer())
                .setSubCustomer(t.getSubCustomer())
                .setFactory(t.getFactory())
                .setFactorySite(t.getFactorySite())
                .setTestArea(t.getTestArea())
                .setDeviceId(t.getDeviceId())
                .setLotType(t.getLotType())
                .setTestStage(t.getTestStage())
                .setLotId(t.getLotId())
                .setWaferNo(t.getWaferNo())
                .setProcessStatus(ProcessStatus.CREATE)
                .setComment(DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss") + "\tmesDataLossRecord 过期的结果\n")
                .setCreateTime(date)
                .setUpdateTime(date)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateUser(Constant.SYSTEM)
        ).collect(Collectors.toList());
        mesParseLogRepository.saveAll(mesParseLogs);
    }

}
