plugins {
  id 'application'
}

description = 'dataware source agent wat'

dependencies {
  implementation project(':dataware-source:dataware-source-agent:dataware-source-agent-common')
  implementation project(':dataware-bridge:dataware-bridge-api')

  implementation enforcedPlatform("org.springframework.boot:spring-boot-dependencies:$springBootVersion")
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter'
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter-data-redis'
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter-data-jpa'
  implementation group: 'org.springframework.kafka', name: 'spring-kafka', version: springKafkaVersion
  implementation group: 'com.alibaba', name: 'easyexcel', version: easyExcelVersion
  implementation group: 'mysql', name: 'mysql-connector-java', version: mysqlVersion
  implementation group: 'joda-time', name: 'joda-time', version: jodaTimeVersion
  implementation group: 'org.apache.dubbo', name: 'dubbo-spring-boot-starter', version: dubboVersion
  implementation group: 'org.apache.curator', name: 'curator-framework', version: curatorVersion
  implementation group: 'org.apache.curator', name: 'curator-x-discovery-server', version: curatorVersion
  implementation group: 'com.guwave.bigbrother', name: 'skyeye-driver-logback', version: skyeyeVersion

  implementation("com.guwave.gdp:common:$gdpCommonVersion") {
    transitive = false
  }

  testImplementation group: 'org.springframework.boot', name: 'spring-boot-starter-test'
}

configurations {
  compile.exclude group: 'log4j', module: 'log4j'
  compile.exclude group: 'org.hibernate.validator', module: 'hibernate-validator'
  compile.exclude group: 'org.apache.tomcat.embed', module: 'tomcat-embed-websocket'
  compile.exclude group: 'org.slf4j', module: 'slf4j-log4j12'
}

compileJava {
  options.compilerArgs = ["-parameters"]
}

jar {
  enabled true
  manifest.attributes 'Main-Class': 'com.guwave.onedata.dataware.source.agent.wat.Application'
}

distributions {
  main {
    contents {
      from('src/main/resources/shell/startup.sh') {
        into '.'
      }
    }
  }
}

application {
  mainClassName = 'com.guwave.onedata.dataware.source.agent.wat.Application'
  applicationDistribution.from('src/main/resources/properties').into('properties')
}

startScripts {
  doLast {
    unixScript.text = unixScript.text.replaceAll(":\\\$APP_HOME/lib/(.*)\n", ":\\\$APP_HOME/lib/\\*\n")
  }
}
