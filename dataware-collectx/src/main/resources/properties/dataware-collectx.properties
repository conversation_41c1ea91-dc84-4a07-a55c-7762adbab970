module.name=dataware-collectx

# database config
database.address=riot43:3306
database.name=onedata
database.username=bi
database.password=bi@guwave

# 定时任务个数
task.scheduling.pool.size=15

# 缓存拉取文件任务的cron
scheduler.timer.pushSftpCollectTask=1 0/2 * * * ?
# 执行拉取文件任务的cron
scheduler.timer.pullSftpCollectTask=* * * * * ?
# 拉取文件的线程数
pullTask.convertCorePoolSize=2
# 文件转换的cron
scheduler.timer.convertData=1/5 * * * * ?
# 本地处理转换线程数
convert.convertCorePoolSize=10

# 缓存备份文件任务的cron
scheduler.timer.pushSftpBackupTask=1 0/5 * * * ?
# 执行备份文件任务的cron
scheduler.timer.pullSftpBackupTask=* * * * * ?
# 执行备份文件任务的线程数
pullBackupTask.backupCorePoolSize=1

# redis config
redis.host=riot41.guwave.com
redis.port=6379
redis.database=7
redis.password=devops@guwave
handler.lockExpireTime=86400

# kafka config
kafka.bootstrapServers=riot12.guwave.com:6667,riot13.guwave.com:6667,riot14.guwave.com:6667
kafka.producer.batchSize=104857600
kafka.producer.lingerMs=0
kafka.producer.bufferMemory=104857600
kafka.loadEndFlagTopic=t_dw_load_end_flag
kafka.sleepMilliseconds=0
kafka.producer.retries=3
kafka.producer.properties.retry.backoff.ms=1000

# hdfs模式：
#           HA : 此模式时 gdp.file.hdfsUrl 需要配两个地址，用英文逗号隔开
#           STANDALONE : 此模式时 gdp.file.hdfsUrl 只需要配一个地址
gdp.file.hdfsMode=HA
gdp.file.hdfsUrl=hdfs://riot11.guwave.com:8020,hdfs://riot12.guwave.com:8020
gdp.file.hdfsUser=glory
# /user/glory/guwave/ftp/customer/testArea/factory/fileCategory/mTime/
gdp.file.hdfsRootPath=/user/glory/guwave/ftp/%s/%s/%s/%s/%s/

#本地存储文件路径
sftp.localRootPath=/home/<USER>/deploy/onedata/dataware/dataware-collectx/

# python安装路径
adapter.python.install.path=/usr/bin/python3

# zipSplit等待其他分卷文件的最长时间（min）
zipSplit.convert.zipSplitFileMaxWaitMillion=5

# rar tmpDirectory
rar.tmpDirectory=/home/<USER>/deploy/onedata/dataware/dataware-collectx/rar_tmp/

# License config
licenseBaseUrl=http://riot82.guwave.com:8091

# max fail cnt
backup.maxFailCnt=5

# dubbo config
zookeeper.address=riot12.guwave.com:2181,riot13.guwave.com:2181,riot14.guwave.com:2181
environment.group=prod
# rpc timeout (ms)
rpc.timeout=60000

# 立即运行拉取文件的线程数
immediatelyRunTask.convertCorePoolSize=1