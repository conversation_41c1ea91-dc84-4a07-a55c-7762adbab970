package com.guwave.onedata.dataware.collectx.manager.thread;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpCollectRule;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SftpCollectRuleThreadManager {

    private static final ThreadLocal<SftpCollectRule> sftpCollectRuleThreadLocal = new ThreadLocal<>();

    public static SftpCollectRule getSftpCollectRule() {
        return sftpCollectRuleThreadLocal.get();
    }

    public static void setSftpCollectRule(SftpCollectRule sftpCollectRule) {
        sftpCollectRuleThreadLocal.set(sftpCollectRule);
    }

    public static void clear() {
        sftpCollectRuleThreadLocal.remove();
    }

}
