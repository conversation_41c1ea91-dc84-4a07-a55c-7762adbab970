package com.guwave.onedata.dataware.source.agent.common.handler;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotMetaDataDetail;
import com.guwave.onedata.dataware.parser.stdf.model.BinDefinition;
import com.guwave.onedata.dataware.parser.stdf.serialization.RawDataReader;
import com.guwave.onedata.dataware.parser.stdf.visitor.Visitor;
import com.guwave.onedata.dataware.source.agent.common.visitor.MultiThreadVisitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;

public interface RawDataParseHandler extends DataParseHandler {
    Logger LOGGER = LoggerFactory.getLogger(RawDataParseHandler.class);

    default List<Visitor> parseAndSendData(LotMetaDataDetail lotMetaDataDetail, String localStdfPath) {
        // 解析raw_data
        List<BinDefinition> binDefinitions = getBinDefinitions(lotMetaDataDetail);
        Map<String, String> customerSettingMap = getCustomerSettingMap(lotMetaDataDetail);
        List<Visitor> visitors = new ArrayList<>();
        File localFile = new File(localStdfPath);
        int allThreadCnt = 1;
        for (int i = 1; i <= allThreadCnt; i++) {
            visitors.add(new MultiThreadVisitor(i, getHdfsUtil(), getOdsHdfsTemplatePath(), localFile, generateFillFileMainDataConsumer(lotMetaDataDetail, binDefinitions), allThreadCnt, getBatchSize(), getCkSinkMap(), getEcidRuleRepository(), getUidRuleRepository(), getLotRelationSyncRepository(), null, null, null));
        }

        try {
            Visitor visitor = visitors.get(0);
            new RawDataReader(localStdfPath, visitor, false);
        } finally {
            visitors.forEach(t -> {
                MultiThreadVisitor visitor = (MultiThreadVisitor) t;
                visitor.close();
            });
        }

        return visitors;
    }
}
