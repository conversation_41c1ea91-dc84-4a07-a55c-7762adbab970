module.name=dataware-scheduler
# database config
database.address=riot41.guwave.com:3307
database.name=onedata_dev
database.username=bi
database.password=bi@guwave

# ck config
data.clickhouse.address=****************************************,****************************************,****************************************
data.clickhouse.username=admin
data.clickhouse.password=admin@ck@Guwave
data.clickhouse.cluster=cluster_3shards_1replicas
sink.ck.ods.dbName=ods
sink.ck.dwd.dbName=dwd
data.clickhouse.dwdDbName=dwd
data.clickhouse.dwsDbName=dws
data.clickhouse.maxMutationsCnt=500

# kafka config
kafka.bootstrapServers=riot12.guwave.com:6667,riot13.guwave.com:6667,riot14.guwave.com:6667
kafka.consumer.consumeGroup=OnedataDatawareGdpScheduler
kafka.consumer.autoOffsetReset=earliest
kafka.consumer.autoCommitInterval=1000
kafka.consumer.maxPollRecords=10
kafka.listener.concurrency=1
kafka.calculateEndFlagTopic=t_dw_calculate_end_flag
kafka.calculatePatchFileTopic=t_dw_calculate_patch_file
kafka.loadEndFlagTopic=t_dw_load_end_flag
kafka.repairFinishTopic=t_dw_repair_finish
kafka.repairRecalculateTopic=t_dw_repair_recalculate
kafka.manualFinishTopic=t_dw_manual_warehousing_finish
kafka.computeResultTopic=t_compute_result
kafka.ruleCalculateEndFlagTopic=t_rule_calculate_end_flag
kafka.testItemFinishTopic=t_dw_test_item_finish
kafka.ymsCalculateEndFlagTopic=t_yms_calculate_end_flag
kafka.producer.batchSize=104857600
kafka.producer.lingerMs=0
kafka.producer.bufferMemory=104857600

# redis config
redis.host=riot41.guwave.com
redis.port=6380
redis.database=5
redis.password=devops@guwave
redis.lockExpireTime=86400
redis.unlockExpireTime=60

# buffer pool
spark.buffer.pool.allMaxRunningSize=30
spark.buffer.pool.odsDelaySecond=0
# manual buffer pool
spark.buffer.pool.manualCpDwdMaxRunningSize=10
spark.buffer.pool.manualFtDwdMaxRunningSize=10
spark.buffer.pool.manualAllMaxRunningSize=15

# scheduling
task.scheduling.pool.size=11
scheduler.polling.milliseconds=5000

# noticeBi
noticeBi.open=false
noticeBi.url=/v1/waferMap/thumbnail/generate
noticeBi.headers={"appId":"data","secret":"FAE031E0458BCA36C411F127F40359EA"}

# mesRequest
mesRequest.open=false
mesRequest.url=http://*************:9999/system/api/queryTestData

# mesDataLossRecord过期天数
mesDataLossRecord.expireDay=1

# run mode
runMode.standaloneThreshold=0
runMode.manualThreshold=40000000000

# checkCkSinkTimeout
ck.checkCkSinkTimeout=50000
handler.sink.batchSize=20000

# dubbo config
zookeeper.address=riot12.guwave.com:2181,riot13.guwave.com:2181,riot14.guwave.com:2181
# qa deploy fill with   qa    ,otherwise fill with    prod
environment.group=prod
# rpc timeout (ms)
rpc.timeout=60000
