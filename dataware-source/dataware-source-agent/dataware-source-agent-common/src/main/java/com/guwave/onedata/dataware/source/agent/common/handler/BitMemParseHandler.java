package com.guwave.onedata.dataware.source.agent.common.handler;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotMetaDataDetail;
import com.guwave.onedata.dataware.parser.stdf.model.BinDefinition;
import com.guwave.onedata.dataware.parser.stdf.serialization.BitMemReader;
import com.guwave.onedata.dataware.parser.stdf.visitor.Visitor;
import com.guwave.onedata.dataware.source.agent.common.visitor.MultiThreadVisitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/22
 * @description BitMemParseHandler
 */

public interface BitMemParseHandler extends DataParseHandler {
    Logger LOGGER = LoggerFactory.getLogger(BitMemParseHandler.class);

    @Override
    default List<Visitor> parseAndSendData(LotMetaDataDetail lotMetaDataDetail, String localStdfPath) {
        // 解析bit_mem
        List<BinDefinition> binDefinitions = getBinDefinitions(lotMetaDataDetail);
        Map<String, String> customerSettingMap = getCustomerSettingMap(lotMetaDataDetail);
        List<Visitor> visitors = new ArrayList<>();
        File localFile = new File(localStdfPath);
        int allThreadCnt = 1;
        for (int i = 1; i <= allThreadCnt; i++) {
            visitors.add(new MultiThreadVisitor(i, getHdfsUtil(), getOdsHdfsTemplatePath(), localFile, generateFillFileMainDataConsumer(lotMetaDataDetail, binDefinitions), allThreadCnt, getBatchSize(), getCkSinkMap(), null, null, null, null, null, null));
        }

        try {
            Visitor visitor = visitors.get(0);
            new BitMemReader(localStdfPath, visitor, false);
        } finally {
            visitors.forEach(t -> {
                MultiThreadVisitor visitor = (MultiThreadVisitor) t;
                visitor.close();
            });
        }

        return visitors;
    }

}
