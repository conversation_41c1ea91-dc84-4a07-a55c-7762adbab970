package com.guwave.onedata.dataware.source.agent.common.fetcher;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.ExceptionType;
import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.contant.FileType;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.SftpBatchStatus;
import com.guwave.onedata.dataware.common.contant.StepType;
import com.guwave.onedata.dataware.common.contant.TransferStatus;
import com.guwave.onedata.dataware.common.contant.UploadType;
import com.guwave.onedata.dataware.common.contant.WarehousingMode;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.meta.PriorityKey;
import com.guwave.onedata.dataware.dao.mysql.domain.dc.FileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import com.guwave.onedata.dataware.dao.mysql.manager.TaskPriorityManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.*;
import com.guwave.onedata.dataware.repair.common.service.DataRepairService;
import com.guwave.onedata.dataware.source.agent.common.exception.FailException;
import com.guwave.onedata.dataware.source.agent.common.exception.NotFailException;
import com.guwave.onedata.dataware.source.agent.common.handler.PreHandler;
import com.guwave.onedata.dataware.source.agent.common.service.FileProcessProgressService;
import com.guwave.onedata.dataware.source.agent.common.util.FileUtil;
import com.guwave.onedata.dataware.source.agent.common.util.ProcessLogThreadLocalUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Copyright (C), 2022, guwave
 * <p>
 * CommonFetcher
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2022-04-27 15:41:06
 */
public interface MultiFilePreFetcher extends CommonFetcher {
    Logger LOGGER = LoggerFactory.getLogger(MultiFilePreFetcher.class);

    default void fetch(PreHandler preHandler) {

        // 查询待解析的文件
        List<SftpFileDetail> files = preHandler.getNeedPreDealSftpFiles();

        if (CollectionUtils.isEmpty(files)) {
            LOGGER.info("没有待处理的文件");
            return;
        }

        SftpFileDetail fileDetail = files.get(0);
        LOGGER.info("find {} to process", fileDetail.getLocalFileName());
        // 先把更新时间更新
        fileDetail
                .setUpdateTime(new Date())
                .setUpdateUser(Constant.SYSTEM);
        getSftpFileDetailRepository().updateUpdateTime(fileDetail.getId(), fileDetail.getUpdateTime(), fileDetail.getUpdateUser(), fileDetail.getProcessStatus());

        // 上锁文件的原始远程目录
        boolean lockOriginFilePath = getFileProcessProgressService().lockOriginFilePath(fileDetail.getCustomer(), fileDetail.getTestArea(), fileDetail.getFactory(), fileDetail.getFactorySite(), fileDetail.getRemoteOriginalFilePath());
        if (!lockOriginFilePath) {
            // 上锁失败
            return;
        }

        // 上锁成功
        try {
            fileDetail = getSftpFileDetailRepository().findById(fileDetail.getId()).orElse(fileDetail);
            if (fileDetail.getProcessStatus() != ProcessStatus.CREATE) {
                LOGGER.info(" {} 已被处理", fileDetail.getLocalFileName());
                return;
            }

            // 根据文件的原始远程目录查询同一目录下的所有文件
            List<SftpFileDetail> sftpFileDetails = getSftpFileDetailRepository().findAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndFileCategoryAndTransferStatusAndProcessStatusAndConvertFlagAndBatchStatusAndRemoteOriginalFilePathAndFtpIp(
                    fileDetail.getCustomer(),
                    fileDetail.getTestArea(),
                    fileDetail.getFactory(),
                    fileDetail.getFactorySite(),
                    fileDetail.getFileCategory(),
                    TransferStatus.SUCCESS,
                    ProcessStatus.CREATE,
                    0,
                    SftpBatchStatus.SUCCESS,
                    fileDetail.getRemoteOriginalFilePath(),
                    fileDetail.getFtpIp(),
                    Pageable.ofSize(2000)
            );
            // 更新处理状态
            Date date = new Date();
            sftpFileDetails.forEach(t -> t.setProcessStatus(ProcessStatus.PROCESSING).setUpdateTime(date));
            getSftpFileDetailRepository().saveAll(sftpFileDetails);

            List<String> fileNames = sftpFileDetails.stream().map(SftpFileDetail::getLocalFileName).collect(Collectors.toList());
            List<FileLoadingLog> lastStepFileLoadingLogs = getFileLoadingLogRepository().findAllByFileNameInAndStep(
                    fileNames,
                    StepType.STEP_TYPE_1000.getStep()
            );
            Map<String, FileLoadingLog> lastStepFileLoadingLogMap = lastStepFileLoadingLogs.stream().collect(Collectors.toMap(FileLoadingLog::getFileName, t -> t, (v1, v2) -> v1));

            ArrayList<LotMetaDataDetail> needSavingData = new ArrayList<>();
            ArrayList<FileLoadingLog> fileLoadingLogs = new ArrayList<>();
            for (SftpFileDetail sftpFileDetail : sftpFileDetails) {
                long startTime = System.currentTimeMillis();
                Date stepStartTime = new Date();
                // 生成fileLoadingLog
                FileLoadingLog fileLoadingLog = insertFileLoadingLog(sftpFileDetail, stepStartTime, lastStepFileLoadingLogMap);
                getFileWarehousingRecordManager().updateFileWarehousingStatus(Collections.singletonList(fileLoadingLog));
                fileLoadingLogs.add(fileLoadingLog);

                // 同一个目录下的文件一起读取
                ProcessLog processLog = null;
                FileInfo fileInfo = null;
                try {
                    // 生成处理日志
                    processLog = generateProcessLog(sftpFileDetail);
                    ProcessLogThreadLocalUtil.setLocal(processLog);
                    fileInfo = getFileInfoRepository().findByFileNameAndUploadTypeAndDeleteFlag(sftpFileDetail.getLocalFileName(), UploadType.AUTO, Boolean.FALSE);
                    if (fileInfo == null) {
                        // 生成数据中心处理文件
                        fileInfo = generateFileInfo(sftpFileDetail);
                    }

                    if (fileDetail.getWarehousingMode() == WarehousingMode.REPAIR) {
                        List<RepairRecord> repairRecords = getRepairRecordRepository().findAllByRepairLotWaferIdAndFileName(fileDetail.getRepairLotWaferId(), fileDetail.getLocalFileName());
                        if (CollectionUtils.isEmpty(repairRecords)) {
                            LOGGER.error("没有待处理的修复记录");
                            throw new FileLoadException(FileLoadExceptionInfo.REPAIR_RECORD_NOT_FOUND_EXCEPTION, FileLoadExceptionInfo.REPAIR_RECORD_NOT_FOUND_EXCEPTION.getMessage(), null);
                        }
                    }

                    // 下载并解压文件
                    downloadAndUncompressHdfsFile(sftpFileDetail);
                    // 获取解压后的文件
                    File uncompressFile = new File(getReadPath(), FileUtil.removeFileSuffix(sftpFileDetail.getLocalFileName(), FileType.ZIP));
                    if (!uncompressFile.exists()) {
                        LOGGER.error("找不到解压后的文件 : {}", uncompressFile.getAbsolutePath());
                        throw new FailException("文件解压失败！,找不到解压后的文件");
                    }

                    // 读取文件
                    LotMetaDataDetail lotMetaDataDetail = preHandler.preDealFile(uncompressFile, sftpFileDetail, fileInfo, processLog, fileLoadingLog);

                    // 新的normal入库记录
                    getDataRepairService().saveNormalLotWaferWarehousingRecord(lotMetaDataDetail, ProcessStatus.PROCESSING, Collections.singletonList(fileInfo.getId()), getModuleName());

                    needSavingData.add(lotMetaDataDetail);
                    // 设置文件处理状态 -->  成功
                    sftpFileDetail.setProcessStatus(ProcessStatus.SUCCESS);
                    //fileLoadingLog成功
                    fileLoadingLog.setProcessStatus(ProcessStatus.SUCCESS);
                } catch (Exception e) {
                    updateFileLoadingLogStatusAfterDealException(fileLoadingLog, e);

                    if (e instanceof NotFailException) {
                        LOGGER.info("{} ：{}", sftpFileDetail.getLocalFileName(), e.getMessage());
                        ProcessLogThreadLocalUtil.appendErrorMessage(e.getMessage());
                    } else {
                        if (e instanceof NumberFormatException) {
                            updateFileLoadingLogStatusAfterDealException(fileLoadingLog, new FileLoadException(FileLoadExceptionInfo.NOT_A_NUMBER_EXCEPTION, ExceptionUtils.getStackTrace(e), null));
                        }
                        LOGGER.error("处理文件{}异常：", sftpFileDetail.getLocalFileName(), e);
                        // 设置文件处理状态 -->  失败
                        sftpFileDetail.setProcessStatus(ProcessStatus.FAIL);
                        updateLotWaferWarehousingRecordAfterDealFail(getRepairRecordRepository(), getLotWaferWarehousingRecordRepository(), getDataRepairService(), fileLoadingLog, e);
                        if (processLog != null) {
                            processLog.setExceptionType(e instanceof FileLoadException ? ((FileLoadException) e).getExceptionType() : ExceptionType.OTHER_EXCEPTION);
                        }
                        ProcessLogThreadLocalUtil.appendErrorMessage(ExceptionUtils.getStackTrace(e));
                    }
                } finally {
                    // 清除threadLocal
                    ProcessLogThreadLocalUtil.clearLocal();

                    Date updateTime = new Date();

                    // 更新sftpFileDetail
                    if (sftpFileDetail.getProcessStatus() == ProcessStatus.PROCESSING) {
                        //  状态还是processing 时， 设置文件的状态 --> CREATE 等待下次重试
                        sftpFileDetail.setProcessStatus(ProcessStatus.CREATE);
                    }
                    sftpFileDetail.setUpdateTime(updateTime);
                    fileLoadingLog.setStepEndTime(updateTime)
                            .setUpdateTime(updateTime);
                    // 更新process_log和fileInfo
                    afterOneFileDeal(processLog, fileInfo, sftpFileDetail.getProcessStatus(), sftpFileDetail.getLocalFileName(), startTime);
                }
            }

            // 更新处理结果
            updatePreStepFileLoadingLogs(fileLoadingLogs, lastStepFileLoadingLogMap);
            getFileLoadingLogRepository().saveAll(fileLoadingLogs);
            getFileWarehousingRecordManager().updateFileWarehousingStatus(fileLoadingLogs);
            // 发送结束消息
            fileLoadingLogs.stream().filter(t -> t.getProcessStatus() == ProcessStatus.FAIL).forEach(t -> getKafKaSink().sendLoadEndFlagMessage(t));
            getTaskPriorityManager().applyLotMetaData(needSavingData);
            getSftpFileDetailRepository().saveAll(sftpFileDetails);
        } catch (Exception e) {
            if (e instanceof NotFailException) {
                LOGGER.info("{} ：{}", fileDetail.getLocalFileName(), e.getMessage());
            } else {
                LOGGER.error("处理文件{}异常：", fileDetail.getLocalFileName(), e);
            }
        } finally {
            // 解锁文件目录
            getFileProcessProgressService().unlockOriginFilePath(fileDetail.getCustomer(), fileDetail.getTestArea(), fileDetail.getFactory(), fileDetail.getFactorySite(), fileDetail.getRemoteOriginalFilePath());
        }
    }

    default void updatePreStepFileLoadingLogs(ArrayList<FileLoadingLog> fileLoadingLogs, Map<String, FileLoadingLog> preStepFileLoadingLogMap) {
        Date date = new Date();
        List<FileLoadingLog> preStepFileLoadingLogs = fileLoadingLogs.stream().map(t -> {
                    FileLoadingLog preStepFileLoadingLog = preStepFileLoadingLogMap.get(t.getFileName());
                    if (preStepFileLoadingLog != null) {
                        preStepFileLoadingLog.setLotId(t.getLotId());
                        preStepFileLoadingLog.setSblotId(t.getSblotId());
                        preStepFileLoadingLog.setDeviceId(t.getDeviceId());
                        preStepFileLoadingLog.setWaferId(t.getWaferId());
                        preStepFileLoadingLog.setWaferNo(t.getWaferNo());
                        preStepFileLoadingLog.setLotType(t.getLotType());
                        preStepFileLoadingLog.setTesterName(t.getTesterName());
                        preStepFileLoadingLog.setTestStage(t.getTestStage());
                        preStepFileLoadingLog.setTestArea(t.getTestArea());
                        preStepFileLoadingLog.setTesterType(t.getTesterType());
                        preStepFileLoadingLog.setTestProgram(t.getTestProgram());
                        preStepFileLoadingLog.setTestProgramVersion(t.getTestProgramVersion());
                        preStepFileLoadingLog.setStartT(t.getStartT());
                        preStepFileLoadingLog.setFinishT(t.getFinishT());
                        preStepFileLoadingLog.setUpdateTime(date);
                    }
                    return preStepFileLoadingLog;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
        getFileLoadingLogRepository().saveAll(preStepFileLoadingLogs);
    }

    default FileLoadingLog insertFileLoadingLog(SftpFileDetail sftpFileDetail, Date stepStartTime, Map<String, FileLoadingLog> lastStepFileLoadingLogMap) {
        Date date = new Date();
        FileLoadingLog fileLoadingLog = new FileLoadingLog();

        fileLoadingLog
                .setCustomer(sftpFileDetail.getCustomer())
                .setSubCustomer(sftpFileDetail.getSubCustomer())
                .setTestArea(sftpFileDetail.getTestArea())
                .setFactory(sftpFileDetail.getFactory())
                .setFactorySite(sftpFileDetail.getFactorySite())
                .setFab(sftpFileDetail.getFab())
                .setFabSite(sftpFileDetail.getFabSite())
                .setFileCategory(sftpFileDetail.getFileCategory())
                .setFileName(sftpFileDetail.getLocalFileName()) //
                .setOriginFileName(sftpFileDetail.getOriginFileName())
                .setConvertFlag(sftpFileDetail.getConvertFlag())
                .setHdfsPath(sftpFileDetail.getHdfsFilePath())
                .setRemoteFileMtime(sftpFileDetail.getRemoteFileMtime())
                .setStep(StepType.STEP_TYPE_3100.getStep())
                .setStepStartTime(stepStartTime)
                .setFileSize(sftpFileDetail.getFileSize())
                .setOriginFileSize(sftpFileDetail.getOriginFileSize())
                .setFtpIp(sftpFileDetail.getFtpIp())
//                .setStepEndTime()
//                .setDeviceId()
//                .setLotId()
//                .setWaferId()
//                .setWaferNo()
//                .setTestStage()
//                .setTesterName()
//                .setTesterType()
//                .setTestProgram()
//                .setTestProgramVersion()
//                .setStartT()
//                .setFinishT()
                .setWarehousingMode(sftpFileDetail.getWarehousingMode())
                .setRepairLotWaferId(sftpFileDetail.getRepairLotWaferId())
                .setCleanupTaskIds(sftpFileDetail.getCleanupTaskIds())
                .setProcessStatus(ProcessStatus.PROCESSING)
//                .setExceptionType()
//                .setErrorMessage()
                .setCreateTime(stepStartTime)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateTime(date)
                .setUpdateUser(Constant.SYSTEM);

        // 查询上一step数据，补充内容
        FileLoadingLog lastStepFileLoadingLog = lastStepFileLoadingLogMap.get(fileLoadingLog.getFileName());
        if (lastStepFileLoadingLog != null) {
            fileLoadingLog
                    .setSourceFileNames(lastStepFileLoadingLog.getSourceFileNames()) //
                    .setFtpPath(lastStepFileLoadingLog.getFtpPath())
                    .setDataIntegrityFileComment(lastStepFileLoadingLog.getDataIntegrityFileComment())
                    .setDataIntegrityFileLabel(lastStepFileLoadingLog.getDataIntegrityFileLabel())
                    .setWarningMessage(lastStepFileLoadingLog.getWarningMessage())
            ;
        }

        //插入前先删除 当前以及以后的所有行
        getFileLoadingLogRepository().deleteByFileNameAndStep(fileLoadingLog.getFileName(), fileLoadingLog.getStep());

        getFileLoadingLogRepository().save(fileLoadingLog);

        return fileLoadingLog;
    }

    FileProcessProgressService getFileProcessProgressService();

    SftpFileDetailRepository getSftpFileDetailRepository();

    LotMetaDataDetailRepository getLotMetaDataRepository();

    FileLoadingLogRepository getFileLoadingLogRepository();

    RepairRecordRepository getRepairRecordRepository();

    LotWaferWarehousingRecordRepository getLotWaferWarehousingRecordRepository();

    DataRepairService getDataRepairService();

    TaskPriorityManager getTaskPriorityManager();

    String getModuleName();
}
