package com.guwave.onedata.dataware.scheduler.repository;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.model.key.LotWaferPrimaryInputCntKey;
import com.guwave.onedata.dataware.common.model.key.LotWaferPrimaryWaferLotIdKey;
import com.guwave.onedata.dataware.scheduler.provider.CkProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 2023/7/5 16:38
 * DwsFlowidBinIndex
 * <AUTHOR>
 */
@Component
public class DieDetailRepository {

    private static final Logger LOGGER = LoggerFactory.getLogger(DieDetailRepository.class);


    @Autowired
    private CkProvider ckProvider;

    List<TestArea> SUPPORT_CP_TEST_AREA_LIST = TestArea.getCPList();

    private static final String SQL_TEMPLATE = "select count(1) from {DB_NAME}.dwd_die_detail_cluster where IS_DELETE = '0' " +
            "and CUSTOMER = '{CUSTOMER}' and UPLOAD_TYPE = 'AUTO' and TEST_AREA = '{TEST_AREA}' and FACTORY = '{FACTORY}' " +
            "and DEVICE_ID = '{DEVICE_ID}' " +
            "and LOT_TYPE = '{LOT_TYPE}' " +
            "and TEST_STAGE = '{TEST_STAGE}' " +
            "and LOT_ID = '{LOT_ID}'";
    private final String WAFER_NO_TEMPLATE = " and WAFER_NO = '{WAFER_NO}'";

    private static final String WAFER_LOT_ID_SQL_TEMPLATE = "select " +
            "distinct CUSTOMER customer,SUB_CUSTOMER subCustomer, FACTORY factory, FACTORY_SITE factorySite, TEST_AREA testArea, DEVICE_ID deviceId, LOT_ID, SBLOT_ID sblotId, WAFER_LOT_ID waferLotId, WAFER_NO waferNo, IS_STANDARD_ECID isStandardEcid " +
            "from {DB_NAME}.dwd_die_detail_cluster where IS_DELETE = '0' " +
            "and CUSTOMER = '{CUSTOMER}' and SUB_CUSTOMER = '{SUB_CUSTOMER}' and UPLOAD_TYPE = 'AUTO' and TEST_AREA = '{TEST_AREA}' and FACTORY = '{FACTORY}' " +
            "and DEVICE_ID = '{DEVICE_ID}' " +
            "and LOT_TYPE = '{LOT_TYPE}' " +
            "and TEST_STAGE = '{TEST_STAGE}' " +
            "and LOT_ID = '{LOT_ID}'";

    private static final String FT_INPUT_SQL_TEMPLATE = "select " +
            "CUSTOMER customer, SUB_CUSTOMER subCustomer, FACTORY factory, FACTORY_SITE factorySite, TEST_AREA testArea, DEVICE_ID deviceId, LOT_ID lotId, SBLOT_ID sblotId, count(distinct ECID) count " +
            "from {DB_NAME}.dwd_die_detail_cluster where IS_DELETE = '0' and IS_DUP_FINAL_TEST_IGNORE_TP = '1' and IS_FIRST_TEST_IGNORE_TP = '1' " +
            "and CUSTOMER = '{CUSTOMER}' and SUB_CUSTOMER = '{SUB_CUSTOMER}' and UPLOAD_TYPE = 'AUTO' and TEST_AREA = '{TEST_AREA}' and FACTORY = '{FACTORY}' " +
            "and DEVICE_ID = '{DEVICE_ID}' " +
            "and LOT_TYPE = '{LOT_TYPE}' " +
            "and TEST_STAGE = '{TEST_STAGE}' " +
            "and LOT_ID = '{LOT_ID}' " +
            "group by CUSTOMER, SUB_CUSTOMER, FACTORY, FACTORY_SITE, TEST_AREA , DEVICE_ID, LOT_ID, SBLOT_ID";

    private static final String CP_INPUT_SQL_TEMPLATE = "select " +
            "CUSTOMER customer, SUB_CUSTOMER subCustomer, FACTORY factory, FACTORY_SITE factorySite, TEST_AREA testArea, DEVICE_ID deviceId, LOT_ID lotId, count(distinct ECID) count " +
            "from {DB_NAME}.dwd_die_detail_cluster where  IS_DELETE = '0' and IS_DUP_FINAL_TEST_IGNORE_TP = '1' " +
            "and CUSTOMER = '{CUSTOMER}' and SUB_CUSTOMER = '{SUB_CUSTOMER}' and UPLOAD_TYPE = 'AUTO' and TEST_AREA = '{TEST_AREA}' and FACTORY = '{FACTORY}' " +
            "and DEVICE_ID = '{DEVICE_ID}' " +
            "and LOT_TYPE = '{LOT_TYPE}' " +
            "and TEST_STAGE = '{TEST_STAGE}' " +
            "and LOT_ID = '{LOT_ID}' " +
            "and WAFER_NO = '{WAFER_NO}' " +
            "group by CUSTOMER, SUB_CUSTOMER, FACTORY, FACTORY_SITE, TEST_AREA , DEVICE_ID, LOT_ID";

    public Long countData(String dataBase, String customer, String factory, TestArea testArea, String deviceId, LotType lotType, String testStage,  String lotId, String waferNo) {
        String sql = SQL_TEMPLATE.replace(Constant.DB_NAME, dataBase)
                .replace(Constant.CUSTOMER, customer)
                .replace(Constant.TEST_AREA, testArea.getArea())
                .replace(Constant.FACTORY, factory)
                .replace(Constant.DEVICE_ID, deviceId)
                .replace(Constant.LOT_TYPE, lotType.getType())
                .replace(Constant.TEST_STAGE, testStage)
                .replace(Constant.LOT_ID, lotId);
        if (SUPPORT_CP_TEST_AREA_LIST.contains(testArea)) {
            sql = sql + WAFER_NO_TEMPLATE.replace(Constant.WAFER_NO, waferNo);
        }

        LOGGER.info("查询sql: {}", sql);
        return ckProvider.count(sql);
    }

    public List<LotWaferPrimaryWaferLotIdKey> getWaferLotIdList(String dataBase, String customer, String subCustomer, String factory, TestArea testArea, String deviceId, LotType lotType, String testStage,  String lotId) {
        String sql = WAFER_LOT_ID_SQL_TEMPLATE.replace(Constant.DB_NAME, dataBase)
                .replace(Constant.CUSTOMER, customer)
                .replace(Constant.SUB_CUSTOMER, subCustomer)
                .replace(Constant.TEST_AREA, testArea.getArea())
                .replace(Constant.FACTORY, factory)
                .replace(Constant.DEVICE_ID, deviceId)
                .replace(Constant.LOT_TYPE, lotType.getType())
                .replace(Constant.TEST_STAGE, testStage)
                .replace(Constant.LOT_ID, lotId);

        LOGGER.info("查询sql: {}", sql);
        return ckProvider.readCk(sql, LotWaferPrimaryWaferLotIdKey.class);
    }

    public List<LotWaferPrimaryInputCntKey> getInputCount(String dataBase, String customer, String subCustomer, String factory, TestArea testArea, String deviceId, LotType lotType, String testStage, String lotId, String waferNo) {
        String sql = FT_INPUT_SQL_TEMPLATE;
        if (SUPPORT_CP_TEST_AREA_LIST.contains(testArea)) {
            sql = CP_INPUT_SQL_TEMPLATE.replace(Constant.WAFER_NO, waferNo);
        }
        sql = sql.replace(Constant.DB_NAME, dataBase)
                .replace(Constant.CUSTOMER, customer)
                .replace(Constant.SUB_CUSTOMER, subCustomer)
                .replace(Constant.TEST_AREA, testArea.getArea())
                .replace(Constant.FACTORY, factory)
                .replace(Constant.DEVICE_ID, deviceId)
                .replace(Constant.LOT_TYPE, lotType.getType())
                .replace(Constant.TEST_STAGE, testStage)
                .replace(Constant.LOT_ID, lotId);

        LOGGER.info("查询sql: {}", sql);
        return ckProvider.readCk(sql, LotWaferPrimaryInputCntKey.class);
    }
}
