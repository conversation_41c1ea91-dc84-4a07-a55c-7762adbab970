package com.guwave.onedata.dataware.collectx.core;


import com.guwave.onedata.dataware.collectx.entity.FTPFileAttr;

import java.util.List;

public interface IFTPSyncCore {

    String loadFile(String remoteFileName, String localPath) throws Exception;

    void listFTPFileInfo(String absoluteDir, List<FTPFileAttr> fileInfoList) throws Exception;

    FTPFileAttr ftpFileInfo(String absoluteDir, String sourceFileName) throws Exception;

    boolean moveFile(String remoteSourcePath, String remoteTargetPath) throws Exception;

    void mkdir(String remotePath) throws Exception;

    boolean deleteFile(String sourceFilePath) throws Exception;

    boolean uploadFile(String localFilePath, String targetFilePath) throws Exception;

    boolean isConnected();

    void closeConnect();
}
