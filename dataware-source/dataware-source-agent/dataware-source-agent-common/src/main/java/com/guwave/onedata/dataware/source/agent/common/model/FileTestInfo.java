package com.guwave.onedata.dataware.source.agent.common.model;

import java.util.Map;

public class FileTestInfo {
    private Long dieDataCnt;
    private Long testItemDataCnt;
    private Map.Entry<String, Long> testItemCntMin;
    private Map.Entry<String, Long> testItemCntMax;

    public Long getDieDataCnt() {
        return dieDataCnt;
    }

    public FileTestInfo setDieDataCnt(Long dieDataCnt) {
        this.dieDataCnt = dieDataCnt;
        return this;
    }

    public Long getTestItemDataCnt() {
        return testItemDataCnt;
    }

    public FileTestInfo setTestItemDataCnt(Long testItemDataCnt) {
        this.testItemDataCnt = testItemDataCnt;
        return this;
    }

    public Map.Entry<String, Long> getTestItemCntMin() {
        return testItemCntMin;
    }

    public FileTestInfo setTestItemCntMin(Map.Entry<String, Long> testItemCntMin) {
        this.testItemCntMin = testItemCntMin;
        return this;
    }

    public Map.Entry<String, Long> getTestItemCntMax() {
        return testItemCntMax;
    }

    public FileTestInfo setTestItemCntMax(Map.Entry<String, Long> testItemCntMax) {
        this.testItemCntMax = testItemCntMax;
        return this;
    }
}
