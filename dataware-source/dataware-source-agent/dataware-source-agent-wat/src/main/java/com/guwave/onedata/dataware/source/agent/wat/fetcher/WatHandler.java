package com.guwave.onedata.dataware.source.agent.wat.fetcher;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.key.LotWaferPrimaryKey;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import com.guwave.onedata.dataware.common.model.wat.WatCommon;
import com.guwave.onedata.dataware.common.model.wat.dim.*;
import com.guwave.onedata.dataware.common.model.wat.dwd.DwdWatDieDetail;
import com.guwave.onedata.dataware.common.model.wat.dwd.DwdWatTestItemDetail;
import com.guwave.onedata.dataware.common.model.wat.ods.OdsWat;
import com.guwave.onedata.dataware.common.util.WaferUtil;
import com.guwave.onedata.dataware.dao.mysql.domain.dc.FileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileLoadingLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ProcessLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileDetail;
import com.guwave.onedata.dataware.dao.mysql.manager.LotWaferPrimaryDataManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpFileDetailRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.WatProcessRuleRepository;
import com.guwave.onedata.dataware.source.agent.common.exception.FailException;
import com.guwave.onedata.dataware.source.agent.common.exception.NotFailException;
import com.guwave.onedata.dataware.source.agent.common.handler.SingleParseHandler;
import com.guwave.onedata.dataware.source.agent.common.model.Wafer;
import com.guwave.onedata.dataware.source.agent.common.service.FileProcessProgressService;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import com.guwave.onedata.dataware.source.agent.common.util.HdfsUtil;
import com.guwave.onedata.dataware.source.agent.common.util.WatCommonUtil;
import com.guwave.onedata.dataware.source.agent.wat.handler.impl.WatConsistentHandle;
import com.guwave.onedata.dataware.source.agent.wat.sink.HdfsSink;
import com.guwave.onedata.dataware.source.agent.wat.sink.KafkaSink;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.security.SecureRandom;
import java.time.Duration;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
@Component
public class WatHandler implements SingleParseHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(WatHandler.class);

    private List<String> needDeleteTables;
    private List<String> needCountTables;

    @Value("${spring.data.clickhouse.needDeleteTables}")
    public void setNeedDeleteTables(String needDeleteTable) {
        needDeleteTables = Arrays.asList(needDeleteTable.split(COMMA));
    }

    @Value("${spring.data.clickhouse.needCountTables}")
    public void setNeedCountTables(String needCountTable) {
        needCountTables = Arrays.asList(needCountTable.split(COMMA));
    }

    @Value("${spring.handler.sinkTypes}")
    private String sinkTypes;
    @Value("${spring.hdfs.odsHdfsTemplatePath}")
    private String odsHdfsTemplatePath;
    @Value("${spring.hdfs.otherHdfsTemplatePath}")
    private String otherHdfsTemplatePath;
    @Value("${spring.data.clickhouse.address}")
    private String clickhouseAddress;
    @Value("${spring.data.clickhouse.username}")
    private String clickhouseUsername;
    @Value("${spring.data.clickhouse.password}")
    private String clickhousePassword;
    @Value("${spring.data.clickhouse.cluster}")
    private String clickhouseCluster;
    @Value("${spring.kafka.calculateEndFlagTopic}")
    private String calculateEndFlagTopic;

    @Autowired
    private SftpFileDetailRepository sftpFileDetailRepository;
    @Autowired
    private WatProcessRuleRepository watProcessRuleRepository;
    @Autowired
    private WatConsistentHandle watConsistentHandle;
    @Autowired
    private KafkaSink kafkaSink;
    @Autowired
    private com.guwave.onedata.dataware.source.agent.common.sink.impl.KafkaSink watKafkaSink;
    @Autowired
    private Map<String, CkSink> sinks;
    @Autowired
    private FileProcessProgressService fileProcessProgressService;
    @Autowired
    private HdfsUtil hdfsUtil;
    @Autowired
    private LotWaferPrimaryDataManager lotWaferPrimaryDataManager;

    Map<String, Predicate<OdsWat>> WAT_KEY_FIELD_CHECK_MAP = new LinkedHashMap<String, Predicate<OdsWat>>() {{
        put("deviceId", odsWat -> StringUtils.isNotEmpty(odsWat.getDeviceId()));
        put("testArea", odsWat -> odsWat.getTestArea() != null);
        put("lotType", odsWat -> odsWat.getLotType() != null);
        put("testStage", odsWat -> StringUtils.isNotEmpty(odsWat.getTestStage()));
        put("lotId", odsWat -> StringUtils.isNotEmpty(odsWat.getLotId()));
    }};

    Map<String, Predicate<OdsWat>> WAT_TESTITEM_KEY_FIELD_CHECK_MAP = new LinkedHashMap<String, Predicate<OdsWat>>() {{
        put("waferNo", odsWat -> StringUtils.isNotEmpty(odsWat.getWaferNo()));
        put("siteId", odsWat -> odsWat.getSiteId() != null);
        put("testResult", odsWat -> odsWat.getTestResult() != null);
        put("testValue", odsWat -> odsWat.getTestValue() != null);
        put("startTime", odsWat -> odsWat.getStartTime() != null);
    }};

    public String getCkAddress() {
        String[] detail = clickhouseAddress.split(Constant.COMMA);
        int len = detail.length;
        SecureRandom random = new SecureRandom();
        return detail[random.nextInt(len)];
    }

    @Override
    public List<SftpFileDetail> getNeedParseSftpFiles() {
        return sftpFileDetailRepository.findByTestAreaAndFileCategoryAndTransferStatusAndProcessStatusAndConvertFlagAndBatchStatusOrderByUpdateTimeAsc(
                TestArea.WAT,
                FileCategory.WAT,
                TransferStatus.SUCCESS,
                ProcessStatus.CREATE,
                0,
                SftpBatchStatus.SUCCESS,
                Pageable.ofSize(1)
        );
    }

    @Override
    public void dealFile(File uncompressFile, SftpFileDetail sftpFileDetail, FileInfo fileInfo, ProcessLog processLog, FileLoadingLog fileLoadingLog) throws Exception {
        List<SftpFileDetail> existsDealSuccessSftpFiles = sftpFileDetailRepository.findAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndFileCategoryAndTransferStatusAndProcessStatusAndConvertFlagAndBatchStatusAndOriginFileName(
                        sftpFileDetail.getCustomer(),
                        sftpFileDetail.getTestArea(),
                        sftpFileDetail.getFactory(),
                        sftpFileDetail.getFactorySite(),
                        sftpFileDetail.getFileCategory(),
                        TransferStatus.SUCCESS,
                        ProcessStatus.SUCCESS,
                        0,
                        SftpBatchStatus.SUCCESS,
                        sftpFileDetail.getOriginFileName()
                ).stream().filter(t -> Objects.equals(t.getOriginFileName(), sftpFileDetail.getOriginFileName()) && Objects.equals(t.getOriginFileSize(), sftpFileDetail.getOriginFileSize()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(existsDealSuccessSftpFiles)) {
            SftpFileDetail sameFile = existsDealSuccessSftpFiles.get(0);
            String errorMessage = String.format("存在相同的文件%s已被解析过！", sameFile.getLocalFileName());
            LOGGER.info(errorMessage);
            throw new FileLoadException(FileLoadExceptionInfo.DUPLICATE_FILE_PARSED_EXCEPTION, errorMessage, null);
        }

        List<OdsWat> items = watConsistentHandle.handle(uncompressFile, sftpFileDetail);

        this.validateWatData(items, sftpFileDetail.getLocalFileName());

        OdsWat odsWat = items.get(0);

        // 补充processLog的信息
        processLog
                .setDeviceId(odsWat.getDeviceId())
                .setLotId(odsWat.getLotId());

        // 补充fileLoadingLog信息
        fileLoadingLog
                .setLotId(odsWat.getLotId())
                .setDeviceId(odsWat.getDeviceId())
                .setTestStage(odsWat.getTestStage())
                .setLotType(LotType.of(odsWat.getLotType()))
                .setTesterName(odsWat.getTesterName())
                .setTestProgram(odsWat.getTestProgram())
                .setStartT(odsWat.getStartTime() == null ? null : new Date(odsWat.getStartTime()))
                .setFinishT(odsWat.getEndTime() == null ? null : new Date(odsWat.getEndTime()));


        LOGGER.info("解析ods wat条数: {}", items.size());
        long dataVersion = System.currentTimeMillis();
        items.forEach(t -> t
                .setId(UUID.randomUUID().toString())
                .setUploadTime(sftpFileDetail.getCreateTime().getTime())
                .setDataVersion(dataVersion)
                .setUploadType(UploadType.AUTO.getType())
                .setFileId(fileInfo.getId())
                .setFileName(sftpFileDetail.getLocalFileName())
                .setFileType(sftpFileDetail.getFileCategory().getCategory()));
        this.sink(items,
                sftpFileDetail.getCustomer(),
                sftpFileDetail.getSubCustomer(),
                sftpFileDetail.getFactory(),
                sftpFileDetail.getFactorySite(),
                sftpFileDetail.getFab(),
                sftpFileDetail.getFabSite(),
                uncompressFile.getAbsolutePath());

    }


    /**
     * 入库
     *
     * @param wats OdsWat
     */
    @SuppressWarnings("unchecked")
    private void sink(List<OdsWat> wats, String customer, String subCustomer, String factory, String factorySite, String fab, String fabSite, String sourceFilePath) {
        OdsWat odsWat = wats.get(0);
        Long now = System.currentTimeMillis();

        if (fileProcessProgressService.lockFetcherLotAndWafer(odsWat.getCustomer(), TestArea.WAT, odsWat.getFactory(), odsWat.getFactorySite(), odsWat.getLotId(), EMPTY, odsWat.getTestStage(), LotType.of(odsWat.getLotType()))) {
            try {
                // 写入ods wat
                // this.sinks.get("odsWatSink").doHandle(wats);
                // 写入dwd wat test item detail
                Set<String> waferNos = wats.stream().map(WatCommon::getWaferNo).collect(Collectors.toSet());
                List<DwdWatTestItemDetail> testItemDetails = WatCommonUtil.toDwdWatTestItemDetail(wats);
                LOGGER.info("解析dwd wat test item detail条数: {}", testItemDetails.size());
                this.sinks.get("dwdWatTestItemDetailSink").writeCkAfterTombstone(testItemDetails, customer, factory, TestArea.WAT.getArea(), odsWat.getLotId(), waferNos, odsWat.getDeviceId(), odsWat.getLotType(), odsWat.getTestStage(), 0,
                        this.getCkAddress(), getCkAddressList(), clickhouseUsername, clickhousePassword, now);
                // 写入dwd wat die detail
                List<DwdWatDieDetail> dieDetails = WatCommonUtil.toDwdWatDieDetail(testItemDetails);
                LOGGER.info("解析dwd wat die detail条数: {}", dieDetails.size());
                this.sinks.get("dwdWatDieDetailSink").writeCkAfterTombstone(dieDetails, customer, factory, TestArea.WAT.getArea(), odsWat.getLotId(), waferNos, odsWat.getDeviceId(), odsWat.getLotType(), odsWat.getTestStage(), 0,
                        this.getCkAddress(), getCkAddressList(), clickhouseUsername, clickhousePassword, now);
                // 写入dim wat test item
                List<DimWatTestItem> testItems = WatCommonUtil.toDimWatTestItem(testItemDetails);
                LOGGER.info("解析dim wat test item条数: {}", testItems.size());
                this.sinks.get("dimWatTestItemSink").writeCkAfterTombstone(testItems,
                        customer, factory, TestArea.WAT.getArea(), odsWat.getLotId(), waferNos, odsWat.getDeviceId(), odsWat.getLotType(), odsWat.getTestStage(), null,
                        this.getCkAddress(), getCkAddressList(), clickhouseUsername, clickhousePassword, now);

                // 写入dim wat wafer
                List<DimWatWafer> wafers = WatCommonUtil.toDimWatWafer(testItemDetails);
                LOGGER.info("解析dim wat wafer条数: {}", wafers.size());
                this.sinks.get("dimWatWaferSink").writeCkAfterTombstone(wafers,
                        customer, factory, TestArea.WAT.getArea(), odsWat.getLotId(), waferNos, odsWat.getDeviceId(), odsWat.getLotType(), odsWat.getTestStage(), null,
                        this.getCkAddress(), getCkAddressList(), clickhouseUsername, clickhousePassword, now);

                // 写入dim lot wafer bin
                List<DimWatWaferBin> watWaferBins = WatCommonUtil.toDimWatWaferBin(testItemDetails);
                LOGGER.info("解析dim wat lot wafer bin条数: {}", watWaferBins.size());
                this.sinks.get("dimWatWaferBinSink").writeCkAfterTombstone(watWaferBins,
                        customer, factory, TestArea.WAT.getArea(), odsWat.getLotId(), waferNos, odsWat.getDeviceId(), odsWat.getLotType(), odsWat.getTestStage(), null,
                        this.getCkAddress(), getCkAddressList(), clickhouseUsername, clickhousePassword, now);

                // 写入dim wat site
                List<DimWatWaferSite> watWaferSites = WatCommonUtil.toDimWatWaferSite(testItemDetails);
                LOGGER.info("解析dim wat site_条数: {}", watWaferSites.size());
                this.sinks.get("dimWatWaferSiteSink").writeCkAfterTombstone(watWaferSites,
                        customer, factory, TestArea.WAT.getArea(), odsWat.getLotId(), waferNos, odsWat.getDeviceId(), odsWat.getLotType(), odsWat.getTestStage(), null,
                        this.getCkAddress(), getCkAddressList(), clickhouseUsername, clickhousePassword, now);

                // 写入dim wat test program site local
                List<DimWatTestProgramSite> watTestProgramSites = WatCommonUtil.toDimWatTestProgramSite(testItemDetails);
                LOGGER.info("使用的watTestProgramSites {}", watTestProgramSites);
                LOGGER.info("解析dim wat test program site条数: {}", watTestProgramSites.size());
                DimWatTestProgramSite site = watTestProgramSites.get(0);
                this.sinks.get("dimWatTestProgramSiteSink").doHandle(watTestProgramSites, WatCommonUtil.getTestProgramPartition(site.getCustomer(), site.getTestArea(), site.getFactory()));
                // 写入dim wat test program bin local
                List<DimWatTestProgramBin> watTestProgramBins = WatCommonUtil.toDimWatTestProgramBin(testItemDetails);
                LOGGER.info("解析dim wat test program bin条数: {}", watTestProgramBins.size());
                DimWatTestProgramBin bin = watTestProgramBins.get(0);
                this.sinks.get("dimWatTestProgramBinSink").doHandle(watTestProgramBins, WatCommonUtil.getTestProgramPartition(bin.getCustomer(), bin.getTestArea(), bin.getFactory()));
                // 写入dim wat test program test local
                List<DimWatTestProgramTestItem> watTestProgramTestItems = WatCommonUtil.toDimWatTestProgramTestItem(testItemDetails);
                LOGGER.info("解析dim wat test program test item条数: {}", watTestProgramTestItems.size());
                DimWatTestProgramTestItem item = watTestProgramTestItems.get(0);
                this.sinks.get("dimWatTestProgramTestItemSink").doHandle(watTestProgramTestItems, WatCommonUtil.getTestProgramPartition(item.getCustomer(), item.getTestArea(), item.getFactory()));

                if (sinkTypes.toUpperCase().contains(KAFKA_SINK)) {
                    this.kafkaSink.send(ODS_WAT, wats, customer, subCustomer, factory, factorySite, fab, fabSite, DwLayer.ODS);
                    this.kafkaSink.send(DWD_TEST_ITEM_DETAIL, testItemDetails, customer, subCustomer, factory, factorySite, fab, fabSite, DwLayer.DWD);
                    this.kafkaSink.send(DWD_DIE_DETAIL, dieDetails, customer, subCustomer, factory, factorySite, fab, fabSite, DwLayer.DWD);
                    this.kafkaSink.send(DIM_TEST_ITEM, testItems, customer, subCustomer, factory, factorySite, fab, fabSite, DwLayer.DIM);
                    this.kafkaSink.send(DIM_LOT_WAFER, wafers, customer, subCustomer, factory, factorySite, fab, fabSite, DwLayer.DIM);
                    this.kafkaSink.send(DIM_LOT_WAFER_BIN, watWaferBins, customer, subCustomer, factory, factorySite, fab, fabSite, DwLayer.DIM);
                    this.kafkaSink.send(DIM_TEST_PROGRAM_SITE, watTestProgramSites, customer, subCustomer, factory, factorySite, fab, fabSite, DwLayer.DIM);
                    this.kafkaSink.send(DIM_TEST_PROGRAM_BIN, watTestProgramBins, customer, subCustomer, factory, factorySite, fab, fabSite, DwLayer.DIM);
                    this.kafkaSink.send(DIM_TEST_PROGRAM_TEST_ITEM, watTestProgramTestItems, customer, subCustomer, factory, factorySite, fab, fabSite, DwLayer.DIM);
                }
                if (sinkTypes.toUpperCase().contains(HDFS_SINK)) {
                    HdfsSink hdfsSink = null;
                    try {
                        hdfsSink = new HdfsSink(1, hdfsUtil, odsWat, sourceFilePath, odsHdfsTemplatePath, otherHdfsTemplatePath);

                        hdfsSink.send(ODS_WAT, wats);
                        hdfsSink.send(DWD_TEST_ITEM_DETAIL, testItemDetails);
                        hdfsSink.send(DWD_DIE_DETAIL, dieDetails);
                        hdfsSink.send(DIM_TEST_ITEM, testItems);
                        hdfsSink.send(DIM_LOT_WAFER, wafers);
                        hdfsSink.send(DIM_LOT_WAFER_BIN, watWaferBins);
                        hdfsSink.send(DIM_TEST_PROGRAM_SITE, watTestProgramSites);
                        hdfsSink.send(DIM_TEST_PROGRAM_BIN, watTestProgramBins);
                        hdfsSink.send(DIM_TEST_PROGRAM_TEST_ITEM, watTestProgramTestItems);

                        hdfsSink.afterSend();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        if (hdfsSink != null) {
                            hdfsSink.close();
                        }
                    }
                }
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(wafers)) {
                    LOGGER.info("写入ck数据，dataSize = {}", wats.size());
                    sendWatSaveCkMessage(wats);
                } else {
                    LOGGER.info("写入ck数据，dataSize = 0");
                }
                // 写入lotWaferPrimaryData
                try {
                    updateWatLotWaferPrimaryData(watWaferSites, waferNos);
                } catch (Exception ex) {
                    LOGGER.info("更新TestRawDataLotWaferPrimaryData失败", ex);
                }
            } finally {
                fileProcessProgressService.unlockFetcherLotAndWafer(odsWat.getCustomer(), TestArea.WAT, odsWat.getFactory(), odsWat.getFactorySite(), odsWat.getLotId(), EMPTY, odsWat.getTestStage(), LotType.of(odsWat.getLotType()));
            }
        } else {
            LOGGER.info("lot : {} 上锁失败", odsWat.getLotId());
            throw new NotFailException("lot : " + odsWat.getLotId() + " 上锁失败！");
        }
    }

    private String[] getCkAddressList() {
        return this.clickhouseAddress.split(Constant.COMMA);
    }

    /**
     * 校验WAT数据
     *
     * @param items         解析后的数据
     * @param localFileName localFileName
     */
    private void validateWatData(List<OdsWat> items, String localFileName) {
        if (CollectionUtils.isEmpty(items)) {
            // 解析异常
            LOGGER.error("WAT文件：{}解析失败, 未解析到数据", localFileName);
            throw new FailException("WatFetcher 解析异常：items为空");
        } else {
            List<String> failedFields = new ArrayList<>();

            OdsWat odsWat = items.get(0);
            WAT_KEY_FIELD_CHECK_MAP.forEach((filed, predicate) -> {
                if (!predicate.test(odsWat)) {
                    failedFields.add(filed);
                }
            });

            WAT_TESTITEM_KEY_FIELD_CHECK_MAP.forEach((filed, predicate) -> {
                if (!items.stream().allMatch(predicate)) {
                    failedFields.add(filed);
                }
            });
            if (!CollectionUtils.isEmpty(failedFields)) {
                // 数据异常
                String errorMessage = FileLoadExceptionInfo.KEY_FIELD_NULL_EXCEPTION.getMessage().replace(KEY_FIELD, String.join(COMMA, failedFields));
                LOGGER.error("WAT文件：{}数据异常, 关键字段为空: {}", localFileName, errorMessage);
                throw new FileLoadException(FileLoadExceptionInfo.KEY_FIELD_NULL_EXCEPTION, errorMessage, failedFields);
            }
        }
    }

    private void sendWatSaveCkMessage(List<OdsWat> watWafer) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(watWafer)) {
            LOGGER.info("watWafer is empty");
            return;
        }
        long watDataVersion = watWafer.get(0).getDataVersion();
        List<CalculateEndFlag> calculateEndFlags = watWafer
                .stream()
                .map(t -> new Wafer()
                        .setCustomer(t.getCustomer())
                        .setSubCustomer(t.getSubCustomer())
                        .setTestArea(TestArea.WAT)
                        .setFactory(t.getFactory())
                        .setFactorySite(t.getFactorySite())
                        .setDeviceId(t.getDeviceId())
                        .setLotId(t.getLotId())
                        .setWaferNo(t.getWaferNo())
                        .setTestStage(t.getTestStage())
                        .setFileCategory(FileCategory.WAT)
                        .setLotType(LotType.valueOf(t.getLotType())))
                .distinct()
                .map(this::buildCalculateEndFlag)
                .peek(t -> t.setDataVersion(watDataVersion))
                .collect(Collectors.toList());
        for (CalculateEndFlag calculateEndFlagMessage : calculateEndFlags) {
            String message = JSON.toJSONString(calculateEndFlagMessage);
            LOGGER.info("wat解析写入Ck完成,发送消息通知. {}", message);
            watKafkaSink.send(calculateEndFlagTopic, message);
        }
    }

    private CalculateEndFlag buildCalculateEndFlag(Wafer wafer) {
        return new CalculateEndFlag()
                .setCustomer(wafer.getCustomer())
                .setSubCustomer(wafer.getSubCustomer())
                .setTestArea(wafer.getTestArea())
                .setFactory(wafer.getFactory())
                .setFactorySite(wafer.getFactorySite())
                .setDeviceId(wafer.getDeviceId())
                .setLotId(wafer.getLotId())
                .setWaferNo(wafer.getWaferNo())
                .setTestStage(wafer.getTestStage())
                .setFileCategory(wafer.getFileCategory())
                .setLotType(wafer.getLotType())
                .setPlatform(Platform.CK)
                .setProcessStatus(ProcessStatus.SUCCESS)
                .setTs(System.currentTimeMillis())
                .setDataVersion(System.currentTimeMillis())
                ;

    }

    private void updateWatLotWaferPrimaryData(List<DimWatWaferSite> watWaferSites, Set<String> waferNos) {
        if (CollectionUtils.isEmpty(watWaferSites)) {
            LOGGER.info("updateWatLotWaferPrimaryData, watWaferSites is empty");
            return;
        }

        DimWatWaferSite head = watWaferSites.get(0);
        LotWaferPrimaryKey lotWaferPrimaryKey = new LotWaferPrimaryKey()
                .setCustomer(head.getCustomer())
                .setSubCustomer(head.getSubCustomer())
                .setFactory(head.getFactory())
                .setFactorySite(head.getFactorySite())
                .setTestArea(TestArea.WAT)
                .setDeviceId(head.getDeviceId())
                .setLotId(head.getLotId())
                .setSblotId(EMPTY);

        // dieDetails 的SITE_ID 去重计数作为 inputCount
        Integer inputCount = Math.toIntExact(watWaferSites.stream()
                .map(DimWatWaferSite::getSiteId)
                .filter(Objects::nonNull)
                .distinct()
                .count());

        List<String> waferLotIds = Collections.singletonList(WaferUtil.formatLotId(head.getLotId()));
        List<String> waferNoList = new ArrayList<>(waferNos);
        try {
            LOGGER.info("WAT LotWaferPrimaryData数据更新开始, 数据量:{}", watWaferSites.size());
            lotWaferPrimaryDataManager.saveWatLotWaferPrimaryData(lotWaferPrimaryKey, watWaferSites, waferLotIds, waferNoList, inputCount);
            LOGGER.info("WAT LotWaferPrimaryData数据更新完成, lotWaferPrimaryKey:{}", lotWaferPrimaryKey);
        } catch (Exception e) {
            LOGGER.error("保存WAT LotWaferPrimaryData数据失败, lotWaferPrimaryKey:{}", lotWaferPrimaryKey, e);
            throw new RuntimeException("保存WAT LotWaferPrimaryData数据失败", e);
        }
    }

}
