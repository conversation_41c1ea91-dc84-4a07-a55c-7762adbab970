package com.guwave.onedata.dataware.collectx.manager.thread;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpLog;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;

public class SftpLogThreadLocalUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(SftpLogThreadLocalUtil.class);

    private static final int maxSize = 10000;

    private static final ThreadLocal<SftpLog> sftpLogLocal = new ThreadLocal<>();

    public static void setSftpLog(SftpLog sftpLog) {
        sftpLogLocal.set(sftpLog);
    }

    public static SftpLog getSftpLog() {
        return sftpLogLocal.get();
    }

    public static void appendLogTxt(String msg) {
        if (StringUtils.isNotEmpty(msg)) {
            SftpLog sftpLog = sftpLogLocal.get();
            if (sftpLog != null) {
                try {
                    StringBuilder buffer = new StringBuilder();
                    String oldMsg = sftpLog.getLogText();
                    if (StringUtils.isNotEmpty(oldMsg)) {
                        buffer
                                .append(oldMsg)
                                .append(Constant.ENTER);
                    }

                    if (buffer.length() + msg.length() > maxSize) {
                        buffer.delete(0, buffer.length() + msg.length() - maxSize);
                    }

                    buffer.append(LocalDateTime.now()).append(Constant.SPACE).append(msg);
                    sftpLog.setLogText(buffer.toString());
                } catch (Exception e) {
                    LOGGER.error("异常：", e);
                }
            }
        }
    }

    public static void overrideLogTxt(String msg) {
        SftpLog sftpLog = sftpLogLocal.get();
        if (sftpLog != null) {
            sftpLog.setLogText(msg == null ? "" : msg);
        }
    }

    public static void clear() {
        sftpLogLocal.remove();
    }

}
