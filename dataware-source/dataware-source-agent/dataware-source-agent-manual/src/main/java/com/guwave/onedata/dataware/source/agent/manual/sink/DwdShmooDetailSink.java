package com.guwave.onedata.dataware.source.agent.manual.sink;

import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.source.agent.manual.model.DwdShmooDetail;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

/**
 * 2023/11/16 18:53
 * DwdFileShmooDetailSink
 * <AUTHOR>
 */
@Component
public class DwdShmooDetailSink implements CkSink<DwdShmooDetail> {

    @Value("${spring.sink.ck.dwd.dbName}")
    private String dbName;

    @Override
    public String getTableName() {
        return "dwd_shmoo_detail_cluster";
    }

    @Value("${spring.lotBucketNum}")
    private Integer lotBucketNum;

    @Override
    public String getDbName() {
        return dbName;
    }

    @Override
    public void handle(PreparedStatement statement, List<DwdShmooDetail> items) throws SQLException {
        for (DwdShmooDetail item : items) {
            statement.setObject(1, item.getFileId());
            statement.setObject(2, item.getFileName());
            statement.setObject(3, item.getCustomer());
            statement.setObject(4, item.getUploadType());
            statement.setObject(5, item.getEcid());
            statement.setObject(6, item.getTestProgram());
            statement.setObject(7, item.getTesterName());
            statement.setObject(8, item.getTestArea().getArea());
            statement.setObject(9, item.getDeviceId());
            statement.setObject(10, item.getLotId());
            statement.setObject(11, item.getProberHandlerId());
            statement.setObject(12, item.getProberHandlerTyp());
            statement.setObject(13, item.getTestItem());
            statement.setObject(14, item.getPartId());
            statement.setObject(15, item.getShmooName());
            statement.setObject(16, item.getSiteNumber());
            statement.setObject(17, item.getTestSuite());
            statement.setObject(18, item.getTestPattern());
            statement.setObject(19, item.getSpecification());
            statement.setObject(20, item.getxLabel());
            statement.setObject(21, item.getyLabel());
            statement.setObject(22, item.getXresourceType());
            statement.setObject(23, item.getYresourceType());
            statement.setObject(24, item.getxStart());
            statement.setObject(25, item.getxEnd());
            statement.setObject(26, item.getyStart());
            statement.setObject(27, item.getyEnd());
            statement.setObject(28, item.getxStep());
            statement.setObject(29, item.getyStep());
            statement.setObject(30, item.getShmooJudgeResult());
            statement.setObject(31, item.getSubloop());
            statement.setObject(32, item.getOperatingSequenc());
            statement.setObject(33, item.getTestmethod());
            statement.setObject(34, item.getAxisnum());
            statement.setObject(35, item.getErrcntflag());
            statement.setObject(36, item.getxValue());
            statement.setObject(37, item.getyValue());
            statement.setObject(38, item.getShmooResult());
            statement.setObject(39, item.getIsPrimary());
            statement.setObject(40, item.getStdfPartTxt());
            statement.setObject(41, item.getCharStartEventid());
            statement.setObject(42, item.getProcess());
            statement.setObject(43, item.getxUnit());
            statement.setObject(44, item.getyUnit());
            statement.setObject(45, item.getcPartId());
            statement.setObject(46, this.toTimestamp(item.getStartTime()));
            statement.setObject(47, this.toTimestamp(item.getEndTime()));
            statement.setObject(48, item.getPrimaryX());
            statement.setObject(49, item.getPrimaryY());
            statement.setObject(50, item.getTestTemperature());
            statement.setObject(51, this.toTimestamp(item.getCreateTime()));
            statement.setObject(52, item.getCreateUser());
            statement.setObject(53, this.toTimestamp(item.getUploadTime()));
            statement.setObject(54, item.getShmooResultName());
            statement.setObject(55, item.getIsHoleFlag());
            statement.setObject(56, getLotBucket(item.getLotId(), lotBucketNum));
            statement.setObject(57, item.getTestOrder());
            statement.setObject(58, item.getDataType());
            statement.setObject(59, item.getLotType() == null ? LotType.EMPTY.getType() : item.getLotType().getType());
            statement.setObject(60, item.getWaferId());
            statement.setObject(61, item.getExtraInfo());

            statement.addBatch();
        }
    }

    @Override
    public List<String> getColumns() {
        return Arrays.asList(
                "FILE_ID",
                "FILE_NAME",
                "CUSTOMER",
                "UPLOAD_TYPE",
                "ECID",
                "TEST_PROGRAM",
                "TESTER_NAME",
                "TEST_AREA",
                "DEVICE_ID",
                "LOT_ID",
                "PROBER_HANDLER_ID",
                "PROBER_HANDLER_TYP",
                "TEST_ITEM",
                "PART_ID",
                "SHMOO_NAME",
                "SITE_NUMBER",
                "TEST_SUITE",
                "TEST_PATTERN",
                "SPECIFICATION",
                "X_LABEL",
                "Y_LABEL",
                "XRESOURCE_TYPE",
                "YRESOURCE_TYPE",
                "X_START",
                "X_END",
                "Y_START",
                "Y_END",
                "X_STEP",
                "Y_STEP",
                "SHMOO_JUDGE_RESULT",
                "SUBLOOP",
                "OPERATING_SEQUENC",
                "TESTMETHOD",
                "AXISNUM",
                "ERRCNTFLAG",
                "X_VALUE",
                "Y_VALUE",
                "SHMOO_RESULT",
                "IS_PRIMARY",
                "STDF_PART_TXT",
                "CHAR_START_EVENTID",
                "PROCESS",
                "X_UNIT",
                "Y_UNIT",
                "C_PART_ID",
                "START_TIME",
                "END_TIME",
                "PRIMARY_X",
                "PRIMARY_Y",
                "TEST_TEMPERATURE",
                "CREATE_TIME",
                "CREATE_USER",
                "UPLOAD_TIME",
                "SHMOO_RESULT_NAME",
                "IS_HOLE_FLAG",
                "LOT_BUCKET",
                "TEST_ORDER",
                "DATA_TYPE",
                "LOT_TYPE",
                "WAFER_ID",
                "EXTRA_INFO"
        );
    }

}
