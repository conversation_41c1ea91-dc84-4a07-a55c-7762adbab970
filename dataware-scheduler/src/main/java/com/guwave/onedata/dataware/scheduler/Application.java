package com.guwave.onedata.dataware.scheduler;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.logging.LoggingApplicationListener;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Set;
import java.util.logging.Level;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * Application
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-12-29 11:08:12
 */
@EnableScheduling
@EnableDubbo
@SpringBootApplication(scanBasePackages = {"com.guwave.onedata.dataware.scheduler", "com.guwave.onedata.next.compute.api"}, exclude = {ErrorMvcAutoConfiguration.class})
@PropertySource(value = {"classpath:properties/dataware-scheduler.properties", "file:properties/dataware-scheduler.properties"}, ignoreResourceNotFound = true)
public class Application {

    private static final Logger LOGGER = LoggerFactory.getLogger(Application.class);

    public static void main(String[] args) {
        SpringApplicationBuilder builder = new SpringApplicationBuilder(Application.class);
        Set<ApplicationListener<?>> listeners = builder.application().getListeners();
        listeners.removeIf(listener -> listener instanceof LoggingApplicationListener);
        builder.application().setListeners(listeners);
        builder.run(args);
        java.util.logging.Logger.getLogger("org.apache.spark").setLevel(Level.WARNING);
        LOGGER.info("dataware scheduler start successfully");
    }
}
