package com.guwave.onedata.dataware.collectx.job;

import com.guwave.onedata.dataware.collectx.service.impl.PullSftpBackupTaskService;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PullSftpBackupTaskJob {

    @Autowired
    private PullSftpBackupTaskService pullSftpBackupTaskService;

    @ScheduleSwitch
    @Scheduled(cron = "${spring.scheduler.timer.pullSftpBackupTask}")
    public void pullTask() {
        try {
            pullSftpBackupTaskService.pullTask();
        }catch (Exception e) {
            log.error(e.toString(), e);
        }
    }
}
