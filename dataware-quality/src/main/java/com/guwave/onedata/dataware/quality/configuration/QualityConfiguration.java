package com.guwave.onedata.dataware.quality.configuration;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@ComponentScan(basePackages = {"com.guwave.onedata.dataware.dao.mysql.aspect", "com.guwave.onedata.dataware.dao.ck.connection"})
@EnableTransactionManagement
@EntityScan(basePackages = {"com.guwave.onedata.dataware.dao.mysql.domain"})
@EnableJpaRepositories(basePackages = {"com.guwave.onedata.dataware.dao.mysql.repository"})
public class QualityConfiguration {

}