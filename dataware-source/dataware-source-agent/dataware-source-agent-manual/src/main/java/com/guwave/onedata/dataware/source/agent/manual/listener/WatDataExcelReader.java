package com.guwave.onedata.dataware.source.agent.manual.listener;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.model.wat.ods.OdsWat;
import com.guwave.onedata.dataware.common.util.WaferUtil;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.dataware.parser.stdf.util.DwdCommonUtil;
import com.guwave.onedata.dataware.source.agent.manual.util.WatDataCommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

public class WatDataExcelReader implements ExcelConsumer<Map<String, List<Pair<String, String>>>> {


    private final Map<String, BiConsumer<String, OdsWat>> otherFieldConsumerMap;
    private final Map<String, BiConsumer<Pair<String, String>, OdsWat>> mapFieldConsumerMap;
    private final ManualFileInfo manualFileInfo;


    private List<OdsWat> odsWats = new ArrayList<>();
    private final Long dataVersion = System.currentTimeMillis();

    public WatDataExcelReader(Map<String, BiConsumer<String, OdsWat>> otherFieldConsumerMap, Map<String, BiConsumer<Pair<String, String>, OdsWat>> mapFieldConsumerMap, ManualFileInfo manualFileInfo) {
        this.otherFieldConsumerMap = otherFieldConsumerMap;
        this.mapFieldConsumerMap = mapFieldConsumerMap;
        this.manualFileInfo = manualFileInfo;
    }

    @Override
    public void dealLine(Map<String, List<Pair<String, String>>> fieldWithContentListMap) {
        OdsWat odsWat = new OdsWat();
        fieldWithContentListMap.forEach((field, contentList) -> {
            BiConsumer<String, OdsWat> commonOdsWatDataConsumer = WatDataCommonUtil.WAT_DATA_COMMON_FIELD_CONSUMER_MAP.get(field);
            if (commonOdsWatDataConsumer != null) {
                try {
                    commonOdsWatDataConsumer.accept(contentList.get(0).getValue(), odsWat);
                } catch (Exception e) {
                    throw new RuntimeException(String.format("WAT文件%s数据异常, 异常数据: columnName=%s, columnValue=%s", manualFileInfo.getFileName(), field, contentList.get(0).getValue()), e);
                }
            }
            BiConsumer<String, OdsWat> otherOdsWatDataConsumer = otherFieldConsumerMap.get(field);
            if (otherOdsWatDataConsumer != null) {
                otherOdsWatDataConsumer.accept(contentList.get(0).getValue(), odsWat);
            }
            BiConsumer<Pair<String, String>, OdsWat> mapFieldConsumer = mapFieldConsumerMap.get(field);
            if (mapFieldConsumer != null) {
                contentList.forEach(contentPair -> {
                    mapFieldConsumer.accept(contentPair, odsWat);
                });
            }
        });
        odsWats.add(odsWat);

        // stage
        if (StringUtils.isBlank(odsWat.getTestStage())) {
            odsWat.setTestStage(odsWat.getTestCod());
        }
        if (StringUtils.isBlank(odsWat.getTestStage())) {
            odsWat.setTestStage(Constant.ODS_WAT_TEST_STAGE);
        }

        // starttime
        // endtime
        if (odsWat.getStartTime() == null) {
            odsWat.setStartTime(odsWat.getEndTime());
        }
        if (odsWat.getEndTime() == null) {
            odsWat.setEndTime(odsWat.getStartTime());
        }
        if (odsWat.getStartTime() == null) {
            odsWat.setStartTime(0L);
            odsWat.setEndTime(0L);
        }

        // lotType
        if (StringUtils.isBlank(odsWat.getLotType())) {
            odsWat.setLotType(LotType.EMPTY.getType());
        }

        fillCommonData(odsWat);
    }

    private void fillCommonData(OdsWat odsWat) {
        odsWat.setTestArea(manualFileInfo.getTestArea().getArea());
        odsWat.setCustomer(manualFileInfo.getCustomer());
        odsWat.setSubCustomer(manualFileInfo.getSubCustomer());
        odsWat.setUploadType(manualFileInfo.getUploadType().getType());
        odsWat.setFileId(manualFileInfo.getFileId());
        odsWat.setFileName(manualFileInfo.getFileName());
        odsWat.setFileType(manualFileInfo.getFileCategory().getCategory());
        odsWat.setCreateUser(manualFileInfo.getFileOwner());
        odsWat.setUploadTime(manualFileInfo.getCreateTime().getTime());
        odsWat.setDataVersion(dataVersion);

        if (StringUtils.isNotBlank(manualFileInfo.getDeviceId())) {
            odsWat.setDeviceId(manualFileInfo.getDeviceId());
        }
        if (StringUtils.isNotBlank(manualFileInfo.getTestStage())) {
            odsWat.setTestStage(manualFileInfo.getTestStage());
        }
        if (StringUtils.isNotBlank(manualFileInfo.getFactory())) {
            odsWat.setFactory(manualFileInfo.getFactory());
        }
        if (StringUtils.isNotBlank(manualFileInfo.getFactorySite())) {
            odsWat.setFactorySite(manualFileInfo.getFactorySite());
        }
        if (manualFileInfo.getLotType() != null) {
            odsWat.setLotType(manualFileInfo.getLotType().getType());
        }
        if (StringUtils.isNotBlank(manualFileInfo.getLotId())) {
            odsWat.setLotId(manualFileInfo.getLotId());
        }
        odsWat.setWaferId(WaferUtil.formatStandardWaferId(odsWat.getLotId(), odsWat.getWaferNo()));
        if (StringUtils.isNotBlank(manualFileInfo.getTesterName())) {
            odsWat.setTesterName(manualFileInfo.getTesterName());
        }
        if (StringUtils.isNotBlank(manualFileInfo.getTestProgram())) {
            odsWat.setTestProgram(manualFileInfo.getTestProgram());
        }
        if (StringUtils.isNotBlank(manualFileInfo.getTestTemperature())) {
            odsWat.setTestTemperature(manualFileInfo.getTestTemperature());
        }
        if (StringUtils.isNotBlank(manualFileInfo.getProcess())) {
            odsWat.setProcess(manualFileInfo.getProcess());
        }
        if (StringUtils.isNotBlank(manualFileInfo.getNotch())) {
            odsWat.setWfFlat(manualFileInfo.getNotch());
        }
        if (manualFileInfo.getConditionSet() != null && !manualFileInfo.getConditionSet().isEmpty()) {
            odsWat.setConditionSet(manualFileInfo.getConditionSet());
        }
    }


    @Override
    public void after() {
        // do nothing
    }

    public void dealRemain() {
        // do nothing
    }

    public List<OdsWat> getOdsWats() {
        return odsWats;
    }
}
