package com.guwave.onedata.dataware.source.agent.wat.sink;

import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.model.wat.WatCommon;
import com.guwave.onedata.dataware.common.model.wat.dim.*;
import com.guwave.onedata.dataware.common.model.wat.dwd.DwdWatDieDetail;
import com.guwave.onedata.dataware.common.model.wat.dwd.DwdWatTestItemDetail;
import com.guwave.onedata.dataware.common.model.wat.hdfs.dim.*;
import com.guwave.onedata.dataware.common.model.wat.hdfs.dwd.HdfsTestItemDetail;
import com.guwave.onedata.dataware.common.model.wat.hdfs.dwd.HdfsWatDieDetail;
import com.guwave.onedata.dataware.common.model.wat.ods.OdsWat;
import com.guwave.onedata.dataware.source.agent.common.util.HdfsUtil;
import org.apache.avro.Schema;
import org.apache.avro.reflect.ReflectData;
import org.apache.hadoop.fs.Path;
import org.apache.parquet.avro.AvroParquetWriter;
import org.apache.parquet.hadoop.ParquetFileWriter;
import org.apache.parquet.hadoop.ParquetWriter;
import org.apache.parquet.hadoop.metadata.CompressionCodecName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;
import java.util.function.Function;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

public class HdfsSink {
    private static final Logger LOGGER = LoggerFactory.getLogger(HdfsSink.class);


    private static final String LOCAL_FILE_PREFIX = "file://";
    private static final String PART_PREFIX = "part-";
    private static final String PARQUET_FILE_SUFFIX = ".parquet";

    private static final Map<String, Schema> SCHEMA_MAP = new HashMap<String, Schema>() {{
        put(ODS_WAT, ReflectData.AllowNull.get().getSchema(OdsWat.class));
        put(DWD_DIE_DETAIL, ReflectData.AllowNull.get().getSchema(HdfsWatDieDetail.class));
        put(DWD_TEST_ITEM_DETAIL, ReflectData.AllowNull.get().getSchema(HdfsTestItemDetail.class));
        put(DIM_TEST_ITEM, ReflectData.AllowNull.get().getSchema(HdfsWatTestItem.class));
        put(DIM_LOT_WAFER, ReflectData.AllowNull.get().getSchema(HdfsWatLotWafer.class));
        put(DIM_LOT_WAFER_BIN, ReflectData.AllowNull.get().getSchema(HdfsWatLotWaferBin.class));
        put(DIM_TEST_PROGRAM_SITE, ReflectData.AllowNull.get().getSchema(HdfsWatTestProgramSite.class));
        put(DIM_TEST_PROGRAM_BIN, ReflectData.AllowNull.get().getSchema(HdfsWatTestProgramBin.class));
        put(DIM_TEST_PROGRAM_TEST_ITEM, ReflectData.AllowNull.get().getSchema(HdfsWatTestProgramTestItem.class));
    }};

    private static final Map<String, Function<WatCommon, Object>> WAT_HDFS_DATA_CONVERT_MAP = new HashMap<String, Function<WatCommon, Object>>() {{
        put(ODS_WAT, temp -> temp);
        put(DWD_DIE_DETAIL, temp -> HdfsWatDieDetail.buildWatDieDetail((DwdWatDieDetail) temp));
        put(DWD_TEST_ITEM_DETAIL, temp -> HdfsTestItemDetail.buildTestItemDetail((DwdWatTestItemDetail) temp));
        put(DIM_TEST_ITEM, temp -> HdfsWatTestItem.buildTestItemGdpPojo((DimWatTestItem) temp));
        put(DIM_LOT_WAFER, temp -> HdfsWatLotWafer.buildLotWaferGdpPojo((DimWatWafer) temp));
        put(DIM_LOT_WAFER_BIN, temp -> HdfsWatLotWaferBin.buildLotWaferBin((DimWatWaferBin) temp));
        put(DIM_TEST_PROGRAM_SITE, temp -> HdfsWatTestProgramSite.buildTestProgramSite((DimWatTestProgramSite) temp));
        put(DIM_TEST_PROGRAM_BIN, temp -> HdfsWatTestProgramBin.buildTestProgramBin((DimWatTestProgramBin) temp));
        put(DIM_TEST_PROGRAM_TEST_ITEM, temp -> HdfsWatTestProgramTestItem.buildTestProgramTestItem((DimWatTestProgramTestItem) temp));
    }};

    private static final Map<String, String> DW_LAYER_MAP = new HashMap<String, String>() {{
        put(ODS_WAT, DwLayer.ODS.getLayer().toLowerCase());
        put(DWD_DIE_DETAIL, DwLayer.DWD.getLayer().toLowerCase());
        put(DWD_TEST_ITEM_DETAIL, DwLayer.DWD.getLayer().toLowerCase());
        put(DIM_TEST_ITEM, DwLayer.DIM.getLayer().toLowerCase());
        put(DIM_LOT_WAFER, DwLayer.DIM.getLayer().toLowerCase());
        put(DIM_LOT_WAFER_BIN, DwLayer.DIM.getLayer().toLowerCase());
        put(DIM_TEST_PROGRAM_SITE, DwLayer.DIM.getLayer().toLowerCase());
        put(DIM_TEST_PROGRAM_BIN, DwLayer.DIM.getLayer().toLowerCase());
        put(DIM_TEST_PROGRAM_TEST_ITEM, DwLayer.DIM.getLayer().toLowerCase());
    }};


    private final int threadIndex;
    private HdfsUtil hdfsUtil;
    private final Map<String, ParquetWriter> PARQUET_WRITE_MAP = new HashMap<>();
    private final Map<String, String> PARQUET_LOCAL_PATH = new HashMap<>();
    private final String odsHdfsTemplatePath;
    private final String otherHdfsTemplatePath;
    private WatCommon watCommon;

    public HdfsSink(int threadIndex, HdfsUtil hdfsUtil, WatCommon watCommon, String sourceFilePath, String odsHdfsTemplatePath, String otherHdfsTemplatePath) throws Exception {
        this.threadIndex = threadIndex;
        this.hdfsUtil = hdfsUtil;
        this.watCommon = watCommon;
        this.odsHdfsTemplatePath = odsHdfsTemplatePath;
        this.otherHdfsTemplatePath = otherHdfsTemplatePath;
        SCHEMA_MAP.forEach((type, schema) -> {
            String parquetLocalPath = getParquetLocalPath(sourceFilePath, type);
            this.PARQUET_LOCAL_PATH.put(type, parquetLocalPath);
            try {
                this.PARQUET_WRITE_MAP.put(type, getParquetWrite(schema, parquetLocalPath));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    public <T extends WatCommon> void send(String type, List<T> values) throws Exception {
        ParquetWriter parquetWriter = PARQUET_WRITE_MAP.get(type);
        Function<WatCommon, Object> hdfsConvertFunction = WAT_HDFS_DATA_CONVERT_MAP.get(type);
        for (WatCommon value : values) {
            parquetWriter.write(hdfsConvertFunction.apply(value));
        }
    }

    public void afterSend() {
        this.close();
        PARQUET_LOCAL_PATH.forEach((type, path) -> {
            // 生成的parquet文件上传hdfs
            hdfsUtil.upload(path.replace(LOCAL_FILE_PREFIX, EMPTY), getHdfsUploadPath(type.equals(ODS_WAT) ? odsHdfsTemplatePath : otherHdfsTemplatePath, type), 1);
        });
    }

    public void close() {
        PARQUET_WRITE_MAP.forEach((type, parquetWrite) -> {
            try {
                parquetWrite.close();
            } catch (Exception e) {
                LOGGER.info("parquet写入流关闭失败:", e);
            }
        });
    }

    private String getParquetLocalPath(String sourceFilePath, String type) {
        File sourceFile = new File(sourceFilePath);
        return LOCAL_FILE_PREFIX + new File(sourceFile.getParent(), PART_PREFIX + threadIndex + UNDER_LINE + sourceFile.getName()).getAbsolutePath() + POINT + type.toLowerCase() + PARQUET_FILE_SUFFIX;
    }

    private String getHdfsUploadPath(String hdfsTemplatePath, String type) {
        return hdfsTemplatePath
                .replace(DW_LAYER, DW_LAYER_MAP.get(type))
                .replace(FILE_CATEGORY, FileCategory.WAT.getCategory())
                .replace(TYPE, type)
                .replace(CUSTOMER, watCommon.getCustomer())
                .replace(TEST_AREA, watCommon.getTestArea())
                .replace(FACTORY, watCommon.getFactory())
                .replace(LOT_ID, watCommon.getLotId())
                .replace(DEVICE_ID, watCommon.getDeviceId())
                .replace(TEST_STAGE, watCommon.getTestStage())
                .replace(LOT_TYPE, watCommon.getLotType())
                .replace(FILE_ID, watCommon.getFileId() + EMPTY)
                .replace(WAFER_NO, watCommon.getWaferNo());
    }

    private ParquetWriter getParquetWrite(Schema schema, String parquetLocalPath) throws Exception {
        return AvroParquetWriter.builder(new Path(parquetLocalPath))
                .withSchema(schema)
                .withDataModel(new ReflectData.AllowNull())
                .withDictionaryEncoding(true)
                .withConf(hdfsUtil.getSourceAgentCommonConfiguration().generateHdfsConfig())
                .withWriteMode(ParquetFileWriter.Mode.OVERWRITE)
                .withRowGroupSize(1 * 1024 * 1024)
                .withCompressionCodec(CompressionCodecName.ZSTD)
                .build();
    }
}
