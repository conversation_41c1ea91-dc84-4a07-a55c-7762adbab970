package com.guwave.onedata.dataware.source.agent.common.handler;

import com.guwave.onedata.dataware.dao.mysql.domain.dc.FileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileLoadingLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ProcessLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileDetail;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * Hand<PERSON>
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-19 16:48:37
 */
public interface SingleParseHandler extends Handler {
    Logger LOGGER = LoggerFactory.getLogger(SingleParseHandler.class);

    void dealFile(File uncompressFile, SftpFileDetail sftpFileDetail, FileInfo fileInfo, ProcessLog processLog, FileLoadingLog fileLoadingLog) throws Exception;

    static Date formatDate(String str) {
        try {
            return DateUtils.parseDate(str, "yyyy-MM-dd HH:mm:ss");
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    List<SftpFileDetail> getNeedParseSftpFiles();

    static Long getLongValue(String str) {
        return str.isEmpty() ? null : Long.valueOf(str);
    }

    static Integer getIntegerValue(String str) {
        return str.isEmpty() ? null : Integer.valueOf(str);
    }

    static Double getDoubleValue(String str) {
        return str.isEmpty() ? null : Double.valueOf(str);
    }

    static BigDecimal getBigDecimalValue(String str) {
        return str.isEmpty() ? null : new BigDecimal(str);
    }

}
