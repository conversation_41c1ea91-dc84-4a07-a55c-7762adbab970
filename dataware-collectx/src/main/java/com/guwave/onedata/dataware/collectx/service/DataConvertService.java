package com.guwave.onedata.dataware.collectx.service;

import com.guwave.onedata.dataware.collectx.configuration.HdfsConfig;
import com.guwave.onedata.dataware.collectx.sink.KafkaSink;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.dao.mysql.converter.ZipFileCollectionToStringConverter;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import com.guwave.onedata.dataware.dao.mysql.manager.FileWarehousingRecordManager;
import com.guwave.onedata.dataware.dao.mysql.manager.SourceStandardRuleManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.math3.util.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

public interface DataConvertService extends ConvertService {
    Logger log = LoggerFactory.getLogger(DataConvertService.class);

    default void convertDataAsync() {
        ThreadPoolExecutor threadPoolExecutor = getThreadPoolExecutor();
        if (getThreadActiveCount().get() >= threadPoolExecutor.getCorePoolSize()) {
            log.info("线程池已达最大处理数！");
            return;
        }

        List<SftpFileDetail> needConvertFiles = getNeedConvertFiles();

        if (CollectionUtils.isEmpty(needConvertFiles)) {
            log.info("没有待处理文件！");
            return;
        }

        SftpFileDetail needConvertFile = needConvertFiles.get(0);

        int updateFlag = updateStatusBeforeDeal(needConvertFile);
        if (updateFlag == 0) {
            // 更新处理状态失败时
            log.info("{} 正在被处理！", needConvertFile.getLocalFileName());
            return;
        }

        // 执行转换
        getThreadActiveCount().incrementAndGet();
        threadPoolExecutor.execute(() -> {
                    try {
                        run(needConvertFile);
                    } finally {
                        getThreadActiveCount().decrementAndGet();
                    }
                }
        );
    }

    default void run(SftpFileDetail needConvertFile) {
        File originalCompressFile = null;
        File compressConvertFile = null;
        // 读取/转换的 步骤开始时间 新建一条数据
        Date readOrConvertStartT = new Date();
        FileLoadingLog fileLoadingLog = insertFileLoadingLog(needConvertFile, readOrConvertStartT);

        try {
            // 查询文件对应的转换脚本
            List<SftpFileConvertScript> fileConvertScripts = findConvertScript(needConvertFile);

            if (CollectionUtils.isEmpty(fileConvertScripts)) {
                String errorMessage = String.format("文件%s对应的脚本不存在,文件信息: customer = '%s' and sub_customer = '%s' and test_area = '%s' and factory = '%s' and factory_site = '%s' and file_category = '%s' ",
                        needConvertFile.getLocalFileName(), needConvertFile.getCustomer(), needConvertFile.getSubCustomer(), needConvertFile.getTestArea(), needConvertFile.getFactory(), needConvertFile.getFactorySite(), needConvertFile.getFileCategory());
                log.error(errorMessage);
                throw new FileLoadException(FileLoadExceptionInfo.PYTHON_SCRIPT_NOT_FOUND_EXCEPTION, errorMessage, null);
            } else if (fileConvertScripts.size() > 1) {
                String errorMessage = String.format("文件%s对应的脚本有多条,convertScriptId: %s, 文件信息: customer = '%s' and sub_customer = '%s' and test_area = '%s' and factory = '%s' and factory_site = '%s' and file_category = '%s' ",
                        needConvertFile.getLocalFileName(), fileConvertScripts.stream().map(SftpFileConvertScript::getId).collect(Collectors.toList()), needConvertFile.getCustomer(), needConvertFile.getSubCustomer(), needConvertFile.getTestArea(), needConvertFile.getFactory(), needConvertFile.getFactorySite(), needConvertFile.getFileCategory());
                log.error(errorMessage);
                throw new FileLoadException(FileLoadExceptionInfo.PYTHON_SCRIPT_MULTI_EXCEPTION, errorMessage, null);
            } else {
                // 下载并解压源文件
                originalCompressFile = new File(getNeedConvertLocalPath(), needConvertFile.getLocalFileName());
                downloadAndUncompressHdfsFile(originalCompressFile.getName(), originalCompressFile.getParent() + File.separator, needConvertFile.getHdfsFilePath());
                if (!originalCompressFile.exists()) {
                    log.error("{} 待转换原压缩文件不存在！", originalCompressFile.getAbsolutePath());
                    throw new RuntimeException(originalCompressFile.getAbsolutePath() + "待转换原压缩文件不存在！");
                }

                // 待转换的源文件的全路径
                File originalUncompressFile = new File(originalCompressFile.getParent() + Constant.SLASH + removeFileSuffix(originalCompressFile.getName(), FileType.ZIP), removeFileSuffix(originalCompressFile.getName(), FileType.ZIP));

                // 下载并解压脚本文件
                SftpFileConvertScript sftpFileConvertScript = fileConvertScripts.get(0);
                File compressScriptFile = new File(originalUncompressFile.getParent(), sftpFileConvertScript.getFileName());
                downloadAndUncompressHdfsFile(compressScriptFile.getName(), compressScriptFile.getParent() + File.separator, sftpFileConvertScript.getHdfsFilePath());
                if (!compressScriptFile.exists()) {
                    log.error("{} 转换脚本压缩文件不存在！", compressScriptFile.getAbsolutePath());
                    throw new RuntimeException(compressScriptFile.getAbsolutePath() + "转换脚本压缩文件不存在！");
                }

                // 执行脚本的全路径
                File executeScriptFile = new File(compressScriptFile.getParent() + File.separator + removeFileSuffix(compressScriptFile.getName(), FileType.ZIP), sftpFileConvertScript.getExecuteFileName());

                // 转换脚本是否需要生成转换后的文件
                if (getNeedCompressAndUploadResultFileFlag()) {
                    // 转换后的全路径
                    File convertFile = new File(getConvertLocalPath(), removeFileSuffix(originalCompressFile.getName(), FileType.ZIP) + getConvertFileSuffix());
                    if (!convertFile.getParentFile().exists()) {
                        convertFile.getParentFile().mkdirs();
                    }
                    clean(convertFile.getParent(), convertFile.getName() + Constant.POINT + FileType.ZIP.getType());

                    // 转换后的FileLoadingLog
                    FileLoadingLog convertFileLoadingLog = new FileLoadingLog();
                    // 转换文件
                    Pair<Boolean, HashMap<Object, Object>> convertPair = convertFile(needConvertFile, originalUncompressFile.getAbsolutePath(), convertFile.getAbsolutePath(), executeScriptFile.getAbsolutePath(), convertFileLoadingLog, fileLoadingLog);
                    HashMap<Object, Object> convertPathMap = convertPair.getValue();
                    if (convertPathMap == null || !convertPathMap.containsKey(ConvertStatus.TO_MANY_SUCCESS_PREFIX.getStatus())) {
                        // 压缩转换后的文件
                        compressConvertFile = new File(convertFile.getAbsolutePath() + Constant.POINT + FileType.ZIP.getType());
                        com.guwave.onedata.dataware.collectx.util.FileUtils.compressFileToZip(convertFile.getParent(), convertFile.getName(), compressConvertFile.getParent());

                        if (!compressConvertFile.exists() || !compressConvertFile.isFile()) {
                            log.error("{} 转换后的压缩文件不存在", compressConvertFile.getAbsolutePath());
                            throw new RuntimeException("转换后的压缩文件不存在：" + compressConvertFile.getAbsolutePath());
                        }

                        long fileSize = compressConvertFile.length();
                        // 上传压缩文件
                        String hdfsFullPath = String.format(
                                getHdfsConfig().getHdfsRootPath(),
                                needConvertFile.getCustomer(),
                                needConvertFile.getTestArea().getArea(),
                                needConvertFile.getFactory(),
                                getConvertResultFileCategory().getCategory(),
                                DateFormatUtils.format(needConvertFile.getRemoteFileMtime(), "yyyyMMdd")
                        );
                        getHdfsUtil().uploadToHDFSByFileSystem(compressConvertFile.getAbsolutePath(), hdfsFullPath, 1);

                        // 保存转换后的文件
                        String cleanupTaskIds = needConvertFile.getCleanupTaskIds();
                        if (StringUtils.isNotBlank(cleanupTaskIds) && cleanupTaskIds.contains(Constant.COMMA)) {
                            // 重播一变多
                            String fileName = compressConvertFile.getName();
                            List<Long> cleanupTaskIdList = Arrays.stream(cleanupTaskIds.split(Constant.COMMA)).map(Long::parseLong).collect(Collectors.toList());
                            List<CleanupTask> cleanupTasks =  StreamSupport.stream(getCleanupTaskRepository().findAllById(cleanupTaskIdList).spliterator(), false)
                                    .filter(cleanupTask -> CollectionUtils.isNotEmpty(cleanupTask.getFileList()) && Objects.equals(fileName, cleanupTask.getFileList().stream().findFirst().get()))
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(cleanupTasks)) {
                                cleanupTaskIds = String.valueOf(cleanupTasks.get(0).getId());
                            }
                            log.info("重播一变多, 重播任务id:{}, 一变多后文件：{}", cleanupTaskIds, compressConvertFile.getName());
                        }

                        Date createTime = new Date();
                        SftpFileDetail sftpFileDetail = new SftpFileDetail()
                                .setCustomer(needConvertFile.getCustomer())
                                .setSubCustomer(needConvertFile.getSubCustomer())
                                .setFtpIp(needConvertFile.getFtpIp())
                                .setFactory(needConvertFile.getFactory())
                                .setFactorySite(needConvertFile.getFactorySite())
                                .setFab(needConvertFile.getFab())
                                .setFabSite(needConvertFile.getFabSite())
                                .setTestArea(needConvertFile.getTestArea())
                                .setBatchId(0L)
                                .setBatchStatus(SftpBatchStatus.SUCCESS)
                                .setRemoteFileName(needConvertFile.getRemoteFileName())
                                .setRemoteFileMtime(needConvertFile.getRemoteFileMtime())
                                .setLocalFileName(compressConvertFile.getName())
                                .setOriginFileName(needConvertFile.getOriginFileName())
                                .setOriginFileSize(needConvertFile.getOriginFileSize())
                                .setFileType(FileType.ZIP)
                                .setFileSize(fileSize)
                                .setFileCategory(getConvertResultFileCategory())
                                .setRemoteFilePath(needConvertFile.getRemoteFilePath())
                                .setRemoteOriginalFilePath(needConvertFile.getRemoteOriginalFilePath())
                                .setLocalFilePath(compressConvertFile.getAbsolutePath())
                                .setTransferStatus(TransferStatus.SUCCESS)
                                .setConvertFlag(0)
                                .setSourceSftpFileId(needConvertFile.getId())
                                .setWarehousingMode(needConvertFile.getWarehousingMode())
                                .setRepairLotWaferId(needConvertFile.getRepairLotWaferId())
                                .setCleanupTaskIds(cleanupTaskIds)
                                .setProcessStatus(ProcessStatus.CREATE)
                                .setRemoteFileAttrDetail(needConvertFile.getRemoteFileAttrDetail())
                                .setCreateTime(createTime)
                                .setUpdateTime(createTime)
                                .setCreateUser(Constant.SYSTEM)
                                .setUpdateUser(Constant.SYSTEM)
                                .setHdfsFilePath(hdfsFullPath + compressConvertFile.getName())
                                .setSourceFileNames(convertFileLoadingLog.getSourceFileNames());

                        //转换后文件
                        convertFileLoadingLog
                                .setCustomer(sftpFileDetail.getCustomer())
                                .setSubCustomer(sftpFileDetail.getSubCustomer())
                                .setTestArea(sftpFileDetail.getTestArea())
                                .setFactory(sftpFileDetail.getFactory())
                                .setFactorySite(sftpFileDetail.getFactorySite())
                                .setFab(sftpFileDetail.getFab())
                                .setFabSite(sftpFileDetail.getFabSite())
                                .setFileCategory(sftpFileDetail.getFileCategory())
                                .setFileName(sftpFileDetail.getLocalFileName()) //
                                .setOriginFileName(sftpFileDetail.getOriginFileName())
                                .setFileSize(sftpFileDetail.getFileSize())
                                .setOriginFileSize(sftpFileDetail.getOriginFileSize())
                                .setConvertFlag(sftpFileDetail.getConvertFlag())
                                .setHdfsPath(sftpFileDetail.getHdfsFilePath())
                                .setFtpPath(fileLoadingLog.getFtpPath()) //
                                .setFtpIp(sftpFileDetail.getFtpIp())
                                .setRemoteFileMtime(sftpFileDetail.getRemoteFileMtime())
                                .setStep(StepType.STEP_TYPE_1000.getStep())
                                .setStepEndTime(createTime)
//                            .setTesterName()
//                            .setTesterType()
//                            .setTestProgram()
//                            .setTestProgramVersion()
//                            .setStartT()
//                            .setFinishT()
                                .setWarehousingMode(sftpFileDetail.getWarehousingMode())
                                .setRepairLotWaferId(sftpFileDetail.getRepairLotWaferId())
                                .setCleanupTaskIds(sftpFileDetail.getCleanupTaskIds())
                                .setProcessStatus(ProcessStatus.SUCCESS)
                                .setCreateUser(Constant.SYSTEM)
                                .setUpdateTime(createTime)
                                .setUpdateUser(Constant.SYSTEM);

                        // 查询转换前数据，补充内容
                        List<FileLoadingLog> sourceFileLoadingLogs = getFileLoadingLogRepository().findAllByFileNameInAndStep(
                                getZipFileCollectionToStringConverter().convertToEntityAttribute(sftpFileDetail.getSourceFileNames()),
                                StepType.STEP_TYPE_1000.getStep());
                        Set<String> uniqueWarningMessages = new HashSet<>();
                        if (convertPathMap.containsKey(ConvertStatus.WARNING_MESSAGE_PREFIX.getStatus())) {
                            uniqueWarningMessages.add(convertPathMap.get(ConvertStatus.WARNING_MESSAGE_PREFIX.getStatus()).toString());
                        }
                        if (CollectionUtils.isNotEmpty(sourceFileLoadingLogs)) {
                                for (FileLoadingLog log : sourceFileLoadingLogs) {
                                    String warningMessage = log.getWarningMessage();
                                    if (warningMessage != null && !warningMessage.isEmpty()) {
                                        uniqueWarningMessages.add(warningMessage);
                                    }
                                }
                            fileLoadingLog.setDataIntegrityFileComment(sourceFileLoadingLogs.get(0).getDataIntegrityFileComment())
                                    .setDataIntegrityFileLabel(sourceFileLoadingLogs.get(0).getDataIntegrityFileLabel());
                        }
                        String concatenatedWarningMessages = String.join(Constant.COMMA, uniqueWarningMessages);
                        fileLoadingLog.setWarningMessage(concatenatedWarningMessages);
                        getFileLoadingLogRepository().updateWarningMessageByFileName(fileLoadingLog.getWarningMessage(), fileLoadingLog.getFileName());
                        // 先删除
                        getFileLoadingLogRepository().deleteByFileNameAndStep(convertFileLoadingLog.getFileName(), convertFileLoadingLog.getStep());
                        getFileLoadingLogRepository().save(convertFileLoadingLog);
                        getFileWarehousingRecordManager().updateFileWarehousingStatus(Collections.singletonList(convertFileLoadingLog));
                        getSftpFileDetailRepository().save(sftpFileDetail);

                        // 解析文件元数据,解析失败不影响入库主流程
                        getSourceStandardRuleManager().parseFileNameMetaData(sftpFileDetail);
                    }
                } else {
                    // 仅仅是读取文件
                    readFile(needConvertFile, originalUncompressFile.getAbsolutePath(), executeScriptFile.getAbsolutePath(), fileLoadingLog);
                }
                updateStatusAfterDealSuccess(needConvertFile);
                updateFileLoadingLogStatusAfterDealSuccess(fileLoadingLog);
            }
        } catch (Exception e) {
            updateStatusAfterDealException(needConvertFile, e);
            updateFileLoadingLogStatusAfterDealException(fileLoadingLog, e);
        } finally {
            // 删除需要转换的文件
            if (originalCompressFile != null) {
                clean(originalCompressFile.getParent() + Constant.SLASH, originalCompressFile.getName());
            }
            // 删除转换后的文件
            if (compressConvertFile != null) {
                clean(compressConvertFile.getParent() + Constant.SLASH, compressConvertFile.getName());
            }

            updateStatusFinally(needConvertFile);
            updateFileLoadingLogStatusFinally(fileLoadingLog);
            getFileWarehousingRecordManager().updateFileWarehousingStatus(Collections.singletonList(fileLoadingLog));
        }
    }

    default List<SftpFileConvertScript> findConvertScript(SftpFileDetail needConvertFile) {
        List<SftpFileConvertScript> convertScripts = getSftpFileConvertScriptRepository().findAllBySftpFileName(needConvertFile.getCustomer(), needConvertFile.getSubCustomer(), needConvertFile.getTestArea().getArea(), needConvertFile.getFactory(), needConvertFile.getFactorySite(), getConvertResultFileCategory().getCategory(), needConvertFile.getOriginFileName());
        if (CollectionUtils.isEmpty(convertScripts)) {
            convertScripts = getSftpFileConvertScriptRepository().findAllBySftpFileName(needConvertFile.getCustomer(), needConvertFile.getSubCustomer(), needConvertFile.getTestArea().getArea(), needConvertFile.getFactory(), needConvertFile.getFactorySite(), getConvertResultFileCategory().getCategory(), needConvertFile.getRemoteOriginalFilePath());
        }
        return convertScripts;
    }

    default FileLoadingLog insertFileLoadingLog(SftpFileDetail needConvertFile, Date stepStartTime) {
        FileLoadingLog fileLoadingLog = new FileLoadingLog();
        String remoteOriginalFilePath = needConvertFile.getRemoteOriginalFilePath();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(remoteOriginalFilePath) && !remoteOriginalFilePath.endsWith(Constant.SLASH)) {
            remoteOriginalFilePath = remoteOriginalFilePath + Constant.SLASH;
        }
        fileLoadingLog
                .setCustomer(needConvertFile.getCustomer())
                .setSubCustomer(needConvertFile.getSubCustomer())
                .setTestArea(needConvertFile.getTestArea())
                .setFactory(needConvertFile.getFactory())
                .setFactorySite(needConvertFile.getFactorySite())
                .setFab(needConvertFile.getFab())
                .setFabSite(needConvertFile.getFabSite())
                .setFileCategory(needConvertFile.getFileCategory())
                .setFileName(needConvertFile.getLocalFileName())
                .setOriginFileName(needConvertFile.getOriginFileName())
                .setConvertFlag(needConvertFile.getConvertFlag())
                .setFileSize(needConvertFile.getFileSize())
                .setOriginFileSize(needConvertFile.getOriginFileSize())
                .setSourceFileNames(needConvertFile.getSourceFileNames())
                .setHdfsPath(needConvertFile.getHdfsFilePath())
                .setFtpPath(remoteOriginalFilePath + needConvertFile.getRemoteFileName())
                .setFtpIp(needConvertFile.getFtpIp())
                .setRemoteFileMtime(needConvertFile.getRemoteFileMtime())
                .setStep(StepType.STEP_TYPE_2200.getStep())
                .setStepStartTime(stepStartTime)
                .setWarehousingMode(needConvertFile.getWarehousingMode())
                .setRepairLotWaferId(needConvertFile.getRepairLotWaferId())
                .setCleanupTaskIds(needConvertFile.getCleanupTaskIds())
                .setProcessStatus(ProcessStatus.PROCESSING)
                .setFileSize(needConvertFile.getFileSize())
                .setConvertFlag(needConvertFile.getConvertFlag())
                .setCreateTime(stepStartTime)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateTime(new Date())
                .setUpdateUser(Constant.SYSTEM);

        if (!getNeedCompressAndUploadResultFileFlag()) {
            // collectx 单个文件读取的定时任务（2100）
            fileLoadingLog.setStep(StepType.STEP_TYPE_2100.getStep());
        }

        // 查询step1000数据，补充内容
        List<FileLoadingLog> firstStepFileLoadingLogs = getFileLoadingLogRepository().findAllByFileNameAndStep(
                needConvertFile.getLocalFileName(),
                StepType.STEP_TYPE_1000.getStep());
        if (CollectionUtils.isNotEmpty(firstStepFileLoadingLogs)) {
            Set<String> uniqueWarningMessages = new HashSet<>();
            for (FileLoadingLog log : firstStepFileLoadingLogs) {
                String warningMessage = log.getWarningMessage();
                if (warningMessage != null && !warningMessage.isEmpty()) {
                    uniqueWarningMessages.add(warningMessage);
                }
            }
            String concatenatedWarningMessages = String.join(Constant.COMMA, uniqueWarningMessages);
            fileLoadingLog.setDataIntegrityFileComment(firstStepFileLoadingLogs.get(0).getDataIntegrityFileComment())
                    .setDataIntegrityFileLabel(firstStepFileLoadingLogs.get(0).getDataIntegrityFileLabel())
                    .setWarningMessage(concatenatedWarningMessages);
        }

        // 插入前先删除当前与后续步骤的数据
        getFileLoadingLogRepository().deleteByFileNameAndStep(fileLoadingLog.getFileName(), fileLoadingLog.getStep());
        getFileLoadingLogRepository().save(fileLoadingLog);
        getFileWarehousingRecordManager().updateFileWarehousingStatus(Collections.singletonList(fileLoadingLog));

        return fileLoadingLog;
    }

    int updateStatusBeforeDeal(SftpFileDetail needConvertFile);

    List<SftpFileDetail> getNeedConvertFiles();

    AtomicInteger getThreadActiveCount();

    String getConvertFileSuffix();

    String getNeedConvertLocalPath();

    String getConvertLocalPath();

    SftpFileDetailRepository getSftpFileDetailRepository();

    FileLoadingLogRepository getFileLoadingLogRepository();

    FileWarehousingRecordManager getFileWarehousingRecordManager();

    SftpFileConvertScriptRepository getSftpFileConvertScriptRepository();

    SftpFileConvertRecordRepository getSftpFileConvertRecordRepository();

    CleanupTaskRepository  getCleanupTaskRepository();

    HdfsConfig getHdfsConfig();

    ThreadPoolExecutor getThreadPoolExecutor();

    FileCategory getConvertResultFileCategory();

    void updateStatusFinally(SftpFileDetail needConvertFile);

    void updateFileLoadingLogStatusFinally(FileLoadingLog fileLoadingLog);

    void updateStatusAfterDealException(SftpFileDetail needConvertFile, Exception e);
    KafkaSink getKafkaSink();
    SourceStandardRuleManager getSourceStandardRuleManager();
    default ZipFileCollectionToStringConverter getZipFileCollectionToStringConverter() {
        return ZipFileCollectionToStringConverter.getInstance();
    }

    default void updateFileLoadingLogStatusAfterDealException(FileLoadingLog fileLoadingLog, Exception e) {
        log.error("转换文件异常：", e);
        FileLoadException fileLoadException = e instanceof FileLoadException ? (FileLoadException) e : new FileLoadException(FileLoadExceptionInfo.RAW_DATA_FILE_CONVERT_EXCEPTION, ExceptionUtils.getStackTrace(e), null);

        fileLoadingLog
                .setFailedType(fileLoadException.getFailedType())
                .setFailedFields(fileLoadException.getFailedFields())
                .setExceptionType(fileLoadException.getExceptionType())
                .setExceptionMessage(fileLoadException.getExceptionMessage())
                .setProcessStatus(ProcessStatus.FAIL)
                .setErrorMessage(ExceptionUtils.getStackTrace(e));
        // 发送处理失败通知
        getKafkaSink().sendLoadEndFlagFailMessage(fileLoadingLog);
    }

    void updateStatusAfterDealSuccess(SftpFileDetail needConvertFile);

    void updateFileLoadingLogStatusAfterDealSuccess(FileLoadingLog fileLoadingLog);

    void readFile(SftpFileDetail needConvertFile, String originalFilePath, String executeScriptPath, FileLoadingLog fileLoadingLog) throws Exception;

    Boolean getNeedCompressAndUploadResultFileFlag();

    Pair<Boolean, HashMap<Object, Object>> convertFile(SftpFileDetail needConvertFile, String originalFilePath, String convertFilePath, String executeScriptPath, FileLoadingLog fileLoadingLog, FileLoadingLog sourceFileLoadingLog) throws Exception;

    default Consumer<String> getLogConsumer(FileLoadingLog fileLoadingLog,FileLoadingLog sourceFileLoadingLog) {
        return res -> {
            if (res.startsWith(ConvertStatus.TO_MANY_LOG_PREFIX.getStatus())) {
                String absoulatefileName = res.replace(ConvertStatus.TO_MANY_LOG_PREFIX.getStatus(), Constant.EMPTY).trim();
                if (StringUtils.isNotBlank(absoulatefileName)) {
                    log.info("保存 {} 转换自 {} ", absoulatefileName, fileLoadingLog.getSourceFileNames());
                    Date time = new Date();
                    SftpFileConvertRecord sftpFileConvertRecord = new SftpFileConvertRecord()
                            .setFileName(absoulatefileName)
                            .setSourceFileNames(fileLoadingLog.getSourceFileNames())
                            .setCreateTime(time)
                            .setUpdateTime(time)
                            .setCreateUser(Constant.SYSTEM)
                            .setUpdateUser(Constant.SYSTEM);
                    getSftpFileConvertRecordRepository().save(sftpFileConvertRecord);
                }
            }
            if (res.startsWith(ConvertStatus.WARNING_MESSAGE_PREFIX.getStatus())) {
                String warningMessage = res.replace(ConvertStatus.WARNING_MESSAGE_PREFIX.getStatus(), Constant.EMPTY).trim();
                if (StringUtils.isNotBlank(warningMessage)) {
                    log.info("转换后有警告信息: {} ",  warningMessage);
                    fileLoadingLog.setWarningMessage(warningMessage);
                    sourceFileLoadingLog.setWarningMessage(warningMessage);
                }
            }
        };
    }

    default void saveSftpFileConvertRecord(String convertFileNames, String sourceFileNames) {
        if (StringUtils.isNotBlank(convertFileNames)) {
            String[] currentConvertFileNames = convertFileNames.split(Constant.COMMA);
            if (currentConvertFileNames.length > 0) {
                Set<String> currentConvertFileNameSet = Arrays.stream(currentConvertFileNames).collect(Collectors.toSet());

                // 如果在转换任务结束前已有部分文件被collectX拉取，则需要更新dw_sftp_file_detail和dw_file_loading_log的source_file_names
                List<String> localFileNameList = currentConvertFileNameSet.stream()
                        .map(path -> path.substring(path.lastIndexOf(Constant.SLASH) + 1) + Constant.POINT + FileType.ZIP.getType())
                        .collect(Collectors.toList());
                String cleanupTaskId = sourceFileNames == null ? null :
                        getSftpFileDetailRepository().findAllByLocalFileNameIn(getZipFileCollectionToStringConverter().convertToEntityAttribute(sourceFileNames))
                                .stream()
                                .map(SftpFileDetail::getCleanupTaskIds)
                                .filter(StringUtils::isNotBlank)
                                .flatMap(t -> Stream.of(t.split(Constant.COMMA)).map(s -> Long.parseLong(s.trim())))
                                .distinct()
                                .sorted()
                                .map(String::valueOf)
                                .collect(Collectors.joining(Constant.COMMA));
                // 更新dw_sftp_file_detail
                getSftpFileDetailRepository().updateSourceFileNamesFromLocalFileNames(localFileNameList, sourceFileNames, cleanupTaskId);
                // 更新dw_file_loading_log
                getFileLoadingLogRepository().updateSourceFileNamesFromFileNames(localFileNameList, sourceFileNames, cleanupTaskId);
            }
        }
    }

}
