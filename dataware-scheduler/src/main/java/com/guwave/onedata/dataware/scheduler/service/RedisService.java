package com.guwave.onedata.dataware.scheduler.service;

import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.model.key.LotWaferPrimaryKey;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.MIDDLE_LINE;

@Service
@SuppressWarnings("unchecked")
public class RedisService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RedisService.class);

    private static final List<TestArea> SUPPORT_TEST_AREA_LIST = TestArea.getCPList();

    public final static String lockDwDieValue = "AllDieTable";
    public final static String lockDwDieMesPatchValue = "MesPatch";

    @Value("${spring.redis.lockExpireTime}")
    private long lockExpireTime;
    @Value("${spring.redis.unlockExpireTime}")
    private long unlockExpireTime;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private RedissonClient redissonClient;


    public void clearAll() {
        clearLockDwDieTask();
    }

    public void clearKey(String key) {
        key = key + Constant.MULTIPLICATION_SIGN;
        Set<String> keys = redisTemplate.keys(key);
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }
        keys.forEach(t -> {
            LOGGER.info("删除redis key：{}", t);
            redisTemplate.delete(t);
        });
    }

    /**
     * 对dw任务上锁
     */
    public boolean lockDwDieTask(List<FileCategory> fileCategorys, String customer, TestArea testArea, String factory, String deviceId, LotType lotType, String testStage, String lotId, String waferNo, String value) {
        boolean lock;
        String key = generateDwDieKey(fileCategorys, customer, testArea, factory, deviceId, lotType, testStage, lotId, waferNo);
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            LOGGER.info("lock处理中: {}", key);
            lock = false;
        } else {
            lock = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, value, lockExpireTime, TimeUnit.SECONDS));
        }
        return lock;
    }

    private static String generateDwDieKey(List<FileCategory> fileCategorys, String customer, TestArea testArea, String factory, String deviceId, LotType lotType, String testStage, String lotId, String waferNo) {
        String key = "DwDieRunning:" + fileCategorys.stream().map(FileCategory::getCategory).collect(Collectors.joining(Constant.COMMA)) + Constant.UNDER_LINE + customer + Constant.UNDER_LINE + testArea.getArea()
                + Constant.UNDER_LINE + factory + Constant.UNDER_LINE + deviceId + Constant.UNDER_LINE + lotType.getType()
                + Constant.UNDER_LINE + testStage + Constant.UNDER_LINE + lotId;
        if (SUPPORT_TEST_AREA_LIST.contains(testArea)) {
            key += Constant.UNDER_LINE + waferNo;
        }
        return key;
    }

    /**
     * 对dw任务解锁
     */
    public void unlockDwDieTask(List<FileCategory> fileCategorys, String customer, TestArea testArea, String factory, String deviceId, LotType lotType, String testStage, String lotId, String waferNo, String value) {
        String key = generateDwDieKey(fileCategorys, customer, testArea, factory, deviceId, lotType, testStage, lotId, waferNo);
        String str = redisTemplate.opsForValue().get(key);
        if (value.equals(str)) {
            redisTemplate.delete(key);
        } else {
            LOGGER.info("key : {} ,value : {} 与要解锁的value ： {} 不相同，不删除key！", key, str, value);
        }
    }

    public void clearLockDwDieTask() {
        clearKey("DwDieRunning");
    }

    /**
     * 多个地方更新同一张表拿锁
     */
    public RLock getUpdateDwTaskTableLock(FileCategory fileCategory, String customer, TestArea testArea, String factory, String deviceId, LotType lotType, String testStage, String lotId, String waferNo) {
        String key = "UpdateDwTaskTable:" + fileCategory.getCategory() + Constant.UNDER_LINE + customer + Constant.UNDER_LINE + testArea.getArea()
                + Constant.UNDER_LINE + factory + Constant.UNDER_LINE + deviceId + Constant.UNDER_LINE + lotType.getType()
                + Constant.UNDER_LINE + testStage + Constant.UNDER_LINE + lotId;
        if (SUPPORT_TEST_AREA_LIST.contains(testArea)) {
            key += Constant.UNDER_LINE + waferNo;
        }
        return redissonClient.getLock(key);
    }

    /**
     * 更新主批次表
     */
    public RLock getUpdateLotWaferPrimaryDataLock(LotWaferPrimaryKey lotWaferPrimaryKey) {
        String key = "UpdateLotWaferPrimaryDataLock:" +
                lotWaferPrimaryKey.getCustomer()
                + MIDDLE_LINE
                + lotWaferPrimaryKey.getSubCustomer()
                + MIDDLE_LINE
                + lotWaferPrimaryKey.getFactory()
                + MIDDLE_LINE
                + lotWaferPrimaryKey.getFactorySite()
                + MIDDLE_LINE
                + TestArea.of(lotWaferPrimaryKey.getTestArea())
                + MIDDLE_LINE
                + lotWaferPrimaryKey.getDeviceId()
                + MIDDLE_LINE + lotWaferPrimaryKey.getLotId()
                + MIDDLE_LINE + lotWaferPrimaryKey.getSblotId();

        return redissonClient.getLock(key);
    }


}
