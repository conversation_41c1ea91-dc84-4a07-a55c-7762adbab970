package com.guwave.onedata.dataware.scheduler.service.rpc;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.bridge.api.iface.ICalculateTestItemRpcService;
import com.guwave.onedata.dataware.bridge.api.vo.request.CalTestItemWaferVo;
import com.guwave.onedata.dataware.bridge.api.vo.WaferVo;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.DwLayer;
import com.guwave.onedata.dataware.common.contant.Platform;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.StepType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LayerCalculatePool;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotStockingDetail;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotWaferCalRecord;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotWaferCalStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.TestItemReloadRecord;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.FileLoadingLogRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.FileWarehousingRecordRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LayerCalculatePoolRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LotStockingDetailRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LotWaferCalRecordRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LotWaferCalStatusRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LotWaferWarehousingRecordRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.TestItemReloadRecordRepository;
import com.guwave.onedata.dataware.scheduler.provider.KafKaProvider;
import com.guwave.onedata.dataware.scheduler.util.ThreadPoolUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;


/**
 * 2025/1/14 17:27
 * CalculateTestItemService
 *
 * <AUTHOR>
 */
@DubboService
public class CalculateTestItemRpcService implements ICalculateTestItemRpcService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CalculateTestItemRpcService.class);

    @Autowired
    private LotWaferCalStatusRepository lotWaferCalStatusRepository;
    @Autowired
    private LotWaferCalRecordRepository lotWaferCalRecordRepository;
    @Autowired
    private FileLoadingLogRepository fileLoadingLogRepository;
    @Autowired
    private FileWarehousingRecordRepository fileWarehousingRecordRepository;
    @Autowired
    private LotWaferWarehousingRecordRepository lotWaferWarehousingRecordRepository;
    @Autowired
    private LotStockingDetailRepository lotStockingDetailRepository;
    @Autowired
    private LayerCalculatePoolRepository layerCalculatePoolRepository;
    @Autowired
    private TestItemReloadRecordRepository testItemReloadRecordRepository;

    @Autowired
    private KafKaProvider kafKaProvider;

    @Value("${spring.kafka.loadEndFlagTopic}")
    private String loadEndFlagTopic;

    private Executor executor;

    @PostConstruct
    public void init() {
        executor = ThreadPoolUtils.getNewThreadPoolExecutor(this.getClass().getName(), 1, 1, 10000);
    }


//    @Override
//    public List<WaferVo> findWaferToTriggerCalTestItem(@NonNull String customer,
//                                                       @NonNull TestArea testArea,
//                                                       @NonNull String factory,
//                                                       @NonNull String deviceId,
//                                                       Integer batchSize) {
//        LOGGER.info("查询最近入库成功且没有跑测试项表的批次, customer={},testArea={},factory={},deviceId={},batchSize={}",
//                customer, testArea, factory, deviceId, batchSize);
//        batchSize = batchSize == null ? 1000 : batchSize;
//
//        List<LotWaferCalStatus> lotWaferCalStatusList = lotWaferCalStatusRepository.findAllByCustomerAndTestAreaAndFactoryAndDeviceIdAndCalculateYmsTestItemAndDieStatusAndTestItemStatusOrderByUpdateTimeDesc(
//                customer,
//                testArea,
//                factory,
//                deviceId,
//                0,
//                ProcessStatus.SUCCESS,
//                ProcessStatus.SUCCESS,
//                Pageable.ofSize(batchSize)
//        );
//        if (CollectionUtils.isEmpty(lotWaferCalStatusList)) {
//            LOGGER.info("没找到入库成功且没有跑测试项表的批次");
//            return Collections.emptyList();
//        }
//
//        List<WaferVo> waferVoList = lotWaferCalStatusList.stream()
//                .map(lotWaferCalStatus -> new WaferVo()
//                        .setCustomer(lotWaferCalStatus.getCustomer())
//                        .setSubCustomer(lotWaferCalStatus.getSubCustomer())
//                        .setFactory(lotWaferCalStatus.getFactory())
//                        .setFactorySite(lotWaferCalStatus.getFactorySite())
//                        .setTestArea(lotWaferCalStatus.getTestArea())
//                        .setFileCategory(lotWaferCalStatus.getFileCategory())
//                        .setDeviceId(lotWaferCalStatus.getDeviceId())
//                        .setLotType(lotWaferCalStatus.getLotType())
//                        .setTestStage(lotWaferCalStatus.getTestStage())
//                        .setLotId(lotWaferCalStatus.getLotId())
//                        .setWaferNo(lotWaferCalStatus.getWaferNo())
//                ).collect(Collectors.toList());
//
//        LOGGER.info("找到最近入库成功且没有跑测试项表的wafer, size:{}", waferVoList.size());
//
//        return waferVoList;
//    }

    @Override
    public void triggerCalTestItem(CalTestItemWaferVo calTestItemWaferVo) {
        if (calTestItemWaferVo.getWaferVos().stream().anyMatch(waferVo -> TestArea.getCpMapDataSourceList().contains(waferVo.getTestArea()) || TestArea.getCpInklessMapDataDourceList().contains(waferVo.getTestArea()))) {
            LOGGER.error("dataSource为CP Map、Inkless Map的批次不支持触发跑测项");
            throw new RuntimeException("dataSource为CP Map、Inkless Map的批次不支持触发跑测项");
        }
        if (CollectionUtils.isEmpty(calTestItemWaferVo.getWaferVos())) {
            LOGGER.error("参数校验失败, 参数为空");
            throw new RuntimeException("参数校验失败, 参数为空");
        } else if (Objects.equals(false,calTestItemWaferVo.isCalculateDwTestItem())) {
            LOGGER.error("参数校验失败, calculateDwTestItem必须为true");
            throw new RuntimeException("参数校验失败, calculateDwTestItem必须为true");
        } else if (calTestItemWaferVo.getWaferVos().stream().anyMatch(waferVo -> !WaferVo.paramsCheck(waferVo))) {
            LOGGER.error("参数校验失败, waferVo计算维度值不能为空");
            throw new RuntimeException("参数校验失败, waferVo参数校验失败");
        }
        LOGGER.info("触发已入库的批次跑测项表:{}", JSON.toJSONString(calTestItemWaferVo));

        executor.execute(() -> {
            LOGGER.info("开始触发跑测项表， size:{}", calTestItemWaferVo.getWaferVos().size());
            calTestItem(calTestItemWaferVo);
        });

    }

    private void calTestItem(CalTestItemWaferVo calTestItemWaferVo) {
        List<TestItemReloadRecord> testItemReloadRecordList = calTestItemWaferVo.getWaferVos().stream()
                .map(waferVo -> new TestItemReloadRecord()
                        .setCustomer(waferVo.getCustomer())
                        .setSubCustomer(waferVo.getSubCustomer())
                        .setFactory(waferVo.getFactory())
                        .setFactorySite(waferVo.getFactorySite())
                        .setTestArea(waferVo.getTestArea())
                        .setLotType(waferVo.getLotType())
                        .setDeviceId(waferVo.getDeviceId())
                        .setLotId(waferVo.getLotId())
                        .setWaferNo(waferVo.getWaferNo())
                        .setTestStage(waferVo.getTestStage())
                        .setFileCategory(waferVo.getFileCategory())
                        .setCalculateDwTestItem(calTestItemWaferVo.isCalculateDwTestItem() ? 1 : 0)
                        .setCalculateYmsTestItem(calTestItemWaferVo.isCalculateYmsTestItem() ? 1 : 0)
                        .setProcessStatus(ProcessStatus.CREATE)
                        .setExceptionMessage(null)
                        .setErrorMessage(null)
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date())
                        .setCreateUser(Constant.SYSTEM)
                        .setUpdateUser(Constant.SYSTEM)).collect(Collectors.toList());
        testItemReloadRecordRepository.saveAll(testItemReloadRecordList);

        for (TestItemReloadRecord testItemReloadRecord : testItemReloadRecordList) {
            try {
                LOGGER.info("开始跑测项表,wafer:{}", JSON.toJSONString(testItemReloadRecord));

                LotWaferCalStatus lotWaferCalStatus = lotWaferCalStatusRepository.findFirstByCustomerAndTestAreaAndFileCategoryAndFactoryAndDeviceIdAndLotIdAndWaferNoAndTestStageAndLotTypeOrderByUpdateTimeDesc(
                        testItemReloadRecord.getCustomer(),
                        testItemReloadRecord.getTestArea(),
                        testItemReloadRecord.getFileCategory(),
                        testItemReloadRecord.getFactory(),
                        testItemReloadRecord.getDeviceId(),
                        testItemReloadRecord.getLotId(),
                        testItemReloadRecord.getWaferNo(),
                        testItemReloadRecord.getTestStage(),
                        testItemReloadRecord.getLotType()
                );
                if (lotWaferCalStatus == null) {
                    LOGGER.error("lotId:{},waferNo:{} 没有找到入库成功的记录, 不触发跑测项表", testItemReloadRecord.getLotId(), testItemReloadRecord.getWaferNo());
                    testItemReloadRecord
                            .setExceptionMessage("没有找到入库成功的记录, 不触发跑测项表")
                            .setErrorMessage("没有找到入库成功的记录, 不触发跑测项表")
                            .setProcessStatus(ProcessStatus.FAIL)
                            .setUpdateTime(new Date());
                } else if (!Objects.equals(ProcessStatus.SUCCESS, lotWaferCalStatus.getDieStatus()) || !Objects.equals(ProcessStatus.SUCCESS, lotWaferCalStatus.getTestItemStatus())) {
                    LOGGER.error("lotId:{},waferNo:{} 任务未结束, 不触发跑测项表， dieStatus = {}, testItemStatus = {}", testItemReloadRecord.getLotId(), testItemReloadRecord.getWaferNo(), lotWaferCalStatus.getDieStatus(), lotWaferCalStatus.getTestItemStatus());
                    testItemReloadRecord
                            .setExceptionMessage("任务未结束, 不触发跑测项表")
                            .setErrorMessage("任务未结束, 不触发跑测项表")
                            .setProcessStatus(ProcessStatus.FAIL)
                            .setUpdateTime(new Date());
                } else if (Objects.equals(1, lotWaferCalStatus.getCalculateDwTestItem()) && Objects.equals(1, lotWaferCalStatus.getCalculateYmsTestItem())) {
                    LOGGER.error("lotId:{},waferNo:{} 已经跑过测试项表了，不触发跑测项表", testItemReloadRecord.getLotId(), testItemReloadRecord.getWaferNo());
                    testItemReloadRecord
                            .setExceptionMessage("已经跑过测试项表了，不触发跑测项表")
                            .setErrorMessage("已经跑过测试项表了，不触发跑测项表")
                            .setProcessStatus(ProcessStatus.FAIL)
                            .setUpdateTime(new Date());
                } else {
                    Integer calculateDwTestItem = testItemReloadRecord.getCalculateDwTestItem();
                    Integer calculateYmsTestItem = testItemReloadRecord.getCalculateYmsTestItem();

                    LOGGER.info("lotId:{},waferNo:{} 跑测项表,calculateDwTestItem:{}, calculateYmsTestItem:{}", testItemReloadRecord.getLotId(), testItemReloadRecord.getWaferNo(), calculateDwTestItem, calculateYmsTestItem);

                    List<LotStockingDetail> lotStockingDetails = lotStockingDetailRepository.findAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoAndFileCategory(
                            testItemReloadRecord.getCustomer(),
                            testItemReloadRecord.getTestArea(),
                            testItemReloadRecord.getFactory(),
                            testItemReloadRecord.getFactorySite(),
                            testItemReloadRecord.getDeviceId(),
                            testItemReloadRecord.getLotType(),
                            testItemReloadRecord.getTestStage(),
                            testItemReloadRecord.getLotId(),
                            testItemReloadRecord.getWaferNo(),
                            testItemReloadRecord.getFileCategory()
                    ).stream().filter(t -> Objects.equals(t.getDieFinishFlag(), 1) && Objects.equals(t.getTestItemFinishFlag(), 1)).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(lotStockingDetails)) {
                        LOGGER.error("TestItem不满足更新lotWaferCalStatus状态的条件");
                        testItemReloadRecord
                                .setExceptionMessage("TestItem不满足更新lotWaferCalStatus状态的条件")
                                .setErrorMessage("TestItem不满足更新lotWaferCalStatus状态的条件")
                                .setProcessStatus(ProcessStatus.FAIL)
                                .setUpdateTime(new Date());
                        return;
                    }
                    List<String> fileNames = lotStockingDetails.stream().map(LotStockingDetail::getFileName).collect(Collectors.toList());

                    // 更新lotWaferCalStatus
                    updateLotWaferCalStatus(lotWaferCalStatus, calculateDwTestItem, calculateYmsTestItem);
                    // 更新lotWaferCalRecord
                    updateLotWaferCalRecord(lotWaferCalStatus, calculateDwTestItem, calculateYmsTestItem);
                    // 更新fileLoadingLog
                    fileLoadingLogRepository.updateProcessStatusFromFileNameIn(fileNames, StepType.STEP_TYPE_7100.getStep(), ProcessStatus.PROCESSING);
                    // 更新fileWarehousingRecord
                    fileWarehousingRecordRepository.updateProcessStatusFromFileNameIn(fileNames, StepType.STEP_TYPE_7100.getStep(), ProcessStatus.PROCESSING);
                    // 更新LotWaferWarehousingRecord
                    updateLotWaferWarehousingRecord(lotWaferCalStatus);
                    // 更新lotStockingDetail
                    lotStockingDetailRepository.updateTestItemFinishFlagFromFileNameIn(fileNames, 0);
                    // 发送ADS任务成功的消息到t_dw_load_end_flag
                    sendLoadEndFlag(lotWaferCalStatus);

                    testItemReloadRecord
                            .setExceptionMessage(null)
                            .setErrorMessage(null)
                            .setProcessStatus(ProcessStatus.PROCESSING)
                            .setUpdateTime(new Date());
                }
            } catch (Exception e) {
                testItemReloadRecord
                        .setExceptionMessage("触发跑测项表失败")
                        .setErrorMessage("触发跑测项表失败:" + ExceptionUtils.getStackTrace(e))
                        .setProcessStatus(ProcessStatus.FAIL)
                        .setUpdateTime(new Date());
            } finally {
                testItemReloadRecordRepository.save(testItemReloadRecord);
            }
        }

    }

    private void updateLotWaferCalStatus(LotWaferCalStatus lotWaferCalStatus, Integer calculateDwTestItem, Integer calculateYmsTestItem) {
        LOGGER.info("更新LotWaferCalStatus");
        lotWaferCalStatus
                .setCalculateDwTestItem(calculateDwTestItem)
                .setCalculateYmsTestItem(calculateYmsTestItem)
                .setTestItemStatus(ProcessStatus.PROCESSING)
                .setUpdateTime(new Date());
        lotWaferCalStatusRepository.save(lotWaferCalStatus);
    }

    private void updateLotWaferCalRecord(LotWaferCalStatus lotWaferCalStatus, Integer calculateDwTestItem, Integer calculateYmsTestItem) {
        LOGGER.info("更新LotWaferCalRecord");
        LotWaferCalRecord lotWaferCalRecord = lotWaferCalRecordRepository.findFirstByCustomerAndFactoryAndTestAreaAndFileCategoryAndDeviceIdAndLotIdAndWaferNoAndTestStageAndLotTypeAndLatestFlagOrderByUpdateTimeDesc(
                lotWaferCalStatus.getCustomer(), lotWaferCalStatus.getFactory(), lotWaferCalStatus.getTestArea(), lotWaferCalStatus.getFileCategory(), lotWaferCalStatus.getDeviceId(), lotWaferCalStatus.getLotId(), lotWaferCalStatus.getWaferNo(), lotWaferCalStatus.getTestStage(), lotWaferCalStatus.getLotType(), 1
        );
        lotWaferCalRecordRepository.updateTestItemStatusById(lotWaferCalRecord.getId(), ProcessStatus.PROCESSING, calculateDwTestItem, calculateYmsTestItem);
    }

    private void updateLotWaferWarehousingRecord(LotWaferCalStatus lotWaferCalStatus) {
        LOGGER.info("更新LotWaferWarehousingRecord");
        lotWaferWarehousingRecordRepository.updateProcessStatusForTestItem(
                lotWaferCalStatus.getCustomer(),
                lotWaferCalStatus.getTestArea(),
                lotWaferCalStatus.getFactory(),
                lotWaferCalStatus.getDeviceId(),
                lotWaferCalStatus.getLotType(),
                lotWaferCalStatus.getTestStage(),
                lotWaferCalStatus.getLotId(),
                lotWaferCalStatus.getWaferNo(),
                lotWaferCalStatus.getFileCategory(),
                ProcessStatus.PROCESSING,
                new Date(),
                ProcessStatus.PROCESSING
        );
    }

    private void sendLoadEndFlag(LotWaferCalStatus lotWaferCalStatus) {
        LOGGER.info("发送ADS任务的消息到topic:{}, 触发计算测项", loadEndFlagTopic);

        List<LayerCalculatePool> layerCalculatePoolList = layerCalculatePoolRepository.findByCustomerAndTestAreaAndFactoryAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoAndFileCategoryAndDwLayerAndDelayFlagAndProcessStatusInOrderByIdDesc(
                lotWaferCalStatus.getCustomer(),
                lotWaferCalStatus.getTestArea(),
                lotWaferCalStatus.getFactory(),
                lotWaferCalStatus.getDeviceId(),
                lotWaferCalStatus.getLotType(),
                lotWaferCalStatus.getTestStage(),
                lotWaferCalStatus.getLotId(),
                lotWaferCalStatus.getWaferNo(),
                lotWaferCalStatus.getFileCategory(),
                DwLayer.ADS,
                0,
                Collections.singletonList(ProcessStatus.SUCCESS),
                Pageable.ofSize(1)
        );
        if (CollectionUtils.isEmpty(layerCalculatePoolList)) {
            LOGGER.info("未找到对应的ADS任务");
            return;
        }
        LayerCalculatePool layerCalculatePool = layerCalculatePoolList.get(0);

        CalculateEndFlag calculateEndFlag = buildCalculateEndFlag(layerCalculatePool, lotWaferCalStatus, Platform.GDP);
        kafKaProvider.sendKafka(loadEndFlagTopic, JSON.toJSONString(calculateEndFlag));
        CalculateEndFlag ckCalculateEndFlag = buildCalculateEndFlag(layerCalculatePool, lotWaferCalStatus, Platform.CK);
        kafKaProvider.sendKafka(loadEndFlagTopic, JSON.toJSONString(ckCalculateEndFlag));
    }

    private CalculateEndFlag buildCalculateEndFlag(LayerCalculatePool layerCalculatePool, LotWaferCalStatus lotWaferCalStatus, Platform platform) {
        return new CalculateEndFlag()
                .setCustomer(layerCalculatePool.getCustomer())
                .setSubCustomer(layerCalculatePool.getSubCustomer())
                .setFactory(layerCalculatePool.getFactory())
                .setFactorySite(layerCalculatePool.getFactorySite())
                .setTestArea(layerCalculatePool.getTestArea())
                .setLotType(layerCalculatePool.getLotType())
                .setDeviceId(layerCalculatePool.getDeviceId())
                .setTestStage(layerCalculatePool.getTestStage())
                .setLotId(layerCalculatePool.getLotId())
                .setWaferNo(layerCalculatePool.getWaferNo())
                .setFileCategory(layerCalculatePool.getFileCategory())
                .setDwLayer(layerCalculatePool.getDwLayer())
                .setNextDwLayer(layerCalculatePool.getNextDwLayer())
                .setPriority(layerCalculatePool.getPriority())
                .setPlatform(platform)
                .setWarehousingMode(layerCalculatePool.getWarehousingMode())
                .setRepairLotWaferId(layerCalculatePool.getRepairLotWaferId())
                .setCleanupTaskIds(layerCalculatePool.getCleanupTaskIds())
                .setProcessStatus(layerCalculatePool.getProcessStatus())
                .setAppId(layerCalculatePool.getAppId())
                .setExceptionType(layerCalculatePool.getExceptionType())
                .setExceptionMessage(layerCalculatePool.getExceptionMessage())
                .setErrorMessage(layerCalculatePool.getErrorMessage())
                .setProcessConsume(0L)
                .setTs(System.currentTimeMillis())
                .setDataVersion(layerCalculatePool.getDataVersion())
                .setDieCount(lotWaferCalStatus.getDieTotalCnt())
                .setTestItemCount(lotWaferCalStatus.getTestItemTotalCnt())
                .setCalculateDwTestItem(lotWaferCalStatus.getCalculateDwTestItem())
                .setCalculateYmsTestItem(lotWaferCalStatus.getCalculateYmsTestItem());
    }

}
