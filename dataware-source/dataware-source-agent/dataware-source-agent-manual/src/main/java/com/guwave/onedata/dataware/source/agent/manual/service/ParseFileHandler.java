package com.guwave.onedata.dataware.source.agent.manual.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.repair.FlowIdDetail;
import com.guwave.onedata.dataware.common.util.FlowIdUtil;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.dataware.parser.stdf.model.FileMainData;
import com.guwave.onedata.dataware.parser.stdf.visitor.Visitor;
import com.guwave.onedata.dataware.source.agent.common.model.FileTestInfo;
import com.guwave.onedata.dataware.source.agent.common.sink.filesystem.impl.HdfsSink;
import com.guwave.onedata.dataware.source.agent.common.util.FileSystemUtil;
import com.guwave.onedata.dataware.source.agent.common.util.FileTestInfoUtil;
import com.guwave.onedata.dataware.source.agent.common.util.HdfsUtil;
import com.guwave.onedata.dataware.source.agent.common.visitor.MultiThreadVisitor;
import com.guwave.onedata.dataware.source.agent.manual.model.ManualConvertFileInfo;
import com.guwave.onedata.dataware.source.agent.manual.model.ManualConvertResult;
import com.guwave.onedata.dataware.source.agent.manual.util.PythonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;
import static com.guwave.onedata.dataware.common.contant.Constant.TYPE;

public interface ParseFileHandler {
    Logger LOGGER = LoggerFactory.getLogger(ParseFileHandler.class);

    Boolean support(FileCategory fileCategory, ParseRule parseRule);

    default FileCategory calRealFileCategory(File localFile, File scriptFile, ManualFileInfo manualFileInfo) {
        return manualFileInfo.getFileCategory();
    }

    void dealFile(ManualCalculateTask manualCalculateTask, File localFile, ManualFileInfo manualFileInfo, File scriptFile);

    default boolean hasNext() {
        return true;
    }

    default Consumer<FileMainData> defaultFillFileMainDataConsumer(ManualFileInfo manualFileInfo) {
        return fileMainData -> {
            fileMainData.setCustomer(manualFileInfo.getCustomer());
            fileMainData.setSubCustomer(manualFileInfo.getSubCustomer());
            fileMainData.setFileCategory(manualFileInfo.getFileCategory());
            if (StringUtils.isNotBlank(manualFileInfo.getTestStage())) {
                fileMainData.setTestStage(manualFileInfo.getTestStage());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getFactory())) {
                fileMainData.setFactory(manualFileInfo.getFactory());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getFactorySite())) {
                fileMainData.setFactorySite(manualFileInfo.getFactorySite());
            }
            if (manualFileInfo.getLotType() != null) {
                fileMainData.setLotType(manualFileInfo.getLotType());
            }
            fileMainData.setFileInfoId(manualFileInfo.getFileId());
            fileMainData.setFileName(manualFileInfo.getFileName());
            if (StringUtils.isNotBlank(manualFileInfo.getDeviceId())) {
                fileMainData.setDeviceId(manualFileInfo.getDeviceId());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getLotId())) {
                fileMainData.setLotId(manualFileInfo.getLotId());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getOriginWaferId())) {
                fileMainData.setOriginWaferId(manualFileInfo.getOriginWaferId());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getWaferNo())) {
                fileMainData.setWaferNo(manualFileInfo.getWaferNo());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getSblotId())) {
                fileMainData.setSblotId(manualFileInfo.getSblotId());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getTestProgram())) {
                fileMainData.setTestProgram(manualFileInfo.getTestProgram());
            }
            if (manualFileInfo.getFileCategory() == FileCategory.RAW_DATA) {
                if (StringUtils.isBlank(fileMainData.getTestProgram())) {
                    fileMainData.setTestProgram(DEFAULT_TEST_PROGRAM_SUFFIX + manualFileInfo.getFileId());
                }
            }
            if (StringUtils.isNotBlank(manualFileInfo.getTestTemperature())) {
                fileMainData.setTestTemperature(manualFileInfo.getTestTemperature());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getTestProgramVersion())) {
                fileMainData.setTestProgramVersion(manualFileInfo.getTestProgramVersion());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getTesterName())) {
                fileMainData.setTesterName(manualFileInfo.getTesterName());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getTesterType())) {
                fileMainData.setTesterType(manualFileInfo.getTesterType());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getProberHandlerId())) {
                fileMainData.setProberHandlerId(manualFileInfo.getProberHandlerId());
            }
            if (manualFileInfo.getStartT() != null) {
                fileMainData.setStartT((manualFileInfo.getStartT().getTime() / 1000) + EMPTY);
            }
            if (manualFileInfo.getFinishT() != null) {
                fileMainData.setFinishT((manualFileInfo.getFinishT().getTime() / 1000) + EMPTY);
            }
            if (StringUtils.isNotBlank(manualFileInfo.getTestCod())) {
                fileMainData.setTestCod(manualFileInfo.getTestCod());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getPkgTyp())) {
                fileMainData.setPkgTyp(manualFileInfo.getPkgTyp());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getFloorId())) {
                fileMainData.setFloorId(manualFileInfo.getFloorId());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getRetestBinNum())) {
                fileMainData.setRetestBinNum(manualFileInfo.getRetestBinNum());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getPosX())) {
                fileMainData.setPosX(manualFileInfo.getPosX());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getPosY())) {
                fileMainData.setPosY(manualFileInfo.getPosY());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getNotch())) {
                fileMainData.setNotch(manualFileInfo.getNotch());
            }
            if (StringUtils.isNotBlank(manualFileInfo.getProcess())) {
                fileMainData.setProcess(manualFileInfo.getProcess());
            }
            fileMainData.setCreateUser(manualFileInfo.getFileOwner());
            fileMainData.setUploadType(manualFileInfo.getUploadType());

            FlowIdDetail flowIdDetail = FlowIdUtil.flatStandardFlowId(manualFileInfo.getFlowId());
            fileMainData.setOfflineRetest(flowIdDetail.getOfflineRetest());
            fileMainData.setInterrupt(flowIdDetail.getInterrupt());
            fileMainData.setDupRetest(flowIdDetail.getDupRetest());
            fileMainData.setBatchNum(flowIdDetail.getBatchNum());

            fileMainData.setOfflineRetestIgnoreTp(flowIdDetail.getOfflineRetest());
            fileMainData.setInterruptIgnoreTp(flowIdDetail.getInterrupt());
            fileMainData.setDupRetestIgnoreTp(flowIdDetail.getDupRetest());
            fileMainData.setBatchNumIgnoreTp(flowIdDetail.getBatchNum());

            fileMainData.setConditionSet(manualFileInfo.getConditionSet());
        };
    }

    default void defaultFillManualFileInfo(ManualFileInfo manualFileInfo, List<Visitor> visitors, File localFile) {
        FileTestInfo fileTestInfo = FileTestInfoUtil.calculateFileTestInfo(visitors.stream().map(t -> (MultiThreadVisitor) t).collect(Collectors.toList()));

        FileMainData fileMainData = ((MultiThreadVisitor) visitors.get(0)).getFileMainData();
        manualFileInfo
                .setFileSize(localFile.length())
                .setDeviceId(fileMainData.getDeviceId())
                .setLotId(fileMainData.getLotId())
                .setWaferNo(fileMainData.getWaferNo())
                .setLotType(fileMainData.getLotType())
                .setSblotId(fileMainData.getSblotId())
                .setWaferId(fileMainData.getWaferId())
                .setOriginWaferId(fileMainData.getOriginWaferId())
                .setTestArea(fileMainData.getTestArea())
                .setFactory(fileMainData.getFactory())
                .setFactorySite(fileMainData.getFactorySite())
                .setTestStage(fileMainData.getTestStage())
                .setTestCod(fileMainData.getTestCod())
                .setFlowId(fileMainData.getStandardFlowId())
                .setTesterName(fileMainData.getTesterName())
                .setTesterType(fileMainData.getTesterType())
                .setTestProgram(fileMainData.getTestProgram())
                .setTestProgramVersion(fileMainData.getTestProgramVersion())
                .setTestTemperature(fileMainData.getTestTemperature())
                .setProberHandlerId(fileMainData.getProberHandlerId())
                .setPkgTyp(fileMainData.getPkgTyp())
                .setRetestBinNum(fileMainData.getRetestBinNum())
                .setPosX(fileMainData.getPosX())
                .setPosY(fileMainData.getPosY())
                .setNotch(fileMainData.getNotch())
                .setFloorId(fileMainData.getFloorId())
                .setProcess(fileMainData.getProcess())
                .setStartT(new Date(fileMainData.getStartTime()))
                .setFinishT(new Date(fileMainData.getEndTime()))
                .setConditionSet(fileMainData.getConditionSet())
                .setDieDataCount(fileTestInfo.getDieDataCnt())
                .setTestItemDataCount(fileTestInfo.getTestItemDataCnt())
                .setMaxRecordTestItem(fileTestInfo.getTestItemCntMax() != null ? fileTestInfo.getTestItemCntMax().getKey() : EMPTY)
                .setMaxRecordTestItemCnt(fileTestInfo.getTestItemCntMax() != null ? fileTestInfo.getTestItemCntMax().getValue() : 0L)
                .setMinRecordTestItem(fileTestInfo.getTestItemCntMin() != null ? fileTestInfo.getTestItemCntMin().getKey() : EMPTY)
                .setMinRecordTestItemCnt(fileTestInfo.getTestItemCntMin() != null ? fileTestInfo.getTestItemCntMin().getValue() : 0L);
    }

    default void deleteOdsFile(String odsHdfsTemplatePath, ManualCalculateTask manualCalculateTask, TestArea testArea, FileSystemUtil fileSystemUtil) {
        String odsHdfsDirPathTypeTemplate = odsHdfsTemplatePath
                .replace(FILE_CATEGORY, manualCalculateTask.getFileCategory().getCategory())
                .replace(TEST_AREA, testArea.getArea())
                .replace(CUSTOMER, manualCalculateTask.getCustomer())
                .replace(FILE_ID, manualCalculateTask.getFileId() + EMPTY);
        if (StringUtils.isNotEmpty(manualCalculateTask.getNeedDeleteFileIds())) {
            // 删除ods数据
            HdfsSink.SCHEMA_MAP.keySet().forEach(dataType -> fileSystemUtil.deleteFile(odsHdfsDirPathTypeTemplate.replace(TYPE, dataType), true));
        }
    }

    default Function<List<String>, ManualConvertResult> getDefaultPythonResultFunction() {
        return (strs) -> {
            for (int i = strs.size() - 1; i >= 0; i--) {
                String str = strs.get(i);
                if (str.contains("code") && str.contains("msg") && str.contains("data")) {
                    return JSON.parseObject(str, ManualConvertResult.class);
                }
            }
            return null;
        };
    }

    default ManualConvertResult convertFile(File localFile, File scriptFile, boolean isPreView) {
        LOGGER.info("{} 开始进行脚本转换", localFile.getAbsolutePath());
        ManualConvertResult manualConvertResult;
        try {
            manualConvertResult = PythonUtil.executePython(scriptFile.getAbsolutePath(), Lists.newArrayList(localFile.getAbsolutePath(), isPreView ? "1" : "0"), null, getDefaultPythonResultFunction());
        } catch (Exception e) {
            throw e instanceof FileLoadException ? (FileLoadException) e : new FileLoadException(FileLoadExceptionInfo.RAW_DATA_FILE_CONVERT_EXCEPTION, ExceptionUtils.getStackTrace(e), null);
        }
        LOGGER.info("{} 脚本转换完成", localFile.getAbsolutePath());
        if (manualConvertResult == null) {
            throw new FileLoadException(FileLoadExceptionInfo.RAW_DATA_FILE_CONVERT_EXCEPTION, "返回结果解析不到", null);
        }
        List<ManualConvertFileInfo> fileList = manualConvertResult.getData();
        if (CollectionUtils.isEmpty(fileList)) {
            throw new FileLoadException(FileLoadExceptionInfo.RAW_DATA_FILE_CONVERT_EXCEPTION, "返回结果解析不到", null);
        }
        for (ManualConvertFileInfo manualConvertFileInfo : fileList) {
            if (!new File(manualConvertFileInfo.getFileFullPath()).exists()) {
                throw new FileLoadException(FileLoadExceptionInfo.RAW_DATA_FILE_CONVERT_EXCEPTION, "转换的结果文件 " + manualConvertFileInfo.getFileFullPath() + " 不存在", null);
            }
        }

        return manualConvertResult;
    }
}
