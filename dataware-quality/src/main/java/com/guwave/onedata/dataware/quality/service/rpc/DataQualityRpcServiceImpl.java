package com.guwave.onedata.dataware.quality.service.rpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.guwave.onedata.dataware.bridge.api.iface.IDataQualityRpcService;
import com.guwave.onedata.dataware.bridge.api.vo.request.QualityRuleVo;
import com.guwave.onedata.dataware.bridge.api.vo.response.QualityTryRunRes;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.QualityRule;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.QualityRuleRepository;
import com.guwave.onedata.dataware.quality.service.QualityCheckService;
import org.apache.commons.lang3.ClassUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@DubboService
@Component
public class DataQualityRpcServiceImpl implements IDataQualityRpcService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataQualityRpcServiceImpl.class);

    private static final Pattern CLICKHOUSE_EXCEPTION_PATTERN = Pattern.compile("DB::ExceptionDB::Exception:\\s*(.*?)\\s*Stack\\strace:", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);

    @Autowired
    private QualityRuleRepository qualityRuleRepository;
    @Autowired
    private QualityCheckService qualityCheckService;

    @Override
    public void saveAll(List<QualityRuleVo> qualityRuleVos) {
        LOGGER.info("开始处理数据质量规则 条数：{}", qualityRuleVos.size());
        List<QualityRule> qualityRules = qualityRuleVos.stream().map(t -> {
            QualityRule qualityRule = new QualityRule();
            BeanUtils.copyProperties(t, qualityRule);
            return qualityRule;
        }).collect(Collectors.toList());
        LOGGER.info("开始批量保存数据质量规则 条数：{}", qualityRuleVos.size());
        qualityRuleRepository.saveAll(qualityRules);
        LOGGER.info("结束批量保存数据质量规则 条数：{}", qualityRuleVos.size());
    }

    @Override
    public void deleteAll(List<Long> ids, String userName) {
        qualityRuleRepository.updateDeleteFlag(ids, 1, userName);
    }

    @Override
    public void switchRuleStatus(Long id, Integer status, String userName) {
        qualityRuleRepository.updateStatus(id, status, userName);
    }

    @Override
    public QualityTryRunRes tryRun(QualityRuleVo qualityRuleVo) {
        LOGGER.info("{} 开始试运行：{}", qualityRuleVo.getQualityName(), JSON.toJSONString(qualityRuleVo));
        try {
            QualityRule qualityRule = new QualityRule();
            BeanUtils.copyProperties(qualityRuleVo, qualityRule);
            List<QualityRule> qualityRules = qualityRuleRepository.findAllByExecuteTimeTypeAndSqlTypeAndStatusAndDeleteFlag(qualityRule.getExecuteTimeType(), 0, 1, 0);
            qualityRules = qualityRules.stream().filter(t -> !Objects.equals(qualityRule.getQualityName(), t.getQualityName()) && !Objects.equals(qualityRule.getId(), t.getId())).collect(Collectors.toList());
            qualityRules.add(qualityRule);
            qualityCheckService.runQualityRules(qualityRules, false);
            LOGGER.info("{} 试运行结束", qualityRule.getQualityName());
            String res = JSON.toJSONString(qualityRule.getQualityResult() == null ? new JSONArray() : qualityRule.getQualityResult(), SerializerFeature.WriteMapNullValue);
            return QualityTryRunRes.ok(res);
        } catch (Exception e) {
            LOGGER.error("{} 试运行异常：", qualityRuleVo.getQualityName(), e);
            String message = getRootCauseMessage(e);
            Matcher clickhouseExecptionMatcher = CLICKHOUSE_EXCEPTION_PATTERN.matcher(message);
            if (clickhouseExecptionMatcher.find()) {
                message = clickhouseExecptionMatcher.group(1);
            }
            return QualityTryRunRes.error(true, message);
        }
    }

    public static String getRootCauseMessage(Throwable th) {
        Throwable root = ExceptionUtils.getRootCause(th);
        root = root == null ? th : root;
        return getMessage(root);
    }

    public static String getMessage(Throwable th) {
        if (th == null) {
            return "";
        } else {
            String msg = th.getMessage();
            return StringUtils.defaultString(msg);
        }
    }
}