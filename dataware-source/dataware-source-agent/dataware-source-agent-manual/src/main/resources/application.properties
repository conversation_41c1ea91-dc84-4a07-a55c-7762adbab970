spring.module.name=${module.name}
# jpa config
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=jdbc:mysql://${database.address}/${database.name}?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&failOverReadOnly=false&useSSL=false&allowPublicKeyRetrieval=true&useTimezone=true&serverTimezone=Asia/Shanghai&allowMultiQueries=true&rewriteBatchedStatements=true
spring.datasource.username=${database.username}
spring.datasource.password=${database.password}
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.read-only=false
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=HikariCP
spring.datasource.hikari.max-lifetime=60000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1

spring.data.jpa.repositories.enabled=true
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.generate-ddl=false
spring.jpa.database=MYSQL
spring.jpa.show-sql=false
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.jdbc.batch_size=10000
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect

# kafka
spring.kafka.bootstrap-servers=${kafka.bootstrapServers}
spring.kafka.producer.retries=${kafka.producer.retries}
spring.kafka.producer.properties.retry.backoff.ms=${kafka.producer.properties.retry.backoff.ms}
spring.kafka.consumer.properties.group.id=${kafka.consumer.consumeGroup}
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.consumer.auto-commit-interval=${kafka.consumer.autoCommitInterval}
spring.kafka.consumer.auto-offset-reset=${kafka.consumer.autoOffsetReset}
spring.kafka.consumer.properties.session.timeout.ms=120000
spring.kafka.consumer.properties.request.timeout.ms=180000
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.ByteArrayDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.ByteArrayDeserializer
spring.kafka.consumer.max-poll-records=${kafka.consumer.maxPollRecords}
spring.kafka.listener.missing-topics-fatal=false
spring.kafka.listener.concurrency=${kafka.listener.concurrency}
spring.kafka.listener.ack-mode=RECORD
spring.kafka.listener.type=SINGLE
spring.kafka.listener.poll-timeout=5000
spring.kafka.producer.acks=1
spring.kafka.producer.batch-size=${kafka.producer.batchSize}
spring.kafka.producer.properties.linger.ms=${kafka.producer.lingerMs}
spring.kafka.producer.buffer-memory=${kafka.producer.bufferMemory}
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.ByteArraySerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.ByteArraySerializer
spring.kafka.properties.max.request.size=104857600
spring.kafka.sleepMilliseconds=${kafka.sleepMilliseconds}
spring.kafka.manualTopic=${kafka.manualTopic}
spring.kafka.manualFinishTopic=${kafka.manualFinishTopic}
spring.kafka.loadEndFlagTopic=${kafka.loadEndFlagTopic}

# scheduling
spring.task.scheduling.pool.size=${task.scheduling.pool.size}
spring.task.scheduling.shutdown.await-termination=true
spring.task.scheduling.shutdown.await-termination-period=60
spring.task.scheduling.thread-name-prefix=listener-file-
spring.scheduler.polling.milliseconds=${scheduler.polling.milliseconds}

spring.handler.file.lockExpireTime=${handler.file.lockExpireTime}
spring.handler.file.unlockExpireTime=${handler.file.unlockExpireTime}
spring.handler.file.readPath=${handler.file.readPath}
spring.handler.file.errorPath=${handler.file.errorPath}
spring.handler.file.hdfsMode=${handler.file.hdfsMode}
spring.handler.file.hdfsUrl=${handler.file.hdfsUrl}
spring.handler.file.hdfsUser=${handler.file.hdfsUser}
spring.handler.stdfTypes=${handler.stdfTypes}
spring.handler.sinkTypes=${handler.sinkTypes}
spring.handler.sink.batchSize=${handler.sink.batchSize}
spring.handler.sink.coreNum=${handler.sink.coreNum}
spring.handler.sink.maxTextDatSize=${handler.sink.maxTextDatSize}

# clickhouse
spring.data.clickhouse.address=${data.clickhouse.address}
spring.data.clickhouse.username=${data.clickhouse.username}
spring.data.clickhouse.password=${data.clickhouse.password}
spring.data.clickhouse.cluster=${data.clickhouse.cluster}

# sink config
spring.sink.ck.ods.dbName=${sink.ck.ods.dbName}
spring.sink.ck.dim.dbName=${sink.ck.dim.dbName}
spring.sink.ck.dwd.dbName=${sink.ck.dwd.dbName}

# ods pathTemplate in hdfs
spring.hdfs.odsHdfsTemplatePath=${hdfs.odsHdfsTemplatePath}

# run mode
spring.runMode.standaloneThreshold=${runMode.standaloneThreshold}

# python安装路径
spring.adapter.python.install.path=${adapter.python.install.path}

spring.maxNoCPartIdCnt=${maxNoCPartIdCnt}

spring.maxRawDataThreadCnt=${maxRawDataThreadCnt}

spring.lotBucketNum=${lotBucketNum}

spring.sink.ck.dftSinkFlag=${sink.ck.dftSinkFlag}

spring.minio.endpoint=${minio.endpoint}
spring.minio.accessKey=${minio.accessKey}
spring.minio.secretKey=${minio.secretKey}
spring.minio.bucket=${minio.bucket}
spring.ods.local.prefix=${ods.local.prefix}

spring.smt8.testTxt.rule=${smt8.testTxt.rule}
