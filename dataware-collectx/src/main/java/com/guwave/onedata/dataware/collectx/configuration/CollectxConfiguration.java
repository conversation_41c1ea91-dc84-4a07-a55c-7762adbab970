package com.guwave.onedata.dataware.collectx.configuration;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * CollectxConfiguration
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-12-29 16:15:51
 */
@Configuration
@ComponentScan(basePackages = {"com.guwave.onedata.dataware.dao.mysql.aspect", "com.guwave.onedata.dataware.dao.mysql.manager"})
@EnableTransactionManagement
@EntityScan(basePackages = {"com.guwave.onedata.dataware.dao.mysql.domain"})
@EnableJpaRepositories(basePackages = {"com.guwave.onedata.dataware.dao.mysql.repository"})
public class CollectxConfiguration {

}
