package com.guwave.onedata.dataware.source.agent.manual.util;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.dataware.source.agent.manual.model.DwdShmooDetail;
import com.guwave.onedata.dataware.source.agent.manual.model.DwdShmooVminVmaxDetail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;

public class ShmooDataCommonUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(ShmooDataCommonUtil.class);

    public static final Map<String, BiConsumer<String, DwdShmooDetail>> SHMOO_DETAIL_FILE_CONSUMER_MAP = new HashMap<String, BiConsumer<String, DwdShmooDetail>>() {
        {
            put("ecid", (str, rawData) -> rawData.setEcid(str));
            put("testProgram", (str, rawData) -> rawData.setTestProgram(str));
            put("testerName", (str, rawData) -> rawData.setTesterName(str));
            put("testArea", (str, rawData) -> rawData.setTestArea(TestArea.of(str)));
            put("deviceId", (str, rawData) -> rawData.setDeviceId(str));
            put("lotId", (str, rawData) -> rawData.setLotId(str));
            put("proberHandlerId", (str, rawData) -> rawData.setProberHandlerId(str));
            put("proberHandlerTyp", (str, rawData) -> rawData.setProberHandlerTyp(str));
            put("testItem", (str, rawData) -> rawData.setTestItem(str));
            put("partId", (str, rawData) -> rawData.setPartId(str));
            put("shmooName", (str, rawData) -> rawData.setShmooName(str));
            put("siteNumber", (str, rawData) -> rawData.setSiteNumber(getIntegerValue(str)));
            put("testSuite", (str, rawData) -> rawData.setTestSuite(str));
            put("testPattern", (str, rawData) -> rawData.setTestPattern(str));
            put("specification", (str, rawData) -> rawData.setSpecification(str));
            put("testOrder", (str, rawData) -> rawData.setTestOrder(getLongValue(str)));
            put("xLabel", (str, rawData) -> rawData.setxLabel(str));
            put("yLabel", (str, rawData) -> rawData.setyLabel(str));
            put("xresourceType", (str, rawData) -> rawData.setXresourceType(str));
            put("yresourceType", (str, rawData) -> rawData.setYresourceType(str));
            put("xStart", (str, rawData) -> rawData.setxStart(toBigDecimal(getDoubleValue(str))));
            put("xEnd", (str, rawData) -> rawData.setxEnd(toBigDecimal(getDoubleValue(str))));
            put("yStart", (str, rawData) -> rawData.setyStart(toBigDecimal(getDoubleValue(str))));
            put("yEnd", (str, rawData) -> rawData.setyEnd(toBigDecimal(getDoubleValue(str))));
            put("xStep", (str, rawData) -> rawData.setxStep(getIntegerValue(str)));
            put("yStep", (str, rawData) -> rawData.setyStep(getIntegerValue(str)));
            put("shmooJudgeResult", (str, rawData) -> rawData.setShmooJudgeResult(str));
            put("subloop", (str, rawData) -> rawData.setSubloop(str));
            put("operatingSequenc", (str, rawData) -> rawData.setOperatingSequenc(str));
            put("testmethod", (str, rawData) -> rawData.setTestmethod(str));
            put("axisnum", (str, rawData) -> rawData.setAxisnum(getIntegerValue(str)));
            put("errcntflag", (str, rawData) -> rawData.setErrcntflag(getIntegerValue(str)));
            put("xValue", (str, rawData) -> rawData.setxValue(toBigDecimal(getDoubleValue(str))));
            put("yValue", (str, rawData) -> rawData.setyValue(toBigDecimal(getDoubleValue(str))));
            put("shmooResult", (str, rawData) -> rawData.setShmooResult(getShortValue(str)));
            put("shmooResultName", (str, rawData) -> rawData.setShmooResultName(str));
            put("isPrimary", (str, rawData) -> rawData.setIsPrimary(getShortValue(str)));
            put("stdfPartTxt", (str, rawData) -> rawData.setStdfPartTxt(str));
            put("charStartEventid", (str, rawData) -> rawData.setCharStartEventid(getIntegerValue(str)));
            put("process", (str, rawData) -> rawData.setProcess(str));
            put("xUnit", (str, rawData) -> rawData.setxUnit(str));
            put("yUnit", (str, rawData) -> rawData.setyUnit(str));
            put("cPartId", (str, rawData) -> rawData.setcPartId(getLongValue(str)));
            put("startTime", (str, rawData) -> rawData.setStartTime(getTimeStampMilliseconds(str)));
            put("endTime", (str, rawData) -> rawData.setEndTime(getTimeStampMilliseconds(str)));
            put("primaryX", (str, rawData) -> rawData.setPrimaryX(toBigDecimal(getDoubleValue(str))));
            put("primaryY", (str, rawData) -> rawData.setPrimaryY(toBigDecimal(getDoubleValue(str))));
            put("isHoleFlag", (str, rawData) -> rawData.setIsHoleFlag(getShortValue(str)));
            put("testTemperature", (str, rawData) -> rawData.setTestTemperature(str));
            put("dataType", (str, rawData) -> rawData.setDataType(str));
            put("lotType", (str, rawData) -> rawData.setLotType(LotType.of(str)));
            put("waferId", (str, rawData) -> rawData.setWaferId(str));
            put("extraInfo", (str, rawData) -> rawData.setExtraInfo(getStrMap(str)));
        }
    };

    public static final Map<String, BiConsumer<String, DwdShmooVminVmaxDetail>> SHMOO_VMIN_FILE_CONSUMER_MAP = new HashMap<String, BiConsumer<String, DwdShmooVminVmaxDetail>>() {
        {
            put("ecid", (str, rawData) -> rawData.setEcid(str));
            put("testProgram", (str, rawData) -> rawData.setTestProgram(str));
            put("testerName", (str, rawData) -> rawData.setTesterName(str));
            put("testArea", (str, rawData) -> rawData.setTestArea(TestArea.of(str)));
            put("deviceId", (str, rawData) -> rawData.setDeviceId(str));
            put("lotId", (str, rawData) -> rawData.setLotId(str));
            put("proberHandlerId", (str, rawData) -> rawData.setProberHandlerId(str));
            put("proberHandlerTyp", (str, rawData) -> rawData.setProberHandlerTyp(str));
            put("testItem", (str, rawData) -> rawData.setTestItem(str));
            put("siteNumber", (str, rawData) -> rawData.setSiteNumber(getIntegerValue(str)));
            put("vminResult", (str, rawData) -> rawData.setVminResult(toBigDecimal(getDoubleValue(str))));
            put("vmaxResult", (str, rawData) -> rawData.setVmaxResult(toBigDecimal(getDoubleValue(str))));
            put("process", (str, rawData) -> rawData.setProcess(str));
            put("testTemperature", (str, rawData) -> rawData.setTestTemperature(str));
            put("partId", (str, rawData) -> rawData.setPartId(str));
            put("cPartId", (str, rawData) -> rawData.setcPartId(getLongValue(str)));
            put("testSuite", (str, rawData) -> rawData.setTestSuite(str));
            put("testPattern", (str, rawData) -> rawData.setTestPattern(str));
            put("shmooName", (str, rawData) -> rawData.setShmooName(str));
            put("testOrder", (str, rawData) -> rawData.setTestOrder(getLongValue(str)));
            put("executionType", (str, rawData) -> rawData.setExecutionType(str));
            put("executionLabel", (str, rawData) -> rawData.setExecutionLabel(str));
            put("executionUnit", (str, rawData) -> rawData.setExecutionUnit(str));
            put("executionValue", (str, rawData) -> rawData.setExecutionValue(toBigDecimal(getDoubleValue(str))));
            put("shmooResult", (str, rawData) -> rawData.setShmooResult(getShortValue(str)));
            put("shmooResultName", (str, rawData) -> rawData.setShmooResultName(str));
            put("specification", (str, rawData) -> rawData.setSpecification(str));
            put("isHoleFlag", (str, rawData) -> rawData.setIsHoleFlag(getShortValue(str)));
            put("shmooJudgeResult", (str, rawData) -> rawData.setShmooJudgeResult(str));
            put("subloop", (str, rawData) -> rawData.setSubloop(str));
            put("operatingSequenc", (str, rawData) -> rawData.setOperatingSequenc(str));
            put("testmethod", (str, rawData) -> rawData.setTestmethod(str));
            put("axisnum", (str, rawData) -> rawData.setAxisnum(getIntegerValue(str)));
            put("errcntflag", (str, rawData) -> rawData.setErrcntflag(getIntegerValue(str)));
            put("stdfPartTxt", (str, rawData) -> rawData.setStdfPartTxt(str));
            put("charStartEventid", (str, rawData) -> rawData.setCharStartEventid(getIntegerValue(str)));
            put("startTime", (str, rawData) -> rawData.setStartTime(getTimeStampMilliseconds(str)));
            put("endTime", (str, rawData) -> rawData.setEndTime(getTimeStampMilliseconds(str)));
            put("dataType", (str, rawData) -> rawData.setDataType(str));
            put("lotType", (str, rawData) -> rawData.setLotType(LotType.of(str)));
            put("waferId", (str, rawData) -> rawData.setWaferId(str));
            put("extraInfo", (str, rawData) -> rawData.setExtraInfo(getStrMap(str)));
        }
    };

    public static final Map<String, BiConsumer<String, ManualFileInfo>> MANUAL_FILE_INFO_OVERWRITE_MAP = new HashMap<String, BiConsumer<String, ManualFileInfo>>() {{
        put("testProgram", (str, manualFileInfo) -> manualFileInfo.setTestProgram(str));
        put("testerName", (str, manualFileInfo) -> manualFileInfo.setTesterName(str));
        put("testArea", (str, manualFileInfo) -> manualFileInfo.setTestArea(TestArea.of(str)));
        put("deviceId", (str, manualFileInfo) -> manualFileInfo.setDeviceId(str));
        put("lotId", (str, manualFileInfo) -> manualFileInfo.setLotId(str));
        put("proberHandlerId", (str, manualFileInfo) -> manualFileInfo.setProberHandlerId(str));
        put("process", (str, manualFileInfo) -> manualFileInfo.setProcess(str));
        put("testTemperature", (str, manualFileInfo) -> manualFileInfo.setTestTemperature(str));
    }};

    private static Short getShortValue(String str) {
        return str.isEmpty() ? null : Double.valueOf(str).shortValue();
    }

    private static Long getLongValue(String str) {
        return str.isEmpty() ? null : Double.valueOf(str).longValue();
    }

    private static Double getDoubleValue(String str) {
        return str.isEmpty() ? null : Double.valueOf(str);
    }

    private static Integer getIntegerValue(String str) {
        Double doubleValue = getDoubleValue(str);
        return doubleValue == null ? null : doubleValue.intValue();
    }

    private static BigDecimal toBigDecimal(Double data) {
        if (null == data) {
            return null;
        } else {
            return BigDecimal.valueOf(data);
        }
    }

    private static Map<String, BigDecimal> getMap(String str) {
        Map<String, BigDecimal> result = new HashMap<>();
        try {
            if (str.isEmpty()) {
                return result;
            }
            Map map = JSON.parseObject(str, Map.class);
            for (Object key : map.keySet()) {
                result.put(key.toString(), toBigDecimal(getDoubleValue(map.get(key).toString())));
            }
        } catch (Exception e) {
            String errorMessage = String.format("解析json str %s 失败: ", str);
            throw new RuntimeException(errorMessage, e);
        }
        return result;
    }

    private static Map<String, String> getStrMap(String str) {
        Map<String, String> result = new HashMap<>();
        if (str.isEmpty()) {
            return result;
        }
        try {
            Map map = JSON.parseObject(str, Map.class);
            for (Object key : map.keySet()) {
                result.put(key.toString(), map.get(key).toString());
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException("json格式解析失败：" + str, e);
        }
    }

    public static final String[] DATE_FORMAT_ALL = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy年MM月dd日 HH:mm:ss",
            "yyyyMMdd HH:mm:ss",
            "yyyy-MM-dd HH:mm",
            "yyyy/MM/dd HH:mm",
            "yyyy年MM月dd日 HH:mm",
            "yyyyMMdd HH:mm",
            "yyyy-MM-dd HH",
            "yyyy/MM/dd HH",
            "yyyy年MM月dd日 HH",
            "yyyyMMdd HH",
            "yyyy-MM-dd",
            "yyyy/MM/dd",
            "yyyy年MM月dd日",
            "yyyyMMdd"
    };

    public static Long getTimeStampMilliseconds(String str) {
        if (str.isEmpty()) {
            return null;
        }
        try {
            Date date = org.apache.commons.lang.time.DateUtils.parseDate(str, DATE_FORMAT_ALL);
            return date.getTime();
        } catch (Exception e) {
            throw new RuntimeException("时间格式解析失败：" + str, e);
        }
    }


}
