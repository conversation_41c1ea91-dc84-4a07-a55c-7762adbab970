package com.guwave.onedata.dataware.scheduler.handler;

import com.guwave.onedata.dataware.common.contant.ExceptionType;
import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LotWaferWarehousingRecordRepository;

import java.util.Date;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * 处理器
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-12-29 16:41:21
 */
public interface Handler {

    /**
     * 具体处理逻辑
     *
     * @param endFlag CalculateEndFlag
     */
    void doHandle(CalculateEndFlag endFlag);

    /**
     * 支持处理什么类型
     *
     * @param endFlag CalculateEndFlag
     * @return 是否支持
     */
    boolean isSupport(CalculateEndFlag endFlag);

    FileLoadExceptionInfo getExceptionInfo(ExceptionType exceptionType);

}
