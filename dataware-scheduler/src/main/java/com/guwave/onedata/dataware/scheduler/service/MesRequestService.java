package com.guwave.onedata.dataware.scheduler.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.model.request.RequestParmeter;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LayerCalculatePool;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotStockingDetail;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.MesDetail;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.RequestDataParseRule;
import com.guwave.onedata.dataware.dao.mysql.manager.SftpFileDetailManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LotStockingDetailRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.MesDetailRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.RequestDataParseRuleRepository;
import com.guwave.onedata.dataware.parser.request.mes.ParseMesDataHandler;
import com.guwave.onedata.dataware.parser.request.mes.model.MesParseData;
import com.guwave.onedata.dataware.parser.request.mes.model.ParseMesSetting;
import com.guwave.onedata.dataware.scheduler.model.key.MesRequestKey;
import com.guwave.onedata.dataware.scheduler.model.value.PostMsg;
import com.guwave.onedata.dataware.scheduler.request.RequestParmBuilder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Component
public class MesRequestService {
    private static final Logger LOGGER = LoggerFactory.getLogger(MesRequestService.class);
    @Value("${spring.mesRequest.open}")
    private Boolean open;
    @Value("${spring.mesRequest.url}")
    private String url;

    private final Map<String, BiConsumer<Map<String, Object>, RequestParmeter>> PARM_MAP = new HashMap<String, BiConsumer<Map<String, Object>, RequestParmeter>>() {{
        put("customer", (map, requestParmeter) -> map.put("{customer}", requestParmeter.getCustomer()));
        put("subCustomer", (map, requestParmeter) -> map.put("{subCustomer}", requestParmeter.getSubCustomer()));
        put("factory", (map, requestParmeter) -> map.put("{factory}", requestParmeter.getFactory()));
        put("factorySite", (map, requestParmeter) -> map.put("{factorySite}", requestParmeter.getFactorySite()));
        put("testArea", (map, requestParmeter) -> map.put("{testArea}", requestParmeter.getTestArea()));
        put("lotId", (map, requestParmeter) -> map.put("{lotId}", requestParmeter.getLotId()));
        put("sblotId", (map, requestParmeter) -> map.put("{sblotId}", requestParmeter.getSblotId()));
        put("lotType", (map, requestParmeter) -> map.put("{lotType}", requestParmeter.getLotType()));
        put("deviceId", (map, requestParmeter) -> map.put("{deviceId}", requestParmeter.getDeviceId()));
        put("waferNo", (map, requestParmeter) -> map.put("{waferNo}", requestParmeter.getWaferNo()));
        put("testStage", (map, requestParmeter) -> map.put("{testStage}", requestParmeter.getTestStage()));
    }};

    private static final MultiValueMap<String, String> headMap = new LinkedMultiValueMap<String, String>() {{
    }};

    private static final String defaultFieldMapping = "sblotId:sblotId,testStage:testStage";

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private MesDetailRepository mesDetailRepository;
    @Autowired
    private RequestDataParseRuleRepository requestDataParseRuleRepository;
    @Autowired
    private MesRequestCkService mesRequestCkService;

    public void requestMes(LayerCalculatePool layerCalculatePool, SftpFileDetailManager.RunTaskFilesContext runTaskFilesInfo) {
        if (open) {
            Map<String, Object> paramMap = new LinkedHashMap<>();
            // 通过查询lot_stocking表查询testStage // 按照sblotId testStage 进行去重，然后轮询发起请求
            List<LotStockingDetail> lotStockingDetails = runTaskFilesInfo.getLotStockingDetails();

            Set<MesRequestKey> mesRequestKeys = lotStockingDetails.stream().map(item -> new MesRequestKey(item.getCustomer(), item.getSubCustomer(), item.getFactory(), item.getFactorySite(), item.getLotType(), item.getTestArea(), item.getLotId(), item.getSblotId(), item.getDeviceId(), item.getWaferNo(), item.getTestStage())).collect(Collectors.toSet());
            LOGGER.info("需要请求的数据 size: {}, 去重后, size:{}", lotStockingDetails.size(), mesRequestKeys.size());

            // 根据SblotIdTestStage查询mesDetail表
            List<MesDetail> mesDetails = mesDetailRepository.findAllByLotId(layerCalculatePool.getLotId());
            Map<MesRequestKey, MesDetail> mesDetailMap = mesDetails.stream().collect(Collectors.toMap(t -> new MesRequestKey(t.getCustomer(), t.getSubCustomer(), t.getFactory(), t.getFactorySite(), t.getLotType(), t.getTestArea(), t.getLotId(), t.getSblotId(), t.getDeviceId(), t.getWaferNo(), t.getTestStage()), t -> t, (v1, v2) -> v1));

            RequestDataParseRule requestDataParseRule = requestDataParseRuleRepository.findByCustomerAndSubCustomerAndFactoryAndFactorySiteAndTestAreaAndLotTypeAndDeviceIdAndLotIdAndWaferNoAndTestStageAndFileCategoryAndDeleteFlag(
                    layerCalculatePool.getCustomer(), layerCalculatePool.getSubCustomer(), layerCalculatePool.getFactory(), layerCalculatePool.getFactorySite(), layerCalculatePool.getTestArea(), layerCalculatePool.getLotType(), layerCalculatePool.getDeviceId(), layerCalculatePool.getLotId(), layerCalculatePool.getWaferNo(), layerCalculatePool.getTestStage(), FileCategory.SUMMARY, 0
            );

            List<MesDetail> modifyMesDetails = new ArrayList<>();
            for (MesRequestKey mesRequestKey : mesRequestKeys) {
                // 请求参数
                RequestParmeter requestParmeter = new RequestParmeter()
                        .setCustomer(mesRequestKey.getCustomer())
                        .setSubCustomer(mesRequestKey.getSubCustomer())
                        .setFactory(mesRequestKey.getFactory())
                        .setFactorySite(mesRequestKey.getFactorySite())
                        .setTestArea(mesRequestKey.getTestArea().getArea())
                        .setLotId(mesRequestKey.getLotId())
                        .setSblotId(mesRequestKey.getSblotId())
                        .setLotType(mesRequestKey.getLotType().getType())
                        .setDeviceId(mesRequestKey.getDeviceId())
                        .setWaferNo(mesRequestKey.getWaferNo())
                        .setTestStage(mesRequestKey.getTestStage());

                RequestParmBuilder requestParmBuilder;
                if (requestDataParseRule == null) {
                    fillParameters(requestParmeter, paramMap);
                    requestParmBuilder = new RequestParmBuilder(url, RequestType.POST, RequestEncodedType.JSON_ENCODED, defaultFieldMapping, headMap, 0, null, null, paramMap);
                } else {
                    checkMesRequestParm(requestDataParseRule, requestParmeter, paramMap);
                    dealHeaders(requestDataParseRule.getRequestHeaders());
                    requestParmBuilder = new RequestParmBuilder(requestDataParseRule.getUrl(), requestDataParseRule.getRequestType(), requestDataParseRule.getRequestEncodedType(), requestDataParseRule.getRequestParameterMapping(), headMap, requestDataParseRule.getPageFlag(), requestDataParseRule.getPageKey(), requestDataParseRule.getPageSizeKey(), paramMap);
                }

                String realUrl = requestParmBuilder.getUrl();
                LOGGER.info("开始请求 : url: {}, 请求参数: {} , 开始时间：{}", realUrl, JSON.toJSONString(paramMap), new Date());
                HttpEntity request = requestParmBuilder.getRequest();
                RequestType requestType = requestParmBuilder.getRequestType();
                RequestCallback requestCallback = restTemplate.httpEntityCallback(request, PostMsg.class);
                ResponseExtractor<ResponseEntity<PostMsg>> responseExtractor = restTemplate.responseEntityExtractor(PostMsg.class);

                ResponseEntity<PostMsg> execute = restTemplate.execute(realUrl, HttpMethod.valueOf(requestType.getType()), requestCallback, responseExtractor, PostMsg.class);
                PostMsg postMsg = execute.getBody();

                LOGGER.info("请求结束 : url: {}, 请求结果：{}，结束时间：{}", realUrl, JSON.toJSONString(postMsg), new Date());

                if (postMsg != null && postMsg.getCode() == 200 && StringUtils.isNotBlank(postMsg.getData())) {
                    ParseMesDataHandler parseMesDataHandler = ParseMesDataHandler.getParseMesDataHandler(getParseMesRule(requestDataParseRule));
                    MesParseData mesParseData = parseMesDataHandler.parseSingleMesData(postMsg.getData());
                    MesDetail mesDetail = mesDetailMap.getOrDefault(mesRequestKey, new MesDetail());

                    Date date = new Date();
                    Date createTime = mesDetail.getCreateTime();

                    mesDetail
                            .setCustomer(layerCalculatePool.getCustomer())
                            .setSubCustomer(layerCalculatePool.getSubCustomer())
                            .setFactory(layerCalculatePool.getFactory())
                            .setFactorySite(layerCalculatePool.getFactorySite())
                            .setLotType(layerCalculatePool.getLotType())
                            .setFileCategory(FileCategory.SUMMARY)
                            .setFileName(Constant.EMPTY)
                            .setTestProgram(convertNullStr(mesParseData.getTestProgram()))
                            .setTestArea(layerCalculatePool.getTestArea())
                            .setTestStage(mesRequestKey.getTestStage())
                            .setDeviceId(layerCalculatePool.getDeviceId())
                            .setLotId(layerCalculatePool.getLotId())
                            .setSblotId(convertNullStr(mesParseData.getXjLot()))
                            .setWaferNo(Constant.EMPTY)
                            .setStdfFiles(Constant.EMPTY)
                            .setTotalCnt(mesParseData.getSum())
                            .setPassCnt(mesParseData.getPass())
                            .setFailCnt(mesParseData.getFail())
                            .setLossCnt(mesParseData.getLoss())
                            .setDamageCnt(mesParseData.getOther())
                            .setDamageLoss(mesParseData.getLoss() + mesParseData.getOther())
                            .setMarkFCnt(mesParseData.getMarkF())
                            .setHbinInfo(JSON.toJSONString(mesParseData.getBinInfos().toArray(), JSON.DEFAULT_GENERATE_FEATURE))
                            .setCreateTime(createTime != null ? createTime : date)
                            .setUpdateTime(date)
                            .setCreateUser(Constant.SYSTEM)
                            .setUpdateUser(Constant.SYSTEM);
                    modifyMesDetails.add(mesDetail);
                }
            }
            mesDetailRepository.saveAll(modifyMesDetails);
            mesRequestCkService.dealMesDetail(modifyMesDetails);
        }
    }

    private void fillParameters(RequestParmeter requestParmeter, Map<String, Object> paramMap) {
        PARM_MAP.forEach((key, value) -> value.accept(paramMap, requestParmeter));
    }

    private ParseMesSetting getParseMesRule(RequestDataParseRule requestDataParseRule) {
        if (requestDataParseRule == null) {
            return new ParseMesSetting(Constant.EMPTY, Constant.EMPTY, ParseRuleMode.SYSTEM);
        }
        return new ParseMesSetting(requestDataParseRule.getScriptMethodName(), requestDataParseRule.getScript(), requestDataParseRule.getRuleMode());
    }


    private String convertNullStr(String str) {
        if (StringUtils.isEmpty(str)) {
            return Constant.EMPTY;
        } else {
            return str;
        }
    }

    @SuppressWarnings("unchecked")
    private void dealHeaders(String headers) {
        if (headers != null) {
            Map<String, String> map = JSONObject.parseObject(headers, Map.class);
            headMap.clear();
            map.forEach(headMap::add);
        }
    }

    private void checkMesRequestParm(RequestDataParseRule requestDataParseRule, RequestParmeter requestParmeter, Map<String, Object> paramMap) {
        Set<String> keySet = PARM_MAP.keySet();
        if (requestDataParseRule.getRequestParameterMapping() != null) {
            for (String requestParameterMapping : requestDataParseRule.getRequestParameterMapping().split(COMMA)) {
                String parm = requestParameterMapping.split(COLON)[1];
                if (!keySet.contains(parm)) {
                    throw new RuntimeException("请求参数不合法, parm: " + parm + " 不支持作为请求参数");
                } else {
                    PARM_MAP.get(parm).accept(paramMap, requestParmeter);
                }
            }
        }

        // 设置分页参数
        if (requestDataParseRule.getPageFlag() == 1) {
            paramMap.compute(PAGE, (k, v) -> (v == null) ? 1 : (Integer) v + 1);
            paramMap.computeIfAbsent(PAGE_SIZE, v -> requestDataParseRule.getPageSize());
        }
    }
}
