package com.guwave.onedata.dataware.source.agent.common.sink.ck.wat.dwd;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.model.wat.dwd.DwdWatDieDetail;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

/**
 * Copyright (C), 2022, guwave
 * <p>
 * DwdWatDieDetailSink
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2022-03-14 11:34:47
 */
@Component
public class DwdWatDieDetailSink implements CkSink<DwdWatDieDetail> {

    @Value("${spring.sink.ck.dwd.dbName}")
    private String dbName;

    @Override
    public String getTableName() {
        return "dwd_die_detail_cluster";
    }

    public String getDbName() {
        return dbName;
    }

    @Override
    public String getPartitionExpr() {
        return "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{SUB_CUSTOMER}', '{DEVICE_ID}', {LOT_BUCKET})";
    }

    @Override
    public void handle(PreparedStatement statement, List<DwdWatDieDetail> items) throws SQLException {
        for (DwdWatDieDetail item : items) {
            // CUSTOMER
            statement.setObject(1, item.getCustomer());
            // UPLOAD_TYPE
            statement.setObject(2, item.getUploadType());
            // FILE_ID
            statement.setObject(3, item.getFileId());
            // FILE_NAME
            statement.setObject(4, item.getFileName());
            // FILE_TYPE
            statement.setObject(5, item.getFileType());
            // DEVICE_ID
            statement.setObject(6, item.getDeviceId());
            // FACTORY
            statement.setObject(7, item.getFactory());
            // FACTORY_SITE
            statement.setObject(8, item.getFactorySite());
            // FAB
            statement.setObject(9, item.getFab());
            // FAB_SITE
            statement.setObject(10, item.getFabSite());
            // LOT_TYPE
            statement.setObject(11, item.getLotType());
            // LOT_ID
            statement.setObject(12, item.getLotId());
            // TEST_AREA
            statement.setObject(13, item.getTestArea());
            // WAFER_ID
            statement.setObject(14, item.getWaferId());
            // START_TIME
            statement.setObject(15, this.toTimestamp(item.getStartTime()));
            // END_TIME
            statement.setObject(16, this.toTimestamp(item.getEndTime()));
            // CREATE_TIME
            statement.setObject(17, new Timestamp(System.currentTimeMillis()));
            // CREATE_USER
            statement.setObject(18, item.getCreateUser());

            // SITE_ID
            statement.setObject(19, item.getSiteId() == null ? Constant.EMPTY_STR : item.getSiteId());
            // RETICLE_X
            statement.setObject(20, item.getReticleX());
            // RETICLE_Y
            statement.setObject(21, item.getReticleY());
            // X_COORD
            statement.setObject(22, item.getxCoord());
            // Y_COORD
            statement.setObject(23, item.getyCoord());
            // WF_FLAT
            statement.setObject(24, item.getWfFlat());
            // TEST_PROGRAM
            statement.setObject(25, item.getTestProgram());
            // ID
            statement.setObject(26, item.getId());
            // WAFER_MARGIN
            statement.setObject(27, item.getWaferMargin());
            // RETICLE_ROW
            statement.setObject(28, item.getReticleRow());
            // RETICLE_COLUMN
            statement.setObject(29, item.getReticleColumn());
            // RETICLE_ROW_CENTER_OFFSET
            statement.setObject(30, item.getReticleRowCenterOffset());
            // RETICLE_COLUMN_CENTER_OFFSET
            statement.setObject(31, item.getReticleColumnCenterOffset());
            // CENTER_OFFSET_X
            statement.setObject(32, item.getCenterOffsetX());
            // CENTER_OFFSET_Y
            statement.setObject(33, item.getCenterOffsetY());
            // CENTER_X
            statement.setObject(34, item.getCenterX());
            // CENTER_Y
            statement.setObject(35, item.getCenterY());
            // WF_UNITS
            statement.setObject(36, item.getWfUnits());
            // DIE_HEIGHT
            statement.setObject(37, item.getDieHeight());
            // DIE_WIDTH
            statement.setObject(38, item.getDieWidth());
            // WAFER_SIZE
            statement.setObject(39, item.getWaferSize());
            // DIE_CNT
            statement.setObject(40, item.getDieCnt());
            // POS_X
            statement.setObject(41, item.getPosX());
            // POS_Y
            statement.setObject(42, item.getPosY());
            // DIE_X
            statement.setObject(43, item.getDieX());
            // DIE_Y
            statement.setObject(44, item.getDieY());
            // RETICLE_T_X
            statement.setObject(45, item.getReticleTX());
            // RETICLE_T_Y
            statement.setObject(46, item.getReticleTY());
            // WAFER_NO
            statement.setObject(47, item.getWaferNo());
            // IS_FIRST_TEST
            statement.setObject(48, item.getIsFirstTest());
            // IS_FINAL_TEST
            statement.setObject(49, item.getIsFinalTest());
            // IS_DUP_FIRST_TEST
            statement.setObject(50, item.getIsDupFirstTest());
            // IS_DUP_FINAL_TEST
            statement.setObject(51, item.getIsDupFinalTest());
            // TEST_STAGE
            statement.setObject(52, item.getTestStage());
            // PROC_ID
            statement.setObject(53, item.getProcess());
            // SPEC_NAM
            statement.setObject(54, item.getTestTemperature());
            // WAFER_LOT_ID
            statement.setObject(55, item.getWaferLotId());
            // CENTER_RETICLE_X
            statement.setObject(56, item.getCenterReticleX());
            // CENTER_RETICLE_Y
            statement.setObject(57, item.getCenterReticleY());
            // CENTER_RETICLE_OFFSET_X
            statement.setObject(58, item.getCenterReticleOffsetX());
            // CENTER_RETICLE_OFFSET_Y
            statement.setObject(59, item.getCenterReticleOffsetY());
            // ORIGINAL_WF_FLAT
            statement.setObject(60, item.getOriginalWfFlat());
            // ORIGINAL_POS_X
            statement.setObject(61, item.getOriginalPosX());
            // ORIGINAL_POS_Y
            statement.setObject(62, item.getOriginalPosY());
            // ORIGINAL_DIE_WIDTH
            statement.setObject(63, item.getOriginalDieWidth());
            // ORIGINAL_DIE_HEIGHT
            statement.setObject(64, item.getOriginalDieHeight());
            // ORIGINAL_RETICLE_ROW
            statement.setObject(65, item.getOriginalReticleRow());
            // ORIGINAL_RETICLE_COLUMN
            statement.setObject(66, item.getOriginalReticleColumn());
            // ORIGINAL_RETICLE_ROW_CENTER_OFFSET
            statement.setObject(67, item.getOriginalReticleRowCenterOffset());
            // ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET
            statement.setObject(68, item.getOriginalReticleColumnCenterOffset());
            // ORIGINAL_CENTER_X
            statement.setObject(69, item.getOriginalCenterX());
            // ORIGINAL_CENTER_Y
            statement.setObject(70, item.getOriginalCenterY());
            // ORIGINAL_CENTER_RETICLE_X
            statement.setObject(71, item.getOriginalCenterReticleX());
            // ORIGINAL_CENTER_RETICLE_Y
            statement.setObject(72, item.getOriginalCenterReticleY());
            // ORIGINAL_CENTER_OFFSET_X
            statement.setObject(73, item.getOriginalCenterOffsetX());
            // ORIGINAL_CENTER_OFFSET_Y
            statement.setObject(74, item.getOriginalCenterOffsetY());
            // ORIGINAL_CENTER_RETICLE_OFFSET_X
            statement.setObject(75, item.getOriginalCenterReticleOffsetX());
            // ORIGINAL_CENTER_RETICLE_OFFSET_Y
            statement.setObject(76, item.getOriginalCenterReticleOffsetY());
            // ORIGINAL_WAFER_SIZE
            statement.setObject(77, item.getOriginalWaferSize());
            // ORIGINAL_WAFER_MARGIN
            statement.setObject(78, item.getOriginalWaferMargin());
            // ORIGINAL_WF_UNITS
            statement.setObject(79, item.getOriginalWfUnits());
            statement.setObject(80, item.getSubCustomer());
            statement.setObject(81, item.getTesterName());
            statement.setObject(82, item.getOperatorName());
            statement.setObject(83, new Timestamp(item.getUploadTime()));
            statement.setObject(84, item.getDataVersion());
            statement.setObject(85, item.getFabwfId());

            statement.addBatch();
        }
    }

    @Override
    public List<String> getColumns() {
        return Arrays.asList(
                "CUSTOMER",
                "UPLOAD_TYPE",
                "FILE_ID",
                "FILE_NAME",
                "FILE_TYPE",
                "DEVICE_ID",
                "FACTORY",
                "FACTORY_SITE",
                "FAB",
                "FAB_SITE",
                "LOT_TYPE",
                "LOT_ID",
                "TEST_AREA",
                "WAFER_ID",
                "START_TIME",
                "END_TIME",
                "CREATE_TIME",
                "CREATE_USER",
                "SITE_ID",
                "RETICLE_X",
                "RETICLE_Y",
                "X_COORD",
                "Y_COORD",
                "WF_FLAT",
                "TEST_PROGRAM",
                "ID",
                "WAFER_MARGIN",
                "RETICLE_ROW",
                "RETICLE_COLUMN",
                "RETICLE_ROW_CENTER_OFFSET",
                "RETICLE_COLUMN_CENTER_OFFSET",
                "CENTER_OFFSET_X",
                "CENTER_OFFSET_Y",
                "CENTER_X",
                "CENTER_Y",
                "WF_UNITS",
                "DIE_HEIGHT",
                "DIE_WIDTH",
                "WAFER_SIZE",
                "DIE_CNT",
                "POS_X",
                "POS_Y",
                "DIE_X",
                "DIE_Y",
                "RETICLE_T_X",
                "RETICLE_T_Y",
                "WAFER_NO",
                "IS_FIRST_TEST",
                "IS_FINAL_TEST",
                "IS_DUP_FIRST_TEST",
                "IS_DUP_FINAL_TEST",
                "TEST_STAGE",
                "PROCESS",
                "TEST_TEMPERATURE",
                "WAFER_LOT_ID",
                "CENTER_RETICLE_X",
                "CENTER_RETICLE_Y",
                "CENTER_RETICLE_OFFSET_X",
                "CENTER_RETICLE_OFFSET_Y",
                "ORIGINAL_WF_FLAT",
                "ORIGINAL_POS_X",
                "ORIGINAL_POS_Y",
                "ORIGINAL_DIE_WIDTH",
                "ORIGINAL_DIE_HEIGHT",
                "ORIGINAL_RETICLE_ROW",
                "ORIGINAL_RETICLE_COLUMN",
                "ORIGINAL_RETICLE_ROW_CENTER_OFFSET",
                "ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET",
                "ORIGINAL_CENTER_X",
                "ORIGINAL_CENTER_Y",
                "ORIGINAL_CENTER_RETICLE_X",
                "ORIGINAL_CENTER_RETICLE_Y",
                "ORIGINAL_CENTER_OFFSET_X",
                "ORIGINAL_CENTER_OFFSET_Y",
                "ORIGINAL_CENTER_RETICLE_OFFSET_X",
                "ORIGINAL_CENTER_RETICLE_OFFSET_Y",
                "ORIGINAL_WAFER_SIZE",
                "ORIGINAL_WAFER_MARGIN",
                "ORIGINAL_WF_UNITS",
                "SUB_CUSTOMER",
                "TESTER_NAME",
                "OPERATOR_NAME",
                "UPLOAD_TIME",
                "DATA_VERSION",
                "FABWF_ID"
        );
    }
}
