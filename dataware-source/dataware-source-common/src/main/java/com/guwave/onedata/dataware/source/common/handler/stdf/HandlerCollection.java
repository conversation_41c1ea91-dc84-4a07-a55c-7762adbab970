package com.guwave.onedata.dataware.source.common.handler.stdf;

import com.guwave.onedata.dataware.common.model.stdf.StdfData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collection;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * HandlerCollection
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-22 13:38:49
 */
public class HandlerCollection implements Handler {
    private static final Logger LOGGER = LoggerFactory.getLogger(HandlerCollection.class);

    private final Collection<Handler> collection = new ArrayList<>();

    public HandlerCollection addHandler(Handler handler) {
        collection.add(handler);
        return this;
    }

    @Override
    public boolean support(StdfData stdfData) {
        return null != getHandle(stdfData);
    }

    @Override
    public void handle(StdfData stdfData) {
        Handler item = this.getHandle(stdfData);
        if (null != item) {
            item.handle(stdfData);
        } else {
            LOGGER.info("未找到对应的处理, type: {}, javaType: {}", stdfData.getType(), stdfData.getJavaType());
        }
    }

    private Handler getHandle(StdfData stdfData) {
        Handler result = null;
        for (Handler item : collection) {
            if (item.support(stdfData)) {
                result = item;
            }
        }
        return result;
    }
}
