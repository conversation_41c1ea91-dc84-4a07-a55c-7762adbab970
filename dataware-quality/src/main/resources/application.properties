spring.module.name=${module.name}
spring.main.web-application-type=none
# jpa config
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=jdbc:mysql://${database.address}/${database.name}?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&failOverReadOnly=false&useSSL=false&allowPublicKeyRetrieval=true&useTimezone=true&serverTimezone=Asia/Shanghai&allowMultiQueries=true&rewriteBatchedStatements=true
spring.datasource.username=${database.username}
spring.datasource.password=${database.password}
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.read-only=false
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=HikariCP
spring.datasource.hikari.max-lifetime=60000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1

spring.data.jpa.repositories.enabled=true
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.generate-ddl=false
spring.jpa.database=MYSQL
spring.jpa.show-sql=false
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.jdbc.batch_size=10000
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect

spring.task.scheduling.pool.size=${task.scheduling.pool.size}
spring.scheduler.timer.dailyTask=${scheduler.timer.dailyTask}
spring.scheduler.timer.realtimeTask=${scheduler.timer.realtimeTask}
spring.scheduler.timer.minuteTask=${scheduler.timer.minuteTask}

# clickhouse
spring.data.clickhouse.address=${data.clickhouse.address}
spring.data.clickhouse.username=${data.clickhouse.username}
spring.data.clickhouse.password=${data.clickhouse.password}
spring.data.clickhouse.cluster=${data.clickhouse.cluster}
spring.data.clickhouse.batchInsertSize=${data.clickhouse.batchInsertSize}

# dubbo config
dubbo.application.name=${module.name}
dubbo.application.serialize-check-status=WARN
dubbo.application.qos-enable=false
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.registry.protocol=zookeeper
dubbo.registry.address=zookeeper://${zookeeper.address}
dubbo.provider.timeout=${rpc.timeout}
dubbo.provider.retries=0
dubbo.provider.group=${environment.group}
dubbo.provider.filter=customerProviderFilter
dubbo.consumer.check=false
dubbo.consumer.timeout=${rpc.timeout}
dubbo.consumer.retries=0
dubbo.consumer.group=${environment.group}