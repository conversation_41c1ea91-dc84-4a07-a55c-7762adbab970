package com.guwave.onedata.dataware.source.agent.manual.handler;

import com.guwave.onedata.dataware.common.contant.ManualType;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingFinishMessage;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingMessage;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualMessage;

public interface ManualTypehandler {

    String FIELD_IS_EMPTY = "字段为空";
    String FIELD_IS_ILLEGALITY = "字段不合法";

    Boolean support(ManualType manualType);

    void generateTask(ManualMessage manualMessage, ManualWarehousingMessage manualWarehousingMessage);

    void fillManualWarehousingFinishMessage(ManualWarehousingFinishMessage manualWarehousingFinishMessage, ManualWarehousingMessage manualWarehousingMessage);
}
