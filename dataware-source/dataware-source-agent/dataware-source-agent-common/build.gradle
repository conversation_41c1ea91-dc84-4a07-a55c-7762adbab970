description = 'dataware source agent common'

dependencies {
  api project(':dataware-parser')
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter', version: springBootVersion
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter-data-redis', version: springBootVersion
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter-data-jpa', version: springBootVersion
  implementation group: 'org.springframework.kafka', name: 'spring-kafka', version: springKafkaVersion
  implementation group: 'com.fasterxml.jackson.core', name: 'jackson-databind', version: jacksonVersion

  api group: 'com.alibaba', name: 'fastjson', version: fastJsonVersion
  implementation group: 'commons-codec', name: 'commons-codec', version: commonsCodecVersion
  implementation group: 'commons-io', name: 'commons-io', version: commonsIoVersion
  implementation group: 'org.apache.commons', name: 'commons-compress', version: commonsCompressVersion
  implementation group: 'org.apache.commons', name: 'commons-lang3', version: commonsLang3Version
  api (group: 'org.apache.hadoop', name: 'hadoop-common', version: hadoopVersion) {
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    exclude group: 'tomcat', module: 'jasper-runtime'
    exclude group: 'tomcat', module: 'jasper-compiler'
  }
  api (group: 'org.apache.hadoop', name: 'hadoop-hdfs', version: hadoopVersion) {
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    exclude group: 'tomcat', module: 'jasper-runtime'
    exclude group: 'tomcat', module: 'jasper-compiler'
  }
  api (group: 'org.apache.hadoop', name: 'hadoop-client', version: hadoopVersion) {
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    exclude group: 'tomcat', module: 'jasper-runtime'
    exclude group: 'tomcat', module: 'jasper-compiler'
    exclude group: 'com.squareup.okhttp', module: 'okhttp'
  }
  api "org.apache.parquet:parquet-avro:$parquetAvroVersion"
  implementation("com.guwave.gdp:common:$gdpCommonVersion") {
    transitive = false
  }
  api (group: 'org.apache.hadoop', name: 'hadoop-aws', version: hadoopVersion) {
    exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    exclude group: 'tomcat', module: 'jasper-runtime'
    exclude group: 'tomcat', module: 'jasper-compiler'
  }

  implementation group: 'joda-time', name: 'joda-time', version: jodaTimeVersion
  implementation group: 'io.minio', name: 'minio', version: minIOVersion
  tasks.withType(GenerateModuleMetadata).configureEach {
    suppressedValidationErrors.add('enforced-platform')
  }
}
