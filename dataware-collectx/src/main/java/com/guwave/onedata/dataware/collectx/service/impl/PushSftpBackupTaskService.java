package com.guwave.onedata.dataware.collectx.service.impl;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.collectx.manager.redis.SftpFileRedisManager;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpBackupRule;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpBackupRuleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class PushSftpBackupTaskService {

    @Autowired
    private SftpBackupRuleRepository sftpBackupRuleRepository;

    public void pushTask() {
        boolean lockPushTask = SftpFileRedisManager.lockPushBackupTask();
        if (!lockPushTask) {
            log.info("pushTask 正在被处理");
            return;
        }

        try {
            List<String> cacheSftpBackupRules = SftpFileRedisManager.getCacheSftpBackupRuleList();
            if (cacheSftpBackupRules == null) {
                cacheSftpBackupRules = new ArrayList<>();
            }

            List<SftpBackupRule> allBackupRules = sftpBackupRuleRepository.findAllByStatusAndDeleteFlagOrderByUpdateTimeDesc(1, 0);
            List<String> needCacheSftpBackupRules = new ArrayList<>();
            for (SftpBackupRule sftpBackupRule : allBackupRules) {
                if (!cacheSftpBackupRules.contains(sftpBackupRule.getId().toString())) {
                    needCacheSftpBackupRules.add(sftpBackupRule.getId().toString());
                }
            }

            log.info("push task {}", JSON.toJSONString(needCacheSftpBackupRules));
            SftpFileRedisManager.pushBackupRuleList(needCacheSftpBackupRules);
        } finally {
            SftpFileRedisManager.unLockPushBackupTask();
        }
    }

}
