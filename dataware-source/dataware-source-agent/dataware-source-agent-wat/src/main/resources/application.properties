spring.module.name=${module.name}
# jpa config
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=jdbc:mysql://${database.address}/${database.name}?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&failOverReadOnly=false&useSSL=false&allowPublicKeyRetrieval=true&useTimezone=true&serverTimezone=Asia/Shanghai&allowMultiQueries=true&rewriteBatchedStatements=true
spring.datasource.username=${database.username}
spring.datasource.password=${database.password}
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.read-only=false
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=HikariCP
spring.datasource.hikari.max-lifetime=60000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1

spring.data.jpa.repositories.enabled=true
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.generate-ddl=false
spring.jpa.database=MYSQL
spring.jpa.show-sql=false
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.jdbc.batch_size=10000
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect

# redis
spring.redis.host=${redis.host}
spring.redis.port=${redis.port}
spring.redis.database=${redis.database}
spring.redis.password=${redis.password}
spring.redis.timeout=1000
spring.redis.jedis.pool.min-idle=1
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.max-active=8
spring.redis.jedis.pool.max-wait=1000ms
spring.data.redis.repositories.enabled=false

# clickhouse
spring.data.clickhouse.address=${data.clickhouse.address}
spring.data.clickhouse.username=${data.clickhouse.username}
spring.data.clickhouse.password=${data.clickhouse.password}
spring.data.clickhouse.cluster=${data.clickhouse.cluster}
spring.data.clickhouse.dwdDbName=${data.clickhouse.dwdDbName}
spring.data.clickhouse.maxMutationsCnt=${data.clickhouse.maxMutationsCnt}

# kafka
spring.kafka.bootstrap-servers=${kafka.bootstrapServers}
spring.kafka.producer.retries=${kafka.producer.retries}
spring.kafka.producer.properties.retry.backoff.ms=${kafka.producer.properties.retry.backoff.ms}
spring.kafka.producer.acks=1
spring.kafka.producer.batch-size=${kafka.producer.batchSize}
spring.kafka.producer.properties.linger.ms=${kafka.producer.lingerMs}
spring.kafka.producer.buffer-memory=${kafka.producer.bufferMemory}
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.ByteArraySerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.ByteArraySerializer
spring.kafka.properties.max.request.size=104857600
spring.kafka.watTopic=${kafka.watTopic}
spring.kafka.loadEndFlagTopic=${kafka.loadEndFlagTopic}
spring.kafka.sleepMilliseconds=${kafka.sleepMilliseconds}
spring.kafka.calculateEndFlagTopic=${kafka.calculateEndFlagTopic}

# scheduling
spring.task.scheduling.pool.size=${task.scheduling.pool.size}
spring.task.scheduling.shutdown.await-termination=true
spring.task.scheduling.shutdown.await-termination-period=60
spring.task.scheduling.thread-name-prefix=listener-file-

spring.scheduler.polling.milliseconds=${scheduler.polling.milliseconds}

spring.handler.file.lockExpireTime=${handler.file.lockExpireTime}
spring.handler.file.unlockExpireTime=${handler.file.unlockExpireTime}
spring.handler.file.readPath=${handler.file.readPath}
spring.handler.file.errorPath=${handler.file.errorPath}
spring.handler.file.hdfsMode=${handler.file.hdfsMode}
spring.handler.file.hdfsUrl=${handler.file.hdfsUrl}
spring.handler.file.hdfsUser=${handler.file.hdfsUser}

# sink config
spring.sink.ck.ods.dbName=${sink.ck.ods.dbName}
spring.sink.ck.dim.dbName=${sink.ck.dim.dbName}
spring.sink.ck.dwd.dbName=${sink.ck.dwd.dbName}

# sinkTypes
spring.handler.sinkTypes=${handler.sinkTypes}

# ods pathTemplate in hdfs
spring.hdfs.odsHdfsTemplatePath=${hdfs.odsHdfsTemplatePath}

# other dwLayer pathTemplate in hdfs
spring.hdfs.otherHdfsTemplatePath=${hdfs.otherHdfsTemplatePath}

# deleteTable
spring.data.clickhouse.needDeleteTables=${data.clickhouse.needDeleteTables}
spring.data.clickhouse.needCountTables=${data.clickhouse.needCountTables}

spring.lotBucketNum=${lotBucketNum}

spring.sink.ck.dftSinkFlag=${sink.ck.dftSinkFlag}

spring.minio.endpoint=${minio.endpoint}
spring.minio.accessKey=${minio.accessKey}
spring.minio.secretKey=${minio.secretKey}
spring.minio.bucket=${minio.bucket}
spring.ods.local.prefix=${ods.local.prefix}


# dubbo config
dubbo.application.name=${module.name}
dubbo.application.serialize-check-status=WARN
dubbo.application.qos-enable=false
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.registry.protocol=zookeeper
dubbo.registry.address=zookeeper://${zookeeper.address}
dubbo.provider.timeout=${rpc.timeout}
dubbo.provider.retries=0
dubbo.provider.group=${environment.group}
dubbo.provider.filter=customerProviderFilter
dubbo.consumer.check=false
dubbo.consumer.timeout=${rpc.timeout}
dubbo.consumer.retries=0
dubbo.consumer.group=${environment.group}