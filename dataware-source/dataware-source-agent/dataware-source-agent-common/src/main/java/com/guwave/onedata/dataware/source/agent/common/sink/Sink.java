package com.guwave.onedata.dataware.source.agent.common.sink;

import com.guwave.onedata.dataware.common.model.stdf.StdfCommon;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotMetaDataDetail;

import java.io.IOException;
import java.util.List;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * Sink
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-11-01 16:36:49
 */
public interface Sink {
    /**
     * send数据
     *
     * @param topic             topic
     * @param type              type
     * @param values            values
     * @param lotMetaDataDetail lotMetaDataDetail
     * @param ts                时间戳
     */
    void send(String topic, String type, List<StdfCommon> values, LotMetaDataDetail lotMetaDataDetail, Long ts) throws Exception;

    void afterSend();
    void close();
    void cleanTmpFile();
}
