plugins {
  id 'application'
}

description = 'dataware source agent manual'

dependencies {
  implementation project(':dataware-source:dataware-source-agent:dataware-source-agent-common')
  implementation enforcedPlatform("org.springframework.boot:spring-boot-dependencies:$springBootVersion")
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter'
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter-data-jpa'
  implementation group: 'mysql', name: 'mysql-connector-java', version: mysqlVersion
  implementation group: 'org.springframework.kafka', name: 'spring-kafka'
  implementation group: 'com.alibaba', name: 'easyexcel', version: easyExcelVersion

  testImplementation group: 'org.springframework.boot', name: 'spring-boot-starter-test'
  implementation group: 'org.apache.tika', name: 'tika-core', version: tikaVersion

  implementation group: 'com.guwave.bigbrother', name: 'skyeye-driver-logback', version: skyeyeVersion
}

configurations {
  compile.exclude group: 'log4j', module: 'log4j'
  compile.exclude group: 'org.hibernate.validator', module: 'hibernate-validator'
  compile.exclude group: 'org.apache.tomcat.embed', module: 'tomcat-embed-websocket'
  compile.exclude group: 'org.slf4j', module: 'slf4j-log4j12'
}

compileJava {
  options.compilerArgs = ["-parameters"]
}

jar {
  enabled true
  manifest.attributes 'Main-Class': 'com.guwave.onedata.dataware.source.agent.manual.Application'
}

application {
  mainClassName = 'com.guwave.onedata.dataware.source.agent.manual.Application'
  applicationDistribution.from('src/main/resources/properties').into('properties')
}

distributions {
  main {
    contents {
      from('src/main/resources/shell/startup.sh') {
        into '.'
      }
    }
  }
}

startScripts {
  doLast {
    unixScript.text = unixScript.text.replaceAll(":\\\$APP_HOME/lib/(.*)\n", ":\\\$APP_HOME/lib/\\*\n")
  }
}
