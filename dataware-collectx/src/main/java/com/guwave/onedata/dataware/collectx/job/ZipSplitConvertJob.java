package com.guwave.onedata.dataware.collectx.job;

import com.guwave.onedata.dataware.collectx.service.ZipSplitConvertService;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ZipSplitConvertJob {

    @Autowired
    private ZipSplitConvertService zipSplitConvertService;

    @ScheduleSwitch
    @Scheduled(cron = "${spring.scheduler.timer.convertData}")
    public void schedulerZipSplitConvert() {
        try {
            zipSplitConvertService.convertAsync();
        } catch (Exception e) {
            log.error(e.toString(), e);
        }
    }
}
