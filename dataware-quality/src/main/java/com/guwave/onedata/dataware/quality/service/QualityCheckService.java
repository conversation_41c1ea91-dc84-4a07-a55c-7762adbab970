package com.guwave.onedata.dataware.quality.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.QualityDailyResult;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.QualityRealtimeResult;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.QualityRule;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.QualityDailyResultRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.QualityRealtimeResultRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.QualityRuleRepository;
import com.guwave.onedata.dataware.quality.util.CkProvider;
import com.guwave.onedata.dataware.quality.util.MysqlProvider;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.*;
import java.util.Date;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
public class QualityCheckService {
    private static final Logger LOGGER = LoggerFactory.getLogger(QualityCheckService.class);

    private static final Pattern CK_TEMP_TABLE_PATTERN = Pattern.compile("create\\s*temporary\\s*table\\s*(?:if\\s*not\\s*exists\\s*)?(\\w*\\.?\\w*)", Pattern.CASE_INSENSITIVE);
    private static final Map<String, String> CK_CAN_REPLACE_PARAM_DIMENSION_MAP = new HashMap<String, String>() {{
        put("CUSTOMER", "{CUSTOMER}");
        put("SUB_CUSTOMER", "{SUB_CUSTOMER}");
        put("FACTORY", "{FACTORY}");
        put("TEST_AREA", "{TEST_AREA}");
        put("DEVICE_ID", "{DEVICE_ID}");
        put("LOT_ID", "{LOT_ID}");
        put("WAFER_NO", "{WAFER_NO}");
        put("TEST_STAGE", "{TEST_STAGE}");
        put("LOT_TYPE", "{LOT_TYPE}");
    }};
    private static final String DEFAULT_CK_TEMP_TABLE_LOT_STOCKING_DETAIL = "dw_lot_stocking_detail";

    @Value("${spring.data.clickhouse.batchInsertSize}")
    private Integer ckBatchInsertSize;

    @Autowired
    private QualityRuleRepository qualityRuleRepository;
    @Autowired
    private QualityDailyResultRepository qualityDailyResultRepository;
    @Autowired
    private QualityRealtimeResultRepository qualityRealtimeResultRepository;

    public void qualityCheck(List<QualityRule> qualityRules, boolean dailyResultFlag) {
        List<QualityRule> qualityResultRules = qualityRules.stream().filter(t -> Objects.equals(1, t.getSqlType()) && (StringUtils.isNotBlank(t.getMysqlQuerySql()) || StringUtils.isNotBlank(t.getCkQuerySql()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(qualityResultRules)) {
            LOGGER.info("没有指标结果查询规则");
            return;
        }

        // 查询指标结果
        Date date = dailyResultFlag ? DateUtils.addDays(new Date(), -1) : new Date();
        String dayKey = DateFormatUtils.format(date, "yyyyMMdd");
        String timeKey = DateFormatUtils.format(date, "HHmmss");
        runQualityRules(qualityRules, true);

        // 保存指标结果
        if (dailyResultFlag) {
            List<QualityDailyResult> qualityResults = qualityResultRules.stream().filter(t -> t.getQualityResult() != null).map(t -> new QualityDailyResult()
                    .setQualityRuleId(t.getId())
                    .setQualityName(t.getQualityName())
                    .setDayKey(dayKey)
                    .setResult(JSON.toJSONString(t.getQualityResult(), SerializerFeature.WriteMapNullValue))
                    .setCreateTime(t.getExecuteSqlStartDate())
                    .setUpdateTime(t.getExecuteSqlEndDate())
                    .setCreateUser(Constant.SYSTEM)
                    .setUpdateUser(Constant.SYSTEM)).collect(Collectors.toList());
            qualityDailyResultRepository.deleteByDayKey(dayKey);
            qualityDailyResultRepository.saveAll(qualityResults);
        } else {
            List<QualityRealtimeResult> qualityResults = qualityResultRules.stream().filter(t -> t.getQualityResult() != null).map(t -> new QualityRealtimeResult()
                    .setQualityRuleId(t.getId())
                    .setQualityName(t.getQualityName())
                    .setDayKey(dayKey)
                    .setTimeKey(timeKey)
                    .setResult(JSON.toJSONString(t.getQualityResult(), SerializerFeature.WriteMapNullValue))
                    .setCreateTime(t.getExecuteSqlStartDate())
                    .setUpdateTime(t.getExecuteSqlEndDate())
                    .setCreateUser(Constant.SYSTEM)
                    .setUpdateUser(Constant.SYSTEM)
            ).collect(Collectors.toList());
            qualityRealtimeResultRepository.deleteByDayKey(DateFormatUtils.format(DateUtils.addDays(date, -60), "yyyyMMdd"));
            qualityRealtimeResultRepository.saveAll(qualityResults);
        }
    }

    public void runQualityRules(List<QualityRule> qualityRules, boolean ignoreError) {
        List<QualityRule> createCkTempTableRules = qualityRules.stream().filter(t -> Objects.equals(0, t.getSqlType()) && StringUtils.isNotBlank(t.getMysqlQuerySql()) && StringUtils.isNotBlank(t.getCkTempTableSql())).collect(Collectors.toList());
        List<QualityRule> qualityResultRules = qualityRules.stream().filter(t -> Objects.equals(1, t.getSqlType()) && (StringUtils.isNotBlank(t.getMysqlQuerySql()) || StringUtils.isNotBlank(t.getCkQuerySql()))).collect(Collectors.toList());
        MysqlProvider.executeConnect(mysqlConnect -> {
            CkProvider.executeConnect(ckConnect -> {
                // 执行初始化sql
                executeInitSql(mysqlConnect, ckConnect, createCkTempTableRules, ignoreError);
                // 执行指标sql
                executeResultSql(mysqlConnect, ckConnect, qualityResultRules, ignoreError);
                return null;
            });
            return null;
        });
    }

    private void executeInitSql(Connection mysqlConnect, Connection ckConnect, List<QualityRule> createCkTempTableRules, boolean ignoreError) {
        if (CollectionUtils.isEmpty(createCkTempTableRules)) {
            return;
        }
        LOGGER.info("开始创建ck临时表");
        createCkTempTableRules.forEach(createCkTempTableRule -> {
            try {
                // 获取ck表名
                Matcher matcher = CK_TEMP_TABLE_PATTERN.matcher(createCkTempTableRule.getCkTempTableSql());
                if (matcher.find()) {
                    String ckTempTableName = matcher.group(1);
                    LOGGER.info("创建ck临时表\n{}\nfrom mysql\n{}", createCkTempTableRule.getCkTempTableSql(), createCkTempTableRule.getMysqlQuerySql());
                    writeCkFormMysql(mysqlConnect, ckConnect, createCkTempTableRule.getMysqlQuerySql(), createCkTempTableRule.getCkTempTableSql(), ckTempTableName);
                } else {
                    throw new RuntimeException(createCkTempTableRule.getQualityName() + " 创建的临时表不合法");
                }
            } catch (Exception e) {
                LOGGER.error("{} 创建ck临时表异常:", createCkTempTableRule.getQualityName(), e);
                if (!ignoreError) {
                    throw new RuntimeException(e);
                }
            }
        });
    }

    private void writeCkFormMysql(Connection mysqlConnect, Connection ckConnect, String mysqlSql, String ckSql, String ckTempTableName) {
        MysqlProvider.executeStatement(mysqlConnect, mysqlStatement -> {
            try {
                ResultSet mysqlResultSet = mysqlStatement.executeQuery(mysqlSql);
                ResultSetMetaData metaData = mysqlResultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
                String ckInsertSql = "insert into " + ckTempTableName + " values" + Arrays.stream(new String[columnCount]).map(t -> "?").collect(Collectors.joining(",", "(", ")"));
                CkProvider.executeStatement(ckConnect, ckStatement -> {
                    try {
                        return ckStatement.execute(ckSql);
                    } catch (SQLException e) {
                        throw new RuntimeException(e);
                    }
                });
                CkProvider.executeBatchInsert(ckConnect, ckInsertSql, ckPreparedStatement -> {
                    try {
                        LOGGER.info("开始插入ck临时表 {}", ckTempTableName);
                        int size = 0;
                        int count = 0;
                        while (mysqlResultSet.next()) {
                            for (int i = 1; i <= columnCount; i++) {
                                Object value = mysqlResultSet.getObject(i);
                                ckPreparedStatement.setObject(i, value);
                            }
                            ckPreparedStatement.addBatch();
                            size++;
                            count++;
                            if (count >= ckBatchInsertSize) {
                                ckPreparedStatement.executeBatch();
                                count = 0;
                            }
                        }
                        if (count > 0) {
                            ckPreparedStatement.executeBatch();
                        }
                        LOGGER.info("{} 结果 {}条，插入 {} 临时表 {}条", mysqlSql, size, ckTempTableName, size);
                    } catch (SQLException e) {
                        throw new RuntimeException(e);
                    }
                });
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
            return null;
        });
    }

    private void executeResultSql(Connection mysqlConnect, Connection ckConnect, List<QualityRule> qualityResultRules, boolean ignoreError) {
        if (CollectionUtils.isEmpty(qualityResultRules)) {
            return;
        }
        LOGGER.info("开始查询指标结果");
        qualityResultRules.forEach(qualityResultRule -> {
            LOGGER.info("开始查询指标结果 {} {}", qualityResultRule.getId(), qualityResultRule.getQualityName());
            qualityResultRule.setExecuteSqlStartDate(new Date());
            try {
                JSONArray result = new JSONArray();
                if (StringUtils.isNotBlank(qualityResultRule.getMysqlQuerySql())) {
                    // 执行mysql指标查询
                    result = executeQuery(mysqlConnect, qualityResultRule.getMysqlQuerySql().replace(Constant.SEMICOLON, Constant.EMPTY) + " limit 100");
                } else if (StringUtils.isNotBlank(qualityResultRule.getCkQuerySql())) {
                    // 执行ck指标查询
                    String ckQuerySql = qualityResultRule.getCkQuerySql().replace(Constant.SEMICOLON, Constant.EMPTY) + " limit 100";
                    List<Map.Entry<String, String>> replaceParamList = CK_CAN_REPLACE_PARAM_DIMENSION_MAP.entrySet().stream().filter(t -> ckQuerySql.contains(t.getValue())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(replaceParamList)) {
                        // 需要参数替换时
                        String groupByStr = replaceParamList.stream().map(Map.Entry::getKey).collect(Collectors.joining(","));
                        String groupSql = "select " + groupByStr + " from " + DEFAULT_CK_TEMP_TABLE_LOT_STOCKING_DETAIL + " where IS_DELETE = 0 group by " + groupByStr;
                        JSONArray groupArray = executeQuery(ckConnect, groupSql);
                        for (Object groupParams : groupArray) {
                            JSONObject jsonObject = (JSONObject) groupParams;
                            String completeCkQuerySql = ckQuerySql;
                            for (Map.Entry<String, Object> replaceParam : jsonObject.entrySet()) {
                                completeCkQuerySql = completeCkQuerySql.replaceAll(CK_CAN_REPLACE_PARAM_DIMENSION_MAP.get(replaceParam.getKey()).replace("{", "\\{").replace("}", "\\}"), replaceParam.getValue().toString());
                            }
                            JSONArray jsonArray = executeQuery(ckConnect, completeCkQuerySql);
                            result.addAll(jsonArray);
                        }
                    } else {
                        result = executeQuery(ckConnect, ckQuerySql);
                    }
                }
                LOGGER.info("结束查询指标结果 {} {}", qualityResultRule.getId(), qualityResultRule.getQualityName());
                qualityResultRule.setQualityResult(result);
            } catch (Exception e) {
                LOGGER.error("查询指标结果 {} {} 异常：", qualityResultRule.getId(), qualityResultRule.getQualityName(), e);
                qualityResultRule.setQualityResult(null);
                if (!ignoreError) {
                    throw new RuntimeException(e);
                }
            } finally {
                qualityResultRule.setExecuteSqlEndDate(new Date());
            }
        });
        LOGGER.info("结束查询指标结果");
    }

    private static JSONArray executeQuery(Connection connection, String sql) {
        return executeStatement(connection, statement -> {
            try {
                LOGGER.info("执行sql 开始：{}", sql);
                ResultSet resultSet = statement.executeQuery(sql);
                JSONArray jsonArray = resultSetToJsonArray(resultSet);
                LOGGER.info("执行sql 结束：{},结果条数：{}", sql, jsonArray.size());
                return jsonArray;
            } catch (Exception e) {
                LOGGER.error("执行sql 异常：{}", sql, e);
                throw new RuntimeException(e);
            }
        });
    }


    private static <R> R executeStatement(Connection connection, Function<Statement, R> function) {
        R res;
        Statement statement = null;
        try {
            statement = connection.createStatement();
            res = function.apply(statement);
        } catch (Exception e) {
            LOGGER.error("executeStatement 异常：", e);
            throw new RuntimeException(e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (Exception e) {
                    LOGGER.info("close statement failed", e);
                }
            }
        }
        return res;
    }

    public static JSONArray resultSetToJsonArray(ResultSet resultSet) throws SQLException {
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();
        JSONArray result = new JSONArray();
        while (resultSet.next()) {
            JSONObject row = new JSONObject();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnLabel(i);
                Object value = resultSet.getObject(i);
                row.put(columnName, value);
            }
            result.add(row);
        }
        return result;
    }
}
