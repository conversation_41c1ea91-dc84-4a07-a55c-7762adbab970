package com.guwave.onedata.dataware.scheduler.model.value;

import java.util.Date;

/**
 * 2025/1/13 14:58
 * LotWaferData
 *
 * <AUTHOR>
 */
public class LotWaferData {
    private String customer;
    private String subCustomer;
    private String testArea;
    private String factory;
    private String factorySite;
    private String deviceId;
    private String lotId;
    private String waferNo;
    private String lotType;
    private String testStage;
    private Date startTime;
    private Date endTime;
    private String testProgram;
    private Long totalCnt;
    private Long firstPassCnt;
    private Long firstFailCnt;
    private Long finalPassCnt;
    private Long finalFailCnt;
    private Long recoveryCnt;
    private Double firstYield;
    private Double finalYield;
    private Double recoveryYield;

    public String getCustomer() {
        return customer;
    }

    public LotWaferData setCustomer(String customer) {
        this.customer = customer;
        return this;
    }

    public String getSubCustomer() {
        return subCustomer;
    }

    public LotWaferData setSubCustomer(String subCustomer) {
        this.subCustomer = subCustomer;
        return this;
    }

    public String getTestArea() {
        return testArea;
    }

    public LotWaferData setTestArea(String testArea) {
        this.testArea = testArea;
        return this;
    }

    public String getFactory() {
        return factory;
    }

    public LotWaferData setFactory(String factory) {
        this.factory = factory;
        return this;
    }

    public String getFactorySite() {
        return factorySite;
    }

    public LotWaferData setFactorySite(String factorySite) {
        this.factorySite = factorySite;
        return this;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public LotWaferData setDeviceId(String deviceId) {
        this.deviceId = deviceId;
        return this;
    }

    public String getLotId() {
        return lotId;
    }

    public LotWaferData setLotId(String lotId) {
        this.lotId = lotId;
        return this;
    }

    public String getWaferNo() {
        return waferNo;
    }

    public LotWaferData setWaferNo(String waferNo) {
        this.waferNo = waferNo;
        return this;
    }

    public String getLotType() {
        return lotType;
    }

    public LotWaferData setLotType(String lotType) {
        this.lotType = lotType;
        return this;
    }

    public String getTestStage() {
        return testStage;
    }

    public LotWaferData setTestStage(String testStage) {
        this.testStage = testStage;
        return this;
    }

    public Date getStartTime() {
        return startTime;
    }

    public LotWaferData setStartTime(Date startTime) {
        this.startTime = startTime;
        return this;
    }

    public Date getEndTime() {
        return endTime;
    }

    public LotWaferData setEndTime(Date endTime) {
        this.endTime = endTime;
        return this;
    }

    public String getTestProgram() {
        return testProgram;
    }

    public LotWaferData setTestProgram(String testProgram) {
        this.testProgram = testProgram;
        return this;
    }

    public Long getTotalCnt() {
        return totalCnt;
    }

    public LotWaferData setTotalCnt(Long totalCnt) {
        this.totalCnt = totalCnt;
        return this;
    }

    public Long getFirstPassCnt() {
        return firstPassCnt;
    }

    public LotWaferData setFirstPassCnt(Long firstPassCnt) {
        this.firstPassCnt = firstPassCnt;
        return this;
    }

    public Long getFirstFailCnt() {
        return firstFailCnt;
    }

    public LotWaferData setFirstFailCnt(Long firstFailCnt) {
        this.firstFailCnt = firstFailCnt;
        return this;
    }

    public Long getFinalPassCnt() {
        return finalPassCnt;
    }

    public LotWaferData setFinalPassCnt(Long finalPassCnt) {
        this.finalPassCnt = finalPassCnt;
        return this;
    }

    public Long getFinalFailCnt() {
        return finalFailCnt;
    }

    public LotWaferData setFinalFailCnt(Long finalFailCnt) {
        this.finalFailCnt = finalFailCnt;
        return this;
    }

    public Long getRecoveryCnt() {
        return recoveryCnt;
    }

    public LotWaferData setRecoveryCnt(Long recoveryCnt) {
        this.recoveryCnt = recoveryCnt;
        return this;
    }

    public Double getFirstYield() {
        return firstYield;
    }

    public LotWaferData setFirstYield(Double firstYield) {
        this.firstYield = firstYield;
        return this;
    }

    public Double getFinalYield() {
        return finalYield;
    }

    public LotWaferData setFinalYield(Double finalYield) {
        this.finalYield = finalYield;
        return this;
    }

    public Double getRecoveryYield() {
        return recoveryYield;
    }

    public LotWaferData setRecoveryYield(Double recoveryYield) {
        this.recoveryYield = recoveryYield;
        return this;
    }
}
