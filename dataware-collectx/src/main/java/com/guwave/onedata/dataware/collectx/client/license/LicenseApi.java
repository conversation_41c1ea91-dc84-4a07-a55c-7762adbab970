package com.guwave.onedata.dataware.collectx.client.license;

import com.guwave.onedata.dataware.collectx.annotation.FeignClient;
import com.guwave.onedata.dataware.collectx.client.license.vo.LicenseRequestVo;
import com.guwave.onedata.dataware.collectx.client.license.vo.LicenseResponseVo;
import feign.Headers;
import feign.RequestLine;

@FeignClient(name = "licenseApi", url = "${license.baseUrl}")
public interface LicenseApi {

    @RequestLine("POST /license/checkWafersAndDataSize")
    @Headers("Content-Type: application/json")
    LicenseResponseVo<String> check(LicenseRequestVo licenseRequestVo);
}
