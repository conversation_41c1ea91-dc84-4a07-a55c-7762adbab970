package com.guwave.onedata.dataware.collectx.util;

import lombok.extern.slf4j.Slf4j;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 *
 */
@Slf4j
public class ConfigLoader {
    public static Properties loadDruidConfig() {
        Properties props = new Properties();
        InputStream is = null;
        try {
            String configFileName = ConfigLoader.class.getClassLoader().getResource("config/druid.properties").getPath();
            is = new FileInputStream(configFileName);
        } catch (FileNotFoundException e) {
            log.error(e.toString(),e);
        }
        try {
            props.load(is);
        } catch (IOException e) {
            log.error(e.toString(),e);
        }
        return props;
    }

    public static Properties loadSFTPConfig() {
        Properties props = new Properties();
        InputStream is = null;
        try {
            String configFileName = ConfigLoader.class.getClassLoader().getResource("config/sftp.properties").getPath();
            is = new FileInputStream(configFileName);
//            is = ConfigLoader.class.getClassLoader().getResourceAsStream("/sftp.properties");
        } catch (FileNotFoundException e) {
            log.error(e.toString(),e);
        }
        try {
            props.load(is);
        } catch (IOException e) {
            log.error(e.toString(),e);
        }
        return props;
    }

    public static Properties loadHdfsConfig(){
        Properties props = new Properties();
        InputStream is = null;
        try {
            String configFileName =  ConfigLoader.class.getClassLoader().getResource("config/hdfs.properties").getPath();
            is = new FileInputStream(configFileName);
        } catch (FileNotFoundException e) {
            log.error(e.toString(),e);
        }
        try {
            props.load(is);
        } catch (IOException e) {
            log.error(e.toString(),e);
        }
        return props;
    }
}
