package com.guwave.onedata.dataware.scheduler.callback;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.DwLayer;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.UploadType;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.MesParseLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SparkAppConfig;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.MesParseLogRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SparkAppConfigRepository;
import com.guwave.onedata.dataware.scheduler.listener.MesDwsPatchAppListener;
import com.guwave.onedata.dataware.scheduler.util.ComputeUtil;
import com.guwave.onedata.dataware.scheduler.util.ThreadPoolUtils;
import com.guwave.onedata.next.compute.api.callback.ComputeCallback;
import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Component
public class MesDwsPatchTaskFinishCallback implements ComputeCallback {

    @Autowired
    private SparkAppConfigRepository sparkAppConfigRepository;
    @Autowired
    private MesDwsPatchAppListener mesDwsPatchAppListener;
    @Autowired
    private MesParseLogRepository mesParseLogRepository;

    private Set<String> supportComputeCodes;
    private Executor executor;

    @PostConstruct
    public void init() {
        supportComputeCodes = sparkAppConfigRepository.findAllByUploadTypeInAndDwLayerIn(Lists.newArrayList(UploadType.AUTO), Lists.newArrayList(DwLayer.MES_DWS_PATCH))
                .stream().map(SparkAppConfig::getMainClass).collect(Collectors.toSet());
        executor = ThreadPoolUtils.getNewThreadPoolExecutor(this.getClass().getName(), 10, 10, 10000);
    }

    @Override
    public void doCallback(ComputeResultMessage computeResultMessage) {
        ProcessStatus processStatus = ComputeUtil.convertProcessStatus(computeResultMessage.getProcessStatus());
        if (processStatus == ProcessStatus.PROCESSING) {
            LOGGER.info("computeResultMessage {} 忽略", JSON.toJSONString(computeResultMessage));
            return;
        }
        List<MesParseLog> mesParseLogs = mesParseLogRepository.findByUniqueIdAndProcessStatus(computeResultMessage.getUniqueId(), ProcessStatus.PROCESSING);
        if (CollectionUtils.isEmpty(mesParseLogs)) {
            LOGGER.info("computeResultMessage {} 没有对应的记录", JSON.toJSONString(computeResultMessage));
            return;
        }

        executor.execute(() -> {
            LOGGER.info("mes dws patch 结束 开始处理：{}", JSON.toJSONString(computeResultMessage));
            mesDwsPatchAppListener.stateChanged(mesParseLogs, computeResultMessage);
        });
    }

    @Override
    public boolean isSupport(String computeCode) {
        return supportComputeCodes.contains(computeCode);
    }
}
