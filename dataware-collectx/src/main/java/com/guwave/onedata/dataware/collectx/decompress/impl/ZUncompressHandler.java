package com.guwave.onedata.dataware.collectx.decompress.impl;

import com.guwave.onedata.dataware.collectx.decompress.UnCompressHandler;
import org.apache.commons.compress.compressors.z.ZCompressorInputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;

public class ZUncompressHandler implements UnCompressHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ZUncompressHandler.class);

    @Override
    public void unCompress(File file, File targetDir) throws Exception {
        try (FileInputStream fin = new FileInputStream(file);
             BufferedInputStream in = new BufferedInputStream(fin);
             ZCompressorInputStream zIn = new ZCompressorInputStream(in)) {
            String fileName = file.getName();
            File outFile = new File(targetDir, fileName.substring(0, fileName.lastIndexOf('.')));
            try (FileOutputStream out = new FileOutputStream(outFile)) {
                byte[] buffer = new byte[1024 * 5];
                int n;
                while (-1 != (n = zIn.read(buffer))) {
                    out.write(buffer, 0, n);
                }
                out.flush();
            } catch (Exception e) {
                LOGGER.error("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
                throw e;
            }
        } catch (Exception e) {
            LOGGER.error("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
            throw e;
        }
    }
}
