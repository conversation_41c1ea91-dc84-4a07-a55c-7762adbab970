package com.guwave.onedata.dataware.collectx.common;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

@EqualsAndHashCode(callSuper = true)
@Data
public class OneDataException extends RuntimeException {

    private static final long serialVersionUID = 8243998624528050991L;

    private String code;

    private String msg;

    public OneDataException(String msg){
        super(msg);
        this.msg = msg;
    }
    
    public OneDataException(String msg, Throwable t) {
        super(msg, t);
        this.msg = msg;
    }
    
    public OneDataException(String code, String msg, Throwable t) {
        super(msg, t);
        this.code = code;
        this.msg = msg;
    }

    public OneDataException(String code, String msg){
        this.code = code;
        if(StringUtils.isNotBlank(msg)){
            this.msg = msg;
        }
    }


    @Override
    public String toString() {
        return "OneDataException{" +
                "code='" + code + '\'' +
                ", msg='" + msg + '\'' +
                '}';
    }
}
