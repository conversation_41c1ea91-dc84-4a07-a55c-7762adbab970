package com.guwave.onedata.dataware.source.agent.manual.handler.impl;

import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.manual.ManualCalculateFinishVo;
import com.guwave.onedata.dataware.common.model.manual.ManualMoveProductionVo;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingFinishMessage;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingMessage;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualMessage;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualCalculateTaskRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualFileInfoRepository;
import com.guwave.onedata.dataware.source.agent.manual.handler.ManualCalculateTaskHandler;
import com.guwave.onedata.dataware.source.agent.manual.handler.ManualTypehandler;
import com.guwave.onedata.dataware.source.agent.manual.model.ManualLotWafer;
import com.guwave.onedata.dataware.source.agent.manual.util.CkUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Component
public class ManualMoveProductionHandler implements ManualTypehandler, ManualCalculateTaskHandler {


    @Autowired
    private ManualFileInfoRepository manualFileInfoRepository;
    @Autowired
    private ManualCalculateTaskRepository manualCalculateTaskRepository;
    @Autowired
    private CkUtil ckUtil;
    @Value("${spring.runMode.standaloneThreshold}")
    private Long standaloneThreshold;

    @Override
    public Boolean support(ManualType manualType) {
        return manualType == ManualType.MOVE_PRODUCTION;
    }

    @Override
    public void generateTask(ManualMessage manualMessage, ManualWarehousingMessage manualWarehousingMessage) {
        ManualMoveProductionVo manualMoveProductionVo = manualWarehousingMessage.getManualMoveProductionVo();

        // 校验参数
        ManualLotWafer manualLotWafer = checkParam(manualMoveProductionVo);

        ArrayList<Long> needDeleteFields = new ArrayList<>();
        if (manualMoveProductionVo.getLastManualProductionFileId() != null) {
            needDeleteFields.add(manualMoveProductionVo.getLastManualProductionFileId());
        }

        // 生成 manual_file_info
        ManualFileInfo manualFileInfo = manualFileInfoRepository.findByFileId(manualMoveProductionVo.getFileId());
        Date date = new Date();
        if (manualFileInfo == null) {
            manualFileInfo = new ManualFileInfo()
                    .setCreateUser(SYSTEM)
                    .setCreateTime(date);
        } else {
            needDeleteFields.add(manualMoveProductionVo.getFileId());
        }
        if (!needDeleteFields.isEmpty()) {
            ckUtil.deleteCkFromFileIds(needDeleteFields);
        }

        // 根据原始文件确定ManualFileSystem
        List<ManualFileInfo> originFile = manualFileInfoRepository.findAllByFileIdIn(manualMoveProductionVo.getOriginFileIds());
        List<ManualFileSystem> manualFileSystems = originFile.stream().map(ManualFileInfo::getManualFileSystem).distinct().collect(Collectors.toList());
        if (manualFileSystems.size() > 1) {
            throw new RuntimeException("More than one ManualFileSystem found.");
        } else {
            manualFileInfo.setManualFileSystem(manualFileSystems.isEmpty() ? ManualFileSystem.HDFS : manualFileSystems.get(0));
        }

        fillManualFileInfoFromManualLotWafer(manualLotWafer, manualFileInfo)
                .setFileId(manualMoveProductionVo.getFileId())
                .setFileName(manualMoveProductionVo.getFileName())
                .setFileOwner(manualMoveProductionVo.getFileOwner())
                .setUploadType(UploadType.MANUAL_PRODUCTION);

        // 保存 manual_file_info
        manualFileInfoRepository.save(manualFileInfo);

        // 生成计算任务
        ManualCalculateTask manualCalculateTask = generateManualCalculateTaskFromManualFileInfo(manualMessage, manualFileInfo)
                .setManualType(ManualType.MOVE_PRODUCTION)
                .setDwLayer(DwLayer.DWD)
                .setNeedReadFileIds(manualFileInfo.getOriginFileIds())
                .setNeedDeleteFileIds(needDeleteFields.stream().map(t -> t + EMPTY).distinct().collect(Collectors.joining(COMMA)))
                .setRunMode(manualFileInfo.getTestItemDataCount() < standaloneThreshold ? RunMode.STANDALONE : RunMode.DISTRIBUTED);

        // 保存 manual_calculate_task
        manualCalculateTaskRepository.save(manualCalculateTask);
    }

    @Override
    public void fillManualWarehousingFinishMessage(ManualWarehousingFinishMessage manualWarehousingFinishMessage, ManualWarehousingMessage manualWarehousingMessage) {
        ManualMoveProductionVo manualMoveProductionVo = manualWarehousingMessage.getManualMoveProductionVo();
        ManualCalculateFinishVo manualCalculateFinishVo = new ManualCalculateFinishVo()
                .setFileId(manualMoveProductionVo.getFileId())
                .setFileName(manualMoveProductionVo.getFileName())
                .setDwLayer(DwLayer.ODS);
        manualWarehousingFinishMessage.setManualCalculateFinishVo(manualCalculateFinishVo);
    }

    private ManualLotWafer checkParam(ManualMoveProductionVo manualMoveProductionVo) {
        ManualLotWafer manualLotWafer = new ManualLotWafer();
        StringBuilder errorMessageSb = new StringBuilder();
        if (StringUtils.isBlank(manualMoveProductionVo.getFileOwner())) {
            errorMessageSb.append("fileOwner").append(FIELD_IS_EMPTY).append(ENTER);
        }
        if (manualMoveProductionVo.getFileId() == null) {
            errorMessageSb.append("fileId").append(FIELD_IS_EMPTY).append(ENTER);
        }
        if (StringUtils.isBlank(manualMoveProductionVo.getFileName())) {
            errorMessageSb.append("fileName").append(FIELD_IS_EMPTY).append(ENTER);
        }
        if (CollectionUtils.isEmpty(manualMoveProductionVo.getOriginFileIds())) {
            errorMessageSb.append("originFileIds").append(FIELD_IS_EMPTY).append(ENTER);
        } else {
            List<ManualFileInfo> originFileInfos = manualFileInfoRepository.findAllByFileIdIn(manualMoveProductionVo.getOriginFileIds());
            Set<Long> existsFileIdSet = originFileInfos.stream().map(ManualFileInfo::getFileId).collect(Collectors.toSet());
            String notExistsFields = manualMoveProductionVo.getOriginFileIds().stream().filter(t -> !existsFileIdSet.contains(t)).map(t -> t + EMPTY).collect(Collectors.joining(COMMA));
            if (StringUtils.isNotEmpty(notExistsFields)) {
                errorMessageSb.append("originFileIds中").append(notExistsFields).append("对应的文件不存在").append(ENTER);
            } else {
                Set<String> customerSet = originFileInfos.stream().map(ManualFileInfo::getCustomer).collect(Collectors.toSet());
                Set<String> subCustomerSet = originFileInfos.stream().map(ManualFileInfo::getSubCustomer).collect(Collectors.toSet());
                Set<String> factorySet = originFileInfos.stream().map(ManualFileInfo::getFactory).collect(Collectors.toSet());
                Set<String> factorySiteSet = originFileInfos.stream().map(ManualFileInfo::getFactorySite).collect(Collectors.toSet());
                Set<TestArea> testAreaSet = originFileInfos.stream().map(ManualFileInfo::getTestArea).collect(Collectors.toSet());
                Set<String> testSatgeSet = originFileInfos.stream().map(ManualFileInfo::getTestStage).collect(Collectors.toSet());
                Set<LotType> lotTypeSet = originFileInfos.stream().map(ManualFileInfo::getLotType).collect(Collectors.toSet());
                Set<String> deviceIdSet = originFileInfos.stream().map(ManualFileInfo::getDeviceId).collect(Collectors.toSet());
                Set<String> lotIdSet = originFileInfos.stream().map(ManualFileInfo::getLotId).collect(Collectors.toSet());
                Set<String> waferNoSet = originFileInfos.stream().map(ManualFileInfo::getWaferNo).collect(Collectors.toSet());
                Set<FileCategory> fileCategorySet = originFileInfos.stream().map(ManualFileInfo::getFileCategory).collect(Collectors.toSet());
                Set<ParseRule> parseRuleSet = originFileInfos.stream().map(ManualFileInfo::getParseRule).collect(Collectors.toSet());

                manualLotWafer.setOriginFileInfos(originFileInfos);
                if (customerSet.size() > 1) {
                    errorMessageSb.append("移入量产库批次存在多个customer").append(ENTER);
                } else {
                    manualLotWafer.setCustomer(customerSet.iterator().next());
                }
                if (subCustomerSet.size() > 1) {
                    errorMessageSb.append("移入量产库批次存在多个subCustomer").append(ENTER);
                } else {
                    manualLotWafer.setSubCustomer(subCustomerSet.iterator().next());
                }
                if (testAreaSet.size() > 1) {
                    errorMessageSb.append("移入量产库批次存在多个testArea").append(ENTER);
                } else {
                    manualLotWafer.setTestArea(testAreaSet.iterator().next());
                }
                if (factorySet.size() > 1) {
                    errorMessageSb.append("移入量产库批次存在多个factory").append(ENTER);
                } else {
                    manualLotWafer.setFactory(factorySet.iterator().next());
                }
                if (factorySiteSet.size() > 1) {
                    errorMessageSb.append("移入量产库批次存在多个factorySite").append(ENTER);
                } else {
                    manualLotWafer.setFactorySite(factorySiteSet.iterator().next());
                }
                if (deviceIdSet.size() > 1) {
                    errorMessageSb.append("移入量产库批次存在多个deviceId").append(ENTER);
                } else {
                    manualLotWafer.setDeviceId(deviceIdSet.iterator().next());
                }
                if (lotIdSet.size() > 1) {
                    errorMessageSb.append("移入量产库批次存在多个lotId").append(ENTER);
                } else {
                    manualLotWafer.setLotId(lotIdSet.iterator().next());
                }
                if (waferNoSet.size() > 1) {
                    errorMessageSb.append("移入量产库批次存在多个waferNo").append(ENTER);
                } else {
                    manualLotWafer.setWaferNo(waferNoSet.iterator().next());
                }
                if (lotTypeSet.size() > 1) {
                    errorMessageSb.append("移入量产库批次存在多个lotType").append(ENTER);
                } else {
                    manualLotWafer.setLotType(lotTypeSet.iterator().next());
                }
                if (testSatgeSet.size() > 1) {
                    errorMessageSb.append("移入量产库批次存在多个testSatge").append(ENTER);
                } else {
                    manualLotWafer.setTestStage(testSatgeSet.iterator().next());
                }
                if (fileCategorySet.size() > 1) {
                    errorMessageSb.append("移入量产库批次存在多个fileCategory").append(ENTER);
                } else {
                    manualLotWafer.setFileCategory(fileCategorySet.iterator().next());
                }
                if (parseRuleSet.contains(ParseRule.SHMOO_SCRIPT)) {
                    errorMessageSb.append("shmoo 不支持移入量产库").append(ENTER);
                }
                if (testAreaSet.contains(TestArea.WAT)) {
                    errorMessageSb.append("wat 不支持移入量产库").append(ENTER);
                }
            }
        }

        String errorMessage = errorMessageSb.toString();
        if (!errorMessage.isEmpty()) {
            throw new FileLoadException(FileLoadExceptionInfo.PARAM_CKECK_FAIL, errorMessage, null).updateExceptionMessage(errorMessage);
        }

        return manualLotWafer;
    }
}
