package com.guwave.onedata.dataware.scheduler.callback;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LayerCalculatePool;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SparkAppConfig;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LayerCalculatePoolRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SparkAppConfigRepository;
import com.guwave.onedata.dataware.scheduler.listener.DwDieAppListener;
import com.guwave.onedata.dataware.scheduler.util.ComputeUtil;
import com.guwave.onedata.dataware.scheduler.util.ThreadPoolUtils;
import com.guwave.onedata.next.compute.api.callback.ComputeCallback;
import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Component
public class SparkAutoDwDieTaskFinishCallback implements ComputeCallback {

    @Autowired
    private SparkAppConfigRepository sparkAppConfigRepository;
    @Autowired
    private LayerCalculatePoolRepository layerCalculatePoolRepository;
    @Autowired
    private DwDieAppListener dwDieAppListener;

    private Set<String> supportComputeCodes;
    private Executor executor;

    @PostConstruct
    public void init() {
        supportComputeCodes = sparkAppConfigRepository.findAllByUploadTypeInAndDwLayerIn(Lists.newArrayList(UploadType.AUTO), Lists.newArrayList(DwLayer.DWD, DwLayer.DWS, DwLayer.ADS))
                .stream().map(SparkAppConfig::getMainClass).collect(Collectors.toSet());
        executor = ThreadPoolUtils.getNewThreadPoolExecutor(this.getClass().getName(), 20, 20, 10000);
    }

    @Override
    public void doCallback(ComputeResultMessage computeResultMessage) {
        ProcessStatus processStatus = ComputeUtil.convertProcessStatus(computeResultMessage.getProcessStatus());
        if (processStatus == ProcessStatus.PROCESSING) {
            LOGGER.info("computeResultMessage {} 忽略", JSON.toJSONString(computeResultMessage));
            return;
        }
        LayerCalculatePool layerCalculatePool = layerCalculatePoolRepository.findByUniqueIdAndProcessStatus(computeResultMessage.getUniqueId(), ProcessStatus.PROCESSING);
        if (layerCalculatePool == null) {
            LOGGER.info("computeResultMessage {} 没有对应的记录", JSON.toJSONString(computeResultMessage));
            return;
        }

        executor.execute(() -> {
            try {
                LOGGER.info("spark auto dw die task 结束 开始处理：{}", JSON.toJSONString(computeResultMessage));
                dwDieAppListener.stateChanged(layerCalculatePool, computeResultMessage);
            } catch (Exception ex) {
                LOGGER.error("更新die相关表失败", ex);
            }
        });
    }

    @Override
    public boolean isSupport(String computeCode) {
        return supportComputeCodes.contains(computeCode);
    }
}
