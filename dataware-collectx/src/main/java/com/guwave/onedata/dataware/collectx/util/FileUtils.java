package com.guwave.onedata.dataware.collectx.util;


import com.guwave.onedata.dataware.collectx.decompress.UnCompressHandler;
import com.guwave.onedata.dataware.collectx.decompress.impl.*;
import com.guwave.onedata.dataware.collectx.entity.FileAttr;
import com.guwave.onedata.dataware.collectx.enums.CompressFileSuffixEnum;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.Zip64Mode;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.tika.Tika;
import org.springframework.util.StringUtils;

import java.io.*;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.util.*;

@Slf4j
public class FileUtils {

    private final static List<String> filterDirList = new ArrayList<String>() {{
        add(".");
        add("..");
    }};
    private final static FilenameFilter dirFilenameFilter = (dir, name) -> !filterDirList.contains(name);

    private final static Map<CompressFileSuffixEnum, UnCompressHandler> canUnCompressMap = new HashMap<CompressFileSuffixEnum, UnCompressHandler>() {{
        put(CompressFileSuffixEnum.ZIP, new ZipUncompressHandler());
        put(CompressFileSuffixEnum.GZ, new GzUncompressHandler());
        put(CompressFileSuffixEnum.Z, new ZUncompressHandler());
        put(CompressFileSuffixEnum.TAR, new TarUncompressHandler());
        put(CompressFileSuffixEnum.SEVENZ, new ServenZUncompressHandler());
        put(CompressFileSuffixEnum.RAR, new RarUncompressHandler());
        put(CompressFileSuffixEnum.RAR4, new RarUncompressHandler());
        put(CompressFileSuffixEnum.TGZ, new TarUncompressHandler());
    }};
    private final static Set<CompressFileSuffixEnum> canUnCompressSuffixSet = canUnCompressMap.keySet();

    public static void deleteDirs(File file) {
        org.apache.commons.io.FileUtils.deleteQuietly(file);
    }

    public static void deleteFile(String path, String fileName) {
        String fileFullName = getFileFullName(path, fileName);
        File file = new File(fileFullName);
        deleteFile(file);
    }

    public static void deleteFile(File file) {
        try {
            if (file != null && file.exists()) {
                file.delete();
            }
        } catch (Exception e) {
            log.error(e.toString(), e);
        }
    }

    public static int getFileLineNumber(String fileName) {
        File file = new File(fileName);
        LineNumberReader rf = null;
        try {
            rf = new LineNumberReader(new FileReader(file));
            if (rf != null) {
                rf.skip(file.length());
                return rf.getLineNumber();
            }
        } catch (Exception e) {

        } finally {
            IOUtils.silenceClose(rf);
        }
        return 0;
    }

    public static boolean checkFileExists(String fileName) {
        File file = new File(fileName);
        if (!file.exists()) {
            log.info("{} doesn't exist.", fileName);
        } else if (file.isFile()) {
            return true;
        } else if (file.isDirectory()) {
            log.info("{} is a folder and not a file.", fileName);
        }
        return false;
    }

    public static boolean checkFolderExists(String filePath) {
        File file = new File(filePath);
        if (!file.exists()) {
            log.info("{} doesn't exist.", filePath);
        } else if (file.isDirectory()) {
            return true;
        } else if (file.isFile()) {
            log.info("{} is a file and not a folder.", filePath);
        }
        return false;
    }

    public static boolean checkBlankFolder(String filePath) {
        if (checkFolderExists(filePath)) {
            File file = new File(filePath);
            if (file.listFiles().length == 0) {
                return true;
            } else {
                log.info("{} is not a blank folder.", filePath);
            }
        }
        return false;
    }

    public static boolean makeFolderIfNotExist(String filePath) {
        if (!checkFolderExists(filePath)) {
            File file = new File(filePath);
            return file.mkdirs();
        }
        return true;
    }

    public static List<File> listFolderFiles(String filePath, List<File> fileList) {
        File file = new File(filePath);
        if (file.exists()) {
            if (file.isFile()) {
                fileList.add(file);
            } else if (file.isDirectory()) {
                File[] files = file.listFiles();
                for (File f : files) {
                    listFolderFiles(f.getPath(), fileList);
                }
            }
        } else {
            log.info("{} doesn't exist.", filePath);
        }
        return fileList;
    }

    public static void clearFolder(String filePath) {
        try {
            File file = new File(filePath);
            if (file.isDirectory()) {
                String[] children = file.list();
                for (String child : children) {
                    clearFolder(filePath + Constant.SLASH + child);
                }
                file.delete();
                log.info("{} folder is deleted.", filePath);
            } else {
                file.delete();
                log.info("{} file is deleted.", filePath);
            }
        } catch (Exception e) {
            log.error(e.toString(), e);
        }
    }

    public static void isFolderEmpty(String folderPath) throws Exception {
        File outFolder = new File(folderPath);
        if (outFolder.exists()) {
            if (outFolder.isFile()) {
                outFolder.delete();
            } else {
                File[] files = outFolder.listFiles();
                if (files != null && files.length > 0) {
                    throw new RuntimeException("Output Folder Exists => "
                            + outFolder.getAbsolutePath());
                }
            }
        }
        outFolder.mkdirs();
    }

    public static String getFileMD5(File file) {
        FileInputStream input = null;
        try {
            input = new FileInputStream(file);
            byte[] buffer = new byte[0x4000];
            MessageDigest md = MessageDigest.getInstance("MD5");
            int length = -1;
            while ((length = input.read(buffer)) > 0)
                md.update(buffer, 0, length);
            byte[] arrayOfByte = md.digest();
            return new BigInteger(1, arrayOfByte).toString(16);
        } catch (Exception ex) {
            log.error("getFileMD5(" + file.getAbsolutePath() + ")", ex);
        } finally {
            IOUtils.silenceClose(input);
        }
        return null;
    }

    public static boolean writeStringToFile(String fileName, String data) {
        File file = new File(fileName);
        if (file.exists() && file.isFile())
            file.delete();

        FileWriter fw = null;
        try {
            fw = new FileWriter(fileName, false);
            BufferedWriter bw = new BufferedWriter(fw);
            bw.write(data);
            bw.close();
            fw.close();
            return true;
        } catch (Exception ex) {
            log.error("writeStringToFile(" + fileName + ")", ex);
        } finally {
            IOUtils.silenceClose(fw);
        }
        return false;
    }

    public static void copyFile(String fromPath, String toPath) {
        FileInputStream is = null;
        FileOutputStream fos = null;
        int bufferCount;
        byte[] buffer = new byte[1024];
        try {
            is = new FileInputStream(new File(fromPath));
            fos = new FileOutputStream(new File(toPath));
            while ((bufferCount = is.read(buffer)) != -1) {
                fos.write(buffer, 0, bufferCount);
            }
        } catch (Exception e) {
            log.error(e.toString(), e);
        } finally {
            try {
                fos.flush();
                fos.close();
                is.close();
            } catch (Exception e) {
                log.error(e.toString(), e);
            }
        }
    }

    public static String getExtension(String f) {
        String ext = "";
        int i = f.lastIndexOf('.');

        if (i > 0 && i < f.length() - 1) {
            ext = f.substring(i + 1);
        }
        return ext;
    }

    public static String getFileNameWithoutExtension(String fileName) {
        int i = fileName.indexOf('.');
        if (i >= 0) {
            return fileName.substring(0, i);
        } else {
            return fileName;
        }
    }

    public static void deCompressFile(List<FileAttr> fileAttrs, File file, Set<CompressFileSuffixEnum> needDecompressTypesConfig) throws Exception {
        if (file.isDirectory()) {
            // 如果是文件夹
            File[] subFileList = file.listFiles(dirFilenameFilter);
            if (subFileList != null) {
                for (File subFile : subFileList) {
                    deCompressFile(fileAttrs, subFile, needDecompressTypesConfig);
                }
            }
        } else if (file.isFile()) {
            // 是文件
            String fileName = file.getName();
            int suffixIndex = fileName.lastIndexOf(Constant.POINT);
            CompressFileSuffixEnum fileSuffix = CompressFileSuffixEnum.of(fileName.substring(suffixIndex + 1));

            Tika tika = new Tika();
            // 根据文件识别实际的压缩类型
            CompressFileSuffixEnum actlType = CompressFileSuffixEnum.ofType(tika.detect(file));

            if (fileSuffix != actlType) {
                log.warn("{}的实际压缩类型其实是{}", file.getName(), actlType);
            }
            if (suffixIndex > -1 && actlType != null && canUnCompressSuffixSet.contains(fileSuffix) && needDecompressTypesConfig.contains(fileSuffix)) {
                // 需要解压
                UnCompressHandler unCompressHandler = canUnCompressMap.get(actlType);
                File targetDir = new File(file.getParent(), fileName.substring(0, suffixIndex));
                if (!targetDir.exists()) {
                    targetDir.mkdirs();
                }
                unCompressHandler.unCompress(file, targetDir);
                File[] unCompressFileDir = targetDir.listFiles(dirFilenameFilter);
                if (unCompressFileDir != null) {
                    for (File unCompressFile : unCompressFileDir) {
                        deCompressFile(fileAttrs, unCompressFile, needDecompressTypesConfig);
                    }
                }
            } else {
                // 不需要解压的文件
                FileAttr fileAttr = new FileAttr();
                fileAttr.setFilePath(file.getParent());
                fileAttr.setFileName(fileName);
                fileAttr.setOriginalFileName(fileName);
                fileAttr.setFullName(file.getAbsolutePath());
                fileAttr.setSize(file.length());
                fileAttr.setOriginalSize(file.length());
                fileAttrs.add(fileAttr);
            }
        }
    }

    public static String getFileNameSuffix(String fileName) {
        if (fileName != null) {
            return fileName.substring(fileName.lastIndexOf(".") + 1);
        } else {
            return null;
        }
    }

    public static void setFileAttrInfo(FileAttr fileAttr, String filePath, String fileName) {
        fileAttr.setFilePath(filePath);
        fileAttr.setFileName(fileName);
        fileAttr.setFullName(getFileFullName(filePath, fileName));
    }

    private static List<FileAttr> addFileAttrs(FileAttr fileAttr, List<FileAttr> fileAttrs) {
        if (fileAttrs == null) {
            fileAttrs = new ArrayList<>();
        }
        fileAttrs.add(fileAttr);
        return fileAttrs;
    }

    public static String getFileFullName(String filePath, String fileName) {
        String fileFullName = "";
        if (filePath.endsWith(Constant.SLASH)) {
            fileFullName = filePath + fileName;
        } else {
            fileFullName = filePath + Constant.SLASH + fileName;
        }
        return fileFullName;
    }

    public static String getFileFullPath(String filePath, String subFilePath) {
        String fileFullName = "";
        if (filePath.endsWith(Constant.SLASH)) {
            fileFullName = filePath + subFilePath;
        } else {
            fileFullName = filePath + Constant.SLASH + subFilePath;
        }
        if (fileFullName.endsWith(Constant.SLASH)) {
            return fileFullName;
        }
        return fileFullName + Constant.SLASH;
    }

    public static String getFileName(String fileFullName) {
        String fName = fileFullName.trim();
        return fName.substring(fName.lastIndexOf(Constant.SLASH) + 1);
    }

    public static String renameFile(String fileName, String prefix, String suffix) {
        String renameFileName = fileName;
        if (StringUtils.hasLength(fileName)) {
            Integer lastSeparatorIndex = fileName.lastIndexOf(Constant.SLASH);
            String folderName = "";
            if (lastSeparatorIndex >= 0) {
                folderName = fileName.substring(0, lastSeparatorIndex + 1);
            }
            String realFileName = fileName.substring(lastSeparatorIndex + 1);
            String fileNameWithoutExtension = getFileNameWithoutExtension(realFileName);
            String ext = realFileName.substring(fileNameWithoutExtension.length());
            renameFileName = folderName + (prefix == null ? "" : prefix.trim()) +
                    fileNameWithoutExtension.trim() +
                    (suffix == null ? "" : suffix.trim()) +
                    ext;
        }
        return renameFileName;
    }

    public static String renameFile(String fileName) {
        return renameFile(fileName, "Guwave" + System.currentTimeMillis() + "_", null);
    }

    public static void compressFileToZip(String originalPath, String fileName, String zipPath) {
        if (!originalPath.endsWith(Constant.SLASH)) {
            originalPath = originalPath + Constant.SLASH;
        }
        if (!zipPath.endsWith(Constant.SLASH)) {
            zipPath = zipPath + Constant.SLASH;
        }

        String zipFileName = fileName + Constant.POINT + FileType.ZIP.getType();
        deleteFile(zipPath, zipFileName);
        String crcFileName = Constant.POINT + fileName + Constant.POINT + FileType.ZIP.getType() + Constant.POINT + Constant.CRC_SUFFIX;
        deleteFile(zipPath, crcFileName);

        File zipFile = new File(zipPath, zipFileName);
        if (!zipFile.getParentFile().exists()) {
            zipFile.getParentFile().mkdirs();
        }

        // 压缩
        compress(originalPath + fileName, zipPath + zipFileName);
        // 删除源文件
        deleteFile(originalPath, fileName);
    }

    /**
     * 压缩文件
     *
     * @param filePath    filePath
     * @param zipFilePath zipFilePath
     */
    public static void compress(String filePath, String zipFilePath) {
        ZipArchiveOutputStream zaos = null;
        File file = new File(filePath);
        try {
            File zipFile = new File(zipFilePath);
            zaos = new ZipArchiveOutputStream(zipFile);
            zaos.setUseZip64(Zip64Mode.AsNeeded);

            ZipArchiveEntry zipArchiveEntry = new ZipArchiveEntry(file, file.getName());
            zaos.putArchiveEntry(zipArchiveEntry);
            try (InputStream is = new BufferedInputStream(new FileInputStream(file))) {
                byte[] buffer = new byte[1024 * 5];
                int len;
                while ((len = is.read(buffer)) != -1) {
                    zaos.write(buffer, 0, len);
                }
                zaos.closeArchiveEntry();
            } catch (Exception e) {
                log.info("压缩失败", e);
            }
            zaos.finish();
        } catch (Exception e) {
            log.info("压缩失败", e);
        } finally {
            try {
                if (zaos != null) {
                    zaos.close();
                }
            } catch (IOException e) {
                log.info("压缩失败", e);
            }
        }
    }

    public static void main(String[] args) {
        System.out.println(getFileNameWithoutExtension("xg.tar.gz"));
        System.out.println(renameFile("test.tar.gz"));
        System.out.println(renameFile("2022.03test.stdf"));
        System.out.println(renameFile("test.txt"));
        System.out.println(renameFile("/test/abc/test.gz"));
        System.out.println(renameFile("/test.gz"));
    }
}
