package com.guwave.onedata.dataware.source.agent.common.util;

import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.parser.stdf.util.CommonStdfParseUtil;
import com.guwave.onedata.dataware.source.agent.common.model.FileTestInfo;
import com.guwave.onedata.dataware.source.agent.common.visitor.MultiThreadVisitor;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class FileTestInfoUtil {
    public static FileTestInfo calculateFileTestInfo(List<MultiThreadVisitor> visitors) {
        Long dieDataCnt = 0L;
        Long testItemDataCnt = 0L;
        Map<String, Long> testItemCntMap = new HashMap<>();
        for (MultiThreadVisitor visitor : visitors) {
            visitor.getTestItemCntMap().forEach((k, v) -> CommonStdfParseUtil.calculateTestItemCntMap(k, v, testItemCntMap));
            dieDataCnt += visitor.getDieDataCnt();
            testItemDataCnt += visitor.getTestItemDataCnt();
        }
        if (dieDataCnt == 0L) {
            throw new FileLoadException(FileLoadExceptionInfo.STDF_NOT_FOUND_DIE_DATA_EXCEPTION, FileLoadExceptionInfo.STDF_NOT_FOUND_DIE_DATA_EXCEPTION.getMessage(), null);
        }
        List<Map.Entry<String, Long>> testItemCntSort = testItemCntMap.entrySet().stream().sorted(Comparator.comparingLong(Map.Entry::getValue)).collect(Collectors.toList());
        Map.Entry<String, Long> testItemCntMin = testItemCntSort.isEmpty() ? null : testItemCntSort.get(0);
        Map.Entry<String, Long> testItemCntMax = testItemCntSort.isEmpty() ? null : testItemCntSort.get(testItemCntSort.size() - 1);

        return new FileTestInfo()
                .setDieDataCnt(dieDataCnt)
                .setTestItemDataCnt(testItemDataCnt)
                .setTestItemCntMin(testItemCntMin)
                .setTestItemCntMax(testItemCntMax);
    }
}
