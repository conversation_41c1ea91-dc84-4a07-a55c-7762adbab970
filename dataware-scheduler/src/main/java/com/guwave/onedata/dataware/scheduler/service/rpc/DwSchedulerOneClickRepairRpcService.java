package com.guwave.onedata.dataware.scheduler.service.rpc;

import com.guwave.onedata.dataware.bridge.api.iface.IDwSchedulerOneClickRepairRpcService;
import com.guwave.onedata.dataware.scheduler.service.RedisService;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@DubboService
@Component
public class DwSchedulerOneClickRepairRpcService implements IDwSchedulerOneClickRepairRpcService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DwSchedulerOneClickRepairRpcService.class);

    @Autowired
    private RedisService redisService;

    @Override
    public void oneClickRepair() {
        redisService.clearAll();
    }
}