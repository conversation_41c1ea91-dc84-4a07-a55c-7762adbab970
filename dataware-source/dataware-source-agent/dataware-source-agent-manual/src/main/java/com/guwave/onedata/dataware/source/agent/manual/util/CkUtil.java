package com.guwave.onedata.dataware.source.agent.manual.util;

import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.DwLayer;
import com.guwave.onedata.dataware.common.contant.UploadType;
import com.guwave.onedata.dataware.dao.ck.connection.ClickHouseConnection;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DwTable;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.DwTableRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualCalculateTaskRepository;
import com.guwave.onedata.dataware.source.agent.manual.model.ManualPartitionKey;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.Statement;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Component
public class CkUtil {
    private static Logger LOGGER = LoggerFactory.getLogger(CkUtil.class);


    private final static String FILE_IDS = "{FILE_IDS}";

    private static final String LOT_ID_TO_LOT_BUCKET = "abs(javaHash('{LOT_ID}')) % 6";
    private final static String DELETE_TABLE_SQL_TEMPLATE = "ALTER TABLE {DB_NAME}.{TABLE_NAME} ON CLUSTER {CLUSTER} UPDATE IS_DELETE = 1 {IN_PARTITION} WHERE "
            + " FILE_ID IN ({FILE_IDS}) AND CREATE_TIME < toDateTime('{CURRENT_TIME}') ";
    private final static String DELETE_FILE_ID_TABLE_SQL_TEMPLATE = "ALTER TABLE {DB_NAME}.{TABLE_NAME} ON CLUSTER {CLUSTER} UPDATE IS_DELETE = 1 {IN_PARTITION} WHERE "
            + " FILE_ID = {FILE_ID} AND CREATE_TIME < toDateTime('{CURRENT_TIME}')";

    private final static List<String> NOT_NEED_TABLE_LIST = Lists.newArrayList(
            "dim_test_program_test_order_local",
            "dim_bin_failitem_local",
            "dim_time_hour_local",
            "dim_time_day_local",
            "dws_wafer_index_local",
            "dws_wafer_overall_yield_index_local"
    );

    private final static List<String> CONTAINS_FILE_ID_TABLE = Lists.newArrayList(
            "dwd_shmoo_vmin_vmax_detail_local",
            "dwd_shmoo_detail_local"
    );


    @Value("${spring.data.clickhouse.cluster}")
    private String clusterName;

    @Autowired
    private DwTableRepository dwTableRepository;

    @Autowired
    private ManualCalculateTaskRepository manualCalculateTaskRepository;

    public void deleteCkFromFileIds(List<Long> fileIds) {
        if (CollectionUtils.isEmpty(fileIds)) {
            return;
        }
        String fileIdsStr = fileIds.stream().map(t -> t + EMPTY).distinct().collect(Collectors.joining(COMMA));

        // 找到需要删除的表并修改partition_key为对应模版
        List<DwTable> needDeleteTables = dwTableRepository.findAllByUploadTypeAndDwLayerInAndCalculateFlag(UploadType.MANUAL, Lists.newArrayList(DwLayer.DWD, DwLayer.DIM, DwLayer.DWS), 1).stream()
                .peek(t -> t.setPartitionKey(t.getPartitionKey().replaceAll("(\\w+)", "'{$1}'")))
                .collect(Collectors.toList());

        // 找到需要删除的文件对应的表维分区度
        List<ManualPartitionKey> deleteFileIdTablePartitions = manualCalculateTaskRepository.findByFileIdIn(fileIds).stream()
                .map(t -> new ManualPartitionKey(t.getCustomer(), t.getSubCustomer(), t.getTestArea(), t.getLotId(), t.getFactory(), t.getDeviceId(), t.getUploadType()))
                .distinct()
                .collect(Collectors.toList());
        List<ManualPartitionKey> deleteNotFileIdTablePartitions = manualCalculateTaskRepository.findByFileIdIn(fileIds).stream()
                .map(t -> new ManualPartitionKey(t.getCustomer(), t.getSubCustomer(), t.getTestArea(), t.getLotId(), t.getFactory(), t.getDeviceId(), t.getUploadType(), t.getFileId()))
                .distinct()
                .collect(Collectors.toList());

        // 构建分区键不包含fileid表的删除SQL
        List<String> deleteNotFileIdTableSql = deleteFileIdTablePartitions.stream()
                .flatMap(t -> needDeleteTables.stream()
                        .filter(dwTable -> !NOT_NEED_TABLE_LIST.contains(dwTable.getLocalName()) && !CONTAINS_FILE_ID_TABLE.contains(dwTable.getLocalName()))
                        .map(dwTable -> {
                            String testArea = t.getTestArea() == null ? EMPTY : t.getTestArea().getArea();
                            String uploadType = t.getUploadType() == null ? EMPTY : t.getUploadType().getType();
                            String lotType = t.getLotType() == null ? EMPTY : t.getLotType().getType();

                            return DELETE_TABLE_SQL_TEMPLATE
                                    .replace(DB_NAME, dwTable.getDb())
                                    .replace(TABLE_NAME, dwTable.getLocalName())
                                    .replace(IN_PARTITION, PARTITION_EXPR_PREFIX + dwTable.getPartitionKey())
                                    .replace(CUSTOMER, t.getCustomer())
                                    .replace(SUB_CUSTOMER, t.getSubCustomer())
                                    .replace(FACTORY, t.getFactory())
                                    .replace(TEST_AREA, testArea)
                                    .replace(LOT_ID, t.getLotId())
                                    .replace(UPLOAD_TYPE, uploadType)
                                    .replace(DEVICE_ID, t.getDeviceId())
                                    .replace(LOT_TYPE, lotType)
                                    .replace(TEST_STAGE, t.getTestStage())
                                    .replace(SINGLE_QUOTATION + LOT_BUCKET + SINGLE_QUOTATION, LOT_ID_TO_LOT_BUCKET.replace(LOT_ID, t.getLotId()))
                                    .replace(CLUSTER_NAME, clusterName)
                                    .replace(FILE_IDS, fileIdsStr)
                                    .replace(CURRENT_TIME, System.currentTimeMillis() / 1000 + EMPTY);
                        })
                        .distinct()
                ).collect(Collectors.toList());

        // 构建分区键包含fileid表的删除SQL
        List<String> deleteFileIdTableSql = deleteNotFileIdTablePartitions.stream()
                .flatMap(t -> needDeleteTables.stream()
                        .filter(dwTable -> !NOT_NEED_TABLE_LIST.contains(dwTable.getLocalName()) && CONTAINS_FILE_ID_TABLE.contains(dwTable.getLocalName()))
                        .map(t1 -> {
                            String testArea = t.getTestArea() == null ? EMPTY : t.getTestArea().getArea();
                            String uploadType = t.getUploadType() == null ? EMPTY : t.getUploadType().getType();
                            String lotType = t.getLotType() == null ? EMPTY : t.getLotType().getType();

                            return DELETE_FILE_ID_TABLE_SQL_TEMPLATE
                                    .replace(DB_NAME, t1.getDb())
                                    .replace(TABLE_NAME, t1.getLocalName())
                                    .replace(IN_PARTITION, PARTITION_EXPR_PREFIX + t1.getPartitionKey())
                                    .replace(CUSTOMER, t.getCustomer())
                                    .replace(SUB_CUSTOMER, t.getSubCustomer())
                                    .replace(FACTORY, t.getFactory())
                                    .replace(TEST_AREA, testArea)
                                    .replace(LOT_ID, t.getLotId())
                                    .replace(UPLOAD_TYPE, uploadType)
                                    .replace(DEVICE_ID, t.getDeviceId())
                                    .replace(LOT_TYPE, lotType)
                                    .replace(TEST_STAGE, t.getTestStage())
                                    .replace(SINGLE_QUOTATION + LOT_BUCKET + SINGLE_QUOTATION, LOT_ID_TO_LOT_BUCKET.replace(LOT_ID, t.getLotId()))
                                    .replace(CLUSTER_NAME, clusterName)
                                    .replace(SINGLE_QUOTATION + FILE_ID + SINGLE_QUOTATION, t.getFileId() + EMPTY)
                                    .replace(FILE_ID, t.getFileId() + EMPTY)
                                    .replace(CURRENT_TIME, System.currentTimeMillis() / 1000 + EMPTY);
                        })
                        .distinct()
                ).collect(Collectors.toList());

        // 合并删除SQL并执行
        executeSqls(Stream.concat(deleteFileIdTableSql.stream(), deleteNotFileIdTableSql.stream()).collect(Collectors.toList()));
    }

    private void executeSqls(List<String> sqls) {
        String allSql = String.join(SEMICOLON, sqls);
        LOGGER.info("执行sql：{}", allSql);
        long start = System.currentTimeMillis();
        executeConnect(connection -> {
            sqls.forEach(sql -> executeStatement(connection, statement -> {
                try {
                    LOGGER.info("执行sql 开始：{}", sql);
                    statement.execute(sql);
                    LOGGER.info("执行sql 结束：{}", sql);
                } catch (Exception e) {
                    LOGGER.error("执行sql 异常：{}", sql, e);
                    throw new RuntimeException(e);
                }
                return null;
            }));
            return null;
        });
        LOGGER.info("执行sql {} 完成，耗时：{}", allSql, System.currentTimeMillis() - start);
    }

    private static <R> R executeConnect(Function<Connection, R> function) {
        R res = null;
        Connection connection = null;
        try {
            connection = ClickHouseConnection.getConnection();
            res = function.apply(connection);
        } catch (Exception e) {
            LOGGER.error("executeConnect 异常：", e);
            throw new RuntimeException(e);
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (Exception e) {
                    LOGGER.info("close connection failed", e);
                }
            }
        }
        return res;
    }

    private static <R> R executeStatement(Connection connection, Function<Statement, R> function) {
        R res = null;
        Statement statement = null;
        try {
            statement = connection.createStatement();
            res = function.apply(statement);
        } catch (Exception e) {
            LOGGER.error("executeStatement 异常：", e);
            throw new RuntimeException(e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (Exception e) {
                    LOGGER.info("close statement failed", e);
                }
            }
        }
        return res;
    }

}
