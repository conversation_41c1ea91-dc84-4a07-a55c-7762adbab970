//package com.guwave.onedata.dataware.scheduler.service;
//
//import com.alibaba.fastjson.JSON;
//import com.guwave.gdp.common.hive.HiveOperate;
//import com.guwave.onedata.dataware.common.contant.*;
//import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
//import com.guwave.onedata.dataware.scheduler.util.HdfsUtil;
//import org.apache.commons.math3.util.Pair;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * Copyright (C), 2021, guwave
// * <p>
// * HiveService
// *
// * <AUTHOR>
// * @version 0.0.1
// * 2021-12-30 15:29:11
// */
//@Component
//public class HiveService {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(HiveService.class);
//
//    private static final String PARTITION_TEMPLATE = "(CUSTOMER='{CUSTOMER}',TEST_AREA='{TEST_AREA}',FACTORY='{FACTORY}',LOT_ID='{LOT_ID}')";
//    private static final String CUSTOMER = "{CUSTOMER}";
//    private static final String TEST_AREA = "{TEST_AREA}";
//    private static final String FACTORY = "{FACTORY}";
//    private static final String LOT_ID = "{LOT_ID}";
//
//    private static final String TYPE = "{TYPE}";
//    private static final String TABLE_TYPE = "{TABLE_TYPE}";
//
//    private static Map<String, String> stdfTypeToHiveOdsTableMap;
//    private static Map<String, String> tableTypeToHiveDwdTableMap;
//    private static Map<String, String> tableTypeToHiveDimTableMap;
//    private static Map<String, String> tableTypeToHiveDwsTableMap;
//
//    @Value("${spring.hive.address}")
//    private String hiveAddress;
//
//    @Value("${spring.hive.username}")
//    private String hiveUsername;
//
//    @Value("${spring.hive.password}")
//    private String hivePassword;
//
//    @Value("${spring.hive.ods.tables}")
//    private String hiveOdsTables;
//
//    @Value("${spring.hive.ods.path}")
//    private String hiveOdsPath;
//
//    @Value("${spring.hive.dwd.tables}")
//    private String hiveDwdTables;
//
//    @Value("${spring.hive.dwd.path}")
//    private String hiveDwdPath;
//
//    @Value("${spring.hive.dim.tables}")
//    private String hiveDimTables;
//
//    @Value("${spring.hive.dim.path}")
//    private String hiveDimPath;
//
//    @Value("${spring.hive.dws.tables}")
//    private String hiveDwsTables;
//
//    @Value("${spring.hive.dws.path}")
//    private String hiveDwsPath;
//
//    @Autowired
//    private HdfsUtil hdfsUtil;
//
//    @PostConstruct
//    public void init() {
//        stdfTypeToHiveOdsTableMap = toMap(hiveOdsTables);
//
//        tableTypeToHiveDwdTableMap = toMap(hiveDwdTables);
//
//        tableTypeToHiveDimTableMap = toMap(hiveDimTables);
//
//        tableTypeToHiveDwsTableMap = toMap(hiveDwsTables);
//
//    }
//
//    private Map<String, String> toMap(String hiveOdsTables) {
//        return Arrays.stream(hiveOdsTables.split(Constant.COMMA)).collect(Collectors.toMap(t -> {
//            String[] split = t.split(Constant.COLON);
//            return split[0];
//        }, t -> {
//            String[] split = t.split(Constant.COLON);
//            return split[1];
//        }, (v1, v2) -> v1));
//    }
//
//    public void run(CalculateEndFlag endFlag) {
//        LOGGER.info("HiveService开始处理: {}", JSON.toJSONString(endFlag));
//        try {
//            // 先删除hive分区
//            dropHivePartition(endFlag);
//
//            // 加载数据进hive
//            loadDataToHive(endFlag);
//        } catch (Exception e) {
//            LOGGER.info("执行hive异常：", e);
//        }
//    }
//
//    private void dropHivePartition(CalculateEndFlag endFlag) {
//        LOGGER.info("HiveService 删除分区: {}", JSON.toJSONString(endFlag));
//
//        List<Pair<String, String>> tableAndPartitionList = new ArrayList<>();
//
//        String partition = buildPartition(endFlag);
//        stdfTypeToHiveOdsTableMap.forEach((type, table) -> {
//            tableAndPartitionList.add(new Pair<>(table, partition));
//        });
//        tableTypeToHiveDwdTableMap.forEach((type, table) -> {
//            tableAndPartitionList.add(new Pair<>(table, partition));
//        });
//        tableTypeToHiveDimTableMap.forEach((type, table) -> {
//            tableAndPartitionList.add(new Pair<>(table, partition));
//        });
//        tableTypeToHiveDwsTableMap.forEach((type, table) -> {
//            tableAndPartitionList.add(new Pair<>(table, partition));
//        });
//
//
//        HiveOperate.deletePartitions(hiveAddress, hiveUsername, hivePassword, tableAndPartitionList);
//    }
//
//    private void loadDataToHive(CalculateEndFlag endFlag) {
//        LOGGER.info("HiveService 添加分区: {}", JSON.toJSONString(endFlag));
//
//        List<Pair<String, Pair<String, String>>> tableAndPathAndPartitionList = new ArrayList<>();
//
//        String partition = buildPartition(endFlag);
//        TestArea testArea = endFlag.getTestArea();
//        stdfTypeToHiveOdsTableMap.forEach((type, table) -> {
//            String hdfsPath = buildHdfsPath(hiveOdsPath, type, endFlag);
//            tableAndPathAndPartitionList.add(new Pair<>(table, new Pair<>(hdfsPath, partition)));
//        });
//        tableTypeToHiveDwdTableMap.forEach((type, table) -> {
//            String hdfsPath = buildHdfsPath(hiveDwdPath, type, endFlag);
//            tableAndPathAndPartitionList.add(new Pair<>(table, new Pair<>(hdfsPath, partition)));
//        });
//        tableTypeToHiveDimTableMap.forEach((type, table) -> {
//            String hdfsPath = buildHdfsPath(hiveDimPath, type, endFlag);
//            tableAndPathAndPartitionList.add(new Pair<>(table, new Pair<>(hdfsPath, partition)));
//        });
//        tableTypeToHiveDwsTableMap.forEach((type, table) -> {
//            String hdfsPath = buildHdfsPath(hiveDwsPath, type, endFlag);
//            tableAndPathAndPartitionList.add(new Pair<>(table, new Pair<>(hdfsPath, partition)));
//
//        });
//
//        List<Pair<String, Pair<String, String>>> existsTableAndPathAndPartitionList = tableAndPathAndPartitionList.stream().filter(
//                tableAndPathAndPartition -> hdfsUtil.isPathExists(tableAndPathAndPartition.getValue().getKey())
//        ).collect(Collectors.toList());
//        HiveOperate.addPartitions(hiveAddress, hiveUsername, hivePassword, existsTableAndPathAndPartitionList);
//    }
//
//    private String buildHdfsPath(String path, String type, CalculateEndFlag endFlag) {
//        return path.replace(TYPE, type)
//                .replace(TABLE_TYPE, type)
//                .replace(TEST_AREA, endFlag.getTestArea().getArea())
//                .replace(CUSTOMER, endFlag.getCustomer())
//                .replace(FACTORY, endFlag.getFactory())
//                .replace(LOT_ID, endFlag.getLotId());
//
//    }
//
//    private String buildPartition(CalculateEndFlag endFlag) {
//        return PARTITION_TEMPLATE
//                .replace(CUSTOMER, endFlag.getCustomer())
//                .replace(TEST_AREA, endFlag.getTestArea().getArea())
//                .replace(FACTORY, endFlag.getFactory())
//                .replace(LOT_ID, endFlag.getLotId());
//    }
//
//}
