package com.guwave.onedata.dataware.collectx.configuration;

import com.guwave.onedata.dataware.common.contant.Constant;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.hdfs.DistributedFileSystem;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;

@Data
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "spring.gdp.file")
public class HdfsConfig {

    String hdfsMode;
    String hdfsUrl;
    String hdfsUser;
    String hdfsRootPath;

    @Bean("fileSystem")
    public FileSystem fileSystem() {
        // 加载默认配置文件
        org.apache.hadoop.conf.Configuration conf = generateConfig();
        if (Constant.HDFS_MODE_HA.equals(hdfsMode)) {
            return genarateFileSystem(URI.create(conf.get("fs.defaultFS")), conf);
        } else {
            conf.set("dfs.replication", "1");
            return genarateFileSystem(URI.create(this.hdfsUrl), conf);
        }
    }

    public org.apache.hadoop.conf.Configuration generateConfig() {
        org.apache.hadoop.conf.Configuration conf = new org.apache.hadoop.conf.Configuration();
        conf.set("fs.hdfs.impl", DistributedFileSystem.class.getName());
        if (Constant.HDFS_MODE_HA.equals(hdfsMode)) {
            String[] hdfsUrls = hdfsUrl.split(Constant.COMMA);
            if (hdfsUrls.length != 2) {
                throw new RuntimeException("HA模式下 gdp.file.hdfsUrl 需要配置两个hdfs地址！");
            }
            // 默认文件系统的名称
            conf.set("fs.defaultFS", "hdfs://mycluster");
            // namenode 集群的名字
            conf.set("dfs.nameservices", "mycluster");
            // mycluster 下有两个 NameNode，逻辑地址分别是 nn1，nn2
            conf.set("dfs.ha.namenodes.mycluster", "nn1,nn2");
            // nn1 的 http 通信地址
            conf.set("dfs.namenode.rpc-address.mycluster.nn1", hdfsUrls[0]);
            // nn2 的 http 通信地址
            conf.set("dfs.namenode.rpc-address.mycluster.nn2", hdfsUrls[1]);
            // 配置读取失败自动切换的实现方式
            conf.set("dfs.client.failover.proxy.provider.mycluster", "org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider");
        }
        return conf;
    }

    private FileSystem genarateFileSystem(URI uri, org.apache.hadoop.conf.Configuration conf) {
        try {
            // 获得hdfs文件系统
            return FileSystem.newInstance(uri, conf, this.hdfsUser);
        } catch (Exception e) {
            log.error("创建FileSystem失败", e);
            throw new RuntimeException(e);
        }
    }
}
