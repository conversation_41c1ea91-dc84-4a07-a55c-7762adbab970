package com.guwave.onedata.dataware.source.agent.common.util;

import com.guwave.onedata.dataware.common.contant.ManualFileSystem;
import com.guwave.onedata.dataware.source.agent.common.configuration.SourceAgentCommonConfiguration;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.file.Files;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Component
public class MinIO<PERSON>til implements FileSystemUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(MinIOUtil.class);

    @Autowired
    private MinioClient minioClient;

    private static final int allRetryCount = 5;

    @Value("${spring.minio.bucket}")
    private String bucket;

    @Autowired
    private SourceAgentCommonConfiguration sourceAgentCommonConfiguration;

    @Override
    public void upload(String filePath, String targetPath, int retryCount) {
        targetPath = targetPath.endsWith(SLASH) ? targetPath : targetPath + SLASH;
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                LOGGER.error("文件不存在: {}", filePath);
                throw new RuntimeException(filePath + "文件不存在");
            }
            // 设置文件的格式，默认设为二进制格式
            String contentType = Files.probeContentType(file.toPath());
            if (contentType == null) {
                contentType = "application/octet-stream";
            }
            LOGGER.info("{} 上传到 {}", filePath, targetPath);
            try (FileInputStream fis = new FileInputStream(file)) {
                minioClient.putObject(
                        PutObjectArgs.builder()
                                .bucket(bucket)
                                .object(targetPath + SLASH + file.getName())
                                .stream(fis, file.length(), -1)
                                .contentType(contentType)
                                .build()
                );
            }
        } catch (Exception e) {
            LOGGER.info("{} -> {} 上传至minIO失败", filePath, targetPath, e);
            if (retryCount > allRetryCount) {
                throw new RuntimeException(e);
            } else {
                LOGGER.info("uploadToMinIO 第{}次尝试重新连接......{}", retryCount, e.getMessage());
                try {
                    Thread.sleep(retryCount * 5000L);
                } catch (InterruptedException ex) {
                    //
                }
                upload(filePath, targetPath, ++retryCount);
            }
        }

    }

    @Override
    public void downloadToLocal(boolean delSrc, String targetPath, String filePath) throws Exception {
        try {
            File file = new File(filePath);
            // 确保目标文件夹存在
            FileUtils.forceMkdirParent(file);

            try (InputStream is = getObject(bucket, targetPath);
                 FileOutputStream fos = new FileOutputStream(file)) {
                IOUtils.copy(is, fos);
            }
        } catch (Exception e) {
            LOGGER.error("从MinIO下载文件到本地失败, path: {},localPath: {}", targetPath, filePath, e);
            throw e;
        }
    }


    @Override
    public void deleteFile(String targetPath, Boolean recursive) {
        try {
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucket)
                            .object(targetPath)
                            .build()
            );
        } catch (Exception e) {
            LOGGER.error("从MinIO删除文件失败, path: {}", targetPath, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public ManualFileSystem getManualFileSystem() {
        return ManualFileSystem.MINIO;
    }

    /**
     * 获取文件输入流
     * @param bucket 桶名称
     * @param objectName 对象名称
     * @return 文件输入流
     */
    private InputStream getObject(String bucket, String objectName) throws Exception {
        return minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(bucket)
                        .object(objectName)
                        .build()
        );
    }

    public SourceAgentCommonConfiguration getSourceAgentCommonConfiguration() {
        return sourceAgentCommonConfiguration;
    }

    public String getBucket() {
        return bucket;
    }

}
