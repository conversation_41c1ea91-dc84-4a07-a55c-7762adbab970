package com.guwave.onedata.dataware.collectx.util;

import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.zip.Zip64Mode;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.Path;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;

@Slf4j
@Component
public class HDFSUtil {

    private int reconnectCount = 5;

    @Autowired
    private FileSystem fileSystem;

    public void uploadToHDFSByFileSystem(String filePath, String hdfsPath, int retryCount) {
        try {
            Path dest = new Path(hdfsPath);
            if (!fileSystem.exists(dest)) {
                fileSystem.mkdirs(dest);
            }
            fileSystem.copyFromLocalFile(true, true, new Path(filePath), dest);
        } catch (Exception e) {
            if (retryCount > reconnectCount) {
                log.error("{} 上传至hdfs {} 失败", filePath, hdfsPath, e);
                throw new FileLoadException(FileLoadExceptionInfo.BACKUP_FAIL, ExceptionUtils.getStackTrace(e), null);
            } else {
                log.info("uploadToHDFSByFileSystem 第{}次尝试重新连接......{}", retryCount, e.getMessage());
                try {
                    Thread.sleep(retryCount * 5000L);
                } catch (InterruptedException ex) {
                    //
                }
                uploadToHDFSByFileSystem(filePath, hdfsPath, ++retryCount);
            }
        }
    }

    /**
     * 压缩文件
     *
     * @param filePath    filePath
     * @param zipFilePath zipFilePath
     */
    public void compress(String filePath, String zipFilePath) {
        ZipArchiveOutputStream zaos = null;
        File file = new File(filePath);
        try {
            File zipFile = new File(zipFilePath);
            zaos = new ZipArchiveOutputStream(zipFile);
            zaos.setUseZip64(Zip64Mode.AsNeeded);

            ZipArchiveEntry zipArchiveEntry = new ZipArchiveEntry(file, file.getName());
            zaos.putArchiveEntry(zipArchiveEntry);
            try (InputStream is = new BufferedInputStream(new FileInputStream(file))) {
                byte[] buffer = new byte[1024 * 5];
                int len;
                while ((len = is.read(buffer)) != -1) {
                    zaos.write(buffer, 0, len);
                }
                zaos.closeArchiveEntry();
            } catch (Exception e) {
                log.warn("压缩失败", e);
                log.error(e.toString(), e);
            }
            zaos.finish();
        } catch (Exception e) {
            log.warn("压缩失败", e);
            log.error(e.toString(), e);
        } finally {
            try {
                if (zaos != null) {
                    zaos.close();
                }
            } catch (IOException e) {
                log.warn("压缩失败", e);
                log.error(e.toString(), e);
            }
        }
    }


    /**
     * get hdfs file
     *
     * @param delSrc   是否删除原始文件
     * @param hdfsPath hdfs路径
     * @param filePath 本地路径
     * @throws IOException
     */
    public void downloadToLocal(boolean delSrc, String hdfsPath, String filePath) throws IOException {
        try {
            this.fileSystem.copyToLocalFile(delSrc, new Path(hdfsPath), new Path(filePath));
        } catch (IOException e) {
            log.error("从hdfs下载文件到本地失败, hdfsPath: {},localPath: {}", hdfsPath, filePath, e);
            throw e;
        }
    }


    /**
     * 将 zip 压缩包解压成文件到指定文件夹下
     *
     * @param targetDir   解压后文件存放的目的地
     * @param zipFilePath 待解压的压缩文件
     */
    public void unCompress(String targetDir, String zipFilePath) throws IOException {
        File file = new File(zipFilePath);
        try (InputStream inputStream = new FileInputStream(file);
             ZipArchiveInputStream zipArchiveInputStream = new ZipArchiveInputStream(inputStream)) {
            byte[] buffer = new byte[1024 * 5];
            ArchiveEntry archiveEntry;
            while (null != (archiveEntry = zipArchiveInputStream.getNextEntry())) {
                String archiveEntryFileName = archiveEntry.getName();
                File entryFile = new File(targetDir, archiveEntryFileName);
                if (!archiveEntry.isDirectory()) {
                    if (!entryFile.getParentFile().exists()) {
                        entryFile.getParentFile().mkdirs();
                    }
                    try (OutputStream outputStream = new FileOutputStream(entryFile)) {
                        int length;
                        while ((length = zipArchiveInputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, length);
                        }
                        outputStream.flush();
                    } catch (IOException e) {
                        log.error("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
                        throw e;
                    }
                } else {
                    if (!entryFile.exists()) {
                        entryFile.mkdirs();
                    }
                }
            }
        } catch (IOException e) {
            log.error("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
            throw e;
        }
    }

}
