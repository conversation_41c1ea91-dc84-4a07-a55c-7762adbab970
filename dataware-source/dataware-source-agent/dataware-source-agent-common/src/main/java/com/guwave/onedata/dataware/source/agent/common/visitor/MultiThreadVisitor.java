package com.guwave.onedata.dataware.source.agent.common.visitor;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.biemem.BitMemData;
import com.guwave.onedata.dataware.common.model.biemem.DieBitMemData;
import com.guwave.onedata.dataware.common.model.biemem.TestItemBitMemData;
import com.guwave.onedata.dataware.common.model.dft.DwdDftDetail;
import com.guwave.onedata.dataware.common.model.raw.DieData;
import com.guwave.onedata.dataware.common.model.raw.EcidData;
import com.guwave.onedata.dataware.common.model.raw.TestItemData;
import com.guwave.onedata.dataware.common.model.raw.UidData;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.EcidRule;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotRelationSync;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.UidRule;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.EcidRuleRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LotRelationSyncRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.UidRuleRepository;
import com.guwave.onedata.dataware.parser.stdf.ecidParse.EcidDieInfo;
import com.guwave.onedata.dataware.parser.stdf.ecidParse.EcidRuleHandler;
import com.guwave.onedata.dataware.parser.stdf.ecidParse.EcidRuleSetting;
import com.guwave.onedata.dataware.parser.stdf.model.FileMainData;
import com.guwave.onedata.dataware.parser.stdf.uidParse.UidDieInfo;
import com.guwave.onedata.dataware.parser.stdf.uidParse.UidParseHandler;
import com.guwave.onedata.dataware.parser.stdf.uidParse.UidRuleSetting;
import com.guwave.onedata.dataware.parser.stdf.util.CommonBitMemParseUtil;
import com.guwave.onedata.dataware.parser.stdf.util.CommonStdfParseUtil;
import com.guwave.onedata.dataware.parser.stdf.util.DwdCommonUtil;
import com.guwave.onedata.dataware.parser.stdf.visitor.Visitor;
import com.guwave.onedata.dataware.sdk.model.ecid.CpLotIdInfo;
import com.guwave.onedata.dataware.sdk.model.ecid.ExtraInfo;
import com.guwave.onedata.dataware.source.agent.common.function.FifthConsumer;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import com.guwave.onedata.dataware.source.agent.common.sink.filesystem.FileSystemSink;
import com.guwave.onedata.dataware.source.agent.common.sink.filesystem.impl.HdfsSink;
import com.guwave.onedata.dataware.source.agent.common.sink.filesystem.impl.LocalftSink;
import com.guwave.onedata.dataware.source.agent.common.sink.filesystem.impl.MinIOSink;
import com.guwave.onedata.dataware.source.agent.common.util.FileSystemUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

public class MultiThreadVisitor implements Visitor {
    private static final Logger LOGGER = LoggerFactory.getLogger(MultiThreadVisitor.class);

    public static final Set<TestArea> NOT_CAL_TEST_ITEM_TEST_AREAS = ImmutableSet.<TestArea>builder()
            .addAll(TestArea.getCpMapDataSourceList())
            .addAll(TestArea.getCpInklessMapDataDourceList())
            .build();

    private final int threadIndex;
    private final FileSystemUtil fileSystemUtil;
    private final String odsHdfsTemplatePath;
    private final File sourceFile;
    private final Consumer<FileMainData> fillFileMainDataConsumer;
    private final Integer allThreadCnt;
    private final Integer batchSize;
    private final Map<String, CkSink> ckSinkMap;
    private final EcidRuleRepository ecidRuleRepository;
    private final UidRuleRepository uidRuleRepository;
    private final LotRelationSyncRepository lotRelationSyncRepository;
    private FileMainData fileMainData;
    private FileSystemSink fileSystemSink;
    private Long dieDataCnt = 0L;
    private Long testItemDataCnt = 0L;
    private final CkSink dwdDftDetailSink;
    private final List<DwdDftDetail> dwdDftDetailList = new ArrayList<>();
    private final Map<String, Long> testItemCntMap = new HashMap<>();

    private List<DieBitMemData> preDieBitMemDatas = new ArrayList<>();

    private EcidRuleHandler ecidRuleHandler;
    private UidParseHandler uidParseHandler;

    private final String parameterType;
    private final String testParameter;
    private final String script;

    private final Map<ManualFileSystem, FifthConsumer<Integer, FileSystemUtil, String, File, Integer, FileSystemSink>> FILE_SYSTEM_SINK_MAP = new HashMap<ManualFileSystem, FifthConsumer<Integer, FileSystemUtil, String, File, Integer, FileSystemSink>>() {{
        put(ManualFileSystem.HDFS, HdfsSink::new);
        put(ManualFileSystem.LOCAL, LocalftSink::new);
        put(ManualFileSystem.MINIO, MinIOSink::new);
    }};

    public MultiThreadVisitor(int threadIndex, FileSystemUtil fileSystemUtil, String odsHdfsTemplatePath, File sourceFile, Consumer<FileMainData> fillFileMainDataConsumer, Integer allThreadCnt, Integer batchSize, Map<String, CkSink> ckSinkMap, EcidRuleRepository ecidRuleRepository, UidRuleRepository uidRuleRepository, LotRelationSyncRepository lotRelationSyncRepository, String parameterType, String testParameter, String script) {
        this.threadIndex = threadIndex;
        this.fileSystemUtil = fileSystemUtil;
        this.odsHdfsTemplatePath = odsHdfsTemplatePath.endsWith(SLASH) ? odsHdfsTemplatePath : odsHdfsTemplatePath + SLASH;
        this.sourceFile = sourceFile;
        this.fillFileMainDataConsumer = fillFileMainDataConsumer;
        this.allThreadCnt = allThreadCnt;
        this.batchSize = batchSize;
        this.ckSinkMap = ckSinkMap;
        this.dwdDftDetailSink = ckSinkMap.get("dwdDftDetailSink");
        this.ecidRuleRepository = ecidRuleRepository;
        this.uidRuleRepository = uidRuleRepository;
        this.lotRelationSyncRepository = lotRelationSyncRepository;
        this.parameterType = parameterType;
        this.testParameter = testParameter;
        this.script = script;
    }

    @Override
    public void beforeFile(FileMainData fileMainData) {
        this.fileMainData = fileMainData;
        fillFileMainDataConsumer.accept(fileMainData);

        this.ecidRuleHandler = EcidRuleHandler.getEcidRuleHandler(getEcidRule(), getCpLotIdByFtLotId(), new ExtraInfo(fileMainData.fileName, fileMainData.lotId, fileMainData.sblotId), getCpLotInfoIdByFtLotId());
        this.uidParseHandler = UidParseHandler.getUidParseHandler(getUidRule(), this.ecidRuleHandler);

        String odsHdfsTemplateForSchemaTypePath = odsHdfsTemplatePath.replace(FILE_CATEGORY, fileMainData.getFileCategory().getCategory()).replace(TEST_AREA, fileMainData.getTestArea().getArea()).replace(CUSTOMER, fileMainData.getCustomer()).replace(FACTORY, fileMainData.getFactory()).replace(DEVICE_ID, fileMainData.getDeviceId()).replace(LOT_ID, fileMainData.getLotId()).replace(WAFER_NO, fileMainData.getWaferNo()).replace(TEST_STAGE, fileMainData.getTestStage()).replace(LOT_TYPE, fileMainData.getLotType().getType()).replace(FILE_ID, fileMainData.getFileInfoId() + EMPTY);
        this.fileSystemSink = FILE_SYSTEM_SINK_MAP.get(fileSystemUtil.getManualFileSystem()).apply(threadIndex, fileSystemUtil, odsHdfsTemplateForSchemaTypePath, sourceFile, allThreadCnt);
    }

    @Override
    public void handleTestItemData(TestItemData testItemData) {
        if (fileMainData.getFileCategory() == FileCategory.RAW_DATA) {
            if (NOT_CAL_TEST_ITEM_TEST_AREAS.contains(fileMainData.getTestArea())) {
                return;
            } else {
                if (testItemData.getTestNum() == null) {
                    throw new FileLoadException(FileLoadExceptionInfo.RAW_DATA_KEY_FIELD_NULL_EXCEPTION, FileLoadExceptionInfo.RAW_DATA_KEY_FIELD_NULL_EXCEPTION.getMessage().replace(Constant.KEY_FIELD, "TEST_NUM"), Lists.newArrayList("TEST_NUM"));
                }
            }
        }
        testItemDataCnt++;
        // 记录每个testItem的个数
        CommonStdfParseUtil.calculateTestItemCntMap(testItemData.getTestItem(), 1L, testItemCntMap);
        fileSystemSink.writeData(TEST_ITEM_DATA, testItemData);

        if (ecidRuleHandler != null) {
            ecidRuleHandler.handle(null, testItemData);
        }
        if (uidParseHandler != null) {
            uidParseHandler.handleTestItemData(testItemData);
        }
    }

    @Override
    public void handleDftData(DwdDftDetail dwdDftDetail) {
        if (dwdDftDetailSink.sinkFlag()) {
            dwdDftDetailList.add(dwdDftDetail);
            if (dwdDftDetailList.size() >= batchSize) {
                // 写入ck
                this.dwdDftDetailSink.doHandle(dwdDftDetailList);
                // 清空数据
                dwdDftDetailList.clear();
            }
        }
    }

    @Override
    public boolean usefulTextDat(String textDat) {
        if (ecidRuleHandler == null) {
            return false;
        }
        return ecidRuleHandler.usefulTextDat(textDat);
    }

    @Override
    public void handleDieData(DieData dieData) {
        dieDataCnt++;
        fileSystemSink.writeData(DIE_DATA, dieData);
        if (ecidRuleHandler != null) {
            ecidRuleHandler.handle(dieData, null);
        }
    }

    @Override
    public void afterFile() {
        writeEcid();
        writeUid();
        this.fileSystemSink.afterWrite();
        if (CollectionUtils.isNotEmpty(dwdDftDetailList)) {
            this.dwdDftDetailSink.doHandle(dwdDftDetailList);
        }
    }

    private void writeUid() {
        if (uidParseHandler != null) {
            uidParseHandler.parseUid();
            Map<Integer, UidDieInfo> uidDieInfoMap = uidParseHandler.getUidDieInfoMap();
            AtomicInteger parseUidDieCnt = new AtomicInteger();
            AtomicInteger standardUidDieCnt = new AtomicInteger();
            uidDieInfoMap.forEach((cPartId, t) -> {
                if (t.isParseFlag()) {
                    parseUidDieCnt.getAndIncrement();
                    if (StringUtils.isNotEmpty(t.getUid())) {
                        standardUidDieCnt.getAndIncrement();
                        fileSystemSink.writeData(UID_DATA, new UidData(fileMainData.getFileInfoId(), t.getcPartId(), t.getUid()));
                    }
                }
            });
            LOGGER.info("{} threadIndex:{} dieCnt:{} parseUidDieCnt:{} standardUidDieCnt:{}", fileMainData.getFileName(), threadIndex, uidDieInfoMap.size(), parseUidDieCnt.get(), standardUidDieCnt.get());
        }
    }

    private void writeEcid() {
        if (ecidRuleHandler != null) {
            ecidRuleHandler.parseEcid();
            Map<Integer, EcidDieInfo> ecidDieInfoMap = ecidRuleHandler.getEcidDieInfoMap();
            AtomicInteger parseEcidDieCnt = new AtomicInteger();
            AtomicInteger standardEcidDieCnt = new AtomicInteger();
            ecidDieInfoMap.forEach((cPartId, t) -> {
                if (t.isParseFlag()) {
                    parseEcidDieCnt.getAndIncrement();
                    if (t.getIsStandardEcid() == 1) {
                        standardEcidDieCnt.getAndIncrement();
                    }
                    fileSystemSink.writeData(ECID_DATA, new EcidData(fileMainData.getFileInfoId(), t.getcPartId(), t.getWaferLotId(), t.getWaferNo(), t.getxCoord(), t.getyCoord(), t.getEcid(), t.getIsStandardEcid(), t.getChipId(), t.getEcidExtra(), t.getEfuseExtra()));
                }
            });
            LOGGER.info("{} threadIndex:{} dieCnt:{} parseEcidDieCnt:{} standardEcidDieCnt:{}", fileMainData.getFileName(), threadIndex, ecidDieInfoMap.size(), parseEcidDieCnt.get(), standardEcidDieCnt.get());
        }
    }

    @Override
    public void handleTestItemBitMemData(BitMemData bitMemData) {
        TestItemBitMemData testItemBitMemData = CommonBitMemParseUtil.buildTestItemBitMemData(bitMemData);
        testItemDataCnt++;
        // 记录每个testNum的个数
        CommonStdfParseUtil.calculateTestItemCntMap(DwdCommonUtil.generateTestItem(testItemBitMemData.getTestNum(), testItemBitMemData.getTestTxt()), 1L, testItemCntMap);
        fileSystemSink.writeData(TEST_ITEM_BIT_MEM_DATA, testItemBitMemData);
    }

    @Override
    public void handleDieBitMemData(BitMemData bitMemData) {
        // 和上一条bitMemData解析出来的die去重
        List<DieBitMemData> currentDieBitMemDatas = CommonBitMemParseUtil.buildDieBitMemData(bitMemData);
        List<DieBitMemData> dieBitMemDatas = currentDieBitMemDatas.stream().filter(dieBitMemData -> !preDieBitMemDatas.contains(dieBitMemData)).collect(Collectors.toList());

        for (DieBitMemData dieBitMemData : dieBitMemDatas) {
            dieDataCnt++;
            fileSystemSink.writeData(DIE_BIT_MEM_DATA, dieBitMemData);
        }
        preDieBitMemDatas = currentDieBitMemDatas;
    }

    @Override
    public void close() {
        if (this.fileSystemSink != null && fileSystemSink instanceof HdfsSink) {
            ((HdfsSink) fileSystemSink).cleanTmpFile();
        }
    }


    private EcidRuleSetting getEcidRule() {
        if (ecidRuleRepository == null) {
            return null;
        } else if (StringUtils.isNotBlank(parameterType) && StringUtils.isNotBlank(script)) {
            return new EcidRuleSetting(null, script, testParameter, null, null, null, null, null, null, null, parameterType, null, EcidRuleModeEnum.CUSTOM.getMode());
        }
        List<EcidRule> ecidRules = ecidRuleRepository.findByCustomerAndDeviceIdAndTestStage(fileMainData.getCustomer(), fileMainData.getDeviceId(), fileMainData.getTestStage());
        Map<String, EcidRule> ecidRuleMap = ecidRules.stream().collect(Collectors.toMap(EcidRule::getTestProgram, t -> t, (v1, v2) -> v1));
        EcidRule ecidRule = ecidRuleMap.getOrDefault(fileMainData.getTestProgram(), ecidRuleMap.get(EMPTY));
        if (ecidRule == null) {
            return null;
        }
        return new EcidRuleSetting(ecidRule.getTestProgram(), ecidRule.getScript(), ecidRule.getDecodeEcid(), ecidRule.getDecodeEcidFields(), ecidRule.getDecodeLotId(), ecidRule.getParameterWaferNo(), ecidRule.getParameterXAddr(), ecidRule.getParameterYAddr(), ecidRule.getRuleType(), ecidRule.getCheckType(), ecidRule.getParameterType(), ecidRule.getFieldPrefix(), ecidRule.getRuleMode());
    }

    private String getCpLotIdByFtLotId() {
        if (lotRelationSyncRepository == null) {
            return null;
        }
        List<LotRelationSync> lotRelationSyncs = findLotRelation(fileMainData.getCustomer(), fileMainData.getDeviceId(), fileMainData.getFactory(), fileMainData.getFactorySite(), fileMainData.getLotId());
        if (lotRelationSyncs.isEmpty()) {
            return EMPTY;
        }
        return lotRelationSyncs.get(0).getCpLotId();
    }

    private CpLotIdInfo getCpLotInfoIdByFtLotId() {
        if (lotRelationSyncRepository == null) {
            return null;
        }
        List<LotRelationSync> lotRelationSyncs = findLotRelation(fileMainData.getCustomer(), fileMainData.getDeviceId(), fileMainData.getFactory(), fileMainData.getFactorySite(), fileMainData.getLotId());
        if (CollectionUtils.isEmpty(lotRelationSyncs)) {
            return null;
        }
        LotRelationSync lotRelationSync = lotRelationSyncs.get(0);
        return new CpLotIdInfo(lotRelationSync.getCpLotId(), lotRelationSync.getCpWaferId(), lotRelationSync.getCpWaferIdOrg(), lotRelationSync.getCpWaferNo());
    }

    public List<LotRelationSync> findLotRelation(String customer, String ftDeviceId, String ftFactory, String ftFactorySite, String ftLotId) {
        List<LotRelationSync> lotRelationSyncs = lotRelationSyncRepository.findAllByCustomerAndFtDeviceIdAndFtLotId(customer, ftDeviceId, ftLotId);
        if (CollectionUtils.isEmpty(lotRelationSyncs)) {
            return new ArrayList<>();
        }
        Map<String, List<LotRelationSync>> lotRelationMap = lotRelationSyncs.stream().collect(Collectors.groupingBy(t -> t.getFtFactory() != null ? t.getFtFactory() : EMPTY));
        List<LotRelationSync> filterLotRelations = lotRelationMap.getOrDefault(ftFactory, lotRelationMap.get(EMPTY));
        if (CollectionUtils.isEmpty(filterLotRelations)) {
            return new ArrayList<>();
        }
        return filterLotRelations;
    }

    private UidRuleSetting getUidRule() {
        if (uidRuleRepository == null) {
            return null;
        }
        List<UidRule> uidRules = uidRuleRepository.findByCustomerAndSubCustomerAndTestAreaAndFactoryAndFactorySite(fileMainData.getCustomer(), fileMainData.getSubCustomer(), fileMainData.getTestArea().getArea(), fileMainData.getFactory(), fileMainData.getFactorySite());
        if (uidRules.isEmpty()) {
            return null;
        }
        UidRule uidRule = uidRules.get(0);
        return new UidRuleSetting(uidRule.getParameterUid(), uidRule.getParameterType(), uidRule.getMatchType(), uidRule.getConcatSign(), uidRule.getRuleType());
    }


    public FileMainData getFileMainData() {
        return fileMainData;
    }

    public Long getDieDataCnt() {
        return dieDataCnt;
    }

    public Long getTestItemDataCnt() {
        return testItemDataCnt;
    }

    public Map<String, Long> getTestItemCntMap() {
        return testItemCntMap;
    }
}
