package com.guwave.onedata.dataware.source.agent.manual.handler.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.manual.ManualCalculateFinishVo;
import com.guwave.onedata.dataware.common.model.manual.ManualMergeVo;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingFinishMessage;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingMessage;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualMessage;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualCalculateTaskRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualFileInfoRepository;
import com.guwave.onedata.dataware.source.agent.manual.handler.ManualCalculateTaskHandler;
import com.guwave.onedata.dataware.source.agent.manual.handler.ManualTypehandler;
import com.guwave.onedata.dataware.source.agent.manual.model.ManualLotWafer;
import com.guwave.onedata.dataware.source.agent.manual.util.CkUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;
import static com.guwave.onedata.dataware.common.contant.Constant.ENTER;

@Component
public class ManualMergeHandler implements ManualTypehandler, ManualCalculateTaskHandler {

    @Autowired
    private ManualFileInfoRepository manualFileInfoRepository;
    @Autowired
    private ManualCalculateTaskRepository manualCalculateTaskRepository;
    @Autowired
    private CkUtil ckUtil;
    @Value("${spring.runMode.standaloneThreshold}")
    private Long standaloneThreshold;

    @Override
    public Boolean support(ManualType manualType) {
        return manualType == ManualType.MERGE;
    }

    @Override
    public void generateTask(ManualMessage manualMessage, ManualWarehousingMessage manualWarehousingMessage) {
        ManualMergeVo manualMergeVo = manualWarehousingMessage.getManualMergeVo();

        // 校验参数
        ManualLotWafer manualLotWafer = checkParam(manualMergeVo);

        // 生成 manual_file_info
        Date date = new Date();
        ManualFileInfo manualFileInfo = manualFileInfoRepository.findByFileId(manualMergeVo.getFileId());
        boolean retryFlag = false;
        if (manualFileInfo == null) {
            manualFileInfo = new ManualFileInfo()
                    .setCreateUser(SYSTEM)
                    .setCreateTime(date);
        } else {
            retryFlag = true;
            ckUtil.deleteCkFromFileIds(Lists.newArrayList(manualMergeVo.getFileId()));
        }

        fillManualFileInfoFromManualLotWafer(manualLotWafer, manualFileInfo)
                .setFileId(manualMergeVo.getFileId())
                .setFileName(manualMergeVo.getFileName())
                .setFileOwner(manualMergeVo.getFileOwner())
                .setUploadType(UploadType.MANUAL);
        Map<Long, Map<ManualMergeModifyField, String>> originFileModifyVos = manualMergeVo.getOriginFileModifyVos();
        if (originFileModifyVos != null) {
            manualFileInfo.setOriginFileModifyFields(JSON.toJSONString(originFileModifyVos));
        }

        // 根据原始文件确定ManualFileSystem
        List<ManualFileInfo> originFile = manualFileInfoRepository.findAllByFileIdIn(manualMergeVo.getOriginFileIds());
        List<ManualFileSystem> manualFileSystems = originFile.stream().map(ManualFileInfo::getManualFileSystem).distinct().collect(Collectors.toList());
        // 检查ManualFileSystem的数量
        if (manualFileSystems.size() > 1) {
            throw new RuntimeException("More than one ManualFileSystem found.");
        } else {
            manualFileInfo.setManualFileSystem(manualFileSystems.isEmpty() ? ManualFileSystem.HDFS : manualFileSystems.get(0));
        }

        // 保存 manual_file_info
        manualFileInfoRepository.save(manualFileInfo);

        // 生成计算任务
        ManualCalculateTask manualCalculateTask = generateManualCalculateTaskFromManualFileInfo(manualMessage, manualFileInfo)
                .setManualType(ManualType.MERGE)
                .setDwLayer(DwLayer.DWD)
                .setNeedReadFileIds(manualFileInfo.getOriginFileIds())
                .setOriginFileModifyFields(manualFileInfo.getOriginFileModifyFields())
                .setNeedDeleteFileIds(retryFlag ? manualFileInfo.getFileId() + EMPTY : EMPTY)
                .setRunMode(manualFileInfo.getTestItemDataCount() < standaloneThreshold ? RunMode.STANDALONE : RunMode.DISTRIBUTED);

        // 保存 manual_calculate_task
        manualCalculateTaskRepository.save(manualCalculateTask);
    }

    @Override
    public void fillManualWarehousingFinishMessage(ManualWarehousingFinishMessage manualWarehousingFinishMessage, ManualWarehousingMessage manualWarehousingMessage) {
        ManualMergeVo manualMergeVo = manualWarehousingMessage.getManualMergeVo();
        ManualCalculateFinishVo manualCalculateFinishVo = new ManualCalculateFinishVo()
                .setFileId(manualMergeVo.getFileId())
                .setFileName(manualMergeVo.getFileName())
                .setDwLayer(DwLayer.ODS);
        manualWarehousingFinishMessage.setManualCalculateFinishVo(manualCalculateFinishVo);
    }

    private ManualLotWafer checkParam(ManualMergeVo manualMergeVo) {
        ManualLotWafer manualLotWafer = new ManualLotWafer();
        StringBuilder errorMessageSb = new StringBuilder();
        if (StringUtils.isBlank(manualMergeVo.getFileOwner())) {
            errorMessageSb.append("fileOwner").append(FIELD_IS_EMPTY).append(ENTER);
        }
        if (manualMergeVo.getFileId() == null) {
            errorMessageSb.append("fileId").append(FIELD_IS_EMPTY).append(ENTER);
        }
        if (StringUtils.isBlank(manualMergeVo.getFileName())) {
            errorMessageSb.append("fileName").append(FIELD_IS_EMPTY).append(ENTER);
        }
        if (CollectionUtils.isEmpty(manualMergeVo.getOriginFileIds())) {
            errorMessageSb.append("originFileIds").append(FIELD_IS_EMPTY).append(ENTER);
        } else {
            List<ManualFileInfo> originFileInfos = manualFileInfoRepository.findAllByFileIdIn(manualMergeVo.getOriginFileIds());
            Set<Long> existsFileIdSet = originFileInfos.stream().map(ManualFileInfo::getFileId).collect(Collectors.toSet());
            String notExistsFields = manualMergeVo.getOriginFileIds().stream().filter(t -> !existsFileIdSet.contains(t)).map(t -> t + EMPTY).collect(Collectors.joining(COMMA));
            if (StringUtils.isNotEmpty(notExistsFields)) {
                errorMessageSb.append("originFileIds中").append(notExistsFields).append("对应的文件不存在").append(ENTER);
            } else {
                Set<String> customerSet = originFileInfos.stream().map(ManualFileInfo::getCustomer).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                Set<String> subCustomerSet = originFileInfos.stream().map(ManualFileInfo::getSubCustomer).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                Set<String> factorySet = originFileInfos.stream().map(ManualFileInfo::getFactory).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                Set<String> factorySiteSet = originFileInfos.stream().map(ManualFileInfo::getFactorySite).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                Set<TestArea> testAreaSet = originFileInfos.stream().map(ManualFileInfo::getTestArea).filter(Objects::nonNull).collect(Collectors.toSet());
                Set<String> testSatgeSet = originFileInfos.stream().map(ManualFileInfo::getTestStage).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                Set<LotType> lotTypeSet = originFileInfos.stream().map(ManualFileInfo::getLotType).filter(Objects::nonNull).collect(Collectors.toSet());
                Set<String> deviceIdSet = originFileInfos.stream().map(ManualFileInfo::getDeviceId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                Set<String> lotIdSet = originFileInfos.stream().map(ManualFileInfo::getLotId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                Set<String> waferNoSet = originFileInfos.stream().map(ManualFileInfo::getWaferNo).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                Set<FileCategory> fileCategorySet = originFileInfos.stream().map(ManualFileInfo::getFileCategory).filter(Objects::nonNull).collect(Collectors.toSet());
                Set<ParseRule> parseRuleSet = originFileInfos.stream().map(ManualFileInfo::getParseRule).collect(Collectors.toSet());

                manualLotWafer.setOriginFileInfos(originFileInfos);
                if (customerSet.size() > 1) {
                    errorMessageSb.append("合并批次存在多个customer").append(ENTER);
                } else if (customerSet.size() == 1) {
                    manualLotWafer.setCustomer(customerSet.iterator().next());
                }
                if (subCustomerSet.size() > 1) {
                    errorMessageSb.append("合并批次存在多个subCustomer").append(ENTER);
                } else if (subCustomerSet.size() == 1) {
                    manualLotWafer.setSubCustomer(subCustomerSet.iterator().next());
                }
                if (testAreaSet.size() > 1) {
                    errorMessageSb.append("合并批次存在多个testArea").append(ENTER);
                } else if (testAreaSet.size() == 1) {
                    manualLotWafer.setTestArea(testAreaSet.iterator().next());
                }
                if (factorySet.size() > 1) {
                    errorMessageSb.append("合并批次存在多个factory").append(ENTER);
                } else if (factorySet.size() == 1) {
                    manualLotWafer.setFactory(factorySet.iterator().next());
                }
                if (factorySiteSet.size() > 1) {
                    errorMessageSb.append("合并批次存在多个factorySite").append(ENTER);
                } else if (factorySiteSet.size() == 1) {
                    manualLotWafer.setFactorySite(factorySiteSet.iterator().next());
                }
                if (deviceIdSet.size() > 1) {
                    errorMessageSb.append("合并批次存在多个deviceId").append(ENTER);
                } else if (deviceIdSet.size() == 1) {
                    manualLotWafer.setDeviceId(deviceIdSet.iterator().next());
                }
                if (lotIdSet.size() > 1) {
                    errorMessageSb.append("合并批次存在多个lotId").append(ENTER);
                } else if (lotIdSet.size() == 1) {
                    manualLotWafer.setLotId(lotIdSet.iterator().next());
                }
                if (waferNoSet.size() > 1) {
                    errorMessageSb.append("合并批次存在多个waferNo").append(ENTER);
                } else if (waferNoSet.size() == 1) {
                    manualLotWafer.setWaferNo(waferNoSet.iterator().next());
                }
                if (lotTypeSet.size() > 1) {
                    errorMessageSb.append("合并批次存在多个lotType").append(ENTER);
                } else if (lotTypeSet.size() == 1) {
                    manualLotWafer.setLotType(lotTypeSet.iterator().next());
                }
                if (testSatgeSet.size() > 1) {
                    errorMessageSb.append("合并批次存在多个testSatge").append(ENTER);
                } else if (testSatgeSet.size() == 1) {
                    manualLotWafer.setTestStage(testSatgeSet.iterator().next());
                }
                if (fileCategorySet.size() > 1) {
                    errorMessageSb.append("合并批次存在多个fileCategory").append(ENTER);
                } else if (fileCategorySet.size() == 1) {
                    manualLotWafer.setFileCategory(fileCategorySet.iterator().next());
                }
                if (parseRuleSet.contains(ParseRule.SHMOO_SCRIPT)) {
                    errorMessageSb.append("shmoo 不支持合并").append(ENTER);
                }
                if (testAreaSet.contains(TestArea.WAT)) {
                    errorMessageSb.append("wat 不支持合并").append(ENTER);
                }
            }
        }

        String errorMessage = errorMessageSb.toString();
        if (!errorMessage.isEmpty()) {
            throw new FileLoadException(FileLoadExceptionInfo.PARAM_CKECK_FAIL, errorMessage, null).updateExceptionMessage(errorMessage);
        }

        return manualLotWafer;
    }
}
