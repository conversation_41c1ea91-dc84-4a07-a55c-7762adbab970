#!/usr/bin/env bash
: '
同一目录下
  collectx 不能起多个
  source-agent-cp ft wat 可以起多个
scheduler 全局唯一一个
通过脚本控制可以给操作上一个限制
'

currentPath=`pwd`
APP_NAME=$currentPath/$2

first_arg=${1:-status}

#usage
usage() {
    echo "Usage: sh start.sh [start|stop|restart|status]"
    exit 1
}

function get_current_path() {
  local current_dir=$(pwd)
  local parent_dir=$(dirname $(dirname $(dirname "$current_dir")))
  local dir_name=$(echo $current_dir | sed "s#$parent_dir##g")
  echo $dir_name
}

#is_exist
is_exist(){
  local dir_name=$(get_current_path)
  pid=`ps -ef | grep -v grep | grep $dir_name | awk '{print $2}'`
  process_number=`ps -ef | grep -v grep |grep -c $dir_name`
  #pid
  if [ -z "${pid}" ]; then
   return 1
  else
    return 0
  fi
}

#start
start(){
  local should_start=true
  is_exist
  if [ $? -eq "0" ]; then
    if ! [[ "$currentPath" =~ .*dataware-source-agent-wat-.* ]];then
        should_start=false
    fi
  fi
  if [[ "$should_start" == "true" ]];then
    echo "start success"
    start_command=$(ls bin/* | grep -v bat)
    nohup $start_command &
  fi
  status
}

#stop
stop(){
  is_exist
  if [ $? -eq "0" ]; then
    kill $pid
    while (true)
    do
        is_exist
        if [ $? -eq "0" ]; then
          sleep 1
        else
          break;
        fi
    done
    echo "stop success"
  else
    echo "$(get_current_path) is not running"
  fi
}

status(){
  is_exist
  if [ $? -eq "0" ]; then
    echo "$(get_current_path) is running. Running number: $process_number"
    for child_pid in $pid;do
      start_time=$(ps -o lstart= -p $child_pid)
      formatted_time=$(date -d "$start_time" +"%Y-%m-%d %H:%M:%S")
      echo "  Pid: $child_pid, start time: $formatted_time"
    done
  else
    echo "$(get_current_path) is NOT running."
  fi
}

#restart
restart(){
  stop
  start
  echo "restart success"
}

#exec
case "$first_arg" in
  "start")
    start
    ;;
  "stop")
    stop
    ;;
  "status")
    status
    ;;
  "restart")
    restart
    ;;
  *)
    usage
    ;;
esac
