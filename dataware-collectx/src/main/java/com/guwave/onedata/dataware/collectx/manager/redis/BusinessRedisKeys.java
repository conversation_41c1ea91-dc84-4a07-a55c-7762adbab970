package com.guwave.onedata.dataware.collectx.manager.redis;

public class BusinessRedisKeys {

    // sftp_collect_rule_list
    public static final String SFTP_COLLECT_RULE_LIST = "sftp_collect_rule_list";

    // lock key
    public static final String PUSH_SFTP_COLLECT_RULE = "push_sftp_collect_rule";
    public static final String SFTP_COLLECT_RULE_ID = "sftp_collect_rule_id_";
    public static final String ZIP_SPLIT_REMOTE_ORIGINAL_FILE_PATH = "zip_split_remote_original_file_path_";

    // sftp_backup_rule_list
    public static final String SFTP_BACKUP_RULE_LIST = "sftp_backup_rule_list";

    // lock key
    public static final String PUSH_SFTP_BACKUP_RULE = "push_sftp_backup_rule";
    public static final String SFTP_BACKUP_RULE_ID = "sftp_backup_rule_id_";


}
