package com.guwave.onedata.dataware.collectx.core;

import com.guwave.onedata.dataware.collectx.entity.FTPFileAttr;
import com.guwave.onedata.dataware.collectx.manager.thread.SftpCollectRuleThreadManager;
import com.guwave.onedata.dataware.collectx.manager.thread.SftpConnectConfThreadManager;
import com.guwave.onedata.dataware.collectx.manager.thread.SftpLogThreadLocalUtil;
import com.guwave.onedata.dataware.collectx.util.FileUtils;
import com.guwave.onedata.dataware.common.contant.FTPServerType;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.model.collectx.SftpConnectConf;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.List;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Slf4j
public class LocalSyncCore extends AbstractFTPSyncCore implements IFTPSyncCore {

    public LocalSyncCore() {
        this(FTPServerType.NORMAL);
    }

    public LocalSyncCore(FTPServerType type) {
        setServerType(type);
    }

    /**
     * 下载文件
     *
     * @param remoteFileName --服务器上的文件名
     * @param localPath      --本地文件名
     * @return true 下载成功，false 下载失败
     */
    public String loadFile(String remoteFileName, String localPath) throws Exception {
        // 下载文件
        File remoteFile = new File(remoteFileName);
        String remoteFileShortName = FileUtils.getFileName(remoteFileName);
        String downFileFullName = FileUtils.getFileFullName(localPath, remoteFileShortName);

        File downFile = new File(downFileFullName);
        if (downFile.exists()) {
            org.apache.commons.io.FileUtils.deleteQuietly(downFile);
        }
        org.apache.commons.io.FileUtils.copyFile(remoteFile, downFile);
        return downFileFullName;
    }

    public void listFTPFileInfo(String absoluteDir, List<FTPFileAttr> fileInfoList) {
        log.debug("listFTPFileInfo>>> LOCAL ls dir: " + absoluteDir);
        SftpConnectConf sftpConnectConf = SftpConnectConfThreadManager.getSftpConnectConf(getServerType());
        File absoluteDirFile = new File(absoluteDir);
        File[] files = absoluteDirFile.listFiles();
        if (files != null) {
            for (File item : files) {
                String fileName = item.getName();
                String subDir = "";
                if (item.isDirectory()) {
                    if ((fileName.equals(".") || fileName.equals(".."))) {
                        continue;
                    }
                    if (!absoluteDir.endsWith("/")) {
                        absoluteDir = absoluteDir + "/";
                    }
                    subDir = absoluteDir + fileName + "/";

                    //根据黑名单目录配置优化
                    if (isBlackRemotePath(subDir, sftpConnectConf.getBlackFileRemoteFullNameRegex())) {
                        continue;
                    }

                    listFTPFileInfo(subDir, fileInfoList);

                } else {
                    FTPFileAttr ftpFileAttr = new FTPFileAttr();
                    long size = item.length();
                    long mTime = item.lastModified();
                    String fileSuffix = "";
                    if (fileName.lastIndexOf(".") + 1 < fileName.length()) {
                        fileSuffix = fileName.substring(fileName.lastIndexOf(".") + 1);
                    } else {
                        fileSuffix = "";
                    }

                    ftpFileAttr.setFileName(fileName);
                    ftpFileAttr.setFilePath(absoluteDir);
                    if (absoluteDir.endsWith("/")) {
                        ftpFileAttr.setFullName(absoluteDir + fileName);
                    } else {
                        ftpFileAttr.setFullName(absoluteDir + "/" + fileName);
                    }
                    ftpFileAttr.setSize(size);
                    ftpFileAttr.setMTime(mTime);
                    ftpFileAttr.setDir(Boolean.FALSE);
                    ftpFileAttr.setFileSuffix(fileSuffix);


                    if (mTime < sftpConnectConf.getFileModifiedRealStartDate().getTime() || mTime > sftpConnectConf.getFileModifiedRealEndDate().getTime()) {
                        continue;
                    }

                    if (getServerType() == FTPServerType.NORMAL) {
                        FileCategory fileCategory = matchFileCategoryWithRemoteFileFullName(ftpFileAttr.getFullName(), SftpCollectRuleThreadManager.getSftpCollectRule());
                        if (!NOT_NEED_PULL_FILE_CATEGORY_LIST.contains(fileCategory)) {
                            ftpFileAttr.setFileCategory(fileCategory);
                        } else {
                            SftpLogThreadLocalUtil.appendLogTxt("文件名：" + ftpFileAttr.getFullName() + "匹配的fileCategory: " + fileCategory + " 不进行下载！");
                            continue;
                        }
                    } else {
                        ftpFileAttr.setFileCategory(FileCategory.OTHER);
                    }

                    fileInfoList.add(ftpFileAttr);
                }
            }
        }
    }

    @Override
    public FTPFileAttr ftpFileInfo(String absoluteDir, String sourceFileName) throws Exception {
        log.debug("ftpFileInfo>>> LOCAL ls dir: " + absoluteDir);

        SftpConnectConf sftpConnectConf = SftpConnectConfThreadManager.getSftpConnectConf(getServerType());

        File absoluteDirFile = new File(absoluteDir);
        File[] files = absoluteDirFile.listFiles();
        if (files != null) {
            for (File item : files) {
                String fileName = item.getName();
                String subDir = "";
                if (item.isDirectory()) {
                    if ((fileName.equals(POINT) || fileName.equals(".."))) {
                        continue;
                    }
                    if (!absoluteDir.endsWith(SLASH)) {
                        absoluteDir = absoluteDir + SLASH;
                    }
                    subDir = absoluteDir + fileName + SLASH;

                    //根据黑名单目录配置优化
                    if (isBlackRemotePath(subDir, sftpConnectConf.getBlackFileRemoteFullNameRegex())) {
                        continue;
                    }

                    ftpFileInfo(subDir, fileName);

                } else {
                    if (!fileName.equals(sourceFileName)) {
                        continue;
                    }
                    FTPFileAttr ftpFileAttr = new FTPFileAttr();
                    long size = item.length();
                    long mTime = item.lastModified();
                    String fileSuffix = "";
                    if (fileName.lastIndexOf(POINT) + 1 < fileName.length()) {
                        fileSuffix = fileName.substring(fileName.lastIndexOf(POINT) + 1);
                    } else {
                        fileSuffix = EMPTY;
                    }

                    ftpFileAttr.setFileName(fileName);
                    ftpFileAttr.setFilePath(absoluteDir);
                    if (absoluteDir.endsWith(SLASH)) {
                        ftpFileAttr.setFullName(absoluteDir + fileName);
                    } else {
                        ftpFileAttr.setFullName(absoluteDir + SLASH + fileName);
                    }
                    ftpFileAttr.setSize(size);
                    ftpFileAttr.setMTime(mTime);
                    ftpFileAttr.setDir(Boolean.FALSE);
                    ftpFileAttr.setFileSuffix(fileSuffix);


                    ftpFileAttr.setFileCategory(FileCategory.OTHER);
                    return ftpFileAttr;
                }
            }
        }
        return null;
    }

    @Override
    public boolean moveFile(String remoteSourcePath, String remoteTargetPath) throws Exception {
        org.apache.commons.io.FileUtils.deleteQuietly(new File(remoteTargetPath));
        org.apache.commons.io.FileUtils.moveFile(new File(remoteSourcePath), new File(remoteTargetPath));
        return true;
    }

    @Override
    public void mkdir(String remotePath) throws Exception {
        org.apache.commons.io.FileUtils.forceMkdir(new File(remotePath));
    }

    @Override
    public boolean deleteFile(String sourceFilePath) {
        return org.apache.commons.io.FileUtils.deleteQuietly(new File(sourceFilePath));
    }

    @Override
    public boolean uploadFile(String localFilePath, String targetFilePath) throws Exception {
        mkdir(targetFilePath);
        String localFileShortName = FileUtils.getFileName(localFilePath);
        String uploadFileFullName = FileUtils.getFileFullName(targetFilePath, localFileShortName);
        org.apache.commons.io.FileUtils.deleteQuietly(new File(uploadFileFullName));
        org.apache.commons.io.FileUtils.copyFile(new File(localFilePath), new File(uploadFileFullName));
        return true;
    }

    /**
     * 判断是否连接
     */
    public boolean isConnected() {
        return true;
    }

    /**
     * 关闭连接
     */
    public void closeConnect() {

    }

}