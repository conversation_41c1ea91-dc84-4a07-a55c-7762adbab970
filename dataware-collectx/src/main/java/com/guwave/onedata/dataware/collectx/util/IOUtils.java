package com.guwave.onedata.dataware.collectx.util;

import lombok.extern.slf4j.Slf4j;

import java.io.Closeable;

@Slf4j
public class IOUtils {
	public static void silenceClose(Closeable obj) {
		try {
			if (obj != null) {
				obj.close();
			}
		} catch (Exception e) {
			log.error(e.toString(), e);
		}
	}

	public static void silenceClose(AutoCloseable obj) {
		try {
			if (obj != null) {
				obj.close();
			}
		} catch (Exception e) {
			log.error(e.toString(), e);
		}
	}
}
