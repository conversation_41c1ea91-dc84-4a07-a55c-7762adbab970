package com.guwave.onedata.dataware.quality.util;

import com.guwave.onedata.dataware.dao.ck.connection.ClickHouseConnection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.function.Consumer;
import java.util.function.Function;

public class CkProvider {
    private static final Logger LOGGER = LoggerFactory.getLogger(CkProvider.class);

    public static <R> R executeConnect(Function<Connection, R> function) {
        R res = null;
        Connection connection = null;
        try {
            connection = ClickHouseConnection.getConnection();
            res = function.apply(connection);
        } catch (Exception e) {
            LOGGER.error("executeConnect 异常：", e);
            throw new RuntimeException(e);
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (Exception e) {
                    LOGGER.info("close connection failed", e);
                }
            }
        }
        return res;
    }

    public static <R> R executeStatement(Connection connection, Function<Statement, R> function) {
        R res = null;
        Statement statement = null;
        try {
            statement = connection.createStatement();
            res = function.apply(statement);
        } catch (Exception e) {
            LOGGER.error("executeStatement 异常：", e);
            throw new RuntimeException(e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (Exception e) {
                    LOGGER.info("close statement failed", e);
                }
            }
        }
        return res;
    }

    public static <R> R executeBatchInsert(Connection connection, String sql, Consumer<PreparedStatement> consumer) {
        R res = null;
        PreparedStatement statement = null;
        try {
            statement = connection.prepareStatement(sql);
            consumer.accept(statement);
        } catch (Exception e) {
            LOGGER.error("executeStatement 异常：", e);
            throw new RuntimeException(e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (Exception e) {
                    LOGGER.info("close statement failed", e);
                }
            }
        }
        return res;
    }


}
