package com.guwave.onedata.dataware.source.agent.common.sink.filesystem;

import com.alibaba.fastjson.JSON;
import org.apache.commons.io.FileUtils;
import org.apache.parquet.hadoop.ParquetWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.Map;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

public interface FileSystemSink {
    static final Logger LOGGER = LoggerFactory.getLogger(FileSystemSink.class);

    default void writeData(String type, Object data) {
        getTypeValueCnt().compute(type, (key, oldValue) -> oldValue + 1L);
        ParquetWriter parquetWriter = getParquetWriteMap().get(type);
        try {
            parquetWriter.write(data);
        } catch (Exception e) {
            LOGGER.error("{} 写入parquet失败 {}", type, JSON.toJSONString(data));
            throw new RuntimeException(e);
        }
    }

    default void closeParquet() {
        getParquetWriteMap().forEach((type, parquetWrite) -> {
            try {
                parquetWrite.close();
            } catch (Exception e) {
                LOGGER.info("parquet写入流关闭失败:", e);
            }
        });
    }

    default Long calculateRowGroupSize() {
        long size = getSourceFile().length() / 100L;
        return Math.min(Math.max(1024L * 1024L, size), 4L * 1024L * 1024L);
    }

    default void cleanTmpFile() {
        closeParquet();
        getParquetLocalPaths().forEach((type, path) -> {
            try {
                File parquetFile = new File(path.replace(LOCAL_FILE_PREFIX, EMPTY));
                File crcFile = new File(parquetFile.getParent(), POINT + parquetFile.getName() + CRC_FILE_SUFFIX);
                LOGGER.info("删除临时文件：{}，{}", parquetFile.getAbsolutePath(), crcFile.getAbsolutePath());
                FileUtils.deleteQuietly(parquetFile);
                FileUtils.deleteQuietly(crcFile);
            } catch (Exception e) {
                LOGGER.info("删除临时文件失败：{}", path);
            }
        });
    }
    default String getParquetLocalPath(String type) {
        return LOCAL_FILE_PREFIX + new File(getSourceFile().getParent(), PART_PREFIX + getThreadIndex() + UNDER_LINE + getSourceFile().getName()).getAbsolutePath() + POINT + type.toLowerCase() + PARQUET_FILE_SUFFIX;
    }

    Map<String, Long> getTypeValueCnt();

    Map<String, ParquetWriter> getParquetWriteMap();

    File getSourceFile();

    void afterWrite();

    Map<String, String> getParquetLocalPaths();

    Integer getThreadIndex();

}
