package com.guwave.onedata.dataware.source.common.handler.stdf;

import com.guwave.onedata.dataware.common.model.stdf.StdfData;
import com.guwave.onedata.dataware.source.common.serialization.protostuff.TypeReference;
import com.guwave.onedata.dataware.source.common.util.ProtostuffUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * JavaTypeHandler
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-22 13:42:03
 */
public abstract class JavaTypeHandler<T> implements Handler {

    private static final Logger LOGGER = LoggerFactory.getLogger(JavaTypeHandler.class);

    protected String typeClass;
    protected TypeReference<T> typeReference;

    @Override
    public boolean support(StdfData stdfData) {
        return typeClass.equals(stdfData.getJavaType());
    }

    @Override
    public void handle(StdfData stdfData) {
        try {
            T t = ProtostuffUtil.deserializer(stdfData.getBody().array(), typeReference);
            // do logic
            LOGGER.info("正在处理{}消息", stdfData.getType());
            this.doHandle(t, System.currentTimeMillis());
            LOGGER.info("处理成功");
        } catch (Exception e) {
            LOGGER.info("处理失败", e);
        }
    }

    public abstract void doHandle(T items, Long ts);
}