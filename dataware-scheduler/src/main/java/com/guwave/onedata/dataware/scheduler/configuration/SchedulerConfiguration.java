package com.guwave.onedata.dataware.scheduler.configuration;

import com.guwave.gdp.common.spark.AppExecutor;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * SchedulerConfiguration
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2021-12-29 16:15:51
 */
@Configuration
@ComponentScan(basePackages = {"com.guwave.onedata.dataware.dao.mysql.aspect", "com.guwave.onedata.dataware.dao.mysql.manager", "com.guwave.onedata.dataware.repair.common", "com.guwave.onedata.dataware.dao.ck.connection", "com.guwave.onedata.dataware.dao.ck.aspect", "com.guwave.onedata.dataware.dao.ck.domain.dw"})
@EnableTransactionManagement
@EntityScan(basePackages = {"com.guwave.onedata.dataware.dao.mysql.domain"})
@EnableJpaRepositories(basePackages = {"com.guwave.onedata.dataware.dao.mysql.repository"})
public class SchedulerConfiguration {
    private static final Logger LOGGER = LoggerFactory.getLogger(SchedulerConfiguration.class);

    /*@Value("${spring.handler.file.hdfsUrl}")
    private String hdfsUrl;
    @Value("${spring.handler.file.hdfsUser}")
    private String hdfsUser;*/

    @Value("${spring.redis.host}")
    private String redisHost;
    @Value("${spring.redis.port}")
    private String redisPort;
    @Value("${spring.redis.password}")
    private String redisPassword;
    @Value("${spring.redis.timeout}")
    private int redisTimeout;
    @Value("${spring.redis.database}")
    private int redisDatabase;

    @Bean("redisTemplateForCalculateEndFlag")
    public RedisTemplate<String, CalculateEndFlag> redisTemplateForCalculateEndFlag(RedisConnectionFactory redisConnectionFactory) {
        Jackson2JsonRedisSerializer<CalculateEndFlag> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(CalculateEndFlag.class);
        RedisTemplate<String, CalculateEndFlag> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        template.setKeySerializer(RedisSerializer.string());
        template.setValueSerializer(jackson2JsonRedisSerializer);

        template.setHashKeySerializer(RedisSerializer.string());
        template.setHashValueSerializer(jackson2JsonRedisSerializer);
        return template;
    }

    @Bean("redisTemplateForInteger")
    public RedisTemplate<String, Integer> redisTemplateForInteger(RedisConnectionFactory redisConnectionFactory) {
        Jackson2JsonRedisSerializer<Integer> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Integer.class);
        RedisTemplate<String, Integer> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        template.setKeySerializer(RedisSerializer.string());
        template.setValueSerializer(jackson2JsonRedisSerializer);

        template.setHashKeySerializer(RedisSerializer.string());
        template.setHashValueSerializer(jackson2JsonRedisSerializer);

        return template;
    }

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.useSingleServer().setAddress("redis://" + redisHost + ":" + redisPort);
        config.useSingleServer().setPassword(redisPassword);
        config.useSingleServer().setTimeout(redisTimeout);
        config.useSingleServer().setDatabase(redisDatabase);
        config.useSingleServer().setConnectionMinimumIdleSize(1);
        return Redisson.create(config);
    }

//    @Bean("fileSystem")
//    public FileSystem fileSystem() {
//        // 加载默认配置文件
//        org.apache.hadoop.conf.Configuration conf = new org.apache.hadoop.conf.Configuration();
//        conf.set("fs.hdfs.impl", DistributedFileSystem.class.getName());
//        try {
//            // 获得hdfs文件系统
//            return FileSystem.newInstance(URI.create(this.hdfsUrl), conf, this.hdfsUser);
//        } catch (Exception e) {
//            LOGGER.error("创建FileSystem失败", e);
//            return null;
//        }
//    }

}
