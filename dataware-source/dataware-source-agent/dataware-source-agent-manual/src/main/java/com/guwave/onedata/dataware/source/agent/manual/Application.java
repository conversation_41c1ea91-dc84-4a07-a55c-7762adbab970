package com.guwave.onedata.dataware.source.agent.manual;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.logging.LoggingApplicationListener;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Set;

/**
 * Copyright (C), 2022, guwave
 * <p>
 * 项目启动器
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2023-08-08 16:34:54
 */
@EnableScheduling
@SpringBootApplication(scanBasePackages = {"com.guwave.onedata.dataware.source.agent.manual", "com.guwave.onedata.dataware.source.agent.common", "com.guwave.onedata.dataware.dao.ck.connection"}, exclude = {ErrorMvcAutoConfiguration.class})
@PropertySource(value = {"classpath:properties/dataware-source-agent-manual.properties", "file:properties/dataware-source-agent-manual.properties"}, ignoreResourceNotFound = true)
public class Application {

    private static final Logger LOGGER = LoggerFactory.getLogger(Application.class);

    public static void main(String[] args) {
        SpringApplicationBuilder builder = new SpringApplicationBuilder(Application.class);
        Set<ApplicationListener<?>> listeners = builder.application().getListeners();
        listeners.removeIf(listener -> listener instanceof LoggingApplicationListener);
        builder.application().setListeners(listeners);
        builder.run(args);
        LOGGER.info("dataware source agent manual start successfully");
    }
}
