# 需要部署的步骤
kill_app=1
exec_full=1
start_app=1

hdfs_collectx_root_path=/user/glory/guwave/ftp/%s/%s/%s/%s/%s/

# topic
calculate_end_flag_topic=t_dw_calculate_end_flag
load_end_flag_topic=t_dw_load_end_flag
batch_wafermap_config_topic=t_dw_batch_wafermap_config
calculate_patch_file_topic=t_dw_calculate_patch_file
cp_topic=t_dw_cp
cp_map_topic=t_dw_cp_map
cp_inkless_map_topic=t_dw_cp_inkless_map
ft_topic=t_dw_ft
kgu_topic=t_dw_kgu
eqc_topic=t_dw_eqc
lat_topic=t_dw_lat
slt_topic=t_dw_slt
cal_topic=t_dw_cal
wat_topic=t_dw_wat
mes_parse_topic=t_dw_mes_parse
repair_topic=t_dw_repair
repair_finish_topic=t_dw_repair_finish
repair_recalculate_topic=t_dw_repair_recalculate
manual_topic=t_dw_manual_warehousing
manual_finish_topic=t_dw_manual_warehousing_finish
compute_result_topic=t_compute_result
clean_up_topic=t_dw_cleanup
wip_end_lot_topic=t_dw_wip_end_lot
delete_app_instance_topic=t_delete_app_instance
# yms的calculate_end_flag topic
yms_calculate_end_flag_topic=t_yms_calculate_end_flag
# redis
redis_handler_lockExpireTime=86400

# dubbo
environment_group=prod
rpc_timeout=60000

# wipApi开启标志，除了银河的客户，其余都为false
wipApi_open=false

# 手动上传使用LOCAL时，ODS层数据文件存放路径
ods_local_prefix=/home/<USER>/deploy/onedata/dataware/ods/data/

# 固定配置
dws_mes_data_loss_record_table=dw_mes_data_loss_record
wafer_overall_yield_read_wafer_index_table=dws_wafer_index_cluster
wafer_overall_yield_device_overall_yield_setting_table=dw_device_overall_yield_setting_sync
wafer_overall_yield_device_relation_sync_table=dc_device_relation_sync
wafer_overall_yield_device_overall_yield_pass_bins_sync_table=dw_device_overall_yield_pass_bins_sync
batch_product_wafermap_config=dw_batch_product_wafermap_config
batch_device_wafermap_config=dw_batch_device_wafermap_config
batch_device_wafermap_config_mapping=dw_batch_device_wafermap_config_mapping
