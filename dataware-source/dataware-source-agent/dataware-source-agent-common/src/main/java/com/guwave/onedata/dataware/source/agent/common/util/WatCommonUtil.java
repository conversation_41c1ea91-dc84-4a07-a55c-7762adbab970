package com.guwave.onedata.dataware.source.agent.common.util;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.UploadType;
import com.guwave.onedata.dataware.common.model.wat.dim.*;
import com.guwave.onedata.dataware.common.model.wat.dwd.DwdWatDieDetail;
import com.guwave.onedata.dataware.common.model.wat.dwd.DwdWatTestItemDetail;
import com.guwave.onedata.dataware.common.model.wat.ods.OdsWat;
import com.guwave.onedata.dataware.common.util.WaferUtil;
import com.guwave.onedata.dataware.parser.stdf.util.DwdCommonUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

public class WatCommonUtil {

    /**
     * 转换
     *
     * @param items items
     * @return result
     */
    public static List<DwdWatTestItemDetail> toDwdWatTestItemDetail(List<OdsWat> items) {

        return items
                .stream()
                .map(item -> {
                    Long testNum = item.getTestNum();
                    String testTxt = item.getTestTxt();
                    String testItem = Constant.EMPTY;
                    if (testNum != null && StringUtils.isNotBlank(testTxt)) {
                        testItem = testNum + ":" + testTxt;
                    } else if (testNum != null) {
                        testItem = String.valueOf(testNum);
                    } else {
                        testItem = testTxt;
                    }

                    return buildDwdWatTestItemDetail(item, testNum, testTxt, testItem);
                }).collect(Collectors.toList());
    }

    public static DwdWatTestItemDetail buildDwdWatTestItemDetail(OdsWat item, Long testNum, String testTxt, String testItem) {
        DwdWatTestItemDetail vo = new DwdWatTestItemDetail();
        vo
                .setUploadTime(item.getUploadTime())
                .setDataVersion(item.getDataVersion())
                .setConditionSet(item.getConditionSet())
                .setId(EMPTY)
                .setWfFlat(item.getWfFlat())
                .setTestProgram(item.getTestProgram())
                .setTestNum(testNum)
                .setTestTxt(testTxt)
                .setTestItem(testItem)
                .setTestitemType(item.getTestitemType())
                .setHiSpec(item.getHiSpec())
                .setLoSpec(item.getLoSpec())
                .setHiLimit(item.getHiLimit())
                .setLoLimit(item.getLoLimit())
                .setOriginHiLimit(item.getHiLimit())
                .setOriginLoLimit(item.getLoLimit())
                .setTarget(item.getTarget())
                .setUnits(item.getUnits())
                .setOriginUnits(item.getUnits())
                .setTestValue(item.getTestValue())
                .setOriginTestValue(null)
                .setTestResult(item.getTestResult())
//                .setxCoord(item.getxCoord())
//                .setyCoord(item.getyCoord())
                .setReticleX(item.getReticleX())
                .setReticleY(item.getReticleY())
                .setSiteId(item.getSiteId())
                .setWaferLotId(WaferUtil.formatLotId(item.getLotId()))
                .setProcess(item.getProcess())
                .setTestCod(item.getTestCod())
                .setTestTemperature(item.getTestTemperature())
                .setTesterName(item.getTesterName())
                .setOperatorName(item.getOperatorName())
//                .setSpecNam(EMPTY)
                .setCustomer(item.getCustomer())
                .setSubCustomer(item.getSubCustomer())
                .setUploadType(item.getUploadType())
                .setFileId(item.getFileId())
                .setFileName(item.getFileName())
                .setFileType(item.getFileType())
                .setLotType(item.getLotType())
                .setTestArea(item.getTestArea())
                .setFactory(item.getFactory())
                .setFactorySite(item.getFactorySite())
                .setFab(item.getFab())
                .setFabSite(item.getFabSite())
                .setDeviceId(item.getDeviceId())
                .setLotId(item.getLotId())
                .setWaferId(item.getWaferId())
                .setFabwfId(item.getFabwfId())
                .setStartTime(item.getStartTime())
                .setEndTime(item.getEndTime())
                .setWaferNo(item.getWaferNo())
                .setTestStage(item.getTestStage())
                .setCreateUser(item.getCreateUser())
        ;

        return vo;
    }

    /**
     * 转换
     *
     * @param items items
     * @return result
     */
    public static List<DwdWatDieDetail> toDwdWatDieDetail(List<DwdWatTestItemDetail> items) {
        List<DwdWatDieDetail> collect = items
                .stream()
                .map(item -> {
                    DwdWatDieDetail vo = new DwdWatDieDetail();
                    vo
                            .setUploadTime(item.getUploadTime())
                            .setDataVersion(item.getDataVersion())
                            .setSiteId(item.getSiteId())
                            .setWaferMargin(item.getWaferMargin())
                            .setReticleRow(item.getReticleRow())
                            .setReticleColumn(item.getReticleColumn())
                            .setReticleRowCenterOffset(item.getReticleRowCenterOffset())
                            .setReticleColumnCenterOffset(item.getReticleColumnCenterOffset())
                            .setCenterX(item.getCenterX())
                            .setCenterY(item.getCenterY())
                            .setCenterOffsetX(item.getCenterOffsetX())
                            .setCenterOffsetY(item.getCenterOffsetY())
                            .setCenterReticleX(item.getCenterReticleX())
                            .setCenterReticleY(item.getCenterReticleY())
                            .setCenterReticleOffsetX(item.getCenterReticleOffsetX())
                            .setCenterReticleOffsetY(item.getCenterReticleOffsetY())
                            .setWfUnits(item.getWfUnits())
                            .setDieHeight(item.getDieHeight())
                            .setDieWidth(item.getDieWidth())
                            .setWaferSize(item.getWaferSize())
                            .setWfFlat(item.getWfFlat())
                            .setDieCnt(item.getDieCnt())
                            .setPosX(item.getPosX())
                            .setPosY(item.getPosY())
                            .setOriginalWaferSize(item.getOriginalWaferSize())
                            .setOriginalWaferMargin(item.getOriginalWaferMargin())
                            .setOriginalWfUnits(item.getOriginalWfUnits())
                            .setOriginalWfFlat(item.getOriginalWfFlat())
                            .setOriginalPosX(item.getOriginalPosX())
                            .setOriginalPosY(item.getOriginalPosY())
                            .setOriginalDieWidth(item.getOriginalDieWidth())
                            .setOriginalDieHeight(item.getOriginalDieHeight())
                            .setOriginalReticleRow(item.getOriginalReticleRow())
                            .setOriginalReticleColumn(item.getOriginalReticleColumn())
                            .setOriginalReticleRowCenterOffset(item.getOriginalReticleRowCenterOffset())
                            .setOriginalReticleColumnCenterOffset(item.getOriginalReticleColumnCenterOffset())
                            .setOriginalCenterX(item.getOriginalCenterX())
                            .setOriginalCenterY(item.getOriginalCenterY())
                            .setOriginalCenterReticleX(item.getOriginalCenterReticleX())
                            .setOriginalCenterReticleY(item.getOriginalCenterReticleY())
                            .setOriginalCenterOffsetX(item.getOriginalCenterOffsetX())
                            .setOriginalCenterOffsetY(item.getOriginalCenterOffsetY())
                            .setOriginalCenterReticleOffsetX(item.getOriginalCenterReticleOffsetX())
                            .setOriginalCenterReticleOffsetY(item.getOriginalCenterReticleOffsetY())
                            .setxCoord(item.getxCoord())
                            .setyCoord(item.getyCoord())
                            .setDieX(item.getDieX())
                            .setDieY(item.getDieY())
                            .setReticleX(item.getReticleX())
                            .setReticleY(item.getReticleY())
                            .setReticleTX(item.getReticleTX())
                            .setReticleTY(item.getReticleTY())
                            .setWaferLotId(item.getWaferLotId())
                            .setProcess(item.getProcess())
                            .setTestCod(item.getTestCod())
                            .setTestTemperature(item.getTestTemperature())
                            .setTesterName(item.getTesterName())
                            .setOperatorName(item.getOperatorName())
                            .setTestProgram(item.getTestProgram())
//                            .setSpecNam(item.getSpecNam())
                            .setCustomer(item.getCustomer())
                            .setSubCustomer(item.getSubCustomer())
                            .setUploadType(item.getUploadType())
                            .setFileId(item.getFileId())
                            .setFileName(item.getFileName())
                            .setFileType(item.getFileType())
                            .setLotType(item.getLotType())
                            .setTestArea(item.getTestArea())
                            .setTestStage(item.getTestStage())
                            .setFactory(item.getFactory())
                            .setFactorySite(item.getFactorySite())
                            .setFab(item.getFab())
                            .setFabSite(item.getFabSite())
                            .setDeviceId(item.getDeviceId())
                            .setLotId(item.getLotId())
                            .setWaferId(item.getWaferId())
                            .setFabwfId(item.getFabwfId())
                            .setStartTime(item.getStartTime())
                            .setEndTime(item.getEndTime())
                            .setWaferNo(item.getWaferNo())
                            .setCreateUser(item.getCreateUser())
                    ;
                    return vo;
                }).distinct().collect(Collectors.toList());
        collect.forEach(t -> t.setId(EMPTY));
        return collect;
    }

    /**
     * 转换
     *
     * @param items items
     * @return result
     */
    public static List<DimWatTestItem> toDimWatTestItem(List<DwdWatTestItemDetail> items) {
        return items
                .stream()
                .map(item -> {
                    DimWatTestItem vo = new DimWatTestItem();
                    vo
                            .setUploadTime(item.getUploadTime())
                            .setConditionSet(new HashMap<>())
                            .setProcess(item.getProcess())
                            .setTestNum(item.getTestNum())
                            .setTestTxt(item.getTestTxt())
                            .setTestItem(item.getTestItem())
                            .setTestProgram(item.getTestProgram())
                            .setHiLimit(item.getHiLimit())
                            .setLoLimit(item.getLoLimit())
                            .setOriginHiLimit(item.getOriginHiLimit())
                            .setOriginLoLimit(item.getOriginLoLimit())
                            .setTarget(item.getTarget())
                            .setUnits(item.getUnits())
                            .setOriginUnits(item.getOriginUnits())
                            .setWaferLotId(item.getWaferLotId())
                            .setTestCod(item.getTestCod())
                            .setTestTemperature(item.getTestTemperature())
                            .setTesterName(item.getTesterName())
                            .setOperatorName(item.getOperatorName())
                            .setCustomer(item.getCustomer())
                            .setSubCustomer(item.getSubCustomer())
                            .setUploadType(item.getUploadType())
                            .setFileId(item.getFileId())
                            .setFileName(item.getFileName())
                            .setFileType(item.getFileType())
                            .setLotType(item.getLotType())
                            .setTestArea(item.getTestArea())
                            .setTestStage(item.getTestStage())
                            .setFactory(item.getFactory())
                            .setFactorySite(item.getFactorySite())
                            .setFab(item.getFab())
                            .setFabSite(item.getFabSite())
                            .setDeviceId(item.getDeviceId())
                            .setLotId(item.getLotId())
                            .setWaferId(item.getWaferId())
                            .setFabwfId(item.getFabwfId())
                            .setStartTime(item.getStartTime())
                            .setEndTime(item.getEndTime())
                            .setWaferNo(item.getWaferNo())
                            .setCreateUser(item.getCreateUser())
                    ;
                    return vo;
                }).distinct().collect(Collectors.toList());
    }

    /**
     * 转换
     *
     * @param items items
     * @return result
     */
    public static List<DimWatWafer> toDimWatWafer(List<DwdWatTestItemDetail> items) {
        return items
                .stream()
                .map(item -> {
                    DimWatWafer vo = new DimWatWafer();
                    vo
                            .setUploadTime(item.getUploadTime())
                            .setTestProgram(item.getTestProgram())
                            .setReticleRow(item.getReticleRow())
                            .setReticleColumn(item.getReticleColumn())
                            .setReticleRowCenterOffset(item.getReticleRowCenterOffset())
                            .setReticleColumnCenterOffset(item.getReticleColumnCenterOffset())
                            .setWfFlat(item.getWfFlat())
                            .setPosX(item.getPosX())
                            .setPosY(item.getPosY())
                            .setOriginalWaferSize(item.getOriginalWaferSize())
                            .setOriginalWaferMargin(item.getOriginalWaferMargin())
                            .setOriginalWfUnits(item.getOriginalWfUnits())
                            .setOriginalWfFlat(item.getOriginalWfFlat())
                            .setProcess(item.getProcess())
                            .setWaferLotId(item.getWaferLotId())
                            .setTestTemperature(item.getTestTemperature())
                            .setTesterName(item.getTesterName())
                            .setCustomer(item.getCustomer())
                            .setSubCustomer(item.getSubCustomer())
                            .setUploadType(item.getUploadType())
                            .setFileId(item.getFileId())
                            .setFileName(item.getFileName())
                            .setFileType(item.getFileType())
                            .setLotType(item.getLotType())
                            .setTestArea(item.getTestArea())
                            .setTestStage(item.getTestStage())
                            .setFactory(item.getFactory())
                            .setFactorySite(item.getFactorySite())
                            .setFab(item.getFab())
                            .setFabSite(item.getFabSite())
                            .setDeviceId(item.getDeviceId())
                            .setLotId(item.getLotId())
                            .setWaferId(item.getWaferId())
                            .setFabwfId(item.getFabwfId())
                            .setStartTime(item.getStartTime())
                            .setEndTime(item.getEndTime())
                            .setWaferNo(item.getWaferNo())
                            .setCreateUser(item.getCreateUser())
                    ;
                    return vo;
                }).distinct().collect(Collectors.toList());
    }

    /**
     * 转换
     *
     * @param items items
     * @return result
     */
    public static List<DimWatWaferBin> toDimWatWaferBin(List<DwdWatTestItemDetail> items) {
        return items
                .stream()
                .map(item -> {
                    DimWatWaferBin vo = new DimWatWaferBin();
                    vo
                            .setUploadTime(item.getUploadTime())
                            .setProcess(item.getProcess())
                            .setWaferLotId(item.getWaferLotId())
                            .setTestProgram(item.getTestProgram())
                            .setTestCod(item.getTestCod())
                            .setTestTemperature(item.getTestTemperature())
                            .setTesterName(item.getTesterName())
                            .setCustomer(item.getCustomer())
                            .setSubCustomer(item.getSubCustomer())
                            .setUploadType(item.getUploadType())
                            .setFileId(item.getFileId())
                            .setFileName(item.getFileName())
                            .setFactory(item.getFactory())
                            .setFactorySite(item.getFactorySite())
                            .setFab(item.getFab())
                            .setFabSite(item.getFabSite())
                            .setTestArea(item.getTestArea())
                            .setTestStage(item.getTestStage())
                            .setLotType(item.getLotType())
                            .setDeviceId(item.getDeviceId())
                            .setStartTime(item.getStartTime())
                            .setEndTime(item.getEndTime())
                            .setLotId(item.getLotId())
                            .setWaferId(item.getWaferId())
                            .setFabwfId(item.getFabwfId())
                            .setWaferNo(item.getWaferNo())
                            .setCreateUser(item.getCreateUser())
                    ;

                    return vo;
                }).distinct().collect(Collectors.toList());
    }

    /**
     * 转换
     *
     * @param items items
     * @return result
     */
    public static List<DimWatWaferSite> toDimWatWaferSite(List<DwdWatTestItemDetail> items) {
        return items
                .stream()
                .map(item -> {
                    DimWatWaferSite vo = new DimWatWaferSite();
                    vo
                            .setSiteId(item.getSiteId())
                            .setUploadTime(item.getUploadTime())
                            .setTestProgram(item.getTestProgram())
                            .setReticleRow(item.getReticleRow())
                            .setReticleColumn(item.getReticleColumn())
                            .setReticleRowCenterOffset(item.getReticleRowCenterOffset())
                            .setReticleColumnCenterOffset(item.getReticleColumnCenterOffset())
                            .setWfFlat(item.getWfFlat())
                            .setPosX(item.getPosX())
                            .setPosY(item.getPosY())
                            .setOriginalWaferSize(item.getOriginalWaferSize())
                            .setOriginalWaferMargin(item.getOriginalWaferMargin())
                            .setOriginalWfUnits(item.getOriginalWfUnits())
                            .setOriginalWfFlat(item.getOriginalWfFlat())
                            .setProcess(item.getProcess())
                            .setWaferLotId(item.getWaferLotId())
                            .setTestTemperature(item.getTestTemperature())
                            .setTesterName(item.getTesterName())
                            .setCustomer(item.getCustomer())
                            .setSubCustomer(item.getSubCustomer())
                            .setUploadType(item.getUploadType())
                            .setFileId(item.getFileId())
                            .setFileName(item.getFileName())
                            .setFileType(item.getFileType())
                            .setLotType(item.getLotType())
                            .setTestArea(item.getTestArea())
                            .setTestStage(item.getTestStage())
                            .setFactory(item.getFactory())
                            .setFactorySite(item.getFactorySite())
                            .setFab(item.getFab())
                            .setFabSite(item.getFabSite())
                            .setDeviceId(item.getDeviceId())
                            .setLotId(item.getLotId())
                            .setWaferId(item.getWaferId())
                            .setFabwfId(item.getFabwfId())
                            .setStartTime(item.getStartTime())
                            .setEndTime(item.getEndTime())
                            .setWaferNo(item.getWaferNo())
                            .setCreateUser(item.getCreateUser())
                    ;
                    return vo;
                }).distinct().collect(Collectors.toList());
    }

    /**
     * 转换
     *
     * @param items items
     * @return result
     */
    public static List<DimWatTestProgramSite> toDimWatTestProgramSite(List<DwdWatTestItemDetail> items) {
        return items
                .stream()
                .map(item -> {
                    DimWatTestProgramSite vo = new DimWatTestProgramSite();
                    vo
                            .setUploadTime(item.getUploadTime())
                            .setTestProgram(item.getTestProgram())
                            .setTestTemperature(item.getTestTemperature())
                            .setSite(DIM_TEST_PROGRAM_NOT_NULL_DEFAULT)
                            .setSiteKey(DIM_TEST_PROGRAM_NOT_NULL_DEFAULT.toString())
                            .setVersion(DIM_TEST_PROGRAM_VERSION_MAX - item.getStartTime() / 1000)
                            .setCustomer(item.getCustomer())
                            .setSubCustomer(item.getSubCustomer())
                            .setUploadType(item.getUploadType())
                            .setFileId(UploadType.AUTO.getType().equals(item.getUploadType()) ? 0L : item.getFileId())
                            .setFactory(item.getFactory())
                            .setFactorySite(item.getFactorySite())
                            .setFab(item.getFab())
                            .setFabSite(item.getFabSite())
                            .setTestArea(item.getTestArea())
                            .setTestStage(item.getTestStage())
                            .setDeviceId(item.getDeviceId())
                            .setCreateUser(item.getCreateUser())
                    ;
                    return vo;
                }).distinct().collect(Collectors.toList());
    }

    /**
     * 转换
     *
     * @param items items
     * @return result
     */
    public static List<DimWatTestProgramBin> toDimWatTestProgramBin(List<DwdWatTestItemDetail> items) {
        return items
                .stream()
                .map(item -> {
                    DimWatTestProgramBin vo = new DimWatTestProgramBin();
                    vo
                            .setUploadTime(item.getUploadTime())
                            .setTestProgram(item.getTestProgram())
                            .setHbinNum(DIM_TEST_PROGRAM_NOT_NULL_DEFAULT)
                            .setHbinNumKey(DIM_TEST_PROGRAM_NOT_NULL_DEFAULT.toString())
                            .setSbinNum(DIM_TEST_PROGRAM_NOT_NULL_DEFAULT)
                            .setSbinNumKey(DIM_TEST_PROGRAM_NOT_NULL_DEFAULT.toString())
                            .setVersion(DIM_TEST_PROGRAM_VERSION_MAX - item.getStartTime() / 1000)
                            .setTestTemperature(item.getTestTemperature())
                            .setCustomer(item.getCustomer())
                            .setSubCustomer(item.getSubCustomer())
                            .setUploadType(item.getUploadType())
                            .setFileId(UploadType.AUTO.getType().equals(item.getUploadType()) ? 0L : item.getFileId())
                            .setFactory(item.getFactory())
                            .setFactorySite(item.getFactorySite())
                            .setFab(item.getFab())
                            .setFabSite(item.getFabSite())
                            .setTestArea(item.getTestArea())
                            .setTestStage(item.getTestStage())
                            .setDeviceId(item.getDeviceId())
                            .setCreateUser(item.getCreateUser())
                    ;
                    return vo;
                }).distinct().collect(Collectors.toList());
    }

    /**
     * 转换
     *
     * @param items items
     * @return result
     */
    public static List<DimWatTestProgramTestItem> toDimWatTestProgramTestItem(List<DwdWatTestItemDetail> items) {
        return items
                .stream()
                .map(item -> {
                    DimWatTestProgramTestItem vo = new DimWatTestProgramTestItem();
                    vo
                            .setUploadTime(item.getUploadTime())
                            .setConditionSet(new HashMap<>())
                            .setTestProgram(item.getTestProgram())
                            .setTestTemperature(item.getTestTemperature())
                            .setTestNum(item.getTestNum() == null ? DIM_TEST_PROGRAM_NOT_NULL_DEFAULT : item.getTestNum())
                            .setTestNumKey((item.getTestNum() == null ? DIM_TEST_PROGRAM_NOT_NULL_DEFAULT : item.getTestNum()).toString())
                            .setTestTxt(item.getTestTxt())
                            .setTestItem(item.getTestItem())
                            .setTestitemType(item.getTestitemType())
                            .setOriginHiLimit(item.getOriginHiLimit())
                            .setOriginLoLimit(item.getOriginLoLimit())
                            .setTarget(item.getTarget())
                            .setOriginUnits(item.getOriginUnits())
                            .setLoLimit(item.getLoLimit())
                            .setHiLimit(item.getHiLimit())
                            .setUnits(item.getUnits())
                            .setVersion(DIM_TEST_PROGRAM_VERSION_MAX - item.getStartTime() / 1000)
                            .setCustomer(item.getCustomer())
                            .setSubCustomer(item.getSubCustomer())
                            .setUploadType(item.getUploadType())
                            .setFileId(UploadType.AUTO.getType().equals(item.getUploadType()) ? 0L : item.getFileId())
                            .setFactory(item.getFactory())
                            .setFactorySite(item.getFactorySite())
                            .setFab(item.getFab())
                            .setFabSite(item.getFabSite())
                            .setTestArea(item.getTestArea())
                            .setTestStage(item.getTestStage())
                            .setDeviceId(item.getDeviceId())
                            .setCreateUser(item.getCreateUser())
                    ;
                    return vo;
                }).distinct().collect(Collectors.toList());
    }


    /**
     * 构建Ck分区
     *
     * @param customer customer
     * @param testArea testArea
     * @param factory  factory
     */
    public static String getTestProgramPartition(String customer, String testArea, String factory) {
        return DIM_TEST_PROGRAM_PARTITION_TEMPLATE.replace(CUSTOMER, customer).replace(TEST_AREA, testArea).replace(FACTORY, factory);
    }

    public static String getLotWaferPartition(String customer, String testArea, String factory, String subCustomer) {
        // ('VC','AUTO','WAT','TSMC')
        return DIM_LOT_WAFER_PARTITION_TEMPLATE.replace(CUSTOMER, customer).replace(TEST_AREA, testArea).replace(FACTORY, factory).replace(SUB_CUSTOMER, subCustomer);
    }

    public static String getTestItemPartition(String customer, String testArea, String factory, String subCustomer, String deviceId) {
        // ('VC','AUTO','WAT','TSMC')
        return DIM_TEST_ITEM_PARTITION_TEMPLATE.replace(CUSTOMER, customer).replace(TEST_AREA, testArea).replace(FACTORY, factory).replace(SUB_CUSTOMER, subCustomer).replace(DEVICE_ID, deviceId);
    }

}
