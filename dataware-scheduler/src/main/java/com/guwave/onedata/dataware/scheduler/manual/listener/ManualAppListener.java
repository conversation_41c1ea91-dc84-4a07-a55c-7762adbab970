package com.guwave.onedata.dataware.scheduler.manual.listener;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.DwLayer;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.model.manual.ManualCalculateFinishVo;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingFinishMessage;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualMessage;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualCalculateTaskRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualMessageRepository;
import com.guwave.onedata.dataware.scheduler.provider.KafKaProvider;
import com.guwave.onedata.dataware.scheduler.util.ComputeUtil;
import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.Date;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * ManualTaskAppListener
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2022-01-04 17:17:36
 */
@Component
public class ManualAppListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(ManualAppListener.class);

    @Value("${spring.kafka.manualFinishTopic}")
    private String manualFinishTopic;

    @Autowired
    private ManualCalculateTaskRepository manualCalculateTaskRepository;
    @Autowired
    private ManualMessageRepository manualMessageRepository;
    @Autowired
    private KafKaProvider kafKaProvider;


    public void stateChanged(ManualCalculateTask manualCalculateTask, ComputeResultMessage computeResultMessage) {
        ProcessStatus processStatus = ComputeUtil.convertProcessStatus(computeResultMessage.getProcessStatus());
        manualCalculateTask
                .setProcessStatus(processStatus)
                .setUpdateTime(new Date())
                .setNumExecutors(computeResultMessage.getNumExecutors())
                .setExecutorCores(computeResultMessage.getExecutorCores())
                .setExecutorMemory(computeResultMessage.getExecutorMemory())
                .setDriverMemory(computeResultMessage.getDriverMemory())
                .setParallelism(computeResultMessage.getParallelism())
                .setExtraConf(computeResultMessage.getExtraConf())
                .setVersion(computeResultMessage.getVersion());

        ManualWarehousingFinishMessage manualWarehousingFinishMessage = buildManualWarehousingFinishMessage(manualCalculateTask, computeResultMessage);
        LOGGER.info("任务处理{},{}", processStatus, JSON.toJSONString(manualWarehousingFinishMessage));

        LOGGER.info("更新任务状态");
        this.manualCalculateTaskRepository.save(manualCalculateTask);

        LOGGER.info("任务结束发送消息");
        kafKaProvider.sendKafka(manualFinishTopic, String.valueOf(manualWarehousingFinishMessage.getManualCalculateFinishVo().getFileId()), JSON.toJSONString(manualWarehousingFinishMessage));
        ManualMessage message = manualMessageRepository.findById(manualCalculateTask.getMessageId()).get()
                .setManualFinishMessage(JSON.toJSONString(manualWarehousingFinishMessage))
                .setProcessStatus(manualWarehousingFinishMessage.getProcessStatus())
                .setExceptionType(manualWarehousingFinishMessage.getExceptionType())
                .setExceptionMessage(manualWarehousingFinishMessage.getExceptionMessage())
                .setErrorMessage(manualWarehousingFinishMessage.getExceptionMessage())
                .setUpdateTime(new Date());
        manualMessageRepository.save(message);
    }

    private ManualWarehousingFinishMessage buildManualWarehousingFinishMessage(ManualCalculateTask manualCalculateTask, ComputeResultMessage computeResultMessage) {
        ManualCalculateFinishVo manualCalculateFinishVo = new ManualCalculateFinishVo()
                .setFileId(manualCalculateTask.getFileId())
                .setFileName(manualCalculateTask.getFileName())
                .setDwLayer(DwLayer.ADS);
        return new ManualWarehousingFinishMessage()
                .setManualType(manualCalculateTask.getManualType())
                .setManualDeleteVo(null)
                .setManualCalculateFinishVo(manualCalculateFinishVo)
                .setProcessStatus(manualCalculateTask.getProcessStatus())
                .setExceptionType(ComputeUtil.convertExceptionType(computeResultMessage.getExceptionType()))
                .setExceptionMessage(manualCalculateTask.getProcessStatus() == ProcessStatus.FAIL ? computeResultMessage.getExceptionMessage() + Constant.ENTER + computeResultMessage.getErrorMessage() : null)
                ;
    }

}
