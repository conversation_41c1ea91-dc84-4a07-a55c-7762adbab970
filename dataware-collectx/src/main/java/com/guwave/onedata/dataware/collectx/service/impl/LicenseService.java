package com.guwave.onedata.dataware.collectx.service.impl;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.collectx.client.license.LicenseApi;
import com.guwave.onedata.dataware.collectx.client.license.vo.LicenseRequestVo;
import com.guwave.onedata.dataware.collectx.client.license.vo.LicenseResponseVo;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

@Service
@AllArgsConstructor
public class LicenseService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LicenseService.class);
    public static final String TRUE = "true";

    private final LicenseApi licenseApi;

    public void check(String companyCode, Long dataSize, Long wafers, StringBuilder errorMsg) {
        try {
            StopWatch sw = new StopWatch();
            sw.start();
            LicenseRequestVo licenseRequestVo = new LicenseRequestVo();
            licenseRequestVo.setCompanyCode(companyCode);
            licenseRequestVo.setDataSize(dataSize);
            licenseRequestVo.setWafers(wafers);
            LOGGER.info("开始请求LicenseApi, 参数: {}", JSON.toJSONString(licenseRequestVo));
            LicenseResponseVo<String> result = this.licenseApi.check(licenseRequestVo);
            sw.stop();
            LOGGER.info("结束请求LicenseApi, 结果: {}, 耗时: {}ms", JSON.toJSONString(result), sw.getTotalTimeMillis());
            if (!result.success() || !TRUE.equalsIgnoreCase(result.getData())) {
                LOGGER.error("校验License失败: {} ", result.getMsg());
                errorMsg.append("校验License失败: ").append(result.getMsg()).append("\n");
            }
        } catch (Exception e) {
            LOGGER.error("请求License接口失败：", e);
            errorMsg.append("校验License失败!\n");
        }
    }
}
