package com.guwave.onedata.dataware.source.common.util;

import org.apache.avro.util.Utf8;

import java.nio.ByteBuffer;

/**
 * Copyright (C), 2022, guwave
 * <p>
 * AvroUtil
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2022-05-18 17:34:08
 */
public class AvroUtil {

    public static String getString(Object object) {
        String result = null;
        if (null != object) {
            if (object instanceof Utf8) {
                result = object.toString();
            }
        }
        return result;
    }

    public static Long getLong(Object object) {
        Long result = null;
        if (null != object) {
            if (object instanceof Long) {
                result = (Long) object;
            }
        }
        return result;
    }

    public static Integer getInteger(Object object) {
        Integer result = null;
        if (null != object) {
            if (object instanceof Integer) {
                result = (Integer) object;
            }
        }
        return result;
    }

    public static long getBasicLong(Object object) {
        long result = 0L;
        if (null != object) {
            if (object instanceof Long) {
                result = (Long) object;
            }
        }
        return result;
    }

    public static byte[] getBytes(Object object) {
        byte[] result = null;
        if (null != object) {
            if (object instanceof ByteBuffer) {
                result = ((ByteBuffer) object).array();
            }
        }
        return result;
    }
}
