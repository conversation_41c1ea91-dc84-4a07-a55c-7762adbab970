package com.guwave.onedata.dataware.dw.dwd.properties;

import com.guwave.gdp.common.properties.Properties;
import com.guwave.onedata.dataware.common.contant.Constant;

import java.security.SecureRandom;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * DwDwdProperties
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-11-18 10:21:57
 */
public class DwDwdProperties implements Properties {

    private static final long serialVersionUID = 7991402994802749188L;

    private String cpResultDir;
    private String ftResultDir;
    private String cpDieDetailResultDir;
    private String dieDetailResultDir;
    private String cpTestItemDetailResultDir;
    private String testItemDetailResultDir;
    private String cpDieBitmemDetailResultDir;
    private String dieBitmemDetailResultDir;
    private String cpTestItemBitmemDetailResultDir;
    private String testItemBitmemDetailResultDir;
    private String cpDimResultDirTemplate;
    private String ftDimResultDirTemplate;
    private Integer dimResultPartition;
    private Integer dieDetailResultPartition;
    private Integer dieBitmemDetailResultPartition;
    private Integer testItemDetailResultPartition;
    private Integer testItemBitmemDetailResultPartition;
    private String timeDayTable;
    private String timeHourTable;
    private Integer dayOfWeek;
    private String standardUnits;
    private String dwdDbName;
    private String dimDbName;
    private String ckProtocol;
    private String ckAddress;
    private String ckUsername;
    private String ckPassword;
    private String ckNodeHost;
    private String ckNodeUser;
    private String ckNodePassword;
    private String ckBatchSize;
    private String ckCluster;
    private Integer parquetBlockSize;
    private String dimNumPartitions;
    private String ckFetchSize;
    private String needDeleteTables;
    private String address;
    private String driver;
    private String username;
    private String password;
    private String fetchSize;
    private String bootstrapServers;
    private String calculateEndFlagTopic;
    private String loadEndFlagTopic;

    // testValue/hiLimit/lowLimit是否需要乘以scale
    private Boolean needMultiplyScale;

    // insert cluster table
    private Boolean insertClusterTable;

    private Boolean cpTestProgramClearFlag;
    private Boolean ftTestProgramClearFlag;
    private Boolean cpTestNumClearFlag;
    private Boolean ftTestNumClearFlag;

    // 用于减少按LotID分区的数量，值越小分区数越少，最小值为1
    private Integer lotBucketNum;
    private Long checkCkSinkTimeout;
    // DieDetail 根路径
    private String allDieDetailPath;

    // TestItemDetail 根路径
    private String allTestItemDetailPath;
    private String zstdMaxPartitionBytes;

    private Integer totalMultiIfLimit;
    private Integer batchDeleteCheckMaxCnt;

    @Override
    public String file() {
        return "dataware-dw-dwd-3.1.0.properties";
    }

    public String getCpResultDir() {
        return cpResultDir;
    }

    public void setCpResultDir(String cpResultDir) {
        this.cpResultDir = cpResultDir;
    }

    public String getFtResultDir() {
        return ftResultDir;
    }

    public void setFtResultDir(String ftResultDir) {
        this.ftResultDir = ftResultDir;
    }

    public String getCpDieDetailResultDir() {
        return cpDieDetailResultDir;
    }

    public void setCpDieDetailResultDir(String cpDieDetailResultDir) {
        this.cpDieDetailResultDir = cpDieDetailResultDir;
    }

    public String getDieDetailResultDir() {
        return dieDetailResultDir;
    }

    public void setDieDetailResultDir(String dieDetailResultDir) {
        this.dieDetailResultDir = dieDetailResultDir;
    }

    public String getCpTestItemDetailResultDir() {
        return cpTestItemDetailResultDir;
    }

    public void setCpTestItemDetailResultDir(String cpTestItemDetailResultDir) {
        this.cpTestItemDetailResultDir = cpTestItemDetailResultDir;
    }

    public String getTestItemDetailResultDir() {
        return testItemDetailResultDir;
    }

    public void setTestItemDetailResultDir(String testItemDetailResultDir) {
        this.testItemDetailResultDir = testItemDetailResultDir;
    }

    public Integer getDieDetailResultPartition() {
        return dieDetailResultPartition;
    }

    public void setDieDetailResultPartition(Integer dieDetailResultPartition) {
        this.dieDetailResultPartition = dieDetailResultPartition;
    }

    public Integer getTestItemDetailResultPartition() {
        return testItemDetailResultPartition;
    }

    public void setTestItemDetailResultPartition(Integer testItemDetailResultPartition) {
        this.testItemDetailResultPartition = testItemDetailResultPartition;
    }

    public Integer getDieBitmemDetailResultPartition() {
        return dieBitmemDetailResultPartition;
    }

    public void setDieBitmemDetailResultPartition(Integer dieBitmemDetailResultPartition) {
        this.dieBitmemDetailResultPartition = dieBitmemDetailResultPartition;
    }

    public Integer getTestItemBitmemDetailResultPartition() {
        return testItemBitmemDetailResultPartition;
    }

    public void setTestItemBitmemDetailResultPartition(Integer testItemBitmemDetailResultPartition) {
        this.testItemBitmemDetailResultPartition = testItemBitmemDetailResultPartition;
    }

    public String getCpDieBitmemDetailResultDir() {
        return cpDieBitmemDetailResultDir;
    }

    public void setCpDieBitmemDetailResultDir(String cpDieBitmemDetailResultDir) {
        this.cpDieBitmemDetailResultDir = cpDieBitmemDetailResultDir;
    }

    public String getDieBitmemDetailResultDir() {
        return dieBitmemDetailResultDir;
    }

    public void setDieBitmemDetailResultDir(String dieBitmemDetailResultDir) {
        this.dieBitmemDetailResultDir = dieBitmemDetailResultDir;
    }

    public String getCpTestItemBitmemDetailResultDir() {
        return cpTestItemBitmemDetailResultDir;
    }

    public void setCpTestItemBitmemDetailResultDir(String cpTestItemBitmemDetailResultDir) {
        this.cpTestItemBitmemDetailResultDir = cpTestItemBitmemDetailResultDir;
    }

    public String getTestItemBitmemDetailResultDir() {
        return testItemBitmemDetailResultDir;
    }

    public void setTestItemBitmemDetailResultDir(String testItemBitmemDetailResultDir) {
        this.testItemBitmemDetailResultDir = testItemBitmemDetailResultDir;
    }

    public String getTimeDayTable() {
        return timeDayTable;
    }

    public void setTimeDayTable(String timeDayTable) {
        this.timeDayTable = timeDayTable;
    }

    public String getTimeHourTable() {
        return timeHourTable;
    }

    public void setTimeHourTable(String timeHourTable) {
        this.timeHourTable = timeHourTable;
    }

    public Integer getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(Integer dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public String getStandardUnits() {
        return standardUnits;
    }

    public void setStandardUnits(String standardUnits) {
        this.standardUnits = standardUnits;
    }

    public String getDwdDbName() {
        return dwdDbName;
    }

    public void setDwdDbName(String dwdDbName) {
        this.dwdDbName = dwdDbName;
    }

    public String getDimDbName() {
        return dimDbName;
    }

    public void setDimDbName(String dimDbName) {
        this.dimDbName = dimDbName;
    }

    public String getCkProtocol() {
        return ckProtocol;
    }

    public void setCkProtocol(String ckProtocol) {
        this.ckProtocol = ckProtocol;
    }

    public String getCkAddress() {
        String[] detail = this.ckAddress.split(Constant.COMMA);
        int len = detail.length;
        SecureRandom random = new SecureRandom();
        return detail[random.nextInt(len)];
    }

    public String[] getCkAddressList() {
        return this.ckAddress.split(Constant.COMMA);
    }

    public String getCkAddressForPartition(String partition) {
        String[] detail = this.ckAddress.split(Constant.COMMA);
        int dataStoreIndex = Math.abs(partition.hashCode()) % detail.length;
        return detail[dataStoreIndex];
    }

    public void setCkAddress(String ckAddress) {
        this.ckAddress = ckAddress;
    }

    public String getCkUsername() {
        return ckUsername;
    }

    public void setCkUsername(String ckUsername) {
        this.ckUsername = ckUsername;
    }

    public String getCkPassword() {
        return ckPassword;
    }

    public void setCkPassword(String ckPassword) {
        this.ckPassword = ckPassword;
    }

    public String getCkBatchSize() {
        return ckBatchSize;
    }

    public void setCkBatchSize(String ckBatchSize) {
        this.ckBatchSize = ckBatchSize;
    }

    public String getCkCluster() {
        return ckCluster;
    }

    public void setCkCluster(String ckCluster) {
        this.ckCluster = ckCluster;
    }

    public String getDimNumPartitions() {
        return dimNumPartitions;
    }

    public void setDimNumPartitions(String dimNumPartitions) {
        this.dimNumPartitions = dimNumPartitions;
    }

    public String getCkFetchSize() {
        return ckFetchSize;
    }

    public void setCkFetchSize(String ckFetchSize) {
        this.ckFetchSize = ckFetchSize;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getFetchSize() {
        return fetchSize;
    }

    public void setFetchSize(String fetchSize) {
        this.fetchSize = fetchSize;
    }

    public String getBootstrapServers() {
        return bootstrapServers;
    }

    public void setBootstrapServers(String bootstrapServers) {
        this.bootstrapServers = bootstrapServers;
    }

    public String getCalculateEndFlagTopic() {
        return calculateEndFlagTopic;
    }

    public void setCalculateEndFlagTopic(String calculateEndFlagTopic) {
        this.calculateEndFlagTopic = calculateEndFlagTopic;
    }

    public String getLoadEndFlagTopic() {
        return loadEndFlagTopic;
    }

    public void setLoadEndFlagTopic(String loadEndFlagTopic) {
        this.loadEndFlagTopic = loadEndFlagTopic;
    }

    public Boolean getNeedMultiplyScale() {
        return needMultiplyScale;
    }

    public void setNeedMultiplyScale(Boolean needMultiplyScale) {
        this.needMultiplyScale = needMultiplyScale;
    }

    public Boolean getInsertClusterTable() {
        return insertClusterTable;
    }

    public void setInsertClusterTable(Boolean insertClusterTable) {
        this.insertClusterTable = insertClusterTable;
    }

    public Boolean getCpTestProgramClearFlag() {
        return cpTestProgramClearFlag;
    }

    public void setCpTestProgramClearFlag(Boolean cpTestProgramClearFlag) {
        this.cpTestProgramClearFlag = cpTestProgramClearFlag;
    }

    public Boolean getFtTestProgramClearFlag() {
        return ftTestProgramClearFlag;
    }

    public void setFtTestProgramClearFlag(Boolean ftTestProgramClearFlag) {
        this.ftTestProgramClearFlag = ftTestProgramClearFlag;
    }

    public Boolean getCpTestNumClearFlag() {
        return cpTestNumClearFlag;
    }

    public void setCpTestNumClearFlag(Boolean cpTestNumClearFlag) {
        this.cpTestNumClearFlag = cpTestNumClearFlag;
    }

    public Boolean getFtTestNumClearFlag() {
        return ftTestNumClearFlag;
    }

    public void setFtTestNumClearFlag(Boolean ftTestNumClearFlag) {
        this.ftTestNumClearFlag = ftTestNumClearFlag;
    }

    public String getDriver() {
        return driver;
    }

    public void setDriver(String driver) {
        this.driver = driver;
    }

    public Integer getLotBucketNum() {
        return lotBucketNum;
    }

    public void setLotBucketNum(Integer lotBucketNum) {
        this.lotBucketNum = lotBucketNum;
    }

    public String getNeedDeleteTables() {
        return needDeleteTables;
    }

    public void setNeedDeleteTables(String needDeleteTables) {
        this.needDeleteTables = needDeleteTables;
    }

    public String getCkNodeHost() {
        return ckNodeHost;
    }

    public void setCkNodeHost(String ckNodeHost) {
        this.ckNodeHost = ckNodeHost;
    }

    public String getCkNodeUser() {
        return ckNodeUser;
    }

    public void setCkNodeUser(String ckNodeUser) {
        this.ckNodeUser = ckNodeUser;
    }

    public String getCkNodePassword() {
        return ckNodePassword;
    }

    public void setCkNodePassword(String ckNodePassword) {
        this.ckNodePassword = ckNodePassword;
    }

    public Integer getParquetBlockSize() {
        return parquetBlockSize;
    }

    public void setParquetBlockSize(Integer parquetBlockSize) {
        this.parquetBlockSize = parquetBlockSize;
    }

    public Long getCheckCkSinkTimeout() {
        return checkCkSinkTimeout;
    }

    public void setCheckCkSinkTimeout(Long checkCkSinkTimeout) {
        this.checkCkSinkTimeout = checkCkSinkTimeout;
    }

    public String getAllDieDetailPath() {
        return allDieDetailPath;
    }

    public void setAllDieDetailPath(String allDieDetailPath) {
        this.allDieDetailPath = allDieDetailPath;
    }

    public String getAllTestItemDetailPath() {
        return allTestItemDetailPath;
    }

    public void setAllTestItemDetailPath(String allTestItemDetailPath) {
        this.allTestItemDetailPath = allTestItemDetailPath;
    }

    public String getZstdMaxPartitionBytes() {
        return zstdMaxPartitionBytes;
    }

    public void setZstdMaxPartitionBytes(String zstdMaxPartitionBytes) {
        this.zstdMaxPartitionBytes = zstdMaxPartitionBytes;
    }

    public Integer getTotalMultiIfLimit() {
        return totalMultiIfLimit;
    }

    public void setTotalMultiIfLimit(Integer totalMultiIfLimit) {
        this.totalMultiIfLimit = totalMultiIfLimit;
    }

    public String getCpDimResultDirTemplate() {
        return cpDimResultDirTemplate;
    }

    public void setCpDimResultDirTemplate(String cpDimResultDirTemplate) {
        this.cpDimResultDirTemplate = cpDimResultDirTemplate;
    }

    public String getFtDimResultDirTemplate() {
        return ftDimResultDirTemplate;
    }

    public void setFtDimResultDirTemplate(String ftDimResultDirTemplate) {
        this.ftDimResultDirTemplate = ftDimResultDirTemplate;
    }

    public Integer getDimResultPartition() {
        return dimResultPartition;
    }

    public void setDimResultPartition(Integer dimResultPartition) {
        this.dimResultPartition = dimResultPartition;
    }

    public Integer getBatchDeleteCheckMaxCnt() {
        return batchDeleteCheckMaxCnt;
    }

    public void setBatchDeleteCheckMaxCnt(Integer batchDeleteCheckMaxCnt) {
        this.batchDeleteCheckMaxCnt = batchDeleteCheckMaxCnt;
    }
}
