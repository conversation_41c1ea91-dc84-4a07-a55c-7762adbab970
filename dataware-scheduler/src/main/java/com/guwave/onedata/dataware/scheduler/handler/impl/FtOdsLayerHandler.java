package com.guwave.onedata.dataware.scheduler.handler.impl;

import com.guwave.onedata.dataware.common.contant.ExceptionType;
import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import com.guwave.onedata.dataware.dao.mysql.manager.SftpFileDetailManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LayerCalculatePoolRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LotStockingDetailRepository;
import com.guwave.onedata.dataware.scheduler.handler.OdsLayerHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class FtOdsLayerHandler implements OdsLayerHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(FtOdsLayerHandler.class);

    private static final List<TestArea> SUPPORT_TEST_AREA_LIST = TestArea.getFTList();

    @Value("${spring.runMode.standaloneThreshold}")
    private Long standaloneThreshold;

    @Value("${spring.runMode.manualThreshold}")
    private Long manualThreshold;

    @Autowired
    private LayerCalculatePoolRepository layerCalculatePoolRepository;
    @Autowired
    private LotStockingDetailRepository lotStockingDetailRepository;
    @Autowired
    private SftpFileDetailManager sftpFileDetailManager;

    @Override
    public boolean isSupport(CalculateEndFlag endFlag) {
        return OdsLayerHandler.super.isSupport(endFlag)
                && SUPPORT_TEST_AREA_LIST.contains(endFlag.getTestArea());
    }

    @Override
    public SftpFileDetailManager getSftpFileDetailManager() {
        return sftpFileDetailManager;
    }

    @Override
    public FileLoadExceptionInfo getExceptionInfo(ExceptionType exceptionType) {
        return null;
    }

    @Override
    public LayerCalculatePoolRepository getLayerCalculatePoolRepository() {
        return layerCalculatePoolRepository;
    }

    @Override
    public LotStockingDetailRepository getLotStockingDetailRepository() {
        return this.lotStockingDetailRepository;
    }

    @Override
    public Long getStandaloneThreshold() {
        return this.standaloneThreshold;
    }

    @Override
    public Long getManualThreshold() {
        return this.manualThreshold;
    }
}
