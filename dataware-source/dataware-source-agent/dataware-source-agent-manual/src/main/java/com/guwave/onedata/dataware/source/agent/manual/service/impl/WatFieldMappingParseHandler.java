package com.guwave.onedata.dataware.source.agent.manual.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.guwave.onedata.dataware.common.contant.DwLayer;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.ParseRule;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.model.wat.ods.OdsWat;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualMessage;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualCalculateTaskRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualFileInfoRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualMessageRepository;
import com.guwave.onedata.dataware.source.agent.manual.listener.ExcelLineListener;
import com.guwave.onedata.dataware.source.agent.manual.listener.WatDataExcelReader;
import com.guwave.onedata.dataware.source.agent.manual.service.ParseFileHandler;
import com.guwave.onedata.dataware.source.agent.manual.util.WatDataCommonUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;

@Component
public class WatFieldMappingParseHandler implements ParseFileHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(WatFieldMappingParseHandler.class);

    public static final Map<String, BiConsumer<String, OdsWat>> OTHER_FIELD_CONSUMER_MAP = new HashMap<String, BiConsumer<String, OdsWat>>() {{

    }};
    public static final Map<String, BiConsumer<Pair<String, String>, OdsWat>> MAP_FIELD_CONSUMER_MAP = new HashMap<String, BiConsumer<Pair<String, String>, OdsWat>>() {{
        put("conditionSet", (pairStr, odsWat) -> {
            if (odsWat.getConditionSet() == null) {
                odsWat.setConditionSet(new HashMap<>());
            }
            odsWat.getConditionSet().put(pairStr.getKey(), pairStr.getValue());
        });
    }};

    @Autowired
    private ManualFileInfoRepository manualFileInfoRepository;
    @Autowired
    private ManualCalculateTaskRepository manualCalculateTaskRepository;
    @Autowired
    private ManualMessageRepository manualMessageRepository;
    @Autowired
    private WatDataCommonUtil watDataCommonUtil;

    @Override
    public Boolean support(FileCategory fileCategory, ParseRule parseRule) {
        return fileCategory == FileCategory.WAT && parseRule == ParseRule.RAW_DATA_FIELD_MAPPING;
    }

    @Override
    public boolean hasNext() {
        return false;
    }

    @Override
    public void dealFile(ManualCalculateTask manualCalculateTask, File localFile, ManualFileInfo manualFileInfo, File scriptFile) {
        // manual任务消息
        ManualMessage manualMessage = manualMessageRepository.findById(manualCalculateTask.getMessageId()).get()
                .setExceptionType(null)
                .setExceptionMessage(EMPTY)
                .setErrorMessage(EMPTY)
                .setProcessStatus(ProcessStatus.PROCESSING);
        // 发送ODS进度消息
        watDataCommonUtil.sendManualWarehousingFinishMessage(manualCalculateTask, DwLayer.ODS, manualMessage.getExceptionType(), manualMessage.getExceptionMessage());

        // 读取文件
        WatDataExcelReader watDataExcelReader = new WatDataExcelReader(OTHER_FIELD_CONSUMER_MAP, MAP_FIELD_CONSUMER_MAP, manualFileInfo);
        ExcelReader excel = EasyExcel.read(localFile, new ExcelLineListener(manualFileInfo.getFieldMapping(), watDataExcelReader))
                .headRowNumber(1)
                .excelType(ExcelTypeEnum.CSV)
                .build();
        try {
            excel.read(new ReadSheet(0));
        } finally {
            excel.finish();
        }
        watDataExcelReader.dealRemain();

        // 写入ck
        watDataCommonUtil.sinkCk(watDataExcelReader.getOdsWats());

        // 发送DWD进度消息
        watDataCommonUtil.sendManualWarehousingFinishMessage(manualCalculateTask, DwLayer.DWD, manualMessage.getExceptionType(), manualMessage.getExceptionMessage());

        Date now = new Date();
        manualFileInfo
                .setFileSize(localFile.length())
                .setUpdateTime(now);
        manualFileInfoRepository.save(manualFileInfo);

        manualCalculateTask
                .setTestArea(manualFileInfo.getTestArea())
                .setProcessStatus(ProcessStatus.SUCCESS)
                .setUpdateTime(now);
        manualCalculateTaskRepository.save(manualCalculateTask);

        // 发送完成消息
        String manualFinishMessage = watDataCommonUtil.sendManualWarehousingFinishMessage(manualCalculateTask, DwLayer.ADS, manualMessage.getExceptionType(), manualMessage.getExceptionMessage());

        manualMessage
                .setManualFinishMessage(manualFinishMessage)
                .setProcessStatus(ProcessStatus.SUCCESS)
                .setUpdateTime(now);
        manualMessageRepository.save(manualMessage);
    }


}
