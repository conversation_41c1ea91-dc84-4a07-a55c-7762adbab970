package com.guwave.onedata.dataware.collectx.manager.redis;

import com.guwave.onedata.dataware.common.contant.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class SftpFileRedisManager {

    private static Long lockExpireTime;

    private static RedisTemplate<String, String> redisTemplate;

    @Autowired
    private void setRedisTemplate(RedisTemplate redisTemplate) {
        SftpFileRedisManager.redisTemplate = redisTemplate;
    }

    @Autowired
    private void setLockExpireTime(@Value("${spring.handler.lockExpireTime}") Long lockExpireTime) {
        SftpFileRedisManager.lockExpireTime = lockExpireTime;
    }


    public void clearAll() {
        clearLockCollectRuleId();
    }

    public static void clearKey(String key) {
        key = key + Constant.MULTIPLICATION_SIGN;
        Set<String> keys = redisTemplate.keys(key);
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }
        keys.forEach(t -> {
            log.info("删除redis key：{}", t);
            redisTemplate.delete(t);
        });
    }

    public static List<String> getCacheSftpCollectRuleList() {
        return redisTemplate.boundListOps(BusinessRedisKeys.SFTP_COLLECT_RULE_LIST).range(0, -1);
    }

    public static void pushCollectRuleList(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            redisTemplate.boundListOps(BusinessRedisKeys.SFTP_COLLECT_RULE_LIST).rightPushAll(ids.toArray(new String[0]));
        }
    }

    public static String pullOneCollectRule() {
        return redisTemplate.boundListOps(BusinessRedisKeys.SFTP_COLLECT_RULE_LIST).leftPop();
    }

    public static boolean lockPushCollectTask() {
        return Boolean.TRUE.equals(redisTemplate.boundValueOps(BusinessRedisKeys.PUSH_SFTP_COLLECT_RULE).setIfAbsent(Constant.EMPTY, 120, TimeUnit.SECONDS));
    }

    public static void unLockPushCollectTask() {
        redisTemplate.delete(BusinessRedisKeys.PUSH_SFTP_COLLECT_RULE);
    }

    public static boolean lockCollectRuleId(String id) {
        String key = BusinessRedisKeys.SFTP_COLLECT_RULE_ID + id;
        return Boolean.TRUE.equals(redisTemplate.boundValueOps(key).setIfAbsent(id, lockExpireTime, TimeUnit.SECONDS));
    }

    public static void unLockCollectRuleId(String id) {
        String key = BusinessRedisKeys.SFTP_COLLECT_RULE_ID + id;
        redisTemplate.delete(key);
    }

    public static void clearLockCollectRuleId() {
        clearKey(BusinessRedisKeys.SFTP_COLLECT_RULE_ID);
    }

    public static List<String> getCacheSftpBackupRuleList() {
        return redisTemplate.boundListOps(BusinessRedisKeys.SFTP_BACKUP_RULE_LIST).range(0, -1);
    }


    public static void pushBackupRuleList(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            redisTemplate.boundListOps(BusinessRedisKeys.SFTP_BACKUP_RULE_LIST).rightPushAll(ids.toArray(new String[0]));
        }
    }

    public static String pullOneBackupRule() {
        return redisTemplate.boundListOps(BusinessRedisKeys.SFTP_BACKUP_RULE_LIST).leftPop();
    }

    public static boolean lockPushBackupTask() {
        return Boolean.TRUE.equals(redisTemplate.boundValueOps(BusinessRedisKeys.PUSH_SFTP_BACKUP_RULE).setIfAbsent(Constant.EMPTY, 120, TimeUnit.SECONDS));
    }

    public static void unLockPushBackupTask() {
        redisTemplate.delete(BusinessRedisKeys.PUSH_SFTP_BACKUP_RULE);
    }

    public static boolean lockBackupRuleId(String id) {
        String key = BusinessRedisKeys.SFTP_BACKUP_RULE_ID + id;
        return Boolean.TRUE.equals(redisTemplate.boundValueOps(key).setIfAbsent(id, lockExpireTime, TimeUnit.SECONDS));
    }

    public static void unLockBackupRuleId(String id) {
        String key = BusinessRedisKeys.SFTP_BACKUP_RULE_ID + id;
        redisTemplate.delete(key);
    }
}
