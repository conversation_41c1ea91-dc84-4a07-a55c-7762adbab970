package com.guwave.onedata.dataware.quality.util;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.sql.Connection;
import java.sql.Statement;
import java.util.function.Function;

@Component
public class MysqlProvider {

    private static final Logger LOGGER = LoggerFactory.getLogger(MysqlProvider.class);

    private static HikariDataSource dataSource;

    @Value("${spring.datasource.driver-class-name}")
    private String mysqlDriver;

    @Value("${spring.datasource.url}")
    private String mysqlAddress;

    @Value("${spring.datasource.username}")
    private String mysqlUsername;

    @Value("${spring.datasource.password}")
    private String mysqlPassword;

    @Value("${spring.datasource.hikari.minimum-idle}")
    private int minimumIdle;

    @Value("${spring.datasource.hikari.maximum-pool-size}")
    private int maximumPoolSize;

    @Value("${spring.datasource.hikari.idle-timeout}")
    private long idleTimeout;

    @Value("${spring.datasource.hikari.max-lifetime}")
    private long maxLifetime;

    @Value("${spring.datasource.hikari.connection-timeout}")
    private long connectionTimeout;

    @PostConstruct
    public void init() {
        HikariConfig config = new HikariConfig();
        config.setPoolName("HikariPool-Mysql");
        config.setDriverClassName(mysqlDriver);
        config.setJdbcUrl(mysqlAddress);
        config.setUsername(mysqlUsername);
        config.setPassword(mysqlPassword);
        config.setMinimumIdle(minimumIdle);
        config.setMaximumPoolSize(maximumPoolSize);
        config.setIdleTimeout(idleTimeout);
        config.setConnectionTimeout(connectionTimeout);
        config.setMaxLifetime(maxLifetime);
        // 连接测试配置
        config.setConnectionTestQuery("SELECT 1");
        config.setValidationTimeout(30000);
        config.setKeepaliveTime(60000);
        dataSource = new HikariDataSource(config);
    }

    public static <R> R executeConnect(Function<Connection, R> function) {
        R res = null;
        try (Connection connection = dataSource.getConnection()) {
            res = function.apply(connection);
        } catch (Exception e) {
            LOGGER.error("executeConnect 异常：", e);
            throw new RuntimeException(e);
        }
        return res;
    }

    public static <R> R executeStatement(Connection connection, Function<Statement, R> function) {
        R res = null;
        Statement statement = null;
        try {
            statement = connection.createStatement();
            res = function.apply(statement);
        } catch (Exception e) {
            LOGGER.error("executeStatement 异常：", e);
            throw new RuntimeException(e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (Exception e) {
                    LOGGER.info("close statement failed", e);
                }
            }
        }
        return res;
    }
}
