package com.guwave.onedata.dataware.source.agent.common.handler;

import com.guwave.onedata.dataware.common.contant.Constant;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * <PERSON><PERSON>
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-19 16:48:37
 */
public interface Handler {

    default String convertNullStr(String str) {
        if (str == null) {
            return Constant.EMPTY;
        }
        return str;
    }
}
