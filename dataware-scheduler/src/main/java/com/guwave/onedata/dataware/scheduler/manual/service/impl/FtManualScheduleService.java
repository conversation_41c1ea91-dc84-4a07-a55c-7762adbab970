package com.guwave.onedata.dataware.scheduler.manual.service.impl;

import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.common.annotation.SkipCheckCkMutation;
import com.guwave.onedata.dataware.common.contant.DwLayer;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualCalculateTaskRepository;
import com.guwave.onedata.dataware.dao.mysql.manager.ManualFileInfoManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualMessageRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SparkAppConfigRepository;
import com.guwave.onedata.dataware.scheduler.manual.service.ManualScheduleService;
import com.guwave.onedata.next.compute.api.iface.IComputeRpcService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
public class FtManualScheduleService implements ManualScheduleService {
    private static final Logger LOGGER = LoggerFactory.getLogger(FtManualScheduleService.class);

    @Value("${spring.spark.buffer.pool.manualFtDwdMaxRunningSize}")
    private Long manualFtDwdMaxRunningSize;
    @Value("${spring.spark.buffer.pool.manualAllMaxRunningSize}")
    private Long manualAllMaxRunningSize;
    @Value("${spring.kafka.manualFinishTopic}")
    private String manualFinishTopic;

    @Autowired
    private SparkAppConfigRepository sparkAppConfigRepository;
    @Autowired
    private ManualCalculateTaskRepository manualCalculateTaskRepository;
    @Autowired
    private ManualMessageRepository manualMessageRepository;
    @Autowired
    private KafkaTemplate<byte[], byte[]> kafkaTemplate;
    @Autowired
    private ManualFileInfoManager manualFileInfoManager;
    @DubboReference
    private IComputeRpcService computeRpcService;

    @ScheduleSwitch
    @SkipCheckCkMutation
    @Scheduled(fixedDelayString = "${spring.scheduler.polling.milliseconds}")
    public void runTask() {
        executeSparkTask();
    }

    @Override
    public DwLayer getDwLayer() {
        return DwLayer.DWD;
    }

    @Override
    public List<TestArea> getTestArea() {
        return TestArea.getFTList();
    }

    @Override
    public void submitTaskFail(ManualCalculateTask manualCalculateTask) {

    }

    @Override
    public void fillTaskParams(Map<String, String> params, ManualCalculateTask manualCalculateTask, ManualFileInfoManager.RunTaskFilesContext runTaskFilesInfo) {

    }

    @Override
    public ManualFileInfoManager getManualFileInfoManager() {
        return manualFileInfoManager;
    }

    @Override
    public IComputeRpcService getComputeRpcService() {
        return computeRpcService;
    }

    @Override
    public Long getManualMaxRunningSize() {
        return manualFtDwdMaxRunningSize;
    }

    @Override
    public Long getManualAllMaxRunningTaskCount() {
        return manualAllMaxRunningSize;
    }

    @Override
    public SparkAppConfigRepository getSparkAppConfigRepository() {
        return sparkAppConfigRepository;
    }

    @Override
    public ManualCalculateTaskRepository getManualCalculateTaskRepository() {
        return manualCalculateTaskRepository;
    }

    @Override
    public ManualMessageRepository getManualMessageRepository() {
        return manualMessageRepository;
    }

    @Override
    public String getManualFinishTopic() {
        return manualFinishTopic;
    }

    @Override
    public KafkaTemplate<byte[], byte[]> getKafkaTemplate() {
        return kafkaTemplate;
    }

}
