package com.guwave.onedata.dataware.collectx.service.impl;

import com.guwave.onedata.dataware.collectx.configuration.HdfsConfig;
import com.guwave.onedata.dataware.collectx.service.SummaryConvertService;
import com.guwave.onedata.dataware.collectx.sink.KafkaSink;
import com.guwave.onedata.dataware.collectx.util.HDFSUtil;
import com.guwave.onedata.dataware.collectx.util.ThreadPoolUtils;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileDetail;
import com.guwave.onedata.dataware.dao.mysql.manager.FileWarehousingRecordManager;
import com.guwave.onedata.dataware.dao.mysql.manager.SourceStandardRuleManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

@Service("summaryConvertServiceImpl")
@Slf4j
public class SummaryConvertServiceImpl implements SummaryConvertService {

    private final static String SUMMARY_CONVERT_FILE_SUFFIX = ".csv";
    private final static String THREAD_PREFIX = "summary";
    private final AtomicInteger threadActiveCount = new AtomicInteger(0);
    private ThreadPoolExecutor THREAD_POOL_EXECUTOR;

    @Value("${spring.summary.convert.needConvertLocalPath}")
    private String needConvertLocalPath;
    @Value("${spring.summary.convert.convertLocalPath}")
    private String convertLocalPath;
    @Value("${spring.convert.convertCorePoolSize}")
    private Integer convertCorePoolSize;

    @Autowired
    private SftpFileDetailRepository sftpFileDetailRepository;
    @Autowired
    private SftpFileConvertScriptRepository sftpFileConvertScriptRepository;
    @Autowired
    private SftpFileConvertRecordRepository sftpFileConvertRecordRepository;
    @Autowired
    private CleanupTaskRepository cleanupTaskRepository;
    @Autowired
    private HDFSUtil hdfsUtil;
    @Autowired
    private HdfsConfig hdfsConfig;
    @Autowired
    private FileLoadingLogRepository fileLoadingLogRepository;
    @Autowired
    private FileWarehousingRecordManager fileWarehousingRecordManager;
    @Autowired
    private KafkaSink kafkaSink;
    @Autowired
    private SourceStandardRuleManager sourceStandardRuleManager;

    @PostConstruct
    public void init() {
        THREAD_POOL_EXECUTOR = ThreadPoolUtils.getNewThreadPoolExecutor(
                THREAD_PREFIX, convertCorePoolSize, convertCorePoolSize, convertCorePoolSize
        );
    }

    @Override
    public List<SftpFileDetail> getNeedConvertFiles() {
        return sftpFileDetailRepository.findByFileCategoryAndTransferStatusAndProcessStatusAndConvertFlagAndBatchStatusOrderByUpdateTimeAsc(
                FileCategory.SUMMARY,
                TransferStatus.SUCCESS,
                ProcessStatus.CREATE,
                1,
                SftpBatchStatus.SUCCESS,
                Pageable.ofSize(1)
        );
    }

    @Override
    public AtomicInteger getThreadActiveCount() {
        return threadActiveCount;
    }

    @Override
    public String getConvertFileSuffix() {
        return SUMMARY_CONVERT_FILE_SUFFIX;
    }

    @Override
    public String getNeedConvertLocalPath() {
        return needConvertLocalPath;
    }

    @Override
    public String getConvertLocalPath() {
        return convertLocalPath;
    }

    @Override
    public SftpFileDetailRepository getSftpFileDetailRepository() {
        return sftpFileDetailRepository;
    }

    @Override
    public FileLoadingLogRepository getFileLoadingLogRepository() {
        return fileLoadingLogRepository;
    }

    @Override
    public FileWarehousingRecordManager getFileWarehousingRecordManager() {
        return fileWarehousingRecordManager;
    }

    @Override
    public SftpFileConvertScriptRepository getSftpFileConvertScriptRepository() {
        return sftpFileConvertScriptRepository;
    }

    @Override
    public SftpFileConvertRecordRepository getSftpFileConvertRecordRepository() {
        return sftpFileConvertRecordRepository;
    }

    @Override
    public CleanupTaskRepository getCleanupTaskRepository() {
        return cleanupTaskRepository;
    }

    @Override
    public HDFSUtil getHdfsUtil() {
        return hdfsUtil;
    }

    @Override
    public HdfsConfig getHdfsConfig() {
        return hdfsConfig;
    }

    @Override
    public ThreadPoolExecutor getThreadPoolExecutor() {
        return THREAD_POOL_EXECUTOR;
    }

    @Override
    public KafkaSink getKafkaSink() {
        return kafkaSink;
    }

    @Override
    public SourceStandardRuleManager getSourceStandardRuleManager() {
        return sourceStandardRuleManager;
    }
}
