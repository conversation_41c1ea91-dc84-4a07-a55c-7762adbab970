package com.guwave.onedata.dataware.scheduler.service;

import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.DwLayer;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LayerCalculatePool;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LayerCalculatePoolRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class OdsDelayScheduleService {
    private static final Logger LOGGER = LoggerFactory.getLogger(OdsDelayScheduleService.class);

    @Value("${spring.spark.buffer.pool.odsDelaySecond}")
    private Long odsDelaySecond;

    @Autowired
    private LayerCalculatePoolRepository layerCalculatePoolRepository;

    @ScheduleSwitch
    @Scheduled(fixedDelayString = "${spring.scheduler.polling.milliseconds}")
    public void runTask() {
        moveFromOdsDelayBufferPool();
    }


    private void moveFromOdsDelayBufferPool() {
        long max = System.currentTimeMillis() - odsDelaySecond * 1000L;
        LOGGER.info("延迟队列满足条件的数据push到缓冲队列, 当前条件: {}", max);
        List<LayerCalculatePool> odsDelayCalculateList = layerCalculatePoolRepository.findAllByDelayFlagAndProcessStatusAndCreateTimeAndDwLayer(
                new Date(max),
                Pageable.ofSize(2000)
        );

        if (!CollectionUtils.isEmpty(odsDelayCalculateList)) {
            LOGGER.info("延迟队列满足条件的个数: {}", odsDelayCalculateList.size());
            Date date = new Date();
            ArrayList<LayerCalculatePool> layerCalculatePools = new ArrayList<>();
            odsDelayCalculateList.forEach(t -> {
                LayerCalculatePool dwdLayerCalculatePool = new LayerCalculatePool();
                BeanUtils.copyProperties(t, dwdLayerCalculatePool);
                dwdLayerCalculatePool
                        .setId(null)
                        .setDwLayer(DwLayer.DWD)
                        .setNextDwLayer(DwLayer.DWS)
                        .setDelayFlag(0)
                        .setNumExecutors(null)
                        .setExecutorCores(null)
                        .setExecutorMemory(null)
                        .setDriverMemory(null)
                        .setParallelism(null)
                        .setExtraConf(null)
                        .setFailCnt(0)
                        .setCreateTime(date)
                        .setUpdateTime(date);
                t
                        .setProcessStatus(ProcessStatus.SUCCESS)
                        .setUpdateTime(date);
                layerCalculatePools.add(dwdLayerCalculatePool);
            });
            layerCalculatePools.addAll(odsDelayCalculateList);
            layerCalculatePoolRepository.saveAll(layerCalculatePools);
        } else {
            LOGGER.info("未找到满足条件的数据");
        }
    }
}
