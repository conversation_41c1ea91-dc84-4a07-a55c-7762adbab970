package com.guwave.onedata.dataware.source.agent.common.sink.ck.wat.dim;

import com.guwave.onedata.dataware.common.model.wat.dim.DimWatWaferBin;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import com.guwave.onedata.dataware.source.agent.common.util.DateUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/13 20:56
 * @description DimWatWaferBinSink
 */
@Component
public class DimWatWaferBinSink implements CkSink<DimWatWaferBin> {

    @Value("${spring.sink.ck.dim.dbName}")
    private String dbName;

    @Override
    public String getTableName() {
        return "dim_lot_wafer_bin_cluster";
    }

    @Override
    public String getPartitionExpr() {
        return "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{SUB_CUSTOMER}')";
    }

    @Override
    public void handle(PreparedStatement statement, List<DimWatWaferBin> items) throws SQLException {
        for (DimWatWaferBin item : items) {
            statement.setObject(1, item.getCustomer());
            statement.setObject(2, item.getUploadType());
            statement.setObject(3, item.getFileId());
            statement.setObject(4, item.getFileName());
            statement.setObject(5, item.getFactory());
            statement.setObject(6, item.getFactorySite());
            statement.setObject(7, item.getFab());
            statement.setObject(8, item.getFabSite());
            statement.setObject(9, item.getTestArea());
            statement.setObject(10, item.getTestStage());
            statement.setObject(11, item.getLotType());
            statement.setObject(12, item.getDeviceId());
            statement.setObject(13, item.getLotId());
            statement.setObject(14, item.getWaferId());
            statement.setObject(15, item.getFabwfId());
            statement.setObject(16, item.getWaferNo());
            statement.setObject(17, item.getWaferLotId());
            statement.setObject(18, item.getTestProgram());
            statement.setObject(19, item.getTestProgramVersion());
            statement.setObject(20, item.getTesterName());
            statement.setObject(21, item.getTesterType());
            statement.setObject(22, item.getProberHandlerTyp());
            statement.setObject(23, item.getProberHandlerId());
            statement.setObject(24, item.getProbecardLoadboardTyp());
            statement.setObject(25, item.getProbecardLoadboardId());
            statement.setObject(26, item.getHbinNum());
            statement.setObject(27, item.getSbinNum());
            statement.setObject(28, item.getSbinPf());
            statement.setObject(29, item.getSbinNam());
            statement.setObject(30, item.getHbinPf());
            statement.setObject(31, item.getHbinNam());
            // CREATE_HOUR_KEY
            statement.setObject(32, DateUtil.getDayHour(System.currentTimeMillis()));
            // CREATE_DAY_KEY
            statement.setObject(33, DateUtil.getDate(System.currentTimeMillis()));
            // CREATE_TIME
            statement.setObject(34, new Timestamp(System.currentTimeMillis()));
            // CREATE_USER
            statement.setObject(35, item.getCreateUser());
            statement.setObject(36, item.getSubCustomer());
            // START_TIME
            statement.setObject(37, this.toTimestamp(item.getStartTime()));
            // END_TIME
            statement.setObject(38, this.toTimestamp(item.getEndTime()));
            statement.setObject(39, item.getTestTemperature());
            statement.setObject(40, item.getProcess());
            // version
            statement.setObject(41, System.currentTimeMillis());
            // wafer_id_key
            statement.setObject(42, item.getWaferId());
            // wafer_no_key
            statement.setObject(43, item.getWaferNo());
            statement.setObject(44, item.getIsFirstTest());
            statement.setObject(45, item.getIsFinalTest());
            statement.setObject(46, new Timestamp(item.getUploadTime()));

            statement.addBatch();
        }

    }

    @Override
    public List<String> getColumns() {
        return Arrays.asList(
                "CUSTOMER",
                "UPLOAD_TYPE",
                "FILE_ID",
                "FILE_NAME",
                "FACTORY",
                "FACTORY_SITE",
                "FAB",
                "FAB_SITE",
                "TEST_AREA",
                "TEST_STAGE",
                "LOT_TYPE",
                "DEVICE_ID",
                "LOT_ID",
                "WAFER_ID",
                "FABWF_ID",
                "WAFER_NO",
                "WAFER_LOT_ID",
                "TEST_PROGRAM",
                "TEST_PROGRAM_VERSION",
                "TESTER_NAME",
                "TESTER_TYPE",
                "PROBER_HANDLER_TYP",
                "PROBER_HANDLER_ID",
                "PROBECARD_LOADBOARD_TYP",
                "PROBECARD_LOADBOARD_ID",
                "HBIN_NUM",
                "SBIN_NUM",
                "SBIN_PF",
                "SBIN_NAM",
                "HBIN_PF",
                "HBIN_NAM",
                "CREATE_HOUR_KEY",
                "CREATE_DAY_KEY",
                "CREATE_TIME",
                "CREATE_USER",
                "SUB_CUSTOMER",
                "START_TIME",
                "END_TIME",
                "TEST_TEMPERATURE",
                "PROCESS",
                "VERSION",
                "WAFER_ID_KEY",
                "WAFER_NO_KEY",
                "IS_FIRST_TEST",
                "IS_FINAL_TEST",
                "UPLOAD_TIME"
        );
    }

    @Override
    public String getDbName() {
        return dbName;
    }
}
