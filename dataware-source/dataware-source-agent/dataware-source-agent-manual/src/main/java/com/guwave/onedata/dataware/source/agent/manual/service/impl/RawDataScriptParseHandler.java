package com.guwave.onedata.dataware.source.agent.manual.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.model.raw.TestItemData;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.EcidRuleRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LotRelationSyncRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.UidRuleRepository;
import com.guwave.onedata.dataware.parser.stdf.visitor.Visitor;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import com.guwave.onedata.dataware.source.agent.common.util.FileSystemUtil;
import com.guwave.onedata.dataware.source.agent.common.util.HdfsUtil;
import com.guwave.onedata.dataware.source.agent.common.visitor.MultiThreadVisitor;
import com.guwave.onedata.dataware.source.agent.manual.listener.RawDataExcelPreView;
import com.guwave.onedata.dataware.source.agent.manual.listener.RawDataExcelReader;
import com.guwave.onedata.dataware.source.agent.manual.model.ManualConvertFileInfo;
import com.guwave.onedata.dataware.source.agent.manual.model.ManualConvertResult;
import com.guwave.onedata.dataware.source.agent.manual.listener.ExcelLineListener;
import com.guwave.onedata.dataware.source.agent.manual.service.ParseFileHandler;
import com.guwave.onedata.dataware.source.agent.manual.util.RawDataCommonUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;
import java.util.function.BiConsumer;

@Component
public class RawDataScriptParseHandler implements ParseFileHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(RawDataScriptParseHandler.class);

    public static final Map<String, BiConsumer<String, TestItemData>> OTHER_FIELD_CONSUMER_MAP = new HashMap<String, BiConsumer<String, TestItemData>>() {{
        put("conditionSet", (str, rawData) -> rawData.setConditionSet(RawDataCommonUtil.getMapValue(str)));
    }};
    public static final Map<String, BiConsumer<Pair<String, String>, TestItemData>> MAP_FIELD_CONSUMER_MAP = new HashMap<String, BiConsumer<Pair<String, String>, TestItemData>>() {{

    }};

    @Value("${spring.hdfs.odsHdfsTemplatePath}")
    private String odsHdfsTemplatePath;
    @Value("${spring.maxNoCPartIdCnt}")
    private Long maxNoCPartIdCnt;
    @Value("${spring.handler.sink.batchSize}")
    private Integer batchSize;
    @Autowired
    private Map<String, FileSystemUtil> fileSystemUtilMap;
    @Autowired
    private Map<String, CkSink> ckSinkMap;
    @Autowired
    private EcidRuleRepository ecidRuleRepository;
    @Autowired
    private UidRuleRepository uidRuleRepository;
    @Autowired
    private LotRelationSyncRepository lotRelationSyncRepository;
    private static final Map<ManualFileSystem, String> MANUAL_FILE_SYSTEM_MAP = new HashMap<ManualFileSystem, String>() {{
        put(ManualFileSystem.HDFS, "hdfsUtil");
        put(ManualFileSystem.LOCAL, "localFileSystemUtil");
        put(ManualFileSystem.MINIO, "minioFileSystemUtil");
    }};
    @Override
    public Boolean support(FileCategory fileCategory, ParseRule parseRule) {
        return fileCategory == FileCategory.RAW_DATA && parseRule == ParseRule.RAW_DATA_SCRIPT;
    }

    @Override
    public FileCategory calRealFileCategory(File localFile, File scriptFile, ManualFileInfo manualFileInfo) {
        if (manualFileInfo.getTestArea() == TestArea.WAT) {
            return FileCategory.WAT;
        }
        // 预解析一遍文件
        // 执行python转换
        ManualConvertResult manualConvertResult = convertFile(localFile, scriptFile, true);
        File csvFile = new File(manualConvertResult.getData().get(0).getFileFullPath());
        try {
            RawDataExcelPreView rawDataExcelPreView = new RawDataExcelPreView(manualFileInfo);
            ExcelReader excel = EasyExcel.read(csvFile, new ExcelLineListener(manualFileInfo.getFieldMapping(), rawDataExcelPreView))
                    .headRowNumber(1)
                    .excelType(ExcelTypeEnum.CSV)
                    .build();
            try {
                excel.read(new ReadSheet(0));
            } finally {
                excel.finish();
            }
            return rawDataExcelPreView.getRealFileCategory();
        } finally {
            FileUtils.deleteQuietly(csvFile);
        }
    }

    @Override
    public void dealFile(ManualCalculateTask manualCalculateTask, File localFile, ManualFileInfo manualFileInfo, File scriptFile) {
        // 执行python转换
        ManualConvertResult manualConvertResult = convertFile(localFile, scriptFile, false);

        // 读取转换后的文件到ods
        FileSystemUtil fileSystemUtil = fileSystemUtilMap.get(MANUAL_FILE_SYSTEM_MAP.get(manualFileInfo.getManualFileSystem()));
        List<Visitor> visitors = new ArrayList<>();
        int allThreadCnt = 1;
        for (int i = 1; i <= allThreadCnt; i++) {
            visitors.add(new MultiThreadVisitor(i, fileSystemUtil, odsHdfsTemplatePath, localFile, defaultFillFileMainDataConsumer(manualFileInfo), allThreadCnt, batchSize, ckSinkMap, ecidRuleRepository, uidRuleRepository, lotRelationSyncRepository, manualFileInfo.getParameterType(), manualFileInfo.getTestParameter(), manualFileInfo.getScript()));
        }
        try {
            Visitor visitor = visitors.get(0);
            RawDataExcelReader rawDataExcelReader = new RawDataExcelReader(visitor, firstRecordTestArea -> predictTestArea(manualCalculateTask, firstRecordTestArea, fileSystemUtil), maxNoCPartIdCnt, OTHER_FIELD_CONSUMER_MAP, MAP_FIELD_CONSUMER_MAP);
            for (ManualConvertFileInfo manualConvertFileInfo : manualConvertResult.getData()) {
                File csvFile = new File(manualConvertFileInfo.getFileFullPath());
                ExcelReader excel = EasyExcel.read(csvFile, new ExcelLineListener(manualFileInfo.getFieldMapping(), rawDataExcelReader))
                        .headRowNumber(1)
                        .excelType(ExcelTypeEnum.CSV)
                        .build();
                try {
                    excel.read(new ReadSheet(0));
                } finally {
                    excel.finish();
                }
            }
            rawDataExcelReader.dealRemain();
        } finally {
            visitors.forEach(t -> {
                MultiThreadVisitor visitor = (MultiThreadVisitor) t;
                visitor.close();
            });
            // 删除临时文件
            manualConvertResult.getData().forEach(fileData -> {
                FileUtils.deleteQuietly(new File(fileData.getFileFullPath()));
            });
        }

        // 填充manual_file_info
        defaultFillManualFileInfo(manualFileInfo, visitors, localFile);
    }

    private TestArea predictTestArea(ManualCalculateTask manualCalculateTask, TestArea firstRecordTestArea, FileSystemUtil fileSystemUtil) {
        TestArea resultArea = manualCalculateTask.getTestArea() == null ? firstRecordTestArea : manualCalculateTask.getTestArea();

        // 删除此文件对应的ods数据
        deleteOdsFile(odsHdfsTemplatePath, manualCalculateTask, resultArea, fileSystemUtil);

        return resultArea;
    }
}
