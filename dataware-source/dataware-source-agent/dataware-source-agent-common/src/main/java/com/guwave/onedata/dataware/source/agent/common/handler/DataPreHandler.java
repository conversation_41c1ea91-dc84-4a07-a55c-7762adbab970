package com.guwave.onedata.dataware.source.agent.common.handler;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.contant.WarehousingMode;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.repair.DataRepairField;
import com.guwave.onedata.dataware.common.model.repair.FlowIdDetail;
import com.guwave.onedata.dataware.common.util.FlowIdUtil;
import com.guwave.onedata.dataware.dao.mysql.domain.dc.FileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import com.guwave.onedata.dataware.common.model.vo.DeviceInfoDto;
import com.guwave.onedata.dataware.common.model.vo.ProductDeviceInfo;
import com.guwave.onedata.dataware.dao.mysql.manager.SourceStandardRuleManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dc.DeviceInfoRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.RepairRecordRepository;
import com.guwave.onedata.dataware.source.agent.common.exception.NotFailException;
import com.guwave.onedata.dataware.source.agent.common.util.ProcessLogThreadLocalUtil;
import com.guwave.onedata.dataware.common.model.vo.MetaDataVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.io.File;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;


/**
 * Copyright (C), 2021, guwave
 * <p>
 * Handler
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2021-10-19 16:48:37
 */
public interface DataPreHandler extends PreHandler {
    Logger LOGGER = LoggerFactory.getLogger(DataPreHandler.class);

    String RULE_EXPRESSION = "{RULE_EXPRESSION}";
    String JS_ENGINE_NAME = "javascript";


    String DEVICE_ID_FIELD = "deviceId";

    Map<String, BiConsumer<LotMetaDataDetail, Object>> SOURCE_STANDARD_FIELD_CONSUMER_MAP = new HashMap<String, BiConsumer<LotMetaDataDetail, Object>>() {{
        put("testArea", (lotMetaDataDetail, result) -> lotMetaDataDetail.setTestArea(TestArea.of(result.toString())));
        put("lotType", (lotMetaDataDetail, result) -> lotMetaDataDetail.setLotType(LotType.of(result.toString())));
        put("offlineRetest", (lotMetaDataDetail, result) -> lotMetaDataDetail.setOfflineRetest(Double.valueOf(result.toString()).intValue()));
        put("offlineRetestIgnoreTp", (lotMetaDataDetail, result) -> lotMetaDataDetail.setOfflineRetestIgnoreTp(Double.valueOf(result.toString()).intValue()));
        put("interrupt", (lotMetaDataDetail, result) -> lotMetaDataDetail.setInterrupt(Double.valueOf(result.toString()).intValue()));
        put("interruptIgnoreTp", (lotMetaDataDetail, result) -> lotMetaDataDetail.setInterruptIgnoreTp(Double.valueOf(result.toString()).intValue()));
        put("lotId", (lotMetaDataDetail, result) -> lotMetaDataDetail.setLotId(result.toString()));
        put("sblotId", (lotMetaDataDetail, result) -> lotMetaDataDetail.setSblotId(result.toString()));
        put("testCod", (lotMetaDataDetail, result) -> lotMetaDataDetail.setTestCod(result.toString()));
        put("originWaferId", (lotMetaDataDetail, result) -> lotMetaDataDetail.setOriginWaferId(result.toString()));
        put("waferNo", (lotMetaDataDetail, result) -> lotMetaDataDetail.setWaferNo(String.valueOf(Double.valueOf(result.toString()).intValue())));
        put("testStage", (lotMetaDataDetail, result) -> lotMetaDataDetail.setTestStage(result.toString()));
        put("dupRetest", (lotMetaDataDetail, result) -> lotMetaDataDetail.setDupRetest(Double.valueOf(result.toString()).intValue()));
        put("dupRetestIgnoreTp", (lotMetaDataDetail, result) -> lotMetaDataDetail.setDupRetestIgnoreTp(Double.valueOf(result.toString()).intValue()));
        put("batchNum", (lotMetaDataDetail, result) -> lotMetaDataDetail.setBatchNum(Double.valueOf(result.toString()).intValue()));
        put("batchNumIgnoreTp", (lotMetaDataDetail, result) -> lotMetaDataDetail.setBatchNumIgnoreTp(Double.valueOf(result.toString()).intValue()));
        put("floorId", (lotMetaDataDetail, result) -> lotMetaDataDetail.setFloorId(result.toString()));
        put("testTemperature", (lotMetaDataDetail, result) -> lotMetaDataDetail.setTestTemperature(result.toString()));
        put("testerName", (lotMetaDataDetail, result) -> lotMetaDataDetail.setTesterName(result.toString()));
        put("testerType", (lotMetaDataDetail, result) -> lotMetaDataDetail.setTesterType(result.toString()));
        put("probecardLoadboardId", (lotMetaDataDetail, result) -> lotMetaDataDetail.setProbecardLoadboardId(result.toString()));
        put("testProgram", (lotMetaDataDetail, result) -> lotMetaDataDetail.setTestProgram(result.toString()));
        put("testProgramVersion", (lotMetaDataDetail, result) -> lotMetaDataDetail.setTestProgramVersion(result.toString()));
        put("pkgTyp", (lotMetaDataDetail, result) -> lotMetaDataDetail.setPkgTyp(result.toString()));
        put("retestBinNum", (lotMetaDataDetail, result) -> lotMetaDataDetail.setRetestBinNum(result.toString()));
        put("conditionSet", (lotMetaDataDetail, result) -> lotMetaDataDetail.setConditionSet(getMap(result.toString())));
        put("startT", (lotMetaDataDetail, result) -> lotMetaDataDetail.setStartT(result.toString()));
        put("finishT", (lotMetaDataDetail, result) -> lotMetaDataDetail.setFinishT(result.toString()));
        put("realWaferId", (lotMetaDataDetail, result) -> lotMetaDataDetail.setRealWaferId(result.toString()));
    }};

    Map<String, Predicate<LotMetaDataDetail>> KEY_FIELD_CHECK_MAP = new LinkedHashMap<String, Predicate<LotMetaDataDetail>>() {{
        put("deviceId", lotMetaDataDetail -> StringUtils.isNotEmpty(lotMetaDataDetail.getDeviceId()));
        put("testArea", lotMetaDataDetail -> lotMetaDataDetail.getTestArea() != null);
        put("lotType", lotMetaDataDetail -> lotMetaDataDetail.getLotType() != null);
        put("testStage", lotMetaDataDetail -> lotMetaDataDetail.getTestArea() != null
                        && StringUtils.isNotEmpty(lotMetaDataDetail.getTestStage())
//                && Pattern.compile(String.format("^%s[1-9]\\d*$", lotMetaDataDetail.getTestArea().getArea())).matcher(lotMetaDataDetail.getTestStage()).matches()
        );
        put("lotId", lotMetaDataDetail -> StringUtils.isNotEmpty(lotMetaDataDetail.getLotId()));
        put("sblotId", lotMetaDataDetail -> StringUtils.isNotEmpty(lotMetaDataDetail.getSblotId()));
        put("waferId", lotMetaDataDetail -> StringUtils.isNotEmpty(lotMetaDataDetail.getWaferId()) && lotMetaDataDetail.getWaferId().contains(UNDER_LINE));
        put("waferNo", lotMetaDataDetail -> StringUtils.isNotEmpty(lotMetaDataDetail.getWaferNo()) && NumberUtils.isDigits(lotMetaDataDetail.getWaferNo()));
        put("startT", lotMetaDataDetail -> NumberUtils.isDigits(lotMetaDataDetail.getStartT()));
        put("finishT", lotMetaDataDetail -> NumberUtils.isDigits(lotMetaDataDetail.getFinishT()));
        put("offlineRetest", lotMetaDataDetail -> lotMetaDataDetail.getOfflineRetest() != null);
        put("interrupt", lotMetaDataDetail -> lotMetaDataDetail.getInterrupt() != null);
        put("dupRetest", lotMetaDataDetail -> lotMetaDataDetail.getDupRetest() != null);
        put("batchNum", lotMetaDataDetail -> lotMetaDataDetail.getBatchNum() != null);
    }};

    @Override
    default LotMetaDataDetail preDealFile(File uncompressFile, SftpFileDetail sftpFileDetail, FileInfo fileInfo, ProcessLog processLog, FileLoadingLog fileLoadingLog) throws Exception {
        LotMetaDataDetail lotMetaDataDetail = buildLotMetaDataDetail(sftpFileDetail, fileInfo);

        // 解析文件获得基础信息
        MetaDataVo metaDataVo = new MetaDataVo();
        try {
            fillLotMetaDataDetail(lotMetaDataDetail, uncompressFile.getAbsolutePath(), sftpFileDetail, metaDataVo);
        } catch (Exception e) {
            if (e instanceof NotFailException) {
                throw e;
            } else {
                LOGGER.error("fillLotMetaDataDetail 异常：", e);
                ProcessLogThreadLocalUtil.appendErrorMessage("基础信息 : " + JSON.toJSONString(metaDataVo));
                if (e instanceof FileLoadException) {
                    throw e;
                }
                throw new RuntimeException(e);
            }
        }

        // 补充processLog的信息
        processLog
                .setLotType(lotMetaDataDetail.getLotType())
                .setDeviceId(lotMetaDataDetail.getDeviceId())
                .setLotId(lotMetaDataDetail.getLotId())
                .setWaferId(lotMetaDataDetail.getWaferId())
                .setOriginWaferId(lotMetaDataDetail.getOriginWaferId())
                .setWaferNo(lotMetaDataDetail.getWaferNo());


        // 设置startT和finishT
        if (StringUtils.isEmpty(lotMetaDataDetail.getStartT())) {
            lotMetaDataDetail.setStartT(lotMetaDataDetail.getOriginStartT());
        }

        if (StringUtils.isEmpty(lotMetaDataDetail.getFinishT())) {
            lotMetaDataDetail.setFinishT(lotMetaDataDetail.getOriginFinishT());
        }

        if (StringUtils.isEmpty(lotMetaDataDetail.getStartT())) {
            lotMetaDataDetail.setStartT(lotMetaDataDetail.getFinishT());
        }
        if (StringUtils.isEmpty(lotMetaDataDetail.getFinishT())) {
            lotMetaDataDetail.setFinishT(lotMetaDataDetail.getStartT());
        }
        if (StringUtils.isEmpty(lotMetaDataDetail.getStartT())) {
            lotMetaDataDetail
                    .setStartT(0 + Constant.EMPTY)
                    .setFinishT(0 + Constant.EMPTY);
        }

        //fileLoadingLog补充解析后的信息
        fileLoadingLog
                .setStartT(NumberUtils.isDigits(lotMetaDataDetail.getStartT()) ? new Date(Long.parseLong(lotMetaDataDetail.getStartT()) * 1000L) : null) //待确认
                .setFinishT(NumberUtils.isDigits(lotMetaDataDetail.getFinishT()) ? new Date(Long.parseLong(lotMetaDataDetail.getFinishT()) * 1000L) : null) //待确认
                .setDeviceId(StringUtils.isNotEmpty(lotMetaDataDetail.getDeviceId()) ? lotMetaDataDetail.getDeviceId() : fileLoadingLog.getDeviceId())
                .setTesterName(StringUtils.isNotEmpty(lotMetaDataDetail.getTesterName()) ? lotMetaDataDetail.getTesterName() : fileLoadingLog.getTesterName())
                .setLotType(lotMetaDataDetail.getLotType() != null ? lotMetaDataDetail.getLotType() : fileLoadingLog.getLotType())
                .setTesterType(StringUtils.isNotEmpty(lotMetaDataDetail.getTesterType()) ? lotMetaDataDetail.getTesterType() : fileLoadingLog.getTesterType())
                .setTestProgram(StringUtils.isNotEmpty(lotMetaDataDetail.getTestProgram()) ? lotMetaDataDetail.getTestProgram() : fileLoadingLog.getTestProgram())
                .setTestProgramVersion(StringUtils.isNotEmpty(lotMetaDataDetail.getTestProgramVersion()) ? lotMetaDataDetail.getTestProgramVersion() : fileLoadingLog.getTestProgramVersion())
                .setLotId(StringUtils.isNotEmpty(lotMetaDataDetail.getLotId()) ? lotMetaDataDetail.getLotId() : fileLoadingLog.getLotId())
                .setSblotId(StringUtils.isNotEmpty(lotMetaDataDetail.getSblotId()) ? lotMetaDataDetail.getSblotId() : fileLoadingLog.getSblotId())
                .setWaferId(StringUtils.isNotEmpty(lotMetaDataDetail.getWaferId()) ? lotMetaDataDetail.getWaferId() : fileLoadingLog.getWaferId())
                .setWaferNo(StringUtils.isNotEmpty(lotMetaDataDetail.getWaferNo()) ? lotMetaDataDetail.getWaferNo() : fileLoadingLog.getWaferNo())
                .setTestStage(StringUtils.isNotEmpty(lotMetaDataDetail.getTestStage()) ? lotMetaDataDetail.getTestStage() : fileLoadingLog.getTestStage())
                .setTestArea(lotMetaDataDetail.getTestArea() != null ? lotMetaDataDetail.getTestArea() : fileLoadingLog.getTestArea())
        ;

        // 校验必要字段非空
        Pair<Boolean, List<String>> checkKeyFiledPair = checkLotMetaDataDetailKeyField(lotMetaDataDetail);
        if (checkKeyFiledPair.getKey()) {
            return lotMetaDataDetail;
        } else {
            String lotMetaDataDetailStr = JSON.toJSONString(lotMetaDataDetail);
            String metaDataVoStr = JSON.toJSONString(metaDataVo);
            List<String> keyFieldNullList = checkKeyFiledPair.getValue();
            String exceptionMessage = keyFieldNullList.stream().map(field -> FileLoadExceptionInfo.KEY_FIELD_NULL_EXCEPTION.getMessage().replace(KEY_FIELD, field)).collect(Collectors.joining(COMMA));
            LOGGER.error("{} 必要字段为空, exceptionMessage: {}, lotMetaDataDetail:{} , 基础信息 : {}", sftpFileDetail.getLocalFileName(), exceptionMessage, lotMetaDataDetailStr, metaDataVoStr);
            String errorMessage = new StringBuilder("必要字段为空：")
                    .append(lotMetaDataDetailStr)
                    .append(Constant.ENTER)
                    .append("基础信息 : ")
                    .append(metaDataVoStr).toString();
            throw new FileLoadException(FileLoadExceptionInfo.KEY_FIELD_NULL_EXCEPTION, errorMessage, keyFieldNullList)
                    .updateExceptionMessage(exceptionMessage);
        }

    }


    default void fillLotMetaDataDetail(MetaDataVo metaDataVo, LotMetaDataDetail lotMetaDataDetail) {
        // 放置一些额外信息
        List<DeviceInfoDto> deviceInfos = getDeviceInfoRepository().findDeviceInfo(lotMetaDataDetail.getCustomer(), lotMetaDataDetail.getFactory(), lotMetaDataDetail.getFactorySite(), metaDataVo.getPartTyp());
        metaDataVo.setDeviceInfos(deviceInfos.stream().map(ProductDeviceInfo::of).collect(Collectors.toList()));

        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName(JS_ENGINE_NAME);
        String metaData = JSON.toJSONString(metaDataVo);

        // 如果是修复的数据，查询dw_repair_record获取修复后字段的值
        List<RepairRecord> repairRecords = lotMetaDataDetail.getWarehousingMode() == WarehousingMode.NORMAL ? new ArrayList<>() : getRepairRecordRepository().findAllByRepairLotWaferIdAndFileName(lotMetaDataDetail.getRepairLotWaferId(), lotMetaDataDetail.getFileName());
        if (lotMetaDataDetail.getWarehousingMode() == WarehousingMode.REPAIR && CollectionUtils.isEmpty(repairRecords)) {
            LOGGER.error("没有待处理的修复记录");
            throw new FileLoadException(FileLoadExceptionInfo.REPAIR_RECORD_NOT_FOUND_EXCEPTION, FileLoadExceptionInfo.REPAIR_RECORD_NOT_FOUND_EXCEPTION.getMessage(), null);
        }

        // 如果字段是被修复的,根据修复的规则获取。如果是正常入库，该字段若deviceId配置了获取规则，则从deviceId获取规则表里面根据配置的规则获取
        if (CollectionUtils.isNotEmpty(repairRecords) && StringUtils.isNotBlank(repairRecords.get(0).getDeviceId())) {
            lotMetaDataDetail.setDeviceId(repairRecords.get(0).getDeviceId());
        } else {
            List<SourceStandardDeviceIdRule> sourceStandardDeviceIdRules = getSourceStandardRuleManager().getSourceStandardDeviceIdRuleList(lotMetaDataDetail.getCustomer(), lotMetaDataDetail.getSubCustomer(), lotMetaDataDetail.getTestArea(), lotMetaDataDetail.getFactory(), lotMetaDataDetail.getFactorySite());
            Object deviceId = getSourceStandardRuleManager().getDeviceIdFromRule(engine, metaData, sourceStandardDeviceIdRules);
            if (deviceId != null) {
                lotMetaDataDetail.setDeviceId(deviceId.toString());
            }
        }

        metaDataVo.setPartTyp(convertNullStr(lotMetaDataDetail.getDeviceId()));
        metaData = JSON.toJSONString(metaDataVo);

        List<SourceStandardFieldRule> sourceStandardFieldRuleList = getSourceStandardRuleManager().getSourceStandardFieldRuleList(
                lotMetaDataDetail.getCustomer(),
                lotMetaDataDetail.getSubCustomer(),
                lotMetaDataDetail.getTestArea(),
                lotMetaDataDetail.getFactory(),
                lotMetaDataDetail.getFactorySite(),
                lotMetaDataDetail.getDeviceId()
        );

        beforeFillLotMetaDataDetail(metaDataVo, lotMetaDataDetail);

        if (CollectionUtils.isNotEmpty(sourceStandardFieldRuleList)) {
            for (SourceStandardFieldRule sourceStandardFieldRule : sourceStandardFieldRuleList) {
                String field = sourceStandardFieldRule.getField();
                String completeJsFun = SourceStandardRuleManager.jsFun
                        .replace(RULE_EXPRESSION, sourceStandardFieldRule.getRuleExpression());

                Object result = getSourceStandardRuleManager().getResultFromJs(engine, metaData, field, completeJsFun);

                BiConsumer<LotMetaDataDetail, Object> consumer = SOURCE_STANDARD_FIELD_CONSUMER_MAP.get(field);
                if (result != null && consumer != null) {
                    try {
                        consumer.accept(lotMetaDataDetail, result);
                    } catch (Exception e) {
                        throw new RuntimeException(String.format("解析文件%s 字段 %s 失败，读取到的值为 %s .", lotMetaDataDetail.getFileName(), field, result), e);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(repairRecords)) {
            RepairRecord repairRecord = repairRecords.get(0);
            // 历史数据dw_file_loading_log表关键字段没有值
            lotMetaDataDetail
                    .setTestArea(repairRecord.getTestArea() != null ? repairRecord.getTestArea() : lotMetaDataDetail.getTestArea())
                    .setLotId(StringUtils.isNotBlank(repairRecord.getLotId()) ? repairRecord.getLotId() : lotMetaDataDetail.getLotId())
                    .setWaferNo(StringUtils.isNotBlank(repairRecord.getWaferNo()) ? repairRecord.getWaferNo() : lotMetaDataDetail.getWaferNo())
                    .setTestStage(StringUtils.isNotBlank(repairRecord.getTestStage()) ? repairRecord.getTestStage() : lotMetaDataDetail.getTestStage())
                    .setTestProgram(StringUtils.isNotBlank(repairRecord.getTestProgram()) ? repairRecord.getTestProgram() : lotMetaDataDetail.getTestProgram())
                    .setSblotId(StringUtils.isNotBlank(repairRecord.getSblotId()) ? repairRecord.getSblotId() : lotMetaDataDetail.getSblotId())
                    .setLotType(repairRecord.getLotType() != null ? repairRecord.getLotType() : lotMetaDataDetail.getLotType());
            // 数据修复覆盖flowId、flowIdIgnoreTp
            if (repairRecord.getModifyFlowId() != null) {
                FlowIdDetail flowIdDetail = FlowIdUtil.flatStandardFlowId(repairRecord.getModifyFlowId());
                lotMetaDataDetail.setOfflineRetest(flowIdDetail.getOfflineRetest())
                        .setInterrupt(flowIdDetail.getInterrupt())
                        .setDupRetest(flowIdDetail.getDupRetest())
                        .setBatchNum(flowIdDetail.getBatchNum())
                        .setOfflineRetestIgnoreTp(flowIdDetail.getOfflineRetest())
                        .setInterruptIgnoreTp(flowIdDetail.getInterrupt())
                        .setDupRetestIgnoreTp(flowIdDetail.getDupRetest())
                        .setBatchNumIgnoreTp(flowIdDetail.getBatchNum())
                ;
            }
            if (StringUtils.isNotEmpty(repairRecord.getRepairField())) {
                Map<String, Object> repairFieldMap = JSON.parseArray(repairRecord.getRepairField(), DataRepairField.class).stream()
                        .collect(Collectors.toMap(dataRepairField -> dataRepairField.getRepairField().getSourceRuleField(), DataRepairField::getModifyValue));
                repairFieldMap.forEach((field, modifyValue) -> {
                    BiConsumer<LotMetaDataDetail, Object> consumer = SOURCE_STANDARD_FIELD_CONSUMER_MAP.get(field);
                    if (modifyValue != null && consumer != null) {
                        try {
                            consumer.accept(lotMetaDataDetail, modifyValue);
                        } catch (Exception e) {
                            throw new RuntimeException(String.format("数据修复文件%s 字段 %s 失败，读取到的值为 %s .", lotMetaDataDetail.getFileName(), field, modifyValue), e);
                        }
                    }
                });
            }
        }
        // 处理flowId和flowIdIgnoreTp
        lotMetaDataDetail.setOfflineRetest(getDefaultValue(lotMetaDataDetail.getOfflineRetest(), lotMetaDataDetail.getOfflineRetestIgnoreTp()));
        lotMetaDataDetail.setOfflineRetestIgnoreTp(getDefaultValue(lotMetaDataDetail.getOfflineRetestIgnoreTp(), lotMetaDataDetail.getOfflineRetest()));

        lotMetaDataDetail.setInterrupt(getDefaultValue(lotMetaDataDetail.getInterrupt(), lotMetaDataDetail.getInterruptIgnoreTp()));
        lotMetaDataDetail.setInterruptIgnoreTp(getDefaultValue(lotMetaDataDetail.getInterruptIgnoreTp(), lotMetaDataDetail.getInterrupt()));

        lotMetaDataDetail.setDupRetest(getDefaultValue(lotMetaDataDetail.getDupRetest(), lotMetaDataDetail.getDupRetestIgnoreTp()));
        lotMetaDataDetail.setDupRetestIgnoreTp(getDefaultValue(lotMetaDataDetail.getDupRetestIgnoreTp(), lotMetaDataDetail.getDupRetest()));

        lotMetaDataDetail.setBatchNum(getDefaultValue(lotMetaDataDetail.getBatchNum(), lotMetaDataDetail.getBatchNumIgnoreTp()));
        lotMetaDataDetail.setBatchNumIgnoreTp(getDefaultValue(lotMetaDataDetail.getBatchNumIgnoreTp(), lotMetaDataDetail.getBatchNum()));

        afterFillLotMetaDataDetail(metaDataVo, lotMetaDataDetail);

    }

    default Integer getDefaultValue(Integer value, Integer defaultValue) {
        return Objects.isNull(value) ? defaultValue : value;
    }

    default LotMetaDataDetail buildLotMetaDataDetail(SftpFileDetail sftpFileDetail, FileInfo fileInfo) {
        Date date = new Date();
        String remoteOriginalFilePath = sftpFileDetail.getRemoteOriginalFilePath();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(remoteOriginalFilePath) && !remoteOriginalFilePath.endsWith(Constant.SLASH)) {
            remoteOriginalFilePath = remoteOriginalFilePath + Constant.SLASH;
        }
        return new LotMetaDataDetail()
                .setCustomer(sftpFileDetail.getCustomer())
                .setSubCustomer(sftpFileDetail.getSubCustomer())
                .setTestArea(sftpFileDetail.getTestArea())
                .setFactory(sftpFileDetail.getFactory())
                .setFactorySite(sftpFileDetail.getFactorySite())
                .setFileType(sftpFileDetail.getFileType())
                .setFileCategory(sftpFileDetail.getFileCategory())
                .setFileName(sftpFileDetail.getLocalFileName())
                .setOriginFileName(sftpFileDetail.getOriginFileName())
                .setHdfsPath(sftpFileDetail.getHdfsFilePath())
                .setFtpPath(remoteOriginalFilePath + sftpFileDetail.getRemoteFileName())
                .setWarehousingMode(sftpFileDetail.getWarehousingMode())
                .setRepairLotWaferId(sftpFileDetail.getRepairLotWaferId())
                .setCleanupTaskIds(sftpFileDetail.getCleanupTaskIds())
                .setPriority(0)
                .setRemoteFileMtime(sftpFileDetail.getRemoteFileMtime())
                .setProcessStatus(ProcessStatus.CREATE)
                .setFileInfoId(fileInfo.getId())
                .setCreateTime(date)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateTime(date)
                .setUpdateUser(Constant.SYSTEM);
    }

    static Map<String, String> getMap(String str) {
        Map<String, String> result = new HashMap<>();
        if (str.isEmpty()) {
            return result;
        }
        Map map = JSON.parseObject(str, Map.class);
        for (Object key : map.keySet()) {
            result.put(key.toString(), map.get(key).toString());
        }
        return result;
    }

    void beforeFillLotMetaDataDetail(MetaDataVo metaDataVo, LotMetaDataDetail lotMetaDataDetail);

    void afterFillLotMetaDataDetail(MetaDataVo metaDataVo, LotMetaDataDetail lotMetaDataDetail);

    default Pair<Boolean, List<String>> checkLotMetaDataDetailKeyField(LotMetaDataDetail lotMetaDataDetail) {
        boolean checkFlag = true;
        List<String> keyFieldNullList = new ArrayList<>();

        HashSet<String> keyFileds = Sets.newHashSet(getKeyFields().split(Constant.COMMA));
        for (Map.Entry<String, Predicate<LotMetaDataDetail>> entry : KEY_FIELD_CHECK_MAP.entrySet()) {
            String field = entry.getKey();
            Predicate<LotMetaDataDetail> predicate = entry.getValue();
            if (keyFileds.contains(field)) {
                if (!predicate.test(lotMetaDataDetail)) {
                    checkFlag = false;
                    String exceptionMessage = FileLoadExceptionInfo.KEY_FIELD_NULL_EXCEPTION.getMessage().replace(KEY_FIELD, field);
                    ProcessLogThreadLocalUtil.appendErrorMessage(exceptionMessage);
                    keyFieldNullList.add(field);
                }
            }
        }
        return Pair.of(checkFlag, keyFieldNullList);
    }

    String getKeyFields();

    void fillLotMetaDataDetail(LotMetaDataDetail lotMetaDataDetail, String localStdfPath, SftpFileDetail sftpFileDetail, MetaDataVo metaDataVo) throws Exception;

    RepairRecordRepository getRepairRecordRepository();

    DeviceInfoRepository getDeviceInfoRepository();

    SourceStandardRuleManager getSourceStandardRuleManager();

}
