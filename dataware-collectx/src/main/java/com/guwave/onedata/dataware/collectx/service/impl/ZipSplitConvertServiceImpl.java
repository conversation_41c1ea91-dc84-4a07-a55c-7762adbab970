package com.guwave.onedata.dataware.collectx.service.impl;

import com.guwave.onedata.dataware.collectx.configuration.HdfsConfig;
import com.guwave.onedata.dataware.collectx.service.ZipSplitConvertService;
import com.guwave.onedata.dataware.collectx.sink.KafkaSink;
import com.guwave.onedata.dataware.collectx.util.HDFSUtil;
import com.guwave.onedata.dataware.collectx.util.ThreadPoolUtils;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.converter.ZipFileCollectionToStringConverter;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileDetail;
import com.guwave.onedata.dataware.dao.mysql.manager.FileWarehousingRecordManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.FileLoadingLogRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpBatchInfoRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpFileDetailRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

@Service("zipSplitConvertServiceImpl")
@Slf4j
public class ZipSplitConvertServiceImpl implements ZipSplitConvertService {

    private final static String THREAD_PREFIX = "zipSplit";
    private final AtomicInteger threadActiveCount = new AtomicInteger(0);
    private ThreadPoolExecutor THREAD_POOL_EXECUTOR;

    @Value("${spring.zipSplit.convert.needConvertLocalPath}")
    private String needConvertLocalPath;
    @Value("${spring.zipSplit.convert.convertLocalPath}")
    private String convertLocalPath;
    @Value("${spring.convert.convertCorePoolSize}")
    private Integer convertCorePoolSize;
    @Value("${spring.zipSplit.convert.zipSplitFileMaxWaitMillion}")
    private Long zipSplitFileMaxWaitMillion;

    @Autowired
    private SftpFileDetailRepository sftpFileDetailRepository;
    @Autowired
    private HDFSUtil hdfsUtil;
    @Autowired
    private HdfsConfig hdfsConfig;
    @Autowired
    private SftpBatchInfoRepository sftpBatchInfoRepository;
    @Autowired
    private FileLoadingLogRepository fileLoadingLogRepository;
    @Autowired
    private FileWarehousingRecordManager fileWarehousingRecordManager;
    @Autowired
    private KafkaSink kafkaSink;

    @PostConstruct
    public void init() {
        THREAD_POOL_EXECUTOR = ThreadPoolUtils.getNewThreadPoolExecutor(
                THREAD_PREFIX, convertCorePoolSize, convertCorePoolSize, convertCorePoolSize
        );
    }

    @Override
    public List<SftpFileDetail> getNeedConvertFiles() {
        return sftpFileDetailRepository.findByFileCategoryAndTransferStatusAndProcessStatusAndConvertFlagAndBatchStatusOrderByUpdateTimeAsc(
                FileCategory.ZIP_SPLIT,
                TransferStatus.SUCCESS,
                ProcessStatus.CREATE,
                1,
                SftpBatchStatus.SUCCESS,
                Pageable.ofSize(1)
        );
    }

    @Override
    public AtomicInteger getThreadActiveCount() {
        return threadActiveCount;
    }

    @Override
    public String getNeedConvertLocalPath() {
        return needConvertLocalPath;
    }

    @Override
    public String getConvertLocalPath() {
        return convertLocalPath;
    }

    @Override
    public SftpFileDetailRepository getSftpFileDetailRepository() {
        return sftpFileDetailRepository;
    }

    @Override
    public FileLoadingLogRepository getFileLoadingLogRepository() {
        return fileLoadingLogRepository;
    }

    @Override
    public FileWarehousingRecordManager getFileWarehousingRecordManager() {
        return fileWarehousingRecordManager;
    }

    @Override
    public HDFSUtil getHdfsUtil() {
        return hdfsUtil;
    }

    @Override
    public HdfsConfig getHdfsConfig() {
        return hdfsConfig;
    }

    @Override
    public ThreadPoolExecutor getThreadPoolExecutor() {
        return THREAD_POOL_EXECUTOR;
    }

    @Override
    public KafkaSink getKafkaSink() {
        return kafkaSink;
    }

    @Override
    public ZipFileCollectionToStringConverter getZipFileCollectionToStringConverter() {
        return ZipFileCollectionToStringConverter.getInstance();
    }

    @Override
    public SftpBatchInfoRepository getSftpBatchInfoRepository() {
        return sftpBatchInfoRepository;
    }

    @Override
    public Long getZipSplitFileMaxWaitMillion() {
        return zipSplitFileMaxWaitMillion;
    }
}
