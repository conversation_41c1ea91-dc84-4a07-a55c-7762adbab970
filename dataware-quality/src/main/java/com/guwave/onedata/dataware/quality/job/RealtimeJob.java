package com.guwave.onedata.dataware.quality.job;

import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.QualityRule;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.QualityRuleRepository;
import com.guwave.onedata.dataware.quality.service.QualityCheckService;
import com.guwave.onedata.dataware.quality.service.YarnStatisticsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RealtimeJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(RealtimeJob.class);

    @Autowired
    private QualityCheckService qualityCheckService;

    @Autowired
    private YarnStatisticsService yarnStatisticsService;
    @Autowired
    private QualityRuleRepository qualityRuleRepository;

    @ScheduleSwitch
    @Scheduled(cron = "${spring.scheduler.timer.minuteTask}")
    public void yarnStatisticsTask() {
        try {
            List<QualityRule> qualityRules = qualityRuleRepository.findAllByMysqlQuerySqlAndCkTempTableSqlAndCkQuerySql(Constant.EMPTY, Constant.EMPTY, Constant.EMPTY);
            yarnStatisticsService.doYarnStatistics(qualityRules);
        } catch (Exception e) {
            LOGGER.error("yarnStatisticsTask 异常：", e);
        }
    }

    @ScheduleSwitch
    @Scheduled(cron = "${spring.scheduler.timer.realtimeTask}")
    public void pullTask() {
        try {
            List<QualityRule> qualityRules = qualityRuleRepository.findAllByExecuteTimeTypeAndStatusAndDeleteFlag(2, 1, 0);
            qualityCheckService.qualityCheck(qualityRules, false);
        } catch (Exception e) {
            LOGGER.error("RealtimeJob 异常：", e);
        }
    }
}
