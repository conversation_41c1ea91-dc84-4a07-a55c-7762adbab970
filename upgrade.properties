# 需要部署的步骤
kill_app=1
exec_patch=1
start_app=1
run_spark=0

# 配置文件修改,多个配置用|分隔
# dataware_collectx
dataware_collectx_add_before=
dataware_collectx_add_after=
dataware_collectx_delete=
dataware_collectx_update=
# dataware_source_agent_cp
dataware_source_agent_cp_add_before=
dataware_source_agent_cp_add_after=
dataware_source_agent_cp_delete=
dataware_source_agent_cp_update=
# dataware_source_agent_ft
dataware_source_agent_ft_add_before=
dataware_source_agent_ft_add_after=
dataware_source_agent_ft_delete=
dataware_source_agent_ft_update=
# dataware_source_agent_wat
dataware_source_agent_wat_add_before=
dataware_source_agent_wat_add_after=
dataware_source_agent_wat_delete=
dataware_source_agent_wat_update=
# dataware_source_agent_manual
dataware_source_agent_manual_add_before=
dataware_source_agent_manual_add_after=
dataware_source_agent_manual_delete=
dataware_source_agent_manual_update=
# dataware_repair_engine
dataware_repair_engine_add_before=
dataware_repair_engine_add_after=
dataware_repair_engine_delete=
dataware_repair_engine_update=
# dataware_scheduler
dataware_scheduler_add_before=
dataware_scheduler_add_after=
dataware_scheduler_delete=
dataware_scheduler_update=
# dataware_dw_die
dataware_dw_die_add_before=
dataware_dw_die_add_after="hdfsUrl:localPathPrefix=/data"
dataware_dw_die_delete=
dataware_dw_die_update="handler.file.writePath=/deploy/onedata/dataware/dataware-dw-die/data/write/"
# dataware_dw_test_item
dataware_dw_test_item_add_before=
dataware_dw_test_item_add_after="settings.needClearInvalidDataCustomer:rust.maxThreads=16|rust.maxThreads:hdfsMode=HA|hdfsMode:hdfsUrl=hdfs://gdp01.guwave.com:8020,hdfs://gdp02.guwave.com:8020|hdfsUrl:localPathPrefix=/data"
dataware_dw_test_item_delete=
dataware_dw_test_item_update="data.clickhouse.ckBatchSize=50000"
# dataware_dw_manual dwd
dataware_dw_dwd_manual_add_before=
dataware_dw_dwd_manual_add_after=
dataware_dw_dwd_manual_delete=
dataware_dw_dwd_manual_update=
# dataware_dw_manual dws
dataware_dw_dws_manual_add_before=
dataware_dw_dws_manual_add_after=
dataware_dw_dws_manual_delete=
dataware_dw_dws_manual_update=
# dataware_dw_manual ads
dataware_dw_ads_manual_add_before=
dataware_dw_ads_manual_add_after=
dataware_dw_ads_manual_delete=
dataware_dw_ads_manual_update=
# dataware_dw_standalone
dataware_dw_standalone_add_before=
dataware_dw_standalone_add_after=
dataware_dw_standalone_delete=
dataware_dw_standalone_update=
# dataware_dw_quality：
dataware_quality_add_before=
dataware_quality_add_after=
dataware_quality_delete=
dataware_quality_update=

# 需要增加的topic，多个之间用英文逗号分割
dataware_topics_add=

# spark任务,多个用逗号分隔
returnable_spark_task=
not_returnable_spark_task=

migration_table="dwd.dwd_wip_detail_local:dwd.dwd_wip_detail_local_344_bak"

# 以下配置由研发维护
declare -A properties_map
properties_map["database.address"]="mysql_address"
properties_map["database.name"]="mysql_onedata_database"
properties_map["database.username"]="mysql_onedata_username"
properties_map["database.password"]="mysql_onedata_password"
properties_map["data.mysql.address"]="mysql_onedata_address"
properties_map["data.mysql.username"]="mysql_onedata_username"
properties_map["data.mysql.password"]="mysql_onedata_password"
properties_map["redis.host"]="redis_host"
properties_map["redis.port"]="redis_port"
properties_map["redis.password"]="redis_password"
properties_map["redis.lockExpireTime"]="redis_lockExpireTime"
properties_map["redis.unlockExpireTime"]="redis_unlockExpireTime"
properties_map["redisAddress"]="redis_address"
properties_map["redisPassword"]="redis_password"
properties_map["kafka.bootstrapServers"]="kafka_bootstrap_servers"
properties_map["gdp.file.hdfsMode"]="hdfs_mode"
properties_map["gdp.file.hdfsUrl"]="hdfs_url"
properties_map["gdp.file.hdfsUser"]="hdfs_user"
properties_map["handler.file.hdfsMode"]="hdfs_mode"
properties_map["handler.file.hdfsUrl"]="hdfs_url"
properties_map["handler.file.hdfsUser"]="hdfs_user"
properties_map["adapter.python.install.path"]="adapter_python_install_path"
properties_map["licenseBaseUrl"]="license_base_url"
properties_map["zookeeper.address"]="zookeeper_address"
properties_map["data.clickhouse.address"]="ck_address"
properties_map["data.clickhouse.username"]="ck_username"
properties_map["data.clickhouse.password"]="ck_password"
properties_map["data.clickhouse.odsDbName"]="ck_ods_db"
properties_map["data.clickhouse.dwsDbName"]="ck_dws_db"
properties_map["data.clickhouse.dwdDbName"]="ck_dwd_db"
properties_map["sink.ck.ods.dbName"]="ck_ods_db"
properties_map["sink.ck.dim.dbName"]="ck_dim_db"
properties_map["sink.ck.dwd.dbName"]="ck_dwd_db"
properties_map["task.onedata.dataware.dwd.odsDbName"]="ck_ods_db"
properties_map["task.onedata.dataware.dwd.dwdDbName"]="ck_dwd_db"
properties_map["task.onedata.dataware.dim.dimDbName"]="ck_dim_db"
properties_map["task.onedata.dataware.dws.dwsDbName"]="ck_dws_db"
properties_map["task.onedata.dataware.ads.adsDbName"]="ck_ads_db"
properties_map["task.onedata.dataware.meta.metaDbName"]="ck_meta_db"
properties_map["data.clickhouse.ckAddress"]="ck_address"
properties_map["data.clickhouse.ckQueryNodeHost"]="data_clickhouse_remote_address"
properties_map["data.clickhouse.ckUsername"]="ck_username"
properties_map["data.clickhouse.ckPassword"]="ck_password"
properties_map["data.clickhouse.ckNodeHost"]="ck_node_host"
properties_map["data.clickhouse.ckNodeUser"]="ck_node_user"
properties_map["data.clickhouse.ckNodePassword"]="ck_node_password"
properties_map["minio.endpoint"]="minio_endpoint"
properties_map["minio.accessKey"]="minio_accessKey"
properties_map["minio.secretKey"]="minio_secretKey"
properties_map["minio.bucket"]="minio_bucket"
properties_map["hdfsMode"]="hdfs_mode"
properties_map["hdfsUrl"]="hdfs_url"
