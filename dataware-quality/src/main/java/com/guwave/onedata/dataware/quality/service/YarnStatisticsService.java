package com.guwave.onedata.dataware.quality.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.QualityRealtimeResult;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.QualityRule;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.QualityRealtimeResultRepository;
import com.guwave.onedata.next.compute.api.iface.IYarnStatisticsService;
import com.guwave.onedata.next.compute.api.vo.response.YarnStatisticsResponse;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class YarnStatisticsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(YarnStatisticsService.class);

    @DubboReference
    private IYarnStatisticsService yarnStatisticsService;

    @Autowired
    private QualityRealtimeResultRepository qualityRealtimeResultRepository;

    public YarnStatisticsResponse getStatistics(String queueName) {
        return yarnStatisticsService.getStatistics(queueName);
    }

    public void doYarnStatistics(List<QualityRule> qualityRules) {
        if (qualityRules == null || qualityRules.isEmpty()) {
            return;
        }

        for (QualityRule qualityRule : qualityRules) {
            Optional.ofNullable(qualityRule.getQualityName())
                    .map(String::trim)
                    .filter(name -> !name.isEmpty())
                    .ifPresent(queueName -> processQueueStatistics(qualityRule, queueName));
        }
    }

    private void processQueueStatistics(QualityRule qualityRule, String queueName) {
        YarnStatisticsResponse response = getStatistics(queueName);
        if (response == null) {
            LOGGER.info("未找到queueName为{}的统计信息", queueName);
            return;
        }

        Map<String, Object> extractedFields = extractFieldsFromResponse(response);
        String resultJson = toJsonString(extractedFields);

        QualityRealtimeResult qualityResult = createQualityResult(qualityRule, response, resultJson);
        LOGGER.info("qualityResult内容为：{}", JSON.toJSONString(qualityResult, SerializerFeature.WriteMapNullValue));
        qualityRealtimeResultRepository.save(qualityResult);
    }

    private Map<String, Object> extractFieldsFromResponse(YarnStatisticsResponse response) {
        Map<String, Object> extractedFields = new HashMap<>();
        // extractedFields.put("queueMemoryLimit", response.getQueueMemoryLimit());
        extractedFields.put("queueYarnCapacityLimit", response.getQueueYarnCapacityLimit());
        extractedFields.put("queueYarnUsagePercentage", response.getQueueYarnUsagePercentage());
        extractedFields.put("taskQueueUsagePercentage", response.getTaskQueueUsagePercentage());
        return extractedFields;
    }

    private String toJsonString(Map<String, Object> map) {
        return JSON.toJSONString(Arrays.asList(map), SerializerFeature.WriteMapNullValue);
    }

    private QualityRealtimeResult createQualityResult(QualityRule qualityRule, YarnStatisticsResponse response, String resultJson) {
        return new QualityRealtimeResult()
                .setQualityName(qualityRule.getQualityName())
                .setDayKey(formatDate(response.getStatisticsTime(), "yyyyMMdd"))
                .setTimeKey(formatDate(response.getStatisticsTime(), "HHmmss"))
                .setResult(resultJson)
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setCreateUser(Constant.SYSTEM)
                .setUpdateUser(Constant.SYSTEM)
                .setQualityRuleId(qualityRule.getId());
    }

    private String formatDate(long time, String pattern) {
        return DateFormatUtils.format(new Date(time), pattern);
    }
}
