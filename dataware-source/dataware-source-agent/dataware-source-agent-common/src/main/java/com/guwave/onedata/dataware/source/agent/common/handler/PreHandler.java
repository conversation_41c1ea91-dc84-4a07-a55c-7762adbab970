package com.guwave.onedata.dataware.source.agent.common.handler;

import com.guwave.onedata.dataware.dao.mysql.domain.dc.FileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileLoadingLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotMetaDataDetail;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ProcessLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileDetail;

import java.io.File;
import java.util.List;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * Handler
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-19 16:48:37
 */
public interface PreHandler extends Handler {

    List<SftpFileDetail> getNeedPreDealSftpFiles();

    LotMetaDataDetail preDealFile(File uncompressFile, SftpFileDetail sftpFileDetail, FileInfo fileInfo, ProcessLog processLog, FileLoadingLog fileLoadingLog) throws Exception;
}
