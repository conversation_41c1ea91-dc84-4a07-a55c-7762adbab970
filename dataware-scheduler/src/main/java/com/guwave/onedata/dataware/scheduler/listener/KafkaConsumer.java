package com.guwave.onedata.dataware.scheduler.listener;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import com.guwave.onedata.dataware.scheduler.handler.Handler;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * KafkaConsumer
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-12-29 11:10:41
 */
@Component
public class KafkaConsumer {

    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaConsumer.class);

    @Autowired
    private List<Handler> handlers;

    @KafkaListener(topics = "${spring.kafka.loadEndFlagTopic}")
    public void consumer(ConsumerRecord<byte[], byte[]> record) {
        try {
            String jsonStr = new String(record.value());
            CalculateEndFlag endFlag = JSON.parseObject(jsonStr, CalculateEndFlag.class);
            LOGGER.info("开始处理kafka数据, topic: {}, message: {}", record.topic(), jsonStr);
            handlers.forEach(handler -> {
                if (handler.isSupport(endFlag)) {
                    handler.doHandle(endFlag);
                }
            });
            LOGGER.info("处理kafka数据结束, topic: {}", record.topic());
        } catch (Exception e) {
            LOGGER.error("处理kafka数据异常, topic: {}", record.topic(), e);
        }
    }

}
