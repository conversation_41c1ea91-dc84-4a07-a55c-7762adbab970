package com.guwave.onedata.dataware.source.agent.manual.model;

import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;

import java.util.ArrayList;
import java.util.List;

import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;


public class ManualLotWafer {


    private String customer = EMPTY;

    private String subCustomer = EMPTY;

    private TestArea testArea;

    private String factory = EMPTY;

    private String factorySite = EMPTY;

    private String deviceId = EMPTY;

    private String lotId = EMPTY;

    private String waferNo = EMPTY;

    private LotType lotType;

    private String testStage = EMPTY;

    private FileCategory fileCategory;

    private List<ManualFileInfo> originFileInfos = new ArrayList<>();

    public String getCustomer() {
        return customer;
    }

    public ManualLotWafer setCustomer(String customer) {
        this.customer = customer;
        return this;
    }

    public String getSubCustomer() {
        return subCustomer;
    }

    public ManualLotWafer setSubCustomer(String subCustomer) {
        this.subCustomer = subCustomer;
        return this;
    }

    public TestArea getTestArea() {
        return testArea;
    }

    public ManualLotWafer setTestArea(TestArea testArea) {
        this.testArea = testArea;
        return this;
    }

    public String getFactory() {
        return factory;
    }

    public ManualLotWafer setFactory(String factory) {
        this.factory = factory;
        return this;
    }

    public String getFactorySite() {
        return factorySite;
    }

    public ManualLotWafer setFactorySite(String factorySite) {
        this.factorySite = factorySite;
        return this;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public ManualLotWafer setDeviceId(String deviceId) {
        this.deviceId = deviceId;
        return this;
    }

    public String getLotId() {
        return lotId;
    }

    public ManualLotWafer setLotId(String lotId) {
        this.lotId = lotId;
        return this;
    }

    public String getWaferNo() {
        return waferNo;
    }

    public ManualLotWafer setWaferNo(String waferNo) {
        this.waferNo = waferNo;
        return this;
    }

    public LotType getLotType() {
        return lotType;
    }

    public ManualLotWafer setLotType(LotType lotType) {
        this.lotType = lotType;
        return this;
    }

    public String getTestStage() {
        return testStage;
    }

    public ManualLotWafer setTestStage(String testStage) {
        this.testStage = testStage;
        return this;
    }

    public FileCategory getFileCategory() {
        return fileCategory;
    }

    public ManualLotWafer setFileCategory(FileCategory fileCategory) {
        this.fileCategory = fileCategory;
        return this;
    }

    public List<ManualFileInfo> getOriginFileInfos() {
        return originFileInfos;
    }

    public ManualLotWafer setOriginFileInfos(List<ManualFileInfo> originFileInfos) {
        this.originFileInfos = originFileInfos;
        return this;
    }
}
