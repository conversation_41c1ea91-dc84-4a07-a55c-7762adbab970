package com.guwave.onedata.dataware.collectx.service;

import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.collectx.util.AdapterUtil;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileLoadingLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LogMapDetail;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileDetail;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LogMapDetailRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.data.domain.Pageable;

import java.io.File;
import java.util.*;

public interface MutilSftpFileMergeService extends DataConvertService {


    default int updateStatusBeforeDeal(SftpFileDetail needConvertFile) {
        LogMapDetail logMapDetail = needConvertFile.getLogMapDetail();
        // 更新处理状态
        logMapDetail
                .setProcessStatus(ProcessStatus.PROCESSING)
                .setUpdateTime(new Date());
        return getLogMapDetailRepository().updateProcessStatusProcessingFromCreate(logMapDetail.getId(), logMapDetail.getUpdateTime());
    }

    LogMapDetailRepository getLogMapDetailRepository();

    default Boolean getNeedCompressAndUploadResultFileFlag() {
        return Boolean.TRUE;
    }

    default void readFile(SftpFileDetail needConvertFile, String originalFilePath, String executeScriptPath, FileLoadingLog fileLoadingLog) {
        // do nothing
    }

    default void updateStatusAfterDealSuccess(SftpFileDetail needConvertFile) {
        // 设置处理状态
        LogMapDetail logMapDetail = needConvertFile.getLogMapDetail();
        logMapDetail.setProcessStatus(ProcessStatus.SUCCESS);
    }

    default void updateFileLoadingLogStatusAfterDealSuccess(FileLoadingLog fileLoadingLog) {
        fileLoadingLog.setProcessStatus(ProcessStatus.SUCCESS);
    }

    default void updateStatusAfterDealException(SftpFileDetail needConvertFile, Exception e) {
        // 设置处理状态
        LogMapDetail logMapDetail = needConvertFile.getLogMapDetail();
        if (e instanceof FileLoadException && Objects.equals(((FileLoadException) e).getFailedType(), FileLoadExceptionInfo.MAP_FILE_NOT_FOUND_EXCEPTION.name())) {
            // 如果是log未找到对应的map文件  则不失败 等待下次继续处理
            log.info(e.getMessage());
            logMapDetail.setProcessStatus(ProcessStatus.CREATE);
        } else {
            log.error("转换文件异常：", e);
            logMapDetail.setProcessStatus(ProcessStatus.FAIL);
        }
    }

    default void updateStatusFinally(SftpFileDetail needConvertFile) {
        // 更新处理状态
        LogMapDetail logMapDetail = needConvertFile.getLogMapDetail();
        logMapDetail.setUpdateTime(new Date());
        getLogMapDetailRepository().save(logMapDetail);
    }

    default void updateFileLoadingLogStatusFinally(FileLoadingLog fileLoadingLog) {
        fileLoadingLog.setStepEndTime(new Date())
                .setUpdateTime(new Date());
        getFileLoadingLogRepository().save(fileLoadingLog);
    }

    default Pair<Boolean, HashMap<Object, Object>> convertFile(SftpFileDetail needConvertFile, String originalFilePath, String convertFilePath, String executeScriptPath, FileLoadingLog fileLoadingLog,FileLoadingLog sourceFileLoadingLog) throws Exception {
        LogMapDetail logDetail = needConvertFile.getLogMapDetail();
        List<LogMapDetail> mapDetails = getLogMapDetailRepository().findByCustomerAndTestAreaAndFactoryAndFactorySiteAndFtpIpAndFileCategoryAndDeviceIdAndLotIdAndWaferNoAndTestStageAndFlowIdOrderByIdDesc(
                logDetail.getCustomer(),
                logDetail.getTestArea(),
                logDetail.getFactory(),
                logDetail.getFactorySite(),
                logDetail.getFtpIp(),
                FileCategory.MAP,
                logDetail.getDeviceId(),
                logDetail.getLotId(),
                logDetail.getWaferNo(),
                logDetail.getTestStage(),
                logDetail.getFlowId(),
                Pageable.ofSize(1)
        );
        if (CollectionUtils.isEmpty(mapDetails)) {
            throw new FileLoadException(FileLoadExceptionInfo.MAP_FILE_NOT_FOUND_EXCEPTION, "logDetail " + logDetail.getId() + " 对应的mapDetail 不存在", null);
        }

        LogMapDetail mapDetail = mapDetails.get(0);
        SftpFileDetail mapSftpFileDetail = getSftpFileDetailRepository().findById(mapDetail.getSftpFileId()).orElse(null);
        if (mapSftpFileDetail == null) {
            log.error("mapDetail 的 sftpFileId {} 对应的SftpFileDetail不存在", mapDetail.getSftpFileId());
            throw new RuntimeException("mapDetail 的 sftpFileId " + mapDetail.getSftpFileId() + " 对应的SftpFileDetail不存在");
        }

        Date date = new Date();
        //记录转换钱文件名
        fileLoadingLog
                .setSourceFileNames(mapSftpFileDetail.getLocalFileName() + Constant.COMMA + needConvertFile.getLocalFileName())
                .setStepStartTime(date)
                .setCreateTime(date)
                .setProcessStatus(ProcessStatus.PROCESSING)
                .setDeviceId(logDetail.getDeviceId())
                .setLotId(logDetail.getLotId())
                .setWaferNo(logDetail.getWaferNo())
                .setTestStage(logDetail.getTestStage())
        ;

        File logFile = new File(originalFilePath);
        // 下载并解压log对应的map文件
        File compressMapSftpFile = new File(logFile.getParent(), mapSftpFileDetail.getLocalFileName());
        downloadAndUncompressHdfsFile(compressMapSftpFile.getName(), compressMapSftpFile.getParent() + Constant.SLASH, mapSftpFileDetail.getHdfsFilePath());
        if (!compressMapSftpFile.exists()) {
            log.error("{} map压缩文件不存在！", compressMapSftpFile.getAbsolutePath());
            throw new RuntimeException(compressMapSftpFile.getAbsolutePath() + "map压缩文件不存在！");
        }

        // map文件的全路径
        File mapFile = new File(compressMapSftpFile.getParent() + Constant.SLASH + removeFileSuffix(compressMapSftpFile.getName(), FileType.ZIP), removeFileSuffix(compressMapSftpFile.getName(), FileType.ZIP));

        log.info("convert file : log：{}，map：{}  ----->  {}", originalFilePath, mapFile.getAbsolutePath(), convertFilePath);
        Pair<Boolean, HashMap<Object, Object>> resultMap = AdapterUtil.executePython(executeScriptPath, Lists.newArrayList(originalFilePath, mapFile.getAbsolutePath(), convertFilePath, needConvertFile.getOriginFileName(), mapSftpFileDetail.getOriginFileName()), getLogConsumer(fileLoadingLog,sourceFileLoadingLog));
        if (resultMap.getValue() != null && resultMap.getValue().containsKey(ConvertStatus.TO_MANY_SUCCESS_PREFIX.getStatus())) {
            String convertFileNames = String.valueOf(resultMap.getValue().get(ConvertStatus.TO_MANY_SUCCESS_PREFIX.getStatus()));
            log.info("原始文件：{} 转换后生成多个文件: {}", fileLoadingLog.getSourceFileNames(), convertFileNames);
            saveSftpFileConvertRecord(convertFileNames, fileLoadingLog.getSourceFileNames());
        }
        return resultMap;
    }

    default FileCategory getConvertResultFileCategory() {
        return FileCategory.RAW_DATA;
    }
}
