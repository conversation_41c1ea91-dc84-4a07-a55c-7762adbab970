package com.guwave.onedata.dataware.source.agent.wat.handler.impl;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.wat.ods.OdsWat;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileDetail;
import com.guwave.onedata.dataware.source.agent.common.exception.FailException;
import com.guwave.onedata.dataware.source.agent.common.function.ThirdConsumer;
import com.guwave.onedata.dataware.source.agent.common.util.FileUtil;
import com.guwave.onedata.dataware.source.agent.wat.handler.Handler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
public class WatConsistentHandle implements Handler {
    private static final String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private static final Map<String, ThirdConsumer<String, String, OdsWat>> WAT_FIELD_CONSUMER_MAP = new HashMap<String, ThirdConsumer<String, String, OdsWat>>() {{
        put("LOT_ID", (head, str, odsWat) -> odsWat.setLotId(str));
        put("WAFER_NO", (head, str, odsWat) -> odsWat.setWaferNo(Integer.valueOf(str).toString()));
        put("TEST_TXT", (head, str, odsWat) -> odsWat.setTestTxt(str));
        put("TEST_VALUE", (head, str, odsWat) -> odsWat.setTestValue(Double.valueOf(str)));
        put("TEST_NUM", (head, str, odsWat) -> odsWat.setTestNum(Long.valueOf(str)));
        put("HI_LIMIT", (head, str, odsWat) -> odsWat.setHiLimit(Double.valueOf(str)));
        put("LO_LIMIT", (head, str, odsWat) -> odsWat.setLoLimit(Double.valueOf(str)));
        put("TARGET", (head, str, odsWat) -> odsWat.setTarget(Double.valueOf(str)));
        put("UNITS", (head, str, odsWat) -> odsWat.setUnits(str));
        put("TEST_RESULT", (head, str, odsWat) -> odsWat.setTestResult(getIntegerValue(str)));
        put("DEVICE_ID", (head, str, odsWat) -> odsWat.setDeviceId(str));
        put("START_TIME", (head, str, odsWat) -> odsWat.setStartTime(getTimeStamp(str)));
        put("END_TIME", (head, str, odsWat) -> odsWat.setEndTime(getTimeStamp(str)));
        put("TEST_COD", (head, str, odsWat) -> odsWat.setTestCod(str));
//        put("TESTITEM_TYPE", (head, str, odsWat) -> odsWat.setTestitemType(str));
        put("TEST_PROGRAM", (head, str, odsWat) -> odsWat.setTestProgram(str));
        put("WAFER_ID", (head, str, odsWat) -> odsWat.setWaferId(str));
        put("FABWF_ID", (head, str, odsWat) -> odsWat.setFabwfId(str));
        put("TEST_STAGE", (head, str, odsWat) -> odsWat.setTestStage(StringUtils.isNotBlank(str) ? str : Constant.ODS_WAT_TEST_STAGE));
        put("RETICLE_X", (head, str, odsWat) -> odsWat.setReticleX(getIntegerValue(str)));
        put("RETICLE_Y", (head, str, odsWat) -> odsWat.setReticleY(getIntegerValue(str)));
        put("SITE_ID", (head, str, odsWat) -> odsWat.setSiteId(str));
        put("WF_FLAT", (head, str, odsWat) -> odsWat.setWfFlat(str));
        put("TEST_TEMPERATURE", (head, str, odsWat) -> odsWat.setTestTemperature(str));
        put("TESTER_NAME", (head, str, odsWat) -> odsWat.setTesterName(str));
        put("OPERATOR_NAME", (head, str, odsWat) -> odsWat.setOperatorName(str));
        put("PROCESS", (head, str, odsWat) -> odsWat.setProcess(str));
        put("HI_SPEC", (head, str, odsWat) -> odsWat.setHiSpec(getDoubleValue(str)));
        put("LO_SPEC", (head, str, odsWat) -> odsWat.setLoSpec(getDoubleValue(str)));
        put("LOT_TYPE", (head, str, odsWat) -> odsWat.setLotType(str));
        put("CONDITION_SET", (head, str, odsWat) -> odsWat.setConditionSet(getMap(str)));
    }};

    @Override
    public boolean support(SftpFileDetail sftpFileDetail) {
        return true;
    }

    @Override
    public List<OdsWat> handle(SftpFileDetail sftpFileDetail) {
        return null;
    }

    public List<OdsWat> handle(File uncompressFile, SftpFileDetail sftpFileDetail) {
        List<String> lines = FileUtil.read(uncompressFile.getAbsolutePath());

        if (lines.size() < 2) {
            LOGGER.info("{} 文件为空", sftpFileDetail.getLocalFileName());
            throw new FailException("文件为空");
        }

        String headLine = lines.get(0);
        String[] headSplit = headLine.split(Constant.TAB, -1);
        ArrayList<OdsWat> odsWatList = new ArrayList<>();
        for (int i = 1; i < lines.size(); i++) {
            String line = lines.get(i);
            if (StringUtils.isNotEmpty(line)) {
                OdsWat odsWat = new OdsWat();
                odsWat
                        .setCustomer(sftpFileDetail.getCustomer())
                        .setSubCustomer(sftpFileDetail.getSubCustomer())
                        .setFactory(sftpFileDetail.getFactory())
                        .setFactorySite(sftpFileDetail.getFactorySite())
                        .setFab(sftpFileDetail.getFab())
                        .setFabSite(sftpFileDetail.getFabSite())
                        .setTestArea(sftpFileDetail.getTestArea().getArea());
                String[] strSplit = line.split(Constant.TAB, -1);

                if (headSplit.length != strSplit.length) {
                    String errorMessage = String.format("WAT文件%s数据异常, 表头与数据行的列字段不匹配，异常数据在第%s行", sftpFileDetail.getLocalFileName(), i);
                    LOGGER.error(errorMessage);
                    throw new FileLoadException(FileLoadExceptionInfo.HEADER_NOT_MATCH_DATA_ROW_FIELD_EXCEPTION, errorMessage, null);
                }
                for (int j = 0; j < strSplit.length; j++) {
                    String head = headSplit[j].trim();
                    String str = strSplit[j].trim();
                    ThirdConsumer<String, String, OdsWat> consumer = WAT_FIELD_CONSUMER_MAP.get(head);
                    if (consumer != null && StringUtils.isNotEmpty(str) && StringUtils.isNotEmpty(head)) {
                        try {
                            consumer.accept(head, str, odsWat);
                        } catch (Exception e) {
                            String errorMessage = String.format("WAT文件%s数据异常, 异常数据在第%s行, %s列, columnName=%s, columnValue=%s", sftpFileDetail.getLocalFileName(), i + 1, j + 1, head, str);
                            LOGGER.error(errorMessage, e);
                            throw new RuntimeException(errorMessage, e);
                        }
                    }
                }
                odsWatList.add(odsWat);
            }
        }
        return odsWatList;
    }

    private static Long getLongValue(String str) {
        return str.isEmpty() ? null : Double.valueOf(str).longValue();
    }

    private static Integer getIntegerValue(String str) {
        return str.isEmpty() ? null : Double.valueOf(str).intValue();
    }

    private static Double getDoubleValue(String str) {
        return str.isEmpty() ? null : Double.valueOf(str);
    }

    private static Long getTimeStamp(String str) {
        if (str.isEmpty()) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(TIME_FORMAT);
        try {
            Date date = simpleDateFormat.parse(str);
            return date.getTime();
        } catch (ParseException e) {
            LOGGER.info("wat时间转换异常", e);
        }
        return null;
    }

    private static Map<String, String> getMap(String str) {
        Map<String, String> result = new HashMap<>();
        if (str.isEmpty()) {
            return result;
        }
        Map map = JSON.parseObject(str, Map.class);
        for (Object key : map.keySet()) {
            result.put(key.toString(), map.get(key).toString());
        }
        return result;
    }
}
