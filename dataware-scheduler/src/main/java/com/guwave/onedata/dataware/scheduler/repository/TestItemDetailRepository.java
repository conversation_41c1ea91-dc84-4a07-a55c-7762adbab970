package com.guwave.onedata.dataware.scheduler.repository;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.scheduler.provider.CkProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 2023/7/5 16:38
 * DwsFlowidBinIndex
 * <AUTHOR>
 */
@Component
public class TestItemDetailRepository {

    private static final Logger LOGGER = LoggerFactory.getLogger(TestItemDetailRepository.class);


    @Autowired
    private CkProvider ckProvider;

    List<TestArea> SUPPORT_CP_TEST_AREA_LIST = TestArea.getCPList();

    private static final String SQL_TEMPLATE = "select count(1) from {DB_NAME}.dwd_test_item_detail_cluster where IS_DELETE = '0' " +
            "and  CUSTOMER = '{CUSTOMER}' and UPLOAD_TYPE = 'AUTO' and TEST_AREA = '{TEST_AREA}' and FACTORY = '{FACTORY}' " +
            "and DEVICE_ID = '{DEVICE_ID}' " +
            "and LOT_TYPE = '{LOT_TYPE}' " +
            "and TEST_STAGE = '{TEST_STAGE}' " +
            "and LOT_ID = '{LOT_ID}'";
    private final String WAFER_NO_TEMPLATE = " and WAFER_NO = '{WAFER_NO}'";

    public Long countData(String dataBase, String customer, String factory, TestArea testArea, String deviceId, LotType lotType, String testStage, String lotId, String waferNo) {
        String sql = SQL_TEMPLATE.replace(Constant.DB_NAME, dataBase)
                .replace(Constant.CUSTOMER, customer)
                .replace(Constant.TEST_AREA, testArea.getArea())
                .replace(Constant.FACTORY, factory)
                .replace(Constant.DEVICE_ID, deviceId)
                .replace(Constant.LOT_TYPE, lotType.getType())
                .replace(Constant.TEST_STAGE, testStage)
                .replace(Constant.LOT_ID, lotId);
        if (SUPPORT_CP_TEST_AREA_LIST.contains(testArea)) {
            sql = sql + WAFER_NO_TEMPLATE.replace(Constant.WAFER_NO, waferNo);
        }

        LOGGER.info("查询sql: {}", sql);
        return ckProvider.count(sql);
    }
}
