package com.guwave.onedata.dataware.collectx.rpc;

import com.guwave.onedata.dataware.bridge.api.iface.ICollectxImmediatelyRunRpcService;
import com.guwave.onedata.dataware.bridge.api.vo.response.CollectxImmediatelyRunRes;
import com.guwave.onedata.dataware.collectx.manager.redis.SftpFileRedisManager;
import com.guwave.onedata.dataware.collectx.service.impl.PullSftpTaskService;
import com.guwave.onedata.dataware.collectx.util.ThreadPoolUtils;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpCollectImmediatelyRunRecord;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpCollectRule;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpCollectImmediatelyRunRecordRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpCollectRuleRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

@DubboService
@Component
public class CollectxImmediatelyRunRpcServiceImpl implements ICollectxImmediatelyRunRpcService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CollectxImmediatelyRunRpcServiceImpl.class);

    private final static String THREAD_PREFIX = "immediately_pull_sftp_task";
    private final AtomicInteger threadActiveCount = new AtomicInteger(0);
    private ThreadPoolExecutor THREAD_POOL_EXECUTOR;

    @Value("${spring.immediatelyRunTask.convertCorePoolSize}")
    private Integer convertCorePoolSize;

    @Autowired
    private SftpCollectRuleRepository sftpCollectRuleRepository;
    @Autowired
    private PullSftpTaskService pullSftpTaskService;
    @Autowired
    private SftpCollectImmediatelyRunRecordRepository sftpCollectImmediatelyRunRecordRepository;

    @PostConstruct
    public void init() {
        THREAD_POOL_EXECUTOR = ThreadPoolUtils.getNewThreadPoolExecutor(
                THREAD_PREFIX, convertCorePoolSize, convertCorePoolSize, convertCorePoolSize
        );
    }

    @ScheduleSwitch
    @Scheduled(cron = "${spring.scheduler.timer.pullSftpCollectTask}")
    public void pullTask() {
        try {
            if (threadActiveCount.get() >= convertCorePoolSize) {
                LOGGER.info("运行任务达上限");
                return;
            }
            List<SftpCollectImmediatelyRunRecord> records = sftpCollectImmediatelyRunRecordRepository.findByProcessStatusOrderById(ProcessStatus.CREATE, Pageable.ofSize(1));
            if (CollectionUtils.isEmpty(records)) {
                return;
            }
            SftpCollectImmediatelyRunRecord record = records.get(0);
            int updateCnt = sftpCollectImmediatelyRunRecordRepository.updateProcessStatus(
                    record.getId(), record.getProcessStatus(), ProcessStatus.READY, Constant.EMPTY, new Date()
            );
            if (updateCnt == 0) {
                LOGGER.info("立即运行任务：{} 已被处理", record.getId());
                return;
            }
            LOGGER.info("开始处理立即运行任务：{} ,CollectRuleId：{}", record.getId(), record.getCollectRuleId());

            Optional<SftpCollectRule> sftpCollectRuleOption = sftpCollectRuleRepository.findById(record.getCollectRuleId());
            if (!sftpCollectRuleOption.isPresent() || !Objects.equals(0, sftpCollectRuleOption.get().getDeleteFlag())) {
                record.setProcessStatus(ProcessStatus.FAIL).setMessage("任务不存在").setUpdateTime(new Date());
                sftpCollectImmediatelyRunRecordRepository.save(record);
                return;
            }
            SftpCollectRule sftpCollectRule = sftpCollectRuleOption.get();
            boolean lockCollectRuleId = SftpFileRedisManager.lockCollectRuleId(sftpCollectRule.getId().toString());
            if (!lockCollectRuleId) {
                record.setProcessStatus(ProcessStatus.FAIL).setMessage("此任务已经在运行").setUpdateTime(new Date());
                sftpCollectImmediatelyRunRecordRepository.save(record);
                return;
            }
            try {
                // 填充必要的拉取设置
                pullSftpTaskService.fillSftpCollectRule(sftpCollectRule, false);

                record.setProcessStatus(ProcessStatus.PROCESSING).setMessage("任务开始运行").setUpdateTime(new Date());
                sftpCollectImmediatelyRunRecordRepository.save(record);

                // 开始执行任务
                threadActiveCount.incrementAndGet();
                THREAD_POOL_EXECUTOR.execute(() -> {
                            try {
                                executeTask(record, sftpCollectRule);
                            } finally {
                                threadActiveCount.decrementAndGet();
                            }
                        }
                );
            } catch (Throwable e) {
                LOGGER.error("collectRuleId : {} 异常", sftpCollectRule.getId(), e);
                try {
                    FileLoadException exception = e instanceof FileLoadException ? (FileLoadException) e : new FileLoadException(FileLoadExceptionInfo.OTHER_EXCEPTION, ExceptionUtils.getStackTrace(e), null);
                    record.setProcessStatus(ProcessStatus.FAIL).setMessage(exception.getExceptionMessage()).setUpdateTime(new Date());
                    sftpCollectImmediatelyRunRecordRepository.save(record);
                } finally {
                    // 解锁
                    SftpFileRedisManager.unLockCollectRuleId(sftpCollectRule.getId().toString());
                }
            }
        } catch (Throwable e) {
            LOGGER.error(e.toString(), e);
        }
    }

    public void executeTask(SftpCollectImmediatelyRunRecord record, SftpCollectRule sftpCollectRule) {
        try {
            // 开始拉取
            pullSftpTaskService.executeTask(sftpCollectRule);

            record.setProcessStatus(ProcessStatus.SUCCESS).setMessage(Constant.EMPTY).setUpdateTime(new Date());
            sftpCollectImmediatelyRunRecordRepository.save(record);
        } catch (Throwable e) {
            LOGGER.error("collectRuleId : {} 异常", sftpCollectRule.getId(), e);
            record.setProcessStatus(ProcessStatus.FAIL).setMessage(ExceptionUtils.getStackTrace(e)).setUpdateTime(new Date());
            sftpCollectImmediatelyRunRecordRepository.save(record);
        } finally {
            // 解锁
            SftpFileRedisManager.unLockCollectRuleId(sftpCollectRule.getId().toString());
        }
    }

    @Override
    public CollectxImmediatelyRunRes immediatelyRun(Long collectRuleId) {
        LOGGER.info("插入待立即运行任务：{}", collectRuleId);
        SftpCollectImmediatelyRunRecord record = new SftpCollectImmediatelyRunRecord()
                .setCollectRuleId(collectRuleId)
                .setProcessStatus(ProcessStatus.CREATE)
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setCreateUser(Constant.SYSTEM)
                .setUpdateUser(Constant.SYSTEM);
        sftpCollectImmediatelyRunRecordRepository.save(record);

        long maxWaitForCreateMs = 3 * 1000;
        boolean recordRunFlag = false;
        long startTime = System.currentTimeMillis();
        long endTime = startTime;
        while (endTime - startTime <= maxWaitForCreateMs) {
            record = sftpCollectImmediatelyRunRecordRepository.findById(record.getId()).get();
            LOGGER.info("校验 {} 状态 {}", record.getId(), record.getProcessStatus());
            recordRunFlag = record.getProcessStatus() != ProcessStatus.CREATE;
            if (recordRunFlag) {
                break;
            }
            try {
                Thread.sleep(900);
            } catch (InterruptedException e) {
                // nothing
            }
            endTime = System.currentTimeMillis();
        }
        String errMsg = "立即运行资源不足";
        if (!recordRunFlag) {
            int updateCnt = sftpCollectImmediatelyRunRecordRepository.updateProcessStatus(
                    record.getId(), record.getProcessStatus(), ProcessStatus.FAIL, errMsg, new Date()
            );
            recordRunFlag = updateCnt == 0;
        }
        if (!recordRunFlag) {
            return CollectxImmediatelyRunRes.error(errMsg);
        }

        long maxWaitForValidMs = 50 * 1000;
        startTime = System.currentTimeMillis();
        endTime = startTime;
        while (endTime - startTime <= maxWaitForValidMs) {
            record = sftpCollectImmediatelyRunRecordRepository.findById(record.getId()).get();
            LOGGER.info("校验 {} 状态 {}", record.getId(), record.getProcessStatus());
            if (record.getProcessStatus() != ProcessStatus.READY) {
                break;
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                // nothing
            }
            endTime = System.currentTimeMillis();
        }

        if (record.getProcessStatus() == ProcessStatus.FAIL) {
            errMsg = record.getMessage();
            return CollectxImmediatelyRunRes.error(errMsg);
        } else if (record.getProcessStatus() == ProcessStatus.READY) {
            errMsg = "等待规则校验结果超时";
            return CollectxImmediatelyRunRes.error(errMsg);
        } else if (record.getProcessStatus() == ProcessStatus.SUCCESS) {
            return CollectxImmediatelyRunRes.ok("任务运行结束");
        } else {
            return CollectxImmediatelyRunRes.ok("任务开始运行");
        }
    }
}