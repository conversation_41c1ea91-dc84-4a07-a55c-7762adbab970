package com.guwave.onedata.dataware.scheduler.listener;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.common.model.message.TestItemFinishMessage;
import com.guwave.onedata.dataware.scheduler.util.ThreadPoolUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;

@Component
public class SparkAutoDwTestItemTaskFinishConsumer {
    private static final Logger LOGGER = LoggerFactory.getLogger(SparkAutoDwTestItemTaskFinishConsumer.class);


    @Autowired
    private DwTestItemAppListener dwTestItemAppListener;

    private Executor executor;

    @PostConstruct
    public void init() {
        executor = ThreadPoolUtils.getNewThreadPoolExecutor(this.getClass().getName(), 20, 20, 10000);
    }

    @KafkaListener(topics = "${spring.kafka.testItemFinishTopic}")
    public void consumertestItemFinish(ConsumerRecord<byte[], byte[]> record) {
        try {
            String jsonStr = new String(record.value());
            LOGGER.info("开始处理kafka数据, topic: {}, message: {}", record.topic(), jsonStr);
            TestItemFinishMessage testItemFinishMessage = JSON.parseObject(jsonStr, TestItemFinishMessage.class);
            dwTestItemAppListener.stateChanged(testItemFinishMessage);
            LOGGER.info("处理kafka数据结束, topic: {}", record.topic());
        } catch (Exception e) {
            LOGGER.error("处理kafka数据异常, topic: {}", record.topic(), e);
        }
    }
}
