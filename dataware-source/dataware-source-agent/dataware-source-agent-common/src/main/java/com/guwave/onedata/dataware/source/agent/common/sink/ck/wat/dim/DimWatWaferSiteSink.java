package com.guwave.onedata.dataware.source.agent.common.sink.ck.wat.dim;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.model.wat.dim.DimWatWafer;
import com.guwave.onedata.dataware.common.model.wat.dim.DimWatWaferSite;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

/**
 * Copyright (C), 2022, guwave
 * <p>
 * DimWatSiteSink
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2022-03-14 11:34:47
 */
@Component
public class DimWatWaferSiteSink implements CkSink<DimWatWaferSite> {

    @Value("${spring.sink.ck.dim.dbName}")
    private String dbName;

    @Override
    public String getTableName() {
        return "dim_wat_site_cluster";
    }

    @Override
    public String getPartitionExpr() {
        return "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{SUB_CUSTOMER}')";
    }

    @Override
    public void handle(PreparedStatement statement, List<DimWatWaferSite> items) throws SQLException {
        for (DimWatWaferSite item : items) {
            // CUSTOMER
            statement.setObject(1, item.getCustomer());
            // UPLOAD_TYPE
            statement.setObject(2, item.getUploadType());
            // DEVICE_ID
            statement.setObject(3, item.getDeviceId());
            // FACTORY
            statement.setObject(4, item.getFactory());
            // FACTORY_SITE
            statement.setObject(5, item.getFactorySite());
            // FAB
            statement.setObject(6, item.getFab());
            // FAB_SITE
            statement.setObject(7, item.getFabSite());
            // LOT_TYPE
            statement.setObject(8, item.getLotType());
            // LOT_ID
            statement.setObject(9, item.getLotId());
            // TEST_AREA
            statement.setObject(10, item.getTestArea());
            // WAFER_ID
            statement.setObject(11, item.getWaferId());
            // FABWF_ID
            statement.setObject(12, item.getFabwfId());
            // START_TIME
            statement.setObject(13, this.toTimestamp(item.getStartTime()));
            // END_TIME
            statement.setObject(14, this.toTimestamp(item.getEndTime()));
            // CREATE_TIME
            statement.setObject(15, new Timestamp(System.currentTimeMillis()));
            // CREATE_USER
            statement.setObject(16, item.getCreateUser());

            // TEST_PROGRAM
            statement.setObject(17, item.getTestProgram());
            // FILE_ID
            statement.setObject(18, item.getFileId());
            // FILE_NAME
            statement.setObject(19, item.getFileName());
            // WF_FLAT
            statement.setObject(20, item.getWfFlat());
            // POS_X
            statement.setObject(21, item.getPosX());
            // POS_Y
            statement.setObject(22, item.getPosY());
            // Process
            statement.setObject(23, item.getProcess());
            // TEST_TEMPERATURE
            statement.setObject(24, item.getTestTemperature());
            // WAFER_LOT_ID
            statement.setObject(25, item.getWaferLotId());
            // WAFER_NO
            statement.setObject(26, item.getWaferNo());
            // SITE_ID
            statement.setObject(27, item.getSiteId());
            // TEST_STAGE
            statement.setObject(28, item.getTestStage());
            // ORIGINAL_WF_FLAT
            statement.setObject(29, item.getOriginalWfFlat());
            statement.setObject(30, item.getSubCustomer());
            statement.setObject(31, item.getTesterName());
            // version
            statement.setObject(32, System.currentTimeMillis());
            // wafer_id_key
            statement.setObject(33, item.getWaferId());
            // wafer_no_key
            statement.setObject(34, item.getWaferNo());
            statement.setObject(35, new Timestamp(item.getUploadTime()));

            statement.addBatch();
        }
    }

    @Override
    public List<String> getColumns() {
        return Arrays.asList(
                "CUSTOMER",
                "UPLOAD_TYPE",
                "DEVICE_ID",
                "FACTORY",
                "FACTORY_SITE",
                "FAB",
                "FAB_SITE",
                "LOT_TYPE",
                "LOT_ID",
                "TEST_AREA",
                "WAFER_ID",
                "FABWF_ID",
                "START_TIME",
                "END_TIME",
                "CREATE_TIME",
                "CREATE_USER",
                "TEST_PROGRAM",
                "FILE_ID",
                "FILE_NAME",
                "WF_FLAT",
                "POS_X",
                "POS_Y",
                "PROCESS",
                "TEST_TEMPERATURE",
                "WAFER_LOT_ID",
                "WAFER_NO",
                "SITE_ID",
                "TEST_STAGE",
                "ORIGINAL_WF_FLAT",
                "SUB_CUSTOMER",
                "TESTER_NAME",
                "VERSION",
                "WAFER_ID_KEY",
                "WAFER_NO_KEY",
                "UPLOAD_TIME"
        );
    }

    @Override
    public String getDbName() {
        return dbName;
    }
}
