package com.guwave.onedata.dataware.source.agent.common.util;

import com.guwave.onedata.dataware.common.contant.ManualFileSystem;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;

public interface FileSystemUtil {
    static final Logger LOGGER = LoggerFactory.getLogger(FileSystemUtil.class);

    void upload(String filePath, String targetPath, int retryCount);

    void downloadToLocal(boolean delSrc, String targetPath, String filePath) throws IOException, Exception;

    void deleteFile(String targetPath, Boolean recursive);

    /**
     * 将 zip 压缩包解压成文件到指定文件夹下
     *
     * @param targetDir   解压后文件存放的目的地
     * @param zipFilePath 待解压的压缩文件
     */
    default void unCompress(String targetDir, String zipFilePath) throws IOException {
        File file = new File(zipFilePath);
        try (InputStream inputStream = new FileInputStream(file);
             ZipArchiveInputStream zipArchiveInputStream = new ZipArchiveInputStream(inputStream)) {
            byte[] buffer = new byte[1024 * 5];
            ArchiveEntry archiveEntry;
            while (null != (archiveEntry = zipArchiveInputStream.getNextEntry())) {
                String archiveEntryFileName = archiveEntry.getName();
                File entryFile = new File(targetDir, archiveEntryFileName);
                if (!archiveEntry.isDirectory()) {
                    if (!entryFile.getParentFile().exists()) {
                        entryFile.getParentFile().mkdirs();
                    }
                    try (OutputStream outputStream = new FileOutputStream(entryFile)) {
                        int length;
                        while ((length = zipArchiveInputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, length);
                        }
                        outputStream.flush();
                    } catch (IOException e) {
                        LOGGER.error("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
                        throw e;
                    }
                } else {
                    if (!entryFile.exists()) {
                        entryFile.mkdirs();
                    }
                }
            }
        } catch (IOException e) {
            LOGGER.error("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
            throw e;
        }
    }

    ManualFileSystem getManualFileSystem();
}
