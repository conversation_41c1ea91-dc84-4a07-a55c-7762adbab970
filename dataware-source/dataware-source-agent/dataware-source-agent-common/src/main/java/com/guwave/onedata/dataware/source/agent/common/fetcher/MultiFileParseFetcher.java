package com.guwave.onedata.dataware.source.agent.common.fetcher;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import com.guwave.onedata.dataware.common.model.stdf.SendLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dc.FileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.*;
import com.guwave.onedata.dataware.repair.common.service.DataRepairService;
import com.guwave.onedata.dataware.source.agent.common.exception.FailException;
import com.guwave.onedata.dataware.source.agent.common.exception.KeyFieldNullException;
import com.guwave.onedata.dataware.source.agent.common.exception.NotFailException;
import com.guwave.onedata.dataware.source.agent.common.handler.DataParseHandler;
import com.guwave.onedata.dataware.source.agent.common.service.FileProcessProgressService;
import com.guwave.onedata.dataware.source.agent.common.sink.impl.KafkaSink;
import com.guwave.onedata.dataware.source.agent.common.util.FileUtil;
import com.guwave.onedata.dataware.source.agent.common.util.ProcessLogThreadLocalUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

/**
 * Copyright (C), 2022, guwave
 * <p>
 * CommonFetcher
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2022-04-27 15:41:06
 */
public interface MultiFileParseFetcher extends CommonFetcher {
    Logger LOGGER = LoggerFactory.getLogger(MultiFileParseFetcher.class);

    List<RunMode> RUNMODE_POOL = Lists.newArrayList(RunMode.DISTRIBUTED, RunMode.STANDALONE);
    List<ProcessStatus> RUNNING_POOL = Lists.newArrayList(ProcessStatus.CREATE, ProcessStatus.PROCESSING);

    List<TestArea> SUPPORT_CP_TEST_AREA_LIST = TestArea.getCPList();

    default void fetch(DataParseHandler dataParseHandler) {
        Long runningTaskCount = getLayerCalculatePoolRepository().countAllByRunModeInAndProcessStatusIn(RUNMODE_POOL, RUNNING_POOL);
        if (runningTaskCount >= getMaxRunningCount()) {
            LOGGER.info("运行任务达上限");
            return;
        }

        // 查询待解析的文件
        List<LotMetaDataDetail> dataDetails = dataParseHandler.getNeedParseLotMetaDetails();

        if (CollectionUtils.isEmpty(dataDetails)) {
            LOGGER.info("没有待处理的文件");
            return;
        }

        LotMetaDataDetail dataDetail = dataDetails.get(0);
        LOGGER.info("find {} to process", dataDetail.getFileName());
        // 先把更新时间更新
        dataDetail
                .setUpdateTime(new Date())
                .setUpdateUser(Constant.SYSTEM);
        getLotMetaDataRepository().updateUpdateTime(dataDetail.getId(), dataDetail.getUpdateTime(), dataDetail.getUpdateUser(), dataDetail.getProcessStatus());

        // 是否到达处理时间
        if (!validateWaitTime(dataDetail)) {
            LOGGER.info("{} 未到处理时间", dataDetail.getFileName());
            return;
        }

        // 上锁lot或wafer
        boolean lockOriginFilePath = getFileProcessProgressService().lockFetcherLotAndWafer(
                dataDetail.getCustomer(), dataDetail.getTestArea(), dataDetail.getFactory(), dataDetail.getFactorySite(), dataDetail.getLotId(), dataDetail.getWaferNo(), dataDetail.getTestStage(), dataDetail.getLotType()
        );
        if (!lockOriginFilePath) {
            // 上锁失败
            return;
        }

        long allStartTime = System.currentTimeMillis();
        // 上锁成功
        try {
            dataDetail = getLotMetaDataRepository().findById(dataDetail.getId()).orElse(dataDetail);
            if (dataDetail.getProcessStatus() != ProcessStatus.CREATE) {
                LOGGER.info(" {} 已被处理", dataDetail.getFileName());
                return;
            }

            List<LotStockingDetail> sameLotWaferFiles = getLotStockingDetailRepository().findAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndLotIdAndWaferNoAndDeviceIdAndTestStageAndLotTypeAndFileCategory(
                    dataDetail.getCustomer(),
                    dataDetail.getTestArea(),
                    dataDetail.getFactory(),
                    dataDetail.getFactorySite(),
                    dataDetail.getLotId(),
                    dataDetail.getWaferNo(),
                    dataDetail.getDeviceId(),
                    dataDetail.getTestStage(),
                    dataDetail.getLotType(),
                    dataDetail.getFileCategory()
            );

            // 判断相同维度（FT：lot或CP：wafer）的文件是否计算结束
            if (sameLotWaferFiles.stream().anyMatch(t -> Objects.equals(t.getContinueFlag(), 0))) {
                // 上批计算未结束
                throw new NotFailException("相同维度的文件计算未结束");
            }

            List<SftpFileDetail> existsParseFileDetails = getSftpFileDetailRepository().findAllByLocalFileNameIn(sameLotWaferFiles.stream().map(LotStockingDetail::getFileName).collect(Collectors.toSet()));

            // 查询同一维度的所有待解析文件
            List<LotMetaDataDetail> lotMetaDataDetails = getLotMetaDataRepository().findAllByCustomerAndTestAreaAndFactoryAndLotIdAndWaferNoAndDeviceIdAndTestStageAndLotTypeAndFileCategoryAndProcessStatus(
                    dataDetail.getCustomer(),
                    dataDetail.getTestArea(),
                    dataDetail.getFactory(),
                    dataDetail.getLotId(),
                    dataDetail.getWaferNo(),
                    dataDetail.getDeviceId(),
                    dataDetail.getTestStage(),
                    dataDetail.getLotType(),
                    dataDetail.getFileCategory(),
                    ProcessStatus.CREATE
            );
            // 更新处理状态
            Date date = new Date();
            lotMetaDataDetails.forEach(t -> t.setProcessStatus(ProcessStatus.PROCESSING).setUpdateTime(date));
            getLotMetaDataRepository().saveAll(lotMetaDataDetails);

            Map<String, LotMetaDataDetail> lotMetaDataDetailMap = lotMetaDataDetails.stream().collect(Collectors.toMap(LotMetaDataDetail::getFileName, t -> t));
            Map<String, SftpFileDetail> sftpFileDetailMap = getSftpFileDetailRepository().findAllByLocalFileNameIn(lotMetaDataDetailMap.keySet()).stream().collect(Collectors.toMap(SftpFileDetail::getLocalFileName, t -> t));
            Map<String, FileInfo> fileInfoMap = Lists.newArrayList(getFileInfoRepository().findAllById(lotMetaDataDetails.stream().map(LotMetaDataDetail::getFileInfoId).collect(Collectors.toSet()))).stream().collect(Collectors.toMap(FileInfo::getFileName, t -> t));
            List<Long> fileIds = lotMetaDataDetails.stream().map(LotMetaDataDetail::getFileInfoId).collect(Collectors.toList());

            lotMetaDataDetails.sort((a, b) -> {
                SftpFileDetail left = sftpFileDetailMap.get(a.getFileName());
                SftpFileDetail right = sftpFileDetailMap.get(b.getFileName());
                if (left == null && right == null) {
                    return 0;
                } else if (left == null) {
                    return 1;
                } else if (right == null) {
                    return -1;
                } else {
                    return Long.compare(left.getRemoteFileMtime().getTime(), right.getRemoteFileMtime().getTime());
                }
            });

            List<String> fileNames = lotMetaDataDetails.stream().map(LotMetaDataDetail::getFileName).collect(Collectors.toList());
            List<FileLoadingLog> lastStepFileLoadingLogs = getFileLoadingLogRepository().findAllByFileNameInAndStep(
                    fileNames,
                    StepType.STEP_TYPE_3100.getStep()
            );
            Map<String, FileLoadingLog> lastStepFileLoadingLogMap = lastStepFileLoadingLogs.stream().collect(Collectors.toMap(FileLoadingLog::getFileName, t -> t, (v1, v2) -> v1));


            ArrayList<LotStockingDetail> needSavingData = new ArrayList<>();
            ArrayList<FileLoadingLog> fileLoadingLogs = new ArrayList<>();
            for (LotMetaDataDetail lotMetaDataDetail : lotMetaDataDetails) {
                long startTime = System.currentTimeMillis();
                Date stepStartTime = new Date();
                // 生成fileLoadingLog
                FileLoadingLog fileLoadingLog = insertFileLoadingLog(lotMetaDataDetail, stepStartTime, lastStepFileLoadingLogMap);
                getFileWarehousingRecordManager().updateFileWarehousingStatus(Collections.singletonList(fileLoadingLog));
                fileLoadingLogs.add(fileLoadingLog);

                // 同一个lot或wafer下的文件一起解析
                ProcessLog processLog = null;
                FileInfo fileInfo = null;
                try {
                    // 任务开始，更新LotWaferWarehousing
                    getDataRepairService().saveNormalLotWaferWarehousingRecord(lotMetaDataDetail, ProcessStatus.PROCESSING, fileIds, getModuleName());
                    // 生成处理日志
                    processLog = generateProcessLog(lotMetaDataDetail);
                    ProcessLogThreadLocalUtil.setLocal(processLog);
                    SftpFileDetail sftpFileDetail = sftpFileDetailMap.get(lotMetaDataDetail.getFileName());
                    if (sftpFileDetail == null) {
                        throw new FailException(lotMetaDataDetail.getFileName() + "对应的sftpFile不存在");
                    }
                    fileInfo = fileInfoMap.get(lotMetaDataDetail.getFileName());
                    if (fileInfo == null) {
                        throw new FailException(lotMetaDataDetail.getFileName() + "对应的fileInfo不存在");
                    }

                    if (getNeedCheckSameFile() && sftpFileDetail.getFileCategory() == FileCategory.STDF) {
                        List<SftpFileDetail> sameFiles = existsParseFileDetails.stream().filter(t ->
                                // 原始文件大小相同
                                Objects.equals(t.getOriginFileSize(), sftpFileDetail.getOriginFileSize())
                                        &&
                                        // 且 （原始文件名相同 或 文件起止时间相同）
                                        (
                                                Objects.equals(t.getOriginFileName(), sftpFileDetail.getOriginFileName())
                                                        ||
                                                        sameLotWaferFiles.stream().anyMatch(lotStocking ->
                                                                Objects.equals(lotStocking.getFileName(), t.getLocalFileName())
                                                                        && Objects.equals(lotStocking.getStartT(), lotMetaDataDetail.getStartT())
                                                                        && Objects.equals(lotStocking.getFinishT(), lotMetaDataDetail.getFinishT())
                                                        )
                                        )
                        ).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(sameFiles)) {
                            SftpFileDetail sameFile = sameFiles.get(0);
                            String errorMessage = String.format("存在相同的文件%s已被解析过！", sameFile.getLocalFileName());
                            LOGGER.error(errorMessage);
                            throw new FileLoadException(FileLoadExceptionInfo.DUPLICATE_FILE_PARSED_EXCEPTION, errorMessage, null);
                        }
                    }

                    // 下载并解压文件
                    downloadAndUncompressHdfsFile(sftpFileDetail);
                    // 获取解压后的文件
                    File uncompressFile = new File(getReadPath(), FileUtil.removeFileSuffix(sftpFileDetail.getLocalFileName(), FileType.ZIP));
                    if (!uncompressFile.exists()) {
                        LOGGER.error("找不到解压后的文件 : {}", uncompressFile.getAbsolutePath());
                        throw new FailException("文件解压失败！,找不到解压后的文件");
                    }

                    // 解析文件
                    dataParseHandler.parseFile(sftpFileDetail, lotMetaDataDetail, uncompressFile);

                    // 生成lotStockingDetail记录
                    LotStockingDetail lotStockingDetail = generateLotStockingDetail(lotMetaDataDetail);
                    existsParseFileDetails.add(sftpFileDetail);
                    sameLotWaferFiles.add(lotStockingDetail);
                    // 设置处理状态 -->  成功
                    needSavingData.add(lotStockingDetail);
                    lotMetaDataDetail.setProcessStatus(ProcessStatus.SUCCESS);
                    fileLoadingLog.setProcessStatus(ProcessStatus.SUCCESS);
                } catch (Exception e) {
                    updateFileLoadingLogStatusAfterDealException(fileLoadingLog, e);

                    if (e instanceof NotFailException) {
                        LOGGER.info("{} ：{}", lotMetaDataDetail.getFileName(), e.getMessage());
                        ProcessLogThreadLocalUtil.appendErrorMessage(e.getMessage());
                    } else {
                        LOGGER.error("处理文件{}异常：", lotMetaDataDetail.getFileName(), e);
                        // 设置处理状态 -->  失败
                        lotMetaDataDetail.setProcessStatus(ProcessStatus.FAIL);
                        updateLotWaferWarehousingRecordAfterDealFail(getRepairRecordRepository(), getLotWaferWarehousingRecordRepository(), getDataRepairService(), fileLoadingLog, e);
                        if (processLog != null) {
                            processLog.setExceptionType(e instanceof KeyFieldNullException ? ExceptionType.KEY_FIELD_NULL_EXCEPTION : ExceptionType.OTHER_EXCEPTION);
                        }
                        ProcessLogThreadLocalUtil.appendErrorMessage(ExceptionUtils.getStackTrace(e));
                    }
                } finally {
                    // 清除threadLocal
                    ProcessLogThreadLocalUtil.clearLocal();

                    Date updateTime = new Date();

                    // 更新lotMetaDataDetail
                    if (lotMetaDataDetail.getProcessStatus() == ProcessStatus.PROCESSING) {
                        //  状态还是processing 时， 设置状态 --> CREATE 等待下次重试
                        lotMetaDataDetail.setProcessStatus(ProcessStatus.CREATE);
                    }
                    lotMetaDataDetail.setUpdateTime(updateTime);
                    fileLoadingLog.setStepEndTime(updateTime)
                            .setUpdateTime(updateTime);

                    // 更新process_log和fileInfo
                    afterOneFileDeal(processLog, fileInfo, lotMetaDataDetail.getProcessStatus(), lotMetaDataDetail.getFileName(), startTime);
                }
            }
            // 更新处理结果
            getFileLoadingLogRepository().saveAll(fileLoadingLogs);
            getFileWarehousingRecordManager().updateFileWarehousingStatus(fileLoadingLogs);
            fileLoadingLogs.stream().filter(t -> t.getProcessStatus() == ProcessStatus.FAIL).forEach(t -> getKafKaSink().sendLoadEndFlagMessage(t));
            getLotStockingDetailRepository().saveAll(needSavingData);
            getLotMetaDataRepository().saveAll(lotMetaDataDetails);

            // 有解析成功的发送结束标记
            if (!getHistoryFlag() && lotMetaDataDetails.stream().anyMatch(t -> t.getProcessStatus() == ProcessStatus.SUCCESS)) {
                sendEndLog(dataDetail, allStartTime);
            }
        } catch (Exception e) {
            if (e instanceof NotFailException) {
                LOGGER.info("{} ：{}", dataDetail.getFileName(), e.getMessage());
            } else {
                LOGGER.error("处理文件{}异常：", dataDetail.getFileName(), e);
            }
        } finally {
            // 解锁lot和wafer
            getFileProcessProgressService().unlockFetcherLotAndWafer(
                    dataDetail.getCustomer(), dataDetail.getTestArea(), dataDetail.getFactory(), dataDetail.getFactorySite(), dataDetail.getLotId(), dataDetail.getWaferNo(), dataDetail.getTestStage(), dataDetail.getLotType()
            );
        }
    }

    default boolean validateWaitTime(LotMetaDataDetail dataDetail) {
        if (dataDetail.getRepairLotWaferId() == null && StringUtils.isBlank(dataDetail.getCleanupTaskIds())) {
            List<SourceParseFileWaitSetting> sourceParseFileWaitSettings = getSourceParseFileWaitSettingRepository().findByTestAreaAndFactoryAndTestStage(dataDetail.getTestArea(), dataDetail.getFactory(), dataDetail.getTestStage());
            if (CollectionUtils.isNotEmpty(sourceParseFileWaitSettings) && sourceParseFileWaitSettings.get(0).getWaitTime() != null
                    && System.currentTimeMillis() - dataDetail.getCreateTime().getTime() < sourceParseFileWaitSettings.get(0).getWaitTime() * 1000L) {
                return false;
            }
        }
        return true;
    }

    default ProcessLog generateProcessLog(LotMetaDataDetail lotMetaDataDetail) {
        Date date = new Date();
        ProcessLog processLog = new ProcessLog()
                .setCustomer(lotMetaDataDetail.getCustomer())
                .setSubCustomer(lotMetaDataDetail.getSubCustomer())
                .setFactory(lotMetaDataDetail.getFactory())
                .setFactorySite(lotMetaDataDetail.getFactorySite())
                .setTestArea(lotMetaDataDetail.getTestArea())
                .setLotType(lotMetaDataDetail.getLotType())
                .setFileType(lotMetaDataDetail.getFileType())
                .setFileCategory(lotMetaDataDetail.getFileCategory())
                .setFileName(lotMetaDataDetail.getFileName())
                .setDeviceId(lotMetaDataDetail.getDeviceId())
                .setLotId(lotMetaDataDetail.getLotId())
                .setWaferId(lotMetaDataDetail.getWaferId())
                .setOriginWaferId(lotMetaDataDetail.getOriginWaferId())
                .setWaferNo(lotMetaDataDetail.getWaferNo())
                .setProcessCnt(1)
                .setProcessStatus(ProcessStatus.PROCESSING)
                .setProcessTime(date)
                .setCreateTime(date)
                .setUpdateTime(date)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateUser(Constant.SYSTEM);
        getProcessLogRepository().save(processLog);
        return processLog;

    }

    default LotStockingDetail generateLotStockingDetail(LotMetaDataDetail lotMetaDataDetail) {
        Date date = new Date();
        LotStockingDetail lotStockingDetail = new LotStockingDetail()
                .setCustomer(lotMetaDataDetail.getCustomer())
                .setSubCustomer(lotMetaDataDetail.getSubCustomer())
                .setTestArea(lotMetaDataDetail.getTestArea())
                .setFactory(lotMetaDataDetail.getFactory())
                .setFactorySite(lotMetaDataDetail.getFactorySite())
                .setFileType(lotMetaDataDetail.getFileType())
                .setFileCategory(lotMetaDataDetail.getFileCategory())
                .setFileName(lotMetaDataDetail.getFileName())
                .setOriginFileName(lotMetaDataDetail.getOriginFileName())
                .setHdfsPath(lotMetaDataDetail.getHdfsPath())
                .setFtpPath(lotMetaDataDetail.getFtpPath())
                .setDeviceId(lotMetaDataDetail.getDeviceId())
                .setLotId(lotMetaDataDetail.getLotId())
                .setWaferId(lotMetaDataDetail.getWaferId())
                .setOriginWaferId(lotMetaDataDetail.getOriginWaferId())
                .setWaferNo(lotMetaDataDetail.getWaferNo())
                .setLotType(lotMetaDataDetail.getLotType())
                .setOfflineRetest(lotMetaDataDetail.getOfflineRetest())
                .setOfflineRetestIgnoreTp(lotMetaDataDetail.getOfflineRetestIgnoreTp())
                .setInterrupt(lotMetaDataDetail.getInterrupt())
                .setInterruptIgnoreTp(lotMetaDataDetail.getInterruptIgnoreTp())
                .setDupRetest(lotMetaDataDetail.getDupRetest())
                .setDupRetestIgnoreTp(lotMetaDataDetail.getDupRetestIgnoreTp())
                .setBatchNum(lotMetaDataDetail.getBatchNum())
                .setBatchNumIgnoreTp(lotMetaDataDetail.getBatchNumIgnoreTp())
                .setSblotId(lotMetaDataDetail.getSblotId())
                .setTestCod(lotMetaDataDetail.getTestCod())
                .setTestStage(lotMetaDataDetail.getTestStage())
                .setStartT(lotMetaDataDetail.getStartT())
                .setFinishT(lotMetaDataDetail.getFinishT())
                .setPosX(lotMetaDataDetail.getPosX())
                .setPosY(lotMetaDataDetail.getPosY())
                .setNotch(lotMetaDataDetail.getNotch())
                .setDieCount(lotMetaDataDetail.getDieCount())
                .setUseBinDefinitionFlag(lotMetaDataDetail.isUseBinDefinitionFlag() ? 1 : 0)
                .setDieDataCount(lotMetaDataDetail.getDieDataCnt())
                .setTestItemDataCount(lotMetaDataDetail.getTestItemDataCnt())
                .setMaxRecordTestItem(lotMetaDataDetail.getMaxRecordTestItem())
                .setMaxRecordTestItemCnt(lotMetaDataDetail.getMaxRecordTestItemCnt())
                .setMinRecordTestItem(lotMetaDataDetail.getMinRecordTestItem())
                .setMinRecordTestItemCnt(lotMetaDataDetail.getMinRecordTestItemCnt())
                .setWarehousingMode(lotMetaDataDetail.getWarehousingMode())
                .setRepairLotWaferId(lotMetaDataDetail.getRepairLotWaferId())
                .setCleanupTaskIds(lotMetaDataDetail.getCleanupTaskIds())
                .setFinish(getHistoryFlag() ? Boolean.TRUE : Boolean.FALSE)
                .setContinueFlag(0)
                .setDieFinishFlag(0)
                .setTestItemFinishFlag(0)
                .setCreateTime(date)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateTime(date)
                .setUpdateUser(Constant.SYSTEM);
        return lotStockingDetail;
    }

    default void sendEndLog(LotMetaDataDetail dataDetail, long startTIme) {
        Map<String, BiConsumer<LotMetaDataDetail, Long>> endLogConsumerMap = new HashMap<String, BiConsumer<LotMetaDataDetail, Long>>() {{
            put(Constant.HDFS_SINK, ((lotMetaDataDetail, startTime) -> {
                getKafKaSink().send(getLoadEndFlagTopic(), JSON.toJSONString(buildCalculateEndLog(lotMetaDataDetail, startTime)));
            }));
        }};
        for (String sinkType : Lists.newArrayList(HDFS_SINK)) {
            if (getSinkTypes().toUpperCase().contains(sinkType)) {
                BiConsumer<LotMetaDataDetail, Long> sendEndLogConsumer = endLogConsumerMap.get(sinkType);
                sendEndLogConsumer.accept(dataDetail, startTIme);
                break;
            }
        }
    }

    static SendLog buildSendLog(LotMetaDataDetail dataDetail) {
        SendLog sendLog = new SendLog();
        sendLog
                .setPriority(1)
                .setCustomer(dataDetail.getCustomer())
                .setSubCustomer(dataDetail.getSubCustomer())
                .setFileCategory(dataDetail.getFileCategory().getCategory())
                .setTestArea(dataDetail.getTestArea().getArea())
                .setTestStage(dataDetail.getTestStage())
                .setFactory(dataDetail.getFactory())
                .setFactorySite(dataDetail.getFactorySite())
                .setLotType(dataDetail.getLotType().getType())
                .setOfflineRetest(dataDetail.getOfflineRetest())
                .setOfflineRetestIgnoreTp(dataDetail.getOfflineRetestIgnoreTp())
                .setDupRetest(dataDetail.getDupRetest())
                .setDupRetestIgnoreTp(dataDetail.getDupRetestIgnoreTp())
                .setBatchNum(dataDetail.getBatchNum())
                .setBatchNumIgnoreTp(dataDetail.getBatchNumIgnoreTp())
                .setInterrupt(dataDetail.getInterrupt())
                .setInterruptIgnoreTp(dataDetail.getInterruptIgnoreTp())
                .setFileId(dataDetail.getFileInfoId())
                .setFileName(dataDetail.getFileName())
                .setDeviceId(dataDetail.getDeviceId())
                .setLotId(dataDetail.getLotId())
                .setWaferId(dataDetail.getWaferId())
                .setWaferNo(dataDetail.getWaferNo())
                .setType(SEND_LOG)
                .setTs(System.currentTimeMillis());
        return sendLog;
    }

    static CalculateEndFlag buildCalculateEndLog(LotMetaDataDetail dataDetail, long startTime) {
        CalculateEndFlag calculateEndFlag = new CalculateEndFlag();
        long ts = System.currentTimeMillis();
        List<Long> cleanupTaskIds = StringUtils.isBlank(dataDetail.getCleanupTaskIds()) ? null : Stream.of(dataDetail.getCleanupTaskIds().split(Constant.COMMA)).flatMap(t -> Stream.of(t.split(Constant.COMMA)).map(s -> Long.parseLong(s.trim())))
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        calculateEndFlag
                .setCustomer(dataDetail.getCustomer())
                .setSubCustomer(dataDetail.getSubCustomer())
                .setFactory(dataDetail.getFactory())
                .setFactorySite(dataDetail.getFactorySite())
                .setTestArea(dataDetail.getTestArea())
                .setLotType(dataDetail.getLotType())
                .setDeviceId(dataDetail.getDeviceId())
                .setTestStage(dataDetail.getTestStage())
                .setLotId(dataDetail.getLotId())
                .setWaferNo(dataDetail.getWaferNo())
                .setTestStage(dataDetail.getTestStage())
                .setFileCategory(dataDetail.getFileCategory())
                .setDwLayer(DwLayer.ODS)
                .setNextDwLayer(DwLayer.DWD)
                .setPriority(dataDetail.getPriority())
                .setPlatform(Platform.GDP)
                .setWarehousingMode(dataDetail.getWarehousingMode())
                .setRepairLotWaferId(dataDetail.getRepairLotWaferId())
                .setCleanupTaskIds(cleanupTaskIds)
                .setProcessStatus(ProcessStatus.SUCCESS)
                .setProcessConsume(ts - startTime)
                .setTs(ts);
        return calculateEndFlag;
    }

    default FileLoadingLog insertFileLoadingLog(LotMetaDataDetail lotMetaDataDetail, Date stepStartTime, Map<String, FileLoadingLog> lastStepFileLoadingLogMap) {
        Date date = new Date();
        FileLoadingLog fileLoadingLog = new FileLoadingLog();

        fileLoadingLog
                .setCustomer(lotMetaDataDetail.getCustomer())
                .setSubCustomer(lotMetaDataDetail.getSubCustomer())
                .setTestArea(lotMetaDataDetail.getTestArea())
                .setFactory(lotMetaDataDetail.getFactory())
                .setFileCategory(lotMetaDataDetail.getFileCategory())
                .setFileName(lotMetaDataDetail.getFileName()) //
                .setOriginFileName(lotMetaDataDetail.getOriginFileName())
                .setHdfsPath(lotMetaDataDetail.getHdfsPath())
                .setFtpPath(lotMetaDataDetail.getFtpPath()) //
                .setStep(StepType.STEP_TYPE_4100.getStep())
                .setStepStartTime(stepStartTime)
//                .setStepEndTime()
                .setDeviceId(lotMetaDataDetail.getDeviceId())
                .setLotId(lotMetaDataDetail.getLotId())
                .setSblotId(lotMetaDataDetail.getSblotId())
                .setWaferId(lotMetaDataDetail.getWaferId())
                .setWaferNo(lotMetaDataDetail.getWaferNo())
                .setTestStage(lotMetaDataDetail.getTestStage())
                .setLotType(lotMetaDataDetail.getLotType())
                .setTesterName(lotMetaDataDetail.getTesterName())
                .setTesterType(lotMetaDataDetail.getTesterType())
                .setTestProgram(lotMetaDataDetail.getTestProgram())
                .setTestProgramVersion(lotMetaDataDetail.getTestProgramVersion())
                .setWarehousingMode(lotMetaDataDetail.getWarehousingMode())
                .setRepairLotWaferId(lotMetaDataDetail.getRepairLotWaferId())
                .setCleanupTaskIds(lotMetaDataDetail.getCleanupTaskIds())
                .setProcessStatus(ProcessStatus.PROCESSING)
//                .setExceptionType()
//                .setErrorMessage()
                .setCreateTime(stepStartTime)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateTime(date)
                .setUpdateUser(Constant.SYSTEM);

        // 查询上一step数据，补充内容

        FileLoadingLog lastStepFileLoadingLog = lastStepFileLoadingLogMap.get(fileLoadingLog.getFileName());
        if (lastStepFileLoadingLog != null) {
            fileLoadingLog
                    .setFactorySite(lastStepFileLoadingLog.getFactorySite())
                    .setFab(lastStepFileLoadingLog.getFab())
                    .setFabSite(lastStepFileLoadingLog.getFabSite())
                    .setConvertFlag(lastStepFileLoadingLog.getConvertFlag())
                    .setSourceFileNames(lastStepFileLoadingLog.getSourceFileNames()) //
                    .setRemoteFileMtime(lastStepFileLoadingLog.getRemoteFileMtime())
                    .setFileSize(lastStepFileLoadingLog.getFileSize())
                    .setOriginFileSize(lastStepFileLoadingLog.getOriginFileSize())
                    .setStartT(lastStepFileLoadingLog.getStartT())
                    .setFinishT(lastStepFileLoadingLog.getFinishT())
                    .setFtpIp(lastStepFileLoadingLog.getFtpIp())
                    .setDataIntegrityFileComment(lastStepFileLoadingLog.getDataIntegrityFileComment())
                    .setDataIntegrityFileLabel(lastStepFileLoadingLog.getDataIntegrityFileLabel())
                    .setWarningMessage(lastStepFileLoadingLog.getWarningMessage())
            ;
        }

        //插入前先删除 当前以及以后的所有行
        getFileLoadingLogRepository().deleteByFileNameAndStep(fileLoadingLog.getFileName(), fileLoadingLog.getStep());

        getFileLoadingLogRepository().save(fileLoadingLog);

        return fileLoadingLog;
    }

    int getMaxRunningCount();

    LotMetaDataDetailRepository getLotMetaDataRepository();

    LotStockingDetailRepository getLotStockingDetailRepository();

    FileProcessProgressService getFileProcessProgressService();

    SftpFileDetailRepository getSftpFileDetailRepository();

    boolean getNeedCheckWafermapConfig();

    boolean getNeedCheckSameFile();

    String getSinkTypes();

    String getLoadEndFlagTopic();

    KafkaSink getKafKaSink();

    LayerCalculatePoolRepository getLayerCalculatePoolRepository();

    boolean getHistoryFlag();

    FileLoadingLogRepository getFileLoadingLogRepository();

    RepairRecordRepository getRepairRecordRepository();

    LotWaferWarehousingRecordRepository getLotWaferWarehousingRecordRepository();

    DataRepairService getDataRepairService();

    SourceParseFileWaitSettingRepository getSourceParseFileWaitSettingRepository();

    String getModuleName();

}
