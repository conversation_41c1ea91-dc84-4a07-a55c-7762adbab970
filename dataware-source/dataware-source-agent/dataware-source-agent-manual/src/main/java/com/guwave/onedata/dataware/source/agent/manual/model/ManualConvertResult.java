package com.guwave.onedata.dataware.source.agent.manual.model;

import java.util.List;

/**
 * 2023/11/16 11:51
 * ManualConvertResult
 * <AUTHOR>
 */
public class ManualConvertResult {
    private String code;
    private String msg;
    private List<ManualConvertFileInfo> data;

    public String getCode() {
        return code;
    }

    public ManualConvertResult setCode(String code) {
        this.code = code;
        return this;
    }

    public String getMsg() {
        return msg;
    }

    public ManualConvertResult setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public List<ManualConvertFileInfo> getData() {
        return data;
    }

    public ManualConvertResult setData(List<ManualConvertFileInfo> data) {
        this.data = data;
        return this;
    }
}
