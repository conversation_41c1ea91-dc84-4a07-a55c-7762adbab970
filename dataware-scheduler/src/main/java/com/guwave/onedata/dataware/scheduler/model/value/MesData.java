package com.guwave.onedata.dataware.scheduler.model.value;

import java.util.List;
import java.util.Map;

public class MesData {
    Map<String, Integer> bin;
    Integer sum;
    Integer pass;
    Integer fail;
    Integer other;
    Integer loss;
    Integer markF;
    String yield;
    String station;
    String site;
    String xjLot;
    List<BinType> binType;
    String testProgram;

    public MesData() {
    }

    public Map<String, Integer> getBin() {
        return bin;
    }

    public void setBin(Map<String, Integer> bin) {
        this.bin = bin;
    }

    public Integer getSum() {
        return sum;
    }

    public void setSum(Integer sum) {
        this.sum = sum;
    }

    public Integer getPass() {
        return pass;
    }

    public void setPass(Integer pass) {
        this.pass = pass;
    }

    public Integer getFail() {
        return fail;
    }

    public void setFail(Integer fail) {
        this.fail = fail;
    }

    public Integer getOther() {
        return other;
    }

    public void setOther(Integer other) {
        this.other = other;
    }

    public Integer getLoss() {
        return loss;
    }

    public void setLoss(Integer loss) {
        this.loss = loss;
    }

    public Integer getMarkF() {
        return markF;
    }

    public void setMarkF(Integer markF) {
        this.markF = markF;
    }

    public String getYield() {
        return yield;
    }

    public void setYield(String yield) {
        this.yield = yield;
    }

    public String getStation() {
        return station;
    }

    public void setStation(String station) {
        this.station = station;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public String getXjLot() {
        return xjLot;
    }

    public void setXjLot(String xjLot) {
        this.xjLot = xjLot;
    }

    public List<BinType> getBinType() {
        return binType;
    }

    public void setBinType(List<BinType> binType) {
        this.binType = binType;
    }

    public String getTestProgram() {
        return testProgram;
    }

    public void setTestProgram(String testProgram) {
        this.testProgram = testProgram;
    }
}
