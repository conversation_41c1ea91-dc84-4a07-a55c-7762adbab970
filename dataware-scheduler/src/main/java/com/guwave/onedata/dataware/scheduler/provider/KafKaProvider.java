package com.guwave.onedata.dataware.scheduler.provider;

import com.guwave.onedata.dataware.common.contant.ModuleEnum;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.ProjectEnum;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DatawareFailMessageRecord;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.DatawareFailMessageRecordRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Date;

import static com.guwave.onedata.dataware.common.contant.Constant.SYSTEM;

@Component
public class KafKaProvider {
    private static final Logger LOGGER = LoggerFactory.getLogger(KafKaProvider.class);

    @Autowired
    private KafkaTemplate<byte[], byte[]> kafkaTemplate;

    @Autowired
    private DatawareFailMessageRecordRepository datawareFailMessageRecordRepository;



    public void sendKafka(String topic, String messageJsonString) {
        this.kafkaTemplate
                .send(topic, messageJsonString.getBytes(StandardCharsets.UTF_8))
                .addCallback(
                        success -> {
                            // 消息发送到的topic
                            assert success != null;
                            // 消息发送到的分区
                            int partition = success.getRecordMetadata().partition();
                            // 消息在分区内的offset
                            long offset = success.getRecordMetadata().offset();
                            LOGGER.info("消息发送成功, topic: {}, partition: {}, offset: {}, message: {}", topic, partition, offset, messageJsonString);
                        },
                        fail -> {
                            DatawareFailMessageRecord computeFailMessageRecord = new DatawareFailMessageRecord()
                                    .setProject(ProjectEnum.DATAWARE)
                                    .setModule(ModuleEnum.DATAWARE_SCHEDULER)
                                    .setTopic(topic)
                                    .setKey(null)
                                    .setValue(messageJsonString)
                                    .setProcessStatus(ProcessStatus.FAIL)
                                    .setDeleteFlag(0)
                                    .setCreateUser(SYSTEM)
                                    .setUpdateUser(SYSTEM)
                                    .setCreateTime(new Date())
                                    .setUpdateTime(new Date());

                            datawareFailMessageRecordRepository.save(computeFailMessageRecord);
                            LOGGER.info("发送消息失败，将该条消息记入dw_dataware_fail_message_record {}", fail);
                        });

    }

    public void sendKafka(String topic, String key, String messageJsonString) {
        this.kafkaTemplate
                .send(topic, key.getBytes(), messageJsonString.getBytes(StandardCharsets.UTF_8))
                .addCallback(
                        success -> {
                            // 消息发送到的topic
                            assert success != null;
                            // 消息发送到的分区
                            int partition = success.getRecordMetadata().partition();
                            // 消息在分区内的offset
                            long offset = success.getRecordMetadata().offset();
                            LOGGER.info("消息发送成功, topic: {}, partition: {}, offset: {}, message: {}", topic, partition, offset, messageJsonString);
                        },
                        fail -> {
                            DatawareFailMessageRecord datawareFailMessageRecord = new DatawareFailMessageRecord()
                                    .setProject(ProjectEnum.DATAWARE)
                                    .setModule(ModuleEnum.DATAWARE_SCHEDULER)
                                    .setTopic(topic)
                                    .setKey(key)
                                    .setKey(null)
                                    .setValue(messageJsonString)
                                    .setProcessStatus(ProcessStatus.FAIL)
                                    .setDeleteFlag(0)
                                    .setCreateUser(SYSTEM)
                                    .setUpdateUser(SYSTEM)
                                    .setCreateTime(new Date())
                                    .setUpdateTime(new Date());

                            datawareFailMessageRecordRepository.save(datawareFailMessageRecord);
                            LOGGER.info("发送消息失败，将该条消息记入dw_dataware_fail_message_record {}", fail);
                        });
    }
}
