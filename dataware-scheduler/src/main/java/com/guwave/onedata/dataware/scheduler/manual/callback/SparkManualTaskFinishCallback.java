package com.guwave.onedata.dataware.scheduler.manual.callback;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.DwLayer;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.contant.UploadType;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SparkAppConfig;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualCalculateTaskRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SparkAppConfigRepository;
import com.guwave.onedata.dataware.scheduler.manual.listener.ManualAppListener;
import com.guwave.onedata.dataware.scheduler.util.ComputeUtil;
import com.guwave.onedata.dataware.scheduler.util.ThreadPoolUtils;
import com.guwave.onedata.next.compute.api.callback.ComputeCallback;
import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Component
public class SparkManualTaskFinishCallback implements ComputeCallback {


    @Autowired
    private SparkAppConfigRepository sparkAppConfigRepository;
    @Autowired
    private ManualCalculateTaskRepository manualCalculateTaskRepository;
    @Autowired
    private ManualAppListener manualAppListener;

    private Set<String> supportComputeCodes;
    private Executor executor;

    @PostConstruct
    public void init() {
        supportComputeCodes = sparkAppConfigRepository.findAllByUploadTypeInAndDwLayerIn(Lists.newArrayList(UploadType.MANUAL, UploadType.MANUAL_PRODUCTION), Lists.newArrayList(DwLayer.DWD, DwLayer.DWS, DwLayer.ADS))
                .stream().map(SparkAppConfig::getMainClass).collect(Collectors.toSet());
        executor = ThreadPoolUtils.getNewThreadPoolExecutor(this.getClass().getName(), 20, 20, 10000);
    }

    @Override
    public void doCallback(ComputeResultMessage computeResultMessage) {
        ProcessStatus processStatus = ComputeUtil.convertProcessStatus(computeResultMessage.getProcessStatus());
        if (processStatus == ProcessStatus.PROCESSING) {
            LOGGER.info("computeResultMessage {} 忽略", JSON.toJSONString(computeResultMessage));
            return;
        }
        ManualCalculateTask manualCalculateTask = manualCalculateTaskRepository.findByUniqueIdAndProcessStatus(computeResultMessage.getUniqueId(), ProcessStatus.PROCESSING);
        if (manualCalculateTask == null) {
            LOGGER.info("computeResultMessage {} 没有对应的记录", JSON.toJSONString(computeResultMessage));
            return;
        }

        executor.execute(() -> {
            LOGGER.info("spark manual task 结束 开始处理：{}", JSON.toJSONString(computeResultMessage));
            manualAppListener.stateChanged(manualCalculateTask, computeResultMessage);
        });
    }

    @Override
    public boolean isSupport(String computeCode) {
        return supportComputeCodes.contains(computeCode);
    }
}
