package com.guwave.onedata.dataware.source.agent.common.handler;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.key.LotWaferPrimaryKey;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import com.guwave.onedata.dataware.common.model.summary.dwd.DwdMesBinDetail;
import com.guwave.onedata.dataware.common.util.WaferUtil;
import com.guwave.onedata.dataware.dao.mysql.domain.dc.FileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.MesDetail;
import com.guwave.onedata.dataware.dao.mysql.manager.LotWaferPrimaryDataManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dc.FileInfoRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.MesDetailRepository;
import com.guwave.onedata.dataware.parser.stdf.util.DateUtil;
import com.guwave.onedata.dataware.repair.common.model.Wafer;
import com.guwave.onedata.dataware.source.agent.common.service.FileProcessProgressService;
import com.guwave.onedata.dataware.source.agent.common.sink.impl.KafkaSink;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;
import static com.guwave.onedata.dataware.common.contant.Constant.MIDDLE_LINE;

/**
 * 2024/6/18 15:26
 * SummaryHandler
 *
 * <AUTHOR>
 */
public interface SummaryHandler extends Handler {
    Logger LOGGER = LoggerFactory.getLogger(SummaryHandler.class);

    FileInfoRepository getFileInfoRepository();

    KafkaSink getKafkaSink();

    String getCalculateEndFlagTopic();

    MesDetailRepository getMesDetailRepository();

    LotWaferPrimaryDataManager getLotWaferPrimaryDataManager();

    FileProcessProgressService getFileProcessProgressService();

    Map<String, BiConsumer<String, DwdMesBinDetail>> MES_BIN_DETAIL_CONSUMER_MAP = new HashMap<String, BiConsumer<String, DwdMesBinDetail>>() {{
        put("bin", (str, dwdMesBinDetail) -> dwdMesBinDetail.setBinNum(StringUtils.isBlank(str) ? null : Long.valueOf(str)));
        put("binName", (str, dwdMesBinDetail) -> dwdMesBinDetail.setBinNam(str));
        put("cnt", (str, dwdMesBinDetail) -> dwdMesBinDetail.setFinalBinCnt(StringUtils.isBlank(str) ? null : Long.valueOf(str)));
        put("binPf", (str, dwdMesBinDetail) -> dwdMesBinDetail.setBinPf(str));
    }};

    default List<DwdMesBinDetail> buildMesBinDetail(List<MesDetail> mesDetails, Long uploadTime, Long dataVersion) {
        if (CollectionUtils.isEmpty(mesDetails)) {
            LOGGER.info("mesDetails is empty");
            return Collections.emptyList();
        }
        Set<String> mesFileNames = mesDetails.stream().map(MesDetail::getFileName).collect(Collectors.toSet());
        Map<String, Long> fileNameIdMap = getFileInfoRepository().findAllByFileNameInAndUploadTypeAndDeleteFlag(mesFileNames, UploadType.AUTO, false)
                .stream().collect(Collectors.toMap(FileInfo::getFileName, FileInfo::getId));

        Long createTime = System.currentTimeMillis();
        String createHourKey = DateUtil.getDayHour(createTime);
        String createDayKey = DateUtil.getDay(createTime);

        return mesDetails.stream().flatMap(mesDetail -> {
                    List<DwdMesBinDetail> dwdMesBinDetails;
                    try {
                        List<Map> hbinInfoMapList = StringUtils.isBlank(mesDetail.getHbinInfo()) ? Collections.emptyList() : JSON.parseArray(mesDetail.getHbinInfo(), Map.class);
                        List<Map> sbinInfoMapList = StringUtils.isBlank(mesDetail.getSbinInfo()) ? Collections.emptyList() : JSON.parseArray(mesDetail.getSbinInfo(), Map.class);

                        List<DwdMesBinDetail> tmpDwdMesBinDetails = new ArrayList<>(hbinInfoMapList.size() + sbinInfoMapList.size());

                        Map<String, List<Map>> binInfoMap = new HashMap<>(2);
                        binInfoMap.put(Constant.HBIN_NAME_PREFIX, CollectionUtils.isEmpty(hbinInfoMapList) ? Collections.singletonList(new HashMap()) : hbinInfoMapList);
                        binInfoMap.put(Constant.SBIN_NAME_PREFIX, CollectionUtils.isEmpty(sbinInfoMapList) ? Collections.singletonList(new HashMap()) : sbinInfoMapList);

                        binInfoMap.forEach((binType, binInfoMapList) -> {
                            LOGGER.info("mesDetail binType: {}", binType);
                            for (Map map : binInfoMapList) {
                                LOGGER.info("mesDetail binInfo: {}", map);
                                DwdMesBinDetail dwdMesBinDetail = new DwdMesBinDetail().setBinType(binType);
                                MES_BIN_DETAIL_CONSUMER_MAP.forEach((key, value) -> {
                                    if (map.containsKey(key)) {
                                        try {
                                            value.accept(String.valueOf(map.getOrDefault(key, EMPTY)), dwdMesBinDetail);
                                        } catch (Exception ex) {
                                            String errorMessage = String.format("mesDetail binInfo转换异常, binType: %s, binInfo: %s", binType, map);
                                            LOGGER.info(errorMessage, ex);
                                            throw new RuntimeException(errorMessage, ex);
                                        }
                                    }
                                });
                                tmpDwdMesBinDetails.add(dwdMesBinDetail);
                            }
                        });

                        dwdMesBinDetails = tmpDwdMesBinDetails.stream().filter(
                                elem -> Objects.nonNull(elem.getBinNum()) ||
                                        StringUtils.isNotBlank(elem.getBinNam()) ||
                                        Objects.nonNull(elem.getFinalBinCnt()) ||
                                        StringUtils.isNotBlank(elem.getBinPf())
                        ).collect(Collectors.toList());

                        if (CollectionUtils.isEmpty(dwdMesBinDetails)) {
                            LOGGER.info("没有BIN信息,构建一个BIN_TYPE=''的DwdMesBinDetail记录");
                            dwdMesBinDetails.add(new DwdMesBinDetail().setBinType(EMPTY));
                        }
                    } catch (Exception ex) {
                        String errorMessage = String.format("mesDetail binInfo转换异常, HbinInfo: %s, SbinInfo: %s", mesDetail.getHbinInfo(), mesDetail.getSbinInfo());
                        LOGGER.error(errorMessage, ex);
                        throw new FileLoadException(FileLoadExceptionInfo.RAW_DATA_FILE_CONVERT_EXCEPTION, errorMessage + ExceptionUtils.getStackTrace(ex), null);
                    }

                    long startTime;
                    long endTime;
                    if (mesDetail.getStartTime() == null && mesDetail.getEndTime() == null) {
                        startTime = 0L;
                        endTime = 0L;
                    } else {
                        startTime = mesDetail.getStartTime() != null ? mesDetail.getStartTime().getTime() : mesDetail.getEndTime().getTime();
                        endTime = mesDetail.getEndTime() != null ? mesDetail.getEndTime().getTime() : mesDetail.getStartTime().getTime();
                    }

                    String startHourKey = DateUtil.getDayHour(startTime);
                    String startDayKey = DateUtil.getDay(startTime);
                    String endHourKey = DateUtil.getDayHour(endTime);
                    String endDayKey = DateUtil.getDay(endTime);

                    return dwdMesBinDetails.stream().map(dwdMesBinDetail -> dwdMesBinDetail
                            .setCustomer(mesDetail.getCustomer())
                            .setSubCustomer(mesDetail.getSubCustomer())
                            .setUploadType(UploadType.AUTO)
                            .setFactory(mesDetail.getFactory())
                            .setFactorySite(mesDetail.getFactorySite())
                            .setFab(EMPTY)
                            .setFabSite(EMPTY)
                            .setLotType(mesDetail.getLotType())
                            .setTestArea(mesDetail.getTestArea())
                            .setTestStage(mesDetail.getTestStage())
                            .setFileCategory(mesDetail.getFileCategory())
                            .setFileId(fileNameIdMap.get(mesDetail.getFileName()))
                            .setFileName(mesDetail.getFileName())
                            .setTestProgram(mesDetail.getTestProgram())
                            .setTestHead(null)
                            .setTesterName(mesDetail.getTesterName())
                            .setStartTime(startTime)
                            .setEndTime(endTime)
                            .setStartHourKey(startHourKey)
                            .setStartDayKey(startDayKey)
                            .setEndHourKey(endHourKey)
                            .setEndDayKey(endDayKey)
                            .setProberHandlerTyp(EMPTY)
                            .setProberHandlerId(mesDetail.getProberHandlerId())
                            .setProbecardLoadboardTyp(EMPTY)
                            .setProbecardLoadboardId(mesDetail.getProbecardLoadboardId())
                            .setDeviceId(mesDetail.getDeviceId())
                            .setLotId(mesDetail.getLotId())
                            .setSblotId(mesDetail.getSblotId())
                            .setWaferLotId((TestArea.CP.getTestScope().equals(mesDetail.getTestArea().getTestScope()) ? WaferUtil.formatWaferLotId(mesDetail.getLotId(), mesDetail.getWaferId()) : WaferUtil.formatLotId(mesDetail.getSblotId())))
                            .setWaferId(mesDetail.getWaferId())
                            .setOriginWaferId(mesDetail.getOriginWaferId())
                            .setWaferNo(mesDetail.getWaferNo())
                            .setStdfFiles(mesDetail.getStdfFiles())
                            .setBin(buildBin(dwdMesBinDetail.getBinType(), dwdMesBinDetail.getBinNum(), dwdMesBinDetail.getBinNam()))
                            .setTotalCnt(Long.valueOf(mesDetail.getTotalCnt()))
                            .setPassCnt(Long.valueOf(mesDetail.getPassCnt()))
                            .setFailCnt(Long.valueOf(mesDetail.getFailCnt()))
                            .setExtraInfo(mesDetail.getExtraInfo())
                            .setCreateHourKey(createHourKey)
                            .setCreateDayKey(createDayKey)
                            .setCreateTime(createTime)
                            .setCreateUser(mesDetail.getCreateUser())
                            .setUploadTime(uploadTime)
                            .setDataVersion(dataVersion)
                            .setIsDelete(0));
                })
                .collect(Collectors.toList());
    }

    default void sendMesSaveCkMessage(List<DwdMesBinDetail> dwdMesBinDetails, Long dataVersion) {
        if (CollectionUtils.isEmpty(dwdMesBinDetails)) {
            LOGGER.info("dwdMesBinDetails is empty");
            return;
        }
        List<CalculateEndFlag> calculateEndFlags = dwdMesBinDetails
                .stream()
                .map(t -> new Wafer()
                        .setCustomer(t.getCustomer())
                        .setSubCustomer(t.getSubCustomer())
                        .setTestArea(t.getTestArea())
                        .setFactory(t.getFactory())
                        .setFactorySite(t.getFactorySite())
                        .setDeviceId(t.getDeviceId())
                        .setLotId(t.getLotId())
                        .setWaferNo(TestArea.getCPList().contains(t.getTestArea()) ? t.getWaferNo() : EMPTY)
                        .setSblotId(TestArea.getFTList().contains(t.getTestArea()) ? t.getSblotId() : EMPTY)
                        .setTestStage(t.getTestStage())
                        .setFileCategory(t.getFileCategory())
                        .setLotType(t.getLotType()))
                .distinct()
                .map(t -> buildCalculateEndFlag(t, dataVersion))
                .collect(Collectors.toList());
        for (CalculateEndFlag calculateEndFlagMessage : calculateEndFlags) {
            String message = JSON.toJSONString(calculateEndFlagMessage);
            LOGGER.info("mes解析写入Ck完成,发送消息通知. {}", message);
            getKafkaSink().send(getCalculateEndFlagTopic(), message);
        }

    }

    /**
     * 构造HBIN/SBIN：HBIN/SBIN + binNum + "-" + binNam
     */
    default String buildBin(String prefix, Long binNum, String binNam) {
        return binNum != null ? prefix + binNum + MIDDLE_LINE + binNam : EMPTY;
    }

    default CalculateEndFlag buildCalculateEndFlag(Wafer wafer, Long dataVersion) {
        return new CalculateEndFlag()
                .setCustomer(wafer.getCustomer())
                .setSubCustomer(wafer.getSubCustomer())
                .setTestArea(wafer.getTestArea())
                .setFactory(wafer.getFactory())
                .setFactorySite(wafer.getFactorySite())
                .setDeviceId(wafer.getDeviceId())
                .setLotId(wafer.getLotId())
                .setWaferNo(TestArea.getCPList().contains(wafer.getTestArea()) ? wafer.getWaferNo() : EMPTY)
                .setSblotId(TestArea.getFTList().contains(wafer.getTestArea()) ? wafer.getSblotId() : EMPTY)
                .setTestStage(wafer.getTestStage())
                .setFileCategory(wafer.getFileCategory())
                .setLotType(wafer.getLotType())
                .setPlatform(Platform.CK)
                .setProcessStatus(ProcessStatus.SUCCESS)
                .setTs(System.currentTimeMillis())
                .setDataVersion(dataVersion)
                ;

    }

    default <T> List<List<T>> splitList(List<T> originalList, int batchSize) {
        if (CollectionUtils.isEmpty(originalList) || originalList.size() <= batchSize || batchSize <= 0) {
            return Collections.singletonList(originalList);
        }
        // 计算需要拆分的批次数
        int numberOfBatches = (int) Math.ceil(originalList.size() / (double) batchSize);

        // 使用IntStream.range()生成批次索引，并映射到对应的子列表
        return IntStream.range(0, numberOfBatches)
                .mapToObj(batchIndex -> {
                    int start = batchIndex * batchSize;
                    int end = Math.min(start + batchSize, originalList.size());
                    return originalList.subList(start, end);
                })
                .collect(Collectors.toList());
    }

    default void saveLotWaferPrimaryData(List<MesDetail> mesDetails) {
        if (CollectionUtils.isEmpty(mesDetails)) {
            LOGGER.info("准备写入dw_lot_wafer_primary_data表, mesDetails is empty");
            return;
        }


        LOGGER.info("准备写入dw_lot_wafer_primary_data表,dataSize = {}", mesDetails.size());

        MesDetail head = mesDetails.get(0);

        if (TestArea.CP.getTestScope().equals(head.getTestArea().getTestScope())) {
            Set<LotWaferPrimaryKey> lotWaferPrimaryKeys = mesDetails.stream()
                    .map(t -> new LotWaferPrimaryKey()
                            .setCustomer(t.getCustomer())
                            .setSubCustomer(t.getSubCustomer())
                            .setFactory(t.getFactory())
                            .setFactorySite(t.getFactorySite())
                            .setTestArea(TestArea.CP)
                            .setDeviceId(t.getDeviceId())
                            .setLotId(t.getLotId())
                            .setSblotId(EMPTY)
                    )
                    .collect(Collectors.toSet());
            lotWaferPrimaryKeys.forEach((lotWaferPrimaryKey -> {
                List<MesDetail> curMesDetails = getMesDetailRepository().findAllByCustomerAndSubCustomerAndFactoryAndFactorySiteAndTestAreaAndDeviceIdAndLotId(
                        lotWaferPrimaryKey.getCustomer(),
                        lotWaferPrimaryKey.getSubCustomer(),
                        lotWaferPrimaryKey.getFactory(),
                        lotWaferPrimaryKey.getFactorySite(),
                        lotWaferPrimaryKey.getTestArea(),
                        lotWaferPrimaryKey.getDeviceId(),
                        lotWaferPrimaryKey.getLotId());
                if (CollectionUtils.isNotEmpty(curMesDetails)) {
                    try {
                        updateLotWaferPrimaryData(lotWaferPrimaryKey, curMesDetails, Collections.singletonList(WaferUtil.formatLotId(head.getLotId())));
                    } catch (Exception e) {
                        LOGGER.info("更新LotWaferPrimaryData数据失败, lotWaferPrimaryKey:{}", lotWaferPrimaryKey, e);
                    }
                }
            }));

        } else if (TestArea.FT.getTestScope().equals(head.getTestArea().getTestScope())) {
            Set<LotWaferPrimaryKey> lotWaferPrimaryKeys = mesDetails.stream()
                    .map(t -> new LotWaferPrimaryKey()
                            .setCustomer(t.getCustomer())
                            .setSubCustomer(t.getSubCustomer())
                            .setFactory(t.getFactory())
                            .setFactorySite(t.getFactorySite())
                            .setTestArea(t.getTestArea())
                            .setDeviceId(t.getDeviceId())
                            .setLotId(t.getLotId())
                            .setSblotId(t.getSblotId())
                    )
                    .collect(Collectors.toSet());
            lotWaferPrimaryKeys.forEach((lotWaferPrimaryKey -> {
                List<MesDetail> curMesDetails = getMesDetailRepository().findAllByCustomerAndSubCustomerAndFactoryAndFactorySiteAndTestAreaAndDeviceIdAndLotIdAndSblotId(
                        lotWaferPrimaryKey.getCustomer(),
                        lotWaferPrimaryKey.getSubCustomer(),
                        lotWaferPrimaryKey.getFactory(),
                        lotWaferPrimaryKey.getFactorySite(),
                        lotWaferPrimaryKey.getTestArea(),
                        lotWaferPrimaryKey.getDeviceId(),
                        lotWaferPrimaryKey.getLotId(),
                        lotWaferPrimaryKey.getSblotId());
                if (CollectionUtils.isNotEmpty(curMesDetails)) {
                    try {
                        updateLotWaferPrimaryData(lotWaferPrimaryKey, curMesDetails, Collections.EMPTY_LIST);
                    } catch (Exception e) {
                        LOGGER.info("更新LotWaferPrimaryData数据失败, lotWaferPrimaryKey:{}", lotWaferPrimaryKey, e);
                    }
                }
            }));
        }
    }

    default void updateLotWaferPrimaryData(LotWaferPrimaryKey lotWaferPrimaryKey, List<MesDetail> curMesDetails, List<String> waferLotIds) throws InterruptedException {
        LocalTime start = LocalTime.now();
        boolean getLock = getFileProcessProgressService().lockUpdateLotWaferPrimaryData(lotWaferPrimaryKey);
        while (!getLock && Duration.between(start, LocalTime.now()).toMinutes() <= 5) {
            LOGGER.info("未获取到锁");
            Thread.sleep(200L);
            try {
                getLock = getFileProcessProgressService().lockUpdateLotWaferPrimaryData(lotWaferPrimaryKey);
                LOGGER.info("获取锁成功, lotWaferPrimaryKey:{}", lotWaferPrimaryKey);
            } catch (Exception e) {
                LOGGER.info("获取锁失败, lotWaferPrimaryKey:{}", lotWaferPrimaryKey, e);
            }
        }

        if (getLock) {
            try {
                LOGGER.info("LotWaferPrimaryData数据更新开始!");
                getLotWaferPrimaryDataManager().saveMesLotWaferPrimaryData(lotWaferPrimaryKey, curMesDetails, waferLotIds);
            } finally {
                getFileProcessProgressService().unlockUpdateLotWaferPrimaryData(lotWaferPrimaryKey);
                LOGGER.info("释放锁成功, lotWaferPrimaryKey:{}", lotWaferPrimaryKey);
            }

        }
    }


}
