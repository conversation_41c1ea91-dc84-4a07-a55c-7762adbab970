package com.guwave.onedata.dataware.collectx.manager.thread;

import com.guwave.onedata.dataware.common.contant.FTPServerType;
import com.guwave.onedata.dataware.common.model.collectx.SftpConnectConf;
import com.guwave.onedata.dataware.common.util.AESUtil;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpBackupRule;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpCollectRule;

import java.util.HashMap;

public class SftpConnectConfThreadManager {
    private static final ThreadLocal<HashMap<FTPServerType, SftpConnectConf>> CONNECTCONF_THREAD_LOCAL_MAP = new ThreadLocal<>();

    public static SftpConnectConf getSftpConnectConf(FTPServerType ftpServerType) {
        return CONNECTCONF_THREAD_LOCAL_MAP.get().get(ftpServerType);
    }

    public static void setSftpConnectConf(SftpBackupRule sftpBackupRule) {

        HashMap<FTPServerType, SftpConnectConf> hashMap = new HashMap<FTPServerType, SftpConnectConf>() {{
            put(
                    FTPServerType.SOURCE,
                    new SftpConnectConf().setPort(sftpBackupRule.getSourceFtpPort())
                            .setPassword(AESUtil.Decrypt(sftpBackupRule.getSourceFtpPassword()))
                            .setExtInfo(sftpBackupRule.getSourceExtInfo())
                            .setServer(sftpBackupRule.getSourceFtpServer())
                            .setUsername(sftpBackupRule.getSourceFtpUsername())
                            .setUseTls(sftpBackupRule.getSourceFtpUseTls())
                            .setTimeout(sftpBackupRule.getTimeout())
                            .setBlackFileRemoteFullNameRegex(sftpBackupRule.getBlackFileFullNameRegex())
                            .setFileModifiedRealStartDate(sftpBackupRule.getFileModifiedRealStartDate())
                            .setFileModifiedRealEndDate(sftpBackupRule.getFileModifiedRealEndDate())
                            .setTimeDifference(sftpBackupRule.getTimeDifference())
                            .setReconnectCount(sftpBackupRule.getReconnectCount())
            );
            put(
                    FTPServerType.TARGET,
                    new SftpConnectConf().setPort(sftpBackupRule.getTargetFtpPort())
                            .setPassword(AESUtil.Decrypt(sftpBackupRule.getTargetFtpPassword()))
                            .setExtInfo(sftpBackupRule.getTargetExtInfo())
                            .setServer(sftpBackupRule.getTargetFtpServer())
                            .setUsername(sftpBackupRule.getTargetFtpUsername())
                            .setUseTls(sftpBackupRule.getTargetFtpUseTls())
                            .setTimeout(sftpBackupRule.getTimeout())
                            .setBlackFileRemoteFullNameRegex(sftpBackupRule.getBlackFileFullNameRegex())
                            .setFileModifiedRealStartDate(sftpBackupRule.getFileModifiedRealStartDate())
                            .setFileModifiedRealEndDate(sftpBackupRule.getFileModifiedRealEndDate())
                            .setTimeDifference(sftpBackupRule.getTargetFtpTimeDifference())
                            .setReconnectCount(sftpBackupRule.getReconnectCount())
            );
        }};

        CONNECTCONF_THREAD_LOCAL_MAP.set(hashMap);
    }

    public static void setSftpConnectConf(SftpCollectRule sftpCollectRule) {
        HashMap<FTPServerType, SftpConnectConf> hashMap = new HashMap<FTPServerType, SftpConnectConf>() {{
            put(
                    FTPServerType.NORMAL,
                    new SftpConnectConf().setPort(sftpCollectRule.getPort())
                            .setPassword(AESUtil.Decrypt(sftpCollectRule.getPassword()))
                            .setUsername(sftpCollectRule.getUsername())
                            .setExtInfo(sftpCollectRule.getExtInfo())
                            .setServer(sftpCollectRule.getServer())
                            .setUseTls(sftpCollectRule.getUseTls())
                            .setTimeout(sftpCollectRule.getTimeout())
                            .setBlackFileRemoteFullNameRegex(sftpCollectRule.getBlackFileRemoteFullNameRegex())
                            .setFileModifiedRealStartDate(sftpCollectRule.getFileModifiedRealStartDate())
                            .setFileModifiedRealEndDate(sftpCollectRule.getFileModifiedRealEndDate())
                            .setTimeDifference(sftpCollectRule.getTimeDifference())
                            .setReconnectCount(sftpCollectRule.getReconnectCount()));
        }};
        CONNECTCONF_THREAD_LOCAL_MAP.set(hashMap);
    }

    public static void clear() {
        CONNECTCONF_THREAD_LOCAL_MAP.remove();
    }
}
