package com.guwave.onedata.dataware.collectx.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import javax.validation.constraints.NotNull;

@Data
@Configuration
@ConfigurationProperties(prefix = "spring.sftp")
public class SftpConfig {

    @NotNull
    String localPath;
    @NotNull
    String localZipCompressPath;

}
