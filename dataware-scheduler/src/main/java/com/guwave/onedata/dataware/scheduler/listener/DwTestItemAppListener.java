package com.guwave.onedata.dataware.scheduler.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.dw.ods.OdsFileWarehousingRecord;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import com.guwave.onedata.dataware.common.model.message.CalculatePatchFileMessage;
import com.guwave.onedata.dataware.common.model.message.TestItemFinishMessage;
import com.guwave.onedata.dataware.common.model.message.YmsCalculateEndFlagMessage;
import com.guwave.onedata.dataware.dao.ck.domain.dw.ods.OdsFileWarehousingRecordSink;
import com.guwave.onedata.dataware.dao.mysql.domain.dc.FileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import com.guwave.onedata.dataware.dao.mysql.manager.FileWarehousingRecordManager;
import com.guwave.onedata.dataware.dao.mysql.manager.RetryCkRecordManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dc.FileInfoRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.*;
import com.guwave.onedata.dataware.repair.common.model.Wafer;
import com.guwave.onedata.dataware.repair.common.service.DataRepairService;
import com.guwave.onedata.dataware.repair.common.util.CkUtil;
import com.guwave.onedata.dataware.scheduler.provider.KafKaProvider;
import com.guwave.onedata.dataware.scheduler.service.RedisService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.math3.util.Pair;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Component
public class DwTestItemAppListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(DwTestItemAppListener.class);

    private static final List<TestArea> SUPPORT_CP_TEST_AREA_LIST = TestArea.getCPList();

    private final static Set<FileCategory> SEND_YMS_CALCULATE_END_FLAG_FILE_CATEGORY_SET = Sets.newHashSet(FileCategory.STDF, FileCategory.RAW_DATA);

    @Value("${spring.kafka.calculateEndFlagTopic}")
    private String calculateEndFlagTopic;
    @Value("${spring.kafka.calculatePatchFileTopic}")
    private String calculatePatchFileTopic;
    @Value("${spring.kafka.ymsCalculateEndFlagTopic}")
    private String ymsCalculateEndFlagTopic;
    @Value("${spring.module.name}")
    private String moduleName;

    @Autowired
    private FileInfoRepository fileInfoRepository;
    @Autowired
    private LotStockingDetailRepository lotStockingDetailRepository;
    @Autowired
    private LotMetaDataDetailRepository lotMetaDataDetailRepository;
    @Autowired
    private LotWaferCalStatusRepository lotWaferCalStatusRepository;
    @Autowired
    private LotWaferCalRecordRepository lotWaferCalRecordRepository;
    @Autowired
    private FileLoadingLogRepository fileLoadingLogRepository;
    @Autowired
    private FileWarehousingRecordRepository fileWarehousingRecordRepository;
    @Autowired
    private LotWaferWarehousingRecordRepository lotWaferWarehousingRecordRepository;
    @Autowired
    private TestItemReloadRecordRepository testItemReloadRecordRepository;
    @Autowired
    private FileWarehousingRecordManager fileWarehousingRecordManager;
    @Autowired
    private RedisService redisService;
    @Autowired
    private DataRepairService dataRepairService;
    @Autowired
    private KafKaProvider kafKaProvider;
    @Autowired
    private OdsFileWarehousingRecordSink odsFileWarehousingRecordSink;
    @Autowired
    private DwTableRepository dwTableRepository;
    @Autowired
    private CkUtil ckUtil;
    @Autowired
    private RetryCkRecordManager retryCkRecordManager;

    public void stateChanged(TestItemFinishMessage task) {
        List<LotStockingDetail> lotStockingDetails;
        List<String> fileNames;
        List<String> firstDealFileNames;
        LotWaferCalStatus lotWaferCalStatus;
        RLock updateDwTaskTableLock = redisService.getUpdateDwTaskTableLock(task.getFileCategory(), task.getCustomer(), task.getTestArea(), task.getFactory(), task.getDeviceId(), task.getLotType(), task.getTestStage(), task.getLotId(), task.getWaferNo());
        updateDwTaskTableLock.lock();
        int priority;
        try {
            // 更新lotWaferCalStatus
            lotWaferCalStatus = lotWaferCalStatusRepository.findFirstByCustomerAndTestAreaAndFileCategoryAndFactoryAndDeviceIdAndLotIdAndWaferNoAndTestStageAndLotTypeOrderByUpdateTimeDesc(
                    task.getCustomer(), task.getTestArea(), task.getFileCategory(), task.getFactory(), task.getDeviceId(), task.getLotId(), task.getWaferNo(), task.getTestStage(), task.getLotType()
            );
            lotStockingDetails = lotStockingDetailRepository.findAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoAndFileCategory(
                    task.getCustomer(),
                    task.getTestArea(),
                    task.getFactory(),
                    task.getFactorySite(),
                    task.getDeviceId(),
                    task.getLotType(),
                    task.getTestStage(),
                    task.getLotId(),
                    task.getWaferNo(),
                    task.getFileCategory()
            ).stream().filter(t -> Objects.equals(t.getDieFinishFlag(), 1)).collect(Collectors.toList());
            if (lotWaferCalStatus == null || CollectionUtils.isEmpty(lotStockingDetails) || !Objects.equals(lotWaferCalStatus.getDataVersion(), task.getDataVersion()) || ProcessStatus.SUCCESS != lotWaferCalStatus.getDieStatus()) {
                LOGGER.info("{} TestItem不满足更新lotWaferCalStatus状态的条件：{}", updateDwTaskTableLock.getName(), JSON.toJSONString(task));
                return;
            }
            updateLotWaferCalStatus(lotWaferCalStatus, task);
            // 更新lotWaferCalRecord
            updateLotWaferCalRecord(task, lotWaferCalStatus);
            fileNames = lotStockingDetails.stream().map(LotStockingDetail::getFileName).collect(Collectors.toList());
            firstDealFileNames = lotStockingDetails.stream().filter(t -> Objects.equals(t.getTestItemFinishFlag(), 0)).map(LotStockingDetail::getFileName).collect(Collectors.toList());
            // 记录fileLoadingLog 和 fileWarehousingRecord，同步到fileWarehousingRecord到ck
            updateFileLoadingLogs(task, fileNames);
            // 获取该任务的优先级
            LotMetaDataDetail lotMetaDataDetail = lotMetaDataDetailRepository.findAllByFileNameIn(fileNames).stream().max(Comparator.comparing(LotMetaDataDetail::getPriority)).orElse(null);
            priority = lotMetaDataDetail == null ? 0 : lotMetaDataDetail.getPriority();
            // 更新LotWaferWarehousingRecord
            updateLotWaferWarehousingRecord(task, lotStockingDetails);
            // 更新lotStockingDetail
            updateLotStockingDetail(lotStockingDetails, task);
            // 更新testItemReloadRecord
            updateTestItemReloadRecord(task);
        } finally {
            updateDwTaskTableLock.unlock();
        }

        // testItem任务结束发送消息
        sendCalculateEndFlag(task, lotWaferCalStatus, lotStockingDetails, priority);

        // 发送本次任务首次处理的文件列表
        sendCalculatePatchFile(task, firstDealFileNames, lotStockingDetails);

        // 发送yms消息
        sendYmsCalculateEndFlag(task, lotWaferCalStatus);
    }

    private void sendCalculatePatchFile(TestItemFinishMessage task, List<String> firstDealFileNames, List<LotStockingDetail> lotStockingDetails) {
        if (CollectionUtils.isNotEmpty(firstDealFileNames) && task.getProcessStatus() == ProcessStatus.SUCCESS) {
            LOGGER.info("本次任务首次处理的文件：{}", String.join(ENTER, firstDealFileNames));
            Map<Long, String> patchFileMap = fileInfoRepository.findAllByFileNameInAndUploadTypeAndDeleteFlag(firstDealFileNames, UploadType.AUTO, false)
                    .stream().collect(Collectors.toMap(FileInfo::getId, FileInfo::getFileName, (v1, v2) -> v1));
            CalculatePatchFileMessage calculatePatchFileMessage = buildCalculatePatchFileMessage(task, patchFileMap, lotStockingDetails);
            kafKaProvider.sendKafka(calculatePatchFileTopic, JSON.toJSONString(calculatePatchFileMessage));
        } else {
            LOGGER.info("本次任务没有首次处理的文件");
        }
    }

    private void sendCalculateEndFlag(TestItemFinishMessage task, LotWaferCalStatus lotWaferCalStatus, List<LotStockingDetail> lotStockingDetails, int priority) {
        List<CalculateEndFlag> calculateEndFlags = buildCalculateEndFlags(task, lotWaferCalStatus, lotStockingDetails, Platform.GDP, priority);
        LOGGER.info("任务处理 {},{}", task.getProcessStatus(), JSON.toJSONString(calculateEndFlags.get(0)));
        calculateEndFlags.forEach(t -> kafKaProvider.sendKafka(calculateEndFlagTopic, JSON.toJSONString(t)));
        List<CalculateEndFlag> ckCalculateEndFlags = buildCalculateEndFlags(task, lotWaferCalStatus, lotStockingDetails, Platform.CK, priority);
        ckCalculateEndFlags.forEach(t -> kafKaProvider.sendKafka(calculateEndFlagTopic, JSON.toJSONString(t)));
    }

    private void updateLotStockingDetail(List<LotStockingDetail> lotStockingDetails, TestItemFinishMessage task) {
        Date date = new Date();
        if (task.getProcessStatus() == ProcessStatus.SUCCESS) {
            lotStockingDetails.forEach(t -> t.setFinish(true).setTestItemFinishFlag(1).setUpdateTime(date));
            lotStockingDetailRepository.saveAll(lotStockingDetails);
        }
    }

    private void updateLotWaferCalStatus(LotWaferCalStatus lotWaferCalStatus, TestItemFinishMessage task) {
        Date date = new Date();
        lotWaferCalStatus
                .setTestItemStatus(task.getProcessStatus())
                .setTestItemStartTime(new Date(task.getStartTime()))
                .setTestItemEndTime(new Date(task.getTs()))
                .setTestItemCalTime((lotWaferCalStatus.getTestItemEndTime().getTime() - lotWaferCalStatus.getTestItemStartTime().getTime()) / 1000L)
                .setUpdateTime(date)
                .setUpdateUser(SYSTEM);
        lotWaferCalStatusRepository.save(lotWaferCalStatus);
    }

    private void updateLotWaferCalRecord(TestItemFinishMessage task, LotWaferCalStatus lotWaferCalStatus) {
        Date updateTime = new Date();
        LotWaferCalRecord lotWaferCalRecord = lotWaferCalRecordRepository.findFirstByCustomerAndFactoryAndTestAreaAndFileCategoryAndDeviceIdAndLotIdAndWaferNoAndTestStageAndLotTypeAndLatestFlagOrderByUpdateTimeDesc(
                task.getCustomer(), task.getFactory(), task.getTestArea(), task.getFileCategory(), task.getDeviceId(), task.getLotId(), task.getWaferNo(), task.getTestStage(), task.getLotType(), 1
        );
        lotWaferCalRecord
                .setFinalEndTime(lotWaferCalStatus.getTestItemEndTime())
                .setTestItemCalTime(Lists.newArrayList(lotWaferCalStatus.getTestItemStartTime(), lotWaferCalStatus.getTestItemEndTime()).stream().map(t -> DateFormatUtils.format(t, DEFAULT_TIME_FORMAT)).collect(Collectors.joining(WAVY_LINE)))
                .setTestItemCalConsume(lotWaferCalStatus.getTestItemCalTime())
                .setTestItemStatus(lotWaferCalStatus.getTestItemStatus())
                .setUpdateTime(updateTime)
                .setUpdateUser(SYSTEM);
        lotWaferCalRecordRepository.save(lotWaferCalRecord);
    }

    public void updateFileLoadingLogs(TestItemFinishMessage task, List<String> fileNames) {
        Date date = new Date();
        List<FileLoadingLog> stageFileLoadingLogs = fileLoadingLogRepository.findAllByFileNameInAndStep(fileNames, StepType.STEP_TYPE_7100.getStep());
        stageFileLoadingLogs.forEach(stageFileLoadingLog -> {
            stageFileLoadingLog
                    .setProcessStatus(task.getProcessStatus())
                    .setStepEndTime(date)
                    .setUpdateTime(date)
                    .setFailedType(null)
                    .setFailedFields(null)
                    .setExceptionType(null)
                    .setExceptionMessage(null)
                    .setErrorMessage(null);
            if (task.getProcessStatus() == ProcessStatus.FAIL) {
                FileLoadException fileLoadException = new FileLoadException(FileLoadExceptionInfo.TEST_ITEM_PROCESS_FAIL_EXCEPTION, task.getErrorMessage(), null)
                        .updateExceptionMessage(task.getExceptionMessage());
                stageFileLoadingLog
                        .setFailedType(fileLoadException.getFailedType())
                        .setFailedFields(fileLoadException.getFailedFields())
                        .setExceptionType(fileLoadException.getExceptionType())
                        .setExceptionMessage(fileLoadException.getExceptionMessage())
                        .setErrorMessage(fileLoadException.getErrorMessage());
            }
        });
        fileLoadingLogRepository.saveAll(stageFileLoadingLogs);
        fileWarehousingRecordManager.updateFileWarehousingStatus(stageFileLoadingLogs);

        if (task.getProcessStatus() == ProcessStatus.SUCCESS) {
            RetryCkRecord retryCkRecord;
            try {
                // 7100任务成功，更新ck OdsLotWaferWarehousingRecord
                LOGGER.info("7100任务成功，更新ck OdsLotWaferWarehousingRecord");
                List<OdsFileWarehousingRecord> odsFileWarehousingRecords = this.buildOdsFileWarehousingRecords(stageFileLoadingLogs);
                // 刪除ck
                List<Long> fileIds = odsFileWarehousingRecords.stream().map(OdsFileWarehousingRecord::getFileId).collect(Collectors.toList());
                Wafer wafer = new Wafer(task.getCustomer(), task.getSubCustomer(), task.getTestArea(), task.getFactory(), task.getDeviceId(),
                        task.getLotType(), task.getTestStage(), task.getLotId(), task.getWaferNo(), task.getFileCategory());

                List<DwTable> dwTableList = dwTableRepository.findAllByUploadTypeAndClusterNameAndCalculateFlag(
                        UploadType.AUTO,
                        odsFileWarehousingRecordSink.getTableName().replace(Constant.LOCAL_TABLE, Constant.CLUSTER_TABLE),
                        1);
                Long minUpdateTime = odsFileWarehousingRecords.stream().map(OdsFileWarehousingRecord::getUpdateTime).filter(Objects::nonNull).map(Date::getTime).min(Comparator.naturalOrder()).orElse(System.currentTimeMillis());
                LOGGER.info("删除ck数据，刪除的表：{}, 需要删除的维度信息 = {}, fileIds: {}, minUpdateTime = {}", dwTableList.stream().map(DwTable::getClusterName).collect(Collectors.toList()), wafer, fileIds, minUpdateTime);
                retryCkRecord = new RetryCkRecord()
                        .setModuleName(moduleName)
                        .setSqlType(SqlType.DELETE);
                ckUtil.deleteCkFromFileIds(fileIds, wafer, UploadType.AUTO, dwTableList, UPDATE_TIME, minUpdateTime, retryCkRecordManager.getRetryCkRecordConsumer(retryCkRecord));
                retryCkRecordManager.saveRetryCkRecord(retryCkRecord);

                // 写入ck
                LOGGER.info("写入ck数据，写入的表:{}, dataSize = {}", odsFileWarehousingRecordSink.getTableName(), odsFileWarehousingRecords.size());
                retryCkRecord = new RetryCkRecord()
                        .setModuleName(moduleName)
                        .setSqlType(SqlType.INSERT);
                odsFileWarehousingRecordSink.doHandle(odsFileWarehousingRecords, retryCkRecordManager.getRetryCkRecordConsumer(retryCkRecord));
                retryCkRecordManager.saveRetryCkRecord(retryCkRecord);
            } catch (Exception e) {
                LOGGER.info("写入ck数据，写入的表:{}, 失败", odsFileWarehousingRecordSink.getTableName());
            }
        }
    }

    private void updateLotWaferWarehousingRecord(TestItemFinishMessage task, List<LotStockingDetail> lotStockingDetails) {
        // 任务结束，更新LotWaferWarehousing
        lotWaferWarehousingRecordRepository.updateProcessStatusForTestItem(
                task.getCustomer(),
                task.getTestArea(),
                task.getFactory(),
                task.getDeviceId(),
                task.getLotType(),
                task.getTestStage(),
                task.getLotId(),
                task.getWaferNo(),
                task.getFileCategory(),
                task.getProcessStatus(),
                new Date(),
                task.getProcessStatus()
        );
        Map<Long, List<String>> repairLotWaferIdWithFileNames = lotStockingDetails.stream().filter(t -> t.getWarehousingMode() == WarehousingMode.REPAIR && t.getRepairLotWaferId() != null)
                .collect(Collectors.groupingBy(LotStockingDetail::getRepairLotWaferId, Collectors.mapping(LotStockingDetail::getFileName, Collectors.toList())));
        repairLotWaferIdWithFileNames.forEach((repairLotWaferId, repairFileNames) -> {
            LOGGER.info("修复结束");
            FileLoadExceptionInfo fileLoadExceptionInfo = task.getProcessStatus() != ProcessStatus.SUCCESS ? FileLoadExceptionInfo.TEST_ITEM_PROCESS_FAIL_EXCEPTION : null;
            ExceptionType exceptionType = fileLoadExceptionInfo != null ? fileLoadExceptionInfo.getType() : null;
            String exceptionMessage = fileLoadExceptionInfo != null ? fileLoadExceptionInfo.getMessage() : null;
            String errorMessage = fileLoadExceptionInfo != null ? FileLoadExceptionInfo.TEST_ITEM_PROCESS_FAIL_EXCEPTION.getMessage() : null;
            dataRepairService.repairFinish(repairLotWaferId, repairFileNames, task.getProcessStatus(), exceptionType, exceptionMessage, errorMessage);
        });
    }


    private List<CalculateEndFlag> buildCalculateEndFlags(TestItemFinishMessage task, LotWaferCalStatus lotWaferCalStatus, List<LotStockingDetail> lotStockingDetails, Platform platform, int priority) {
        List<Long> repairLotWaferIds = lotStockingDetails.stream().filter(t -> t.getWarehousingMode() == WarehousingMode.REPAIR && t.getRepairLotWaferId() != null)
                .map(LotStockingDetail::getRepairLotWaferId).sorted().collect(Collectors.toList());
        List<Long> cleanupTaskIdList = lotStockingDetails.stream().map(LotStockingDetail::getCleanupTaskIds)
                .filter(StringUtils::isNotBlank)
                .flatMap(t -> Stream.of(t.split(Constant.COMMA)).map(s -> Long.parseLong(s.trim())))
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        List<String> sblotIdList = TestArea.getCPList().contains(task.getTestArea()) ? Collections.emptyList() : lotStockingDetails.stream()
                .map(LotStockingDetail::getSblotId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .sorted()
                .collect(Collectors.toList());

        List<CalculateEndFlag> calculateEndFlags = new ArrayList<>();
        CalculateEndFlag dwdCalculateEndFlag = new CalculateEndFlag()
                .setCustomer(task.getCustomer())
                .setSubCustomer(task.getSubCustomer())
                .setFactory(task.getFactory())
                .setFactorySite(task.getFactorySite())
                .setTestArea(task.getTestArea())
                .setLotType(task.getLotType())
                .setDeviceId(task.getDeviceId())
                .setTestStage(task.getTestStage())
                .setLotId(task.getLotId())
                .setWaferNo(task.getWaferNo())
                .setSblotId(CollectionUtils.isEmpty(sblotIdList) ? EMPTY : String.join(COMMA, sblotIdList))
                .setFileCategory(task.getFileCategory())
                .setDwLayer(DwLayer.DWD)
                .setNextDwLayer(DwLayer.DWS)
                .setPriority(priority)
                .setPlatform(platform)
                .setWarehousingMode(repairLotWaferIds.isEmpty() ? WarehousingMode.NORMAL : WarehousingMode.REPAIR)
                .setRepairLotWaferId(repairLotWaferIds.isEmpty() ? null : repairLotWaferIds.get(repairLotWaferIds.size() - 1))
                .setCleanupTaskIds(cleanupTaskIdList)
                .setProcessStatus(task.getProcessStatus())
                .setAppId(null)
                .setProcessConsume(lotWaferCalStatus.getTestItemCalTime())
                .setTs(System.currentTimeMillis())
                .setDataVersion(task.getDataVersion())
                .setCalculateDwTestItem(lotWaferCalStatus.getCalculateDwTestItem())
                .setCalculateYmsTestItem(lotWaferCalStatus.getCalculateYmsTestItem())
                .setDieCount(lotWaferCalStatus.getDieTotalCnt())
                .setTestItemCount(lotWaferCalStatus.getTestItemTotalCnt());
        calculateEndFlags.add(dwdCalculateEndFlag);
        if (task.getProcessStatus() == ProcessStatus.FAIL) {
            FileLoadException fileLoadException = new FileLoadException(FileLoadExceptionInfo.TEST_ITEM_PROCESS_FAIL_EXCEPTION, task.getErrorMessage(), null)
                    .updateExceptionMessage(task.getExceptionMessage());
            dwdCalculateEndFlag
                    .setExceptionType(fileLoadException.getExceptionType())
                    .setExceptionMessage(task.getExceptionMessage())
                    .setErrorMessage(task.getErrorMessage());
        } else {
            CalculateEndFlag dwsCalculateEndFlag = new CalculateEndFlag();
            BeanUtils.copyProperties(dwdCalculateEndFlag, dwsCalculateEndFlag);
            dwsCalculateEndFlag.setDwLayer(DwLayer.DWS).setNextDwLayer(DwLayer.ADS);
            calculateEndFlags.add(dwsCalculateEndFlag);
            CalculateEndFlag adsCalculateEndFlag = new CalculateEndFlag();
            BeanUtils.copyProperties(dwdCalculateEndFlag, adsCalculateEndFlag);
            adsCalculateEndFlag.setDwLayer(DwLayer.ADS).setNextDwLayer(DwLayer.NONE);
            calculateEndFlags.add(adsCalculateEndFlag);
        }

        return calculateEndFlags;
    }

    private CalculatePatchFileMessage buildCalculatePatchFileMessage(TestItemFinishMessage task, Map<Long, String> patchFileMap, List<LotStockingDetail> lotStockingDetails) {
        Set<String> fileNames = new HashSet<>(patchFileMap.values());
        List<Long> repairLotWaferIds = lotStockingDetails.stream().filter(t -> fileNames.contains(t.getFileName()) && t.getWarehousingMode() == WarehousingMode.REPAIR && t.getRepairLotWaferId() != null)
                .map(LotStockingDetail::getRepairLotWaferId).sorted().collect(Collectors.toList());
        return new CalculatePatchFileMessage()
                .setCustomer(task.getCustomer())
                .setSubCustomer(task.getSubCustomer())
                .setFactory(task.getFactory())
                .setFactorySite(task.getFactorySite())
                .setTestArea(task.getTestArea())
                .setLotType(task.getLotType())
                .setDeviceId(task.getDeviceId())
                .setLotId(task.getLotId())
                .setWaferNo(task.getWaferNo())
                .setTestStage(task.getTestStage())
                .setFileCategory(task.getFileCategory())
                .setWarehousingMode(repairLotWaferIds.isEmpty() ? WarehousingMode.NORMAL : WarehousingMode.REPAIR)
                .setPatchFileMap(patchFileMap)
                .setProcessStatus(task.getProcessStatus())
                .setTs(System.currentTimeMillis())
                ;
    }

    private List<OdsFileWarehousingRecord> buildOdsFileWarehousingRecords(List<FileLoadingLog> fileLoadingLogs) {
        List<String> fileNames = fileLoadingLogs.stream().map(FileLoadingLog::getFileName).collect(Collectors.toList());

        Map<String, Long> fileIdMap = fileInfoRepository.findAllByFileNameInAndUploadTypeAndDeleteFlag(fileNames, UploadType.AUTO, Boolean.FALSE)
                .stream()
                .collect(Collectors.toMap(FileInfo::getFileName, FileInfo::getId));

        Map<Pair<String, Integer>, FileLoadingLog> fileLoadingLogsMap = fileLoadingLogRepository.findAllByFileNameInAndStepLessThanEqual(fileNames, StepType.STEP_TYPE_7100.getStep())
                .stream()
                .collect(Collectors.toMap(t -> new Pair<>(t.getFileName(), t.getStep()), Function.identity()));

        List<FileWarehousingRecord> fileWarehousingRecords = fileWarehousingRecordRepository.findAllFileWarehousingRecordByFileNameIn(fileNames);

        List<OdsFileWarehousingRecord> odsFileWarehousingRecords = new ArrayList<>(fileWarehousingRecords.size());
        for (FileWarehousingRecord fileWarehousingRecord : fileWarehousingRecords) {
            String fileName = fileWarehousingRecord.getFileName();
            FileLoadingLog step1000FileLoadingLog = fileLoadingLogsMap.getOrDefault(new Pair<>(fileName, StepType.STEP_TYPE_1000.getStep()), null);
            FileLoadingLog step3100FileLoadingLog = fileLoadingLogsMap.getOrDefault(new Pair<>(fileName, StepType.STEP_TYPE_3100.getStep()), null);
            FileLoadingLog step4100FileLoadingLog = fileLoadingLogsMap.getOrDefault(new Pair<>(fileName, StepType.STEP_TYPE_4100.getStep()), null);
            FileLoadingLog step5100FileLoadingLog = fileLoadingLogsMap.getOrDefault(new Pair<>(fileName, StepType.STEP_TYPE_5100.getStep()), null);
            FileLoadingLog step6100FileLoadingLog = fileLoadingLogsMap.getOrDefault(new Pair<>(fileName, StepType.STEP_TYPE_6100.getStep()), null);

            OdsFileWarehousingRecord odsFileWarehousingRecord = new OdsFileWarehousingRecord();
            odsFileWarehousingRecords.add(odsFileWarehousingRecord);
            odsFileWarehousingRecord
                    .setCustomer(fileWarehousingRecord.getCustomer())
                    .setSubCustomer(fileWarehousingRecord.getSubCustomer())
                    .setTestArea(TestArea.of(fileWarehousingRecord.getTestArea()))
                    .setFactory(fileWarehousingRecord.getFactory())
                    .setFactorySite(fileWarehousingRecord.getFactorySite())
                    .setFab(fileWarehousingRecord.getFab())
                    .setFabSite(fileWarehousingRecord.getFabSite())
                    .setFileCategory(FileCategory.of(fileWarehousingRecord.getFileCategory()))
                    .setDeviceId(fileWarehousingRecord.getDeviceId())
                    .setLotId(fileWarehousingRecord.getLotId())
                    .setSblotId(fileWarehousingRecord.getSblotId())
                    .setWaferId(fileWarehousingRecord.getWaferId())
                    .setWaferNo(fileWarehousingRecord.getWaferNo())
                    .setTestStage(fileWarehousingRecord.getTestStage())
                    .setLotType(LotType.of(fileWarehousingRecord.getLotType()))
                    .setStep(fileWarehousingRecord.getStep())
                    .setStepStartTime(fileWarehousingRecord.getStepStartTime())
                    .setStepEndTime(fileWarehousingRecord.getStepEndTime())
                    .setStep1000StartTime(step1000FileLoadingLog == null ? null : step1000FileLoadingLog.getStepStartTime())
                    .setStep1000EndTime(step1000FileLoadingLog == null ? null : step1000FileLoadingLog.getStepEndTime())
                    .setStep3100StartTime(step3100FileLoadingLog == null ? null : step3100FileLoadingLog.getStepStartTime())
                    .setStep3100EndTime(step3100FileLoadingLog == null ? null : step3100FileLoadingLog.getStepEndTime())
                    .setStep4100StartTime(step4100FileLoadingLog == null ? null : step4100FileLoadingLog.getStepStartTime())
                    .setStep4100EndTime(step4100FileLoadingLog == null ? null : step4100FileLoadingLog.getStepEndTime())
                    .setStep5100StartTime(step5100FileLoadingLog == null ? null : step5100FileLoadingLog.getStepStartTime())
                    .setStep5100EndTime(step5100FileLoadingLog == null ? null : step5100FileLoadingLog.getStepEndTime())
                    .setStep6100StartTime(step6100FileLoadingLog == null ? null : step6100FileLoadingLog.getStepStartTime())
                    .setStep6100EndTime(step6100FileLoadingLog == null ? null : step6100FileLoadingLog.getStepEndTime())
                    .setStep7100StartTime(fileWarehousingRecord.getStepStartTime())
                    .setStep7100EndTime(fileWarehousingRecord.getStepEndTime())
                    .setFileId(fileIdMap.getOrDefault(fileName, null))
                    .setFileName(fileWarehousingRecord.getFileName())
                    .setOriginFileName(fileWarehousingRecord.getOriginFileName())
                    .setFileSize(fileWarehousingRecord.getFileSize())
                    .setOriginFileSize(fileWarehousingRecord.getOriginFileSize())
                    .setConvertFlag(fileWarehousingRecord.getConvertFlag())
                    .setSourceFileNames(fileWarehousingRecord.getSourceFileNames())
                    .setHdfsPath(fileWarehousingRecord.getHdfsPath())
                    .setFtpPath(fileWarehousingRecord.getFtpPath())
                    .setFtpIp(fileWarehousingRecord.getFtpIp())
                    .setBatchId(fileWarehousingRecord.getBatchId())
                    .setRemoteFileMtime(fileWarehousingRecord.getRemoteFileMtime())
                    .setTesterName(fileWarehousingRecord.getTesterName())
                    .setTesterType(fileWarehousingRecord.getTesterType())
                    .setTestProgram(fileWarehousingRecord.getTestProgram())
                    .setTestProgramVersion(fileWarehousingRecord.getTestProgramVersion())
                    .setStartT(fileWarehousingRecord.getStartT())
                    .setFinishT(fileWarehousingRecord.getFinishT())
                    .setWarehousingCnt(fileWarehousingRecord.getWarehousingCnt())
                    .setWarehousingMode(WarehousingMode.of(fileWarehousingRecord.getWarehousingMode()))
                    .setDataIntegrityFileComment(fileWarehousingRecord.getDataIntegrityFileComment())
                    .setDataIntegrityFileLabel(DataIntegrityFileLabel.of(fileWarehousingRecord.getDataIntegrityFileLabel()))
                    .setRepairLotWaferId(fileWarehousingRecord.getRepairLotWaferId())
                    .setCleanTaskIds(fileWarehousingRecord.getCleanupTaskIds())
                    .setProcessStatus(ProcessStatus.of(fileWarehousingRecord.getProcessStatus()))
                    .setFailedType(fileWarehousingRecord.getFailedType())
                    .setFailedFields(fileWarehousingRecord.getFailedFields())
                    .setExceptionType(ExceptionType.of(fileWarehousingRecord.getExceptionType()))
                    .setExceptionMessage(fileWarehousingRecord.getExceptionMessage())
                    .setErrorMessage(fileWarehousingRecord.getErrorMessage())
                    .setWarningMessage(fileWarehousingRecord.getWarningMessage())
                    .setCreateTime(fileWarehousingRecord.getCreateTime())
                    .setUpdateTime(fileWarehousingRecord.getUpdateTime())
                    .setCreateUser(fileWarehousingRecord.getCreateUser())
                    .setUpdateUser(fileWarehousingRecord.getUpdateUser())
                    .setIsDelete(0);
        }
        return odsFileWarehousingRecords;
    }

    private void sendYmsCalculateEndFlag(TestItemFinishMessage task, LotWaferCalStatus lotWaferCalStatus) {
        if (!SEND_YMS_CALCULATE_END_FLAG_FILE_CATEGORY_SET.contains(task.getFileCategory())) {
            return;
        }
        YmsCalculateEndFlagMessage ymsCalculateEndFlagMessage = new YmsCalculateEndFlagMessage()
                .setCustomer(task.getCustomer())
                .setSubCustomer(task.getSubCustomer())
                .setFactory(task.getFactory())
                .setFactorySite(task.getFactorySite())
                .setTestArea(task.getTestArea())
                .setLotType(task.getLotType())
                .setDeviceId(task.getDeviceId())
                .setLotId(task.getLotId())
                .setWaferNo(task.getWaferNo())
                .setTestStage(task.getTestStage())
                .setFileCategory(task.getFileCategory())
                .setVersion(task.getDataVersion())
                .setDataVersion(task.getDataVersion())
                .setCalculateDwTestItem(lotWaferCalStatus.getCalculateDwTestItem())
                .setCalculateYmsTestItem(lotWaferCalStatus.getCalculateYmsTestItem())
                .setProcessStatus(task.getProcessStatus())
                .setExceptionMessage(task.getExceptionMessage())
                .setErrorMessage(task.getErrorMessage())
                .setStartTime(lotWaferCalStatus.getDieStartTime().getTime())
                .setTs(System.currentTimeMillis());
        String ymsMessage = JSON.toJSONString(ymsCalculateEndFlagMessage);
        LOGGER.info("发送yms消息：{}", ymsMessage);
        kafKaProvider.sendKafka(ymsCalculateEndFlagTopic, ymsMessage);
    }

    private void updateTestItemReloadRecord(TestItemFinishMessage task) {
        LOGGER.info("更新测试项重载记录：lotId:{}, waferNo:{}", task.getLotId(), task.getWaferNo());
        try {
            if (task.getProcessStatus() == ProcessStatus.SUCCESS) {
                testItemReloadRecordRepository.updateProcessStatusFromLotWafer(
                        task.getCustomer(),
                        task.getSubCustomer(),
                        task.getTestArea(),
                        task.getFactory(),
                        task.getFactorySite(),
                        task.getDeviceId(),
                        task.getLotType(),
                        task.getTestStage(),
                        task.getLotId(),
                        task.getWaferNo(),
                        task.getFileCategory(),
                        task.getCalculateDwTestItem(),
                        task.getCalculateYmsTestItem(),
                        task.getProcessStatus()
                );
            }
        } catch (Exception e) {
            LOGGER.info("更新测试项重载记录异常：lotId:{}, waferNo:{}", task.getLotId(), task.getWaferNo(), e);
        }
    }

}
