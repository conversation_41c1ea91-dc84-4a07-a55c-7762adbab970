package com.guwave.onedata.dataware.source.agent.manual.handler.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.manual.ManualCalculateFinishVo;
import com.guwave.onedata.dataware.common.model.manual.ManualFileFieldVo;
import com.guwave.onedata.dataware.common.model.manual.ManualUploadVo;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingFinishMessage;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingMessage;
import com.guwave.onedata.dataware.common.util.FlowIdUtil;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualMessage;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualCalculateTaskRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualFileInfoRepository;
import com.guwave.onedata.dataware.source.agent.manual.handler.ManualCalculateTaskHandler;
import com.guwave.onedata.dataware.source.agent.manual.handler.ManualTypehandler;
import com.guwave.onedata.dataware.source.agent.manual.util.CkUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Component
public class ManualUploadHandler implements ManualTypehandler, ManualCalculateTaskHandler {


    private static final List<ManualEditField> KEY_EDIT_FIELD_LIST = Lists.newArrayList(ManualEditField.FLOW_ID);
    private static final Map<ManualEditField, Pair<BiFunction<ManualUploadVo, String, String>, BiConsumer<ManualFileInfo, String>>> MANUAL_EDIT_FIELD_CHECK_AND_FILL_MAP = new HashMap<ManualEditField, Pair<BiFunction<ManualUploadVo, String, String>, BiConsumer<ManualFileInfo, String>>>() {{
        put(ManualEditField.TEST_AREA, Pair.of(
                (manualUploadVo, value) -> {
                    TestArea testArea = TestArea.of(value);
                    if (testArea == null) {
                        return FIELD_IS_EMPTY;
                    } else if (testArea == TestArea.WAT && manualUploadVo.getFileCategory() == FileCategory.STDF) {
                        return "不能为WAT";
                    }
                    return null;
                },
                (manualFileInfo, value) -> manualFileInfo.setTestArea(TestArea.of(value)))
        );
        put(ManualEditField.DEVICE_ID, Pair.of(
                (manualUploadVo, value) -> {
                    if (StringUtils.isBlank(value)) {
                        return FIELD_IS_EMPTY;
                    }
                    return null;
                },
                ManualFileInfo::setDeviceId)
        );
        put(ManualEditField.TEST_STAGE, Pair.of(
                (manualUploadVo, value) -> {
                    if (StringUtils.isBlank(value)) {
                        return FIELD_IS_EMPTY;
                    }
                    return null;
                },
                ManualFileInfo::setTestStage)
        );
        put(ManualEditField.FACTORY, Pair.of(
                (manualUploadVo, value) -> {
                    if (StringUtils.isBlank(value)) {
                        return FIELD_IS_EMPTY;
                    }
                    return null;
                },
                ManualFileInfo::setFactory)
        );
        put(ManualEditField.FACTORY_SITE, Pair.of(
                (manualUploadVo, value) -> {
                    if (StringUtils.isBlank(value)) {
                        return FIELD_IS_EMPTY;
                    }
                    return null;
                },
                ManualFileInfo::setFactorySite)
        );
        put(ManualEditField.LOT_TYPE, Pair.of(
                (manualUploadVo, value) -> {
                    if (LotType.of(value) == null) {
                        return FIELD_IS_EMPTY;
                    }
                    return null;
                },
                (manualFileInfo, value) -> manualFileInfo.setLotType(LotType.of(value)))
        );
        put(ManualEditField.LOT_ID, Pair.of(
                (manualUploadVo, value) -> {
                    if (StringUtils.isBlank(value)) {
                        return FIELD_IS_EMPTY;
                    }
                    return null;
                },
                ManualFileInfo::setLotId)
        );
        put(ManualEditField.SBLOT_ID, Pair.of(
                (manualUploadVo, value) -> {
                    if (StringUtils.isBlank(value)) {
                        return FIELD_IS_EMPTY;
                    }
                    return null;
                },
                ManualFileInfo::setSblotId)
        );
        put(ManualEditField.WAFER_NO, Pair.of(
                (manualUploadVo, value) -> {
                    try {
                        int no = Integer.parseInt(value);
                        if (no < 1 || no > 25) {
                            throw new RuntimeException("非法数字");
                        }
                    } catch (Exception e) {
                        return FIELD_IS_ILLEGALITY;
                    }
                    return null;
                },
                (manualFileInfo, value) -> manualFileInfo.setWaferNo(Integer.valueOf(value) + EMPTY))
        );
        put(ManualEditField.TESTER, Pair.of(
                (manualUploadVo, value) -> {
                    if (StringUtils.isBlank(value)) {
                        return FIELD_IS_EMPTY;
                    }
                    return null;
                },
                ManualFileInfo::setTesterName)
        );
        put(ManualEditField.PROBER_HANDLER, Pair.of(
                (manualUploadVo, value) -> {
                    if (StringUtils.isBlank(value)) {
                        return FIELD_IS_EMPTY;
                    }
                    return null;
                },
                ManualFileInfo::setProberHandlerId)
        );
        put(ManualEditField.TEST_PROGRAM, Pair.of(
                (manualUploadVo, value) -> {
                    if (StringUtils.isBlank(value)) {
                        return FIELD_IS_EMPTY;
                    }
                    return null;
                },
                ManualFileInfo::setTestProgram)
        );
        put(ManualEditField.TEST_TEMPERATURE, Pair.of(
                (manualUploadVo, value) -> {
                    if (StringUtils.isBlank(value)) {
                        return FIELD_IS_EMPTY;
                    }
                    return null;
                },
                ManualFileInfo::setTestTemperature)
        );
        put(ManualEditField.PROCESS_CORNER, Pair.of(
                (manualUploadVo, value) -> {
                    if (StringUtils.isBlank(value)) {
                        return FIELD_IS_EMPTY;
                    }
                    return null;
                },
                ManualFileInfo::setProcess)
        );
        put(ManualEditField.WAFER_NOTCH, Pair.of(
                (manualUploadVo, value) -> {
                    if (StringUtils.isBlank(value)) {
                        return FIELD_IS_EMPTY;
                    }
                    return null;
                },
                ManualFileInfo::setNotch)
        );
        put(ManualEditField.FLOW_ID, Pair.of(
                (manualUploadVo, value) -> {
                    if (StringUtils.isBlank(value)) {
                        return FIELD_IS_EMPTY;
                    } else if (!FlowIdUtil.FLOW_ID_PATTERN.matcher(value).matches()) {
                        return FIELD_IS_ILLEGALITY;
                    }
                    return null;
                },
                ManualFileInfo::setFlowId)
        );
    }};

    @Autowired
    private ManualFileInfoRepository manualFileInfoRepository;
    @Autowired
    private ManualCalculateTaskRepository manualCalculateTaskRepository;
    @Autowired
    private CkUtil ckUtil;

    @Override
    public Boolean support(ManualType manualType) {
        return manualType == ManualType.UPLOAD;
    }

    @Override
    public void generateTask(ManualMessage manualMessage, ManualWarehousingMessage manualWarehousingMessage) {
        ManualUploadVo manualUploadVo = manualWarehousingMessage.getManualUploadVo();

        // 校验参数
        checkParam(manualUploadVo);

        // 生成 manual_file_info
        Date date = new Date();
        ManualFileInfo manualFileInfo = manualFileInfoRepository.findByFileId(manualUploadVo.getFileId());
        boolean retryFlag = false;
        if (manualFileInfo == null) {
            manualFileInfo = new ManualFileInfo()
                    .setCreateTime(date)
                    .setCreateUser(SYSTEM);
        } else {
            retryFlag = true;
            ckUtil.deleteCkFromFileIds(Lists.newArrayList(manualUploadVo.getFileId()));
        }
        manualFileInfo
                .setCustomer(manualUploadVo.getCustomer())
                .setSubCustomer(manualUploadVo.getSubCustomer())
                .setFileId(manualUploadVo.getFileId())
                .setFileName(manualUploadVo.getFileName())
                .setFileCategory(manualUploadVo.getFileCategory())
                .setParseRule(manualUploadVo.getParseRule())
                .setParseSetting(manualUploadVo.getParseSetting() != null ? JSON.toJSONString(manualUploadVo.getParseSetting()) : EMPTY_JSON)
                .setFileOwner(manualUploadVo.getFileOwner())
                .setUploadType(UploadType.MANUAL)
                .setFilePath(manualUploadVo.getManualFileSystem() != null ? manualUploadVo.getFilePath() : manualUploadVo.getHdfsPath())
                .setScriptPath(manualUploadVo.getManualFileSystem() != null ? manualUploadVo.getScriptPath() : manualUploadVo.getScriptHdfsPath())
                .setConditionSet(manualUploadVo.getConditionSet())
                .setManualFileSystem(manualUploadVo.getManualFileSystem() == null ? ManualFileSystem.HDFS : manualUploadVo.getManualFileSystem())
                .setFieldMapping(manualUploadVo.getFieldMapping())
                .setParameterType(manualUploadVo.getParameterType())
                .setTestParameter(manualUploadVo.getTestParameter())
                .setScript(manualUploadVo.getScript())
                .setDeleteFlag(0)
                .setUpdateUser(SYSTEM)
                .setUpdateTime(date);
        if (StringUtils.isNotBlank(manualFileInfo.getScriptPath())) {
            if (StringUtils.isNotBlank(manualUploadVo.getScriptEntry())) {
                manualFileInfo.setScriptExecuteFileName(manualUploadVo.getScriptEntry().startsWith(SLASH) ? manualUploadVo.getScriptEntry().substring(1) : manualUploadVo.getScriptEntry());
            } else {
                manualFileInfo.setScriptExecuteFileName(manualFileInfo.getScriptPath().substring(manualFileInfo.getScriptPath().lastIndexOf(Constant.SLASH) + 1));
            }
        }

        // 填充修改的字段
        ManualFileInfo finalManualFileInfo = manualFileInfo;
        manualUploadVo.getManualFileFieldVos().stream()
                .filter(t -> MANUAL_EDIT_FIELD_CHECK_AND_FILL_MAP.containsKey(t.getManualEditField()))
                .forEach(t -> MANUAL_EDIT_FIELD_CHECK_AND_FILL_MAP.get(t.getManualEditField()).getRight().accept(finalManualFileInfo, t.getModifyValue()));

        // 额外字段
        Map<String, String> extInfo = manualUploadVo.getManualFileFieldVos().stream()
                .filter(t -> (!MANUAL_EDIT_FIELD_CHECK_AND_FILL_MAP.containsKey(t.getManualEditField())) || manualUploadVo.getFileCategory() == FileCategory.SHMOO)
                .collect(Collectors.toMap(t -> t.getManualEditField().getField(), ManualFileFieldVo::getModifyValue, (v1, v2) -> v1));
        manualFileInfo.setExtInfo(JSON.toJSONString(extInfo));

        // 保存 manual_file_info
        manualFileInfoRepository.save(manualFileInfo);

        // 生成计算任务
        ManualCalculateTask manualCalculateTask = generateManualCalculateTaskFromManualFileInfo(manualMessage, manualFileInfo)
                .setManualType(ManualType.UPLOAD)
                .setDwLayer(DwLayer.ODS)
                .setNeedReadFileIds(manualFileInfo.getFileId() + EMPTY)
                .setNeedDeleteFileIds(retryFlag ? manualFileInfo.getFileId() + EMPTY : EMPTY);

        // 保存 manual_calculate_task
        manualCalculateTaskRepository.save(manualCalculateTask);
    }

    @Override
    public void fillManualWarehousingFinishMessage(ManualWarehousingFinishMessage manualWarehousingFinishMessage, ManualWarehousingMessage manualWarehousingMessage) {
        ManualUploadVo manualUploadVo = manualWarehousingMessage.getManualUploadVo();
        ManualCalculateFinishVo manualCalculateFinishVo = new ManualCalculateFinishVo()
                .setFileId(manualUploadVo.getFileId())
                .setFileName(manualUploadVo.getFileName())
                .setDwLayer(DwLayer.ODS);
        manualWarehousingFinishMessage.setManualCalculateFinishVo(manualCalculateFinishVo);
    }

    private void checkParam(ManualUploadVo manualUploadVo) {
        StringBuilder errorMessageSb = new StringBuilder();
        if (StringUtils.isBlank(manualUploadVo.getCustomer())) {
            errorMessageSb.append("customer").append(FIELD_IS_EMPTY).append(ENTER);
        }
        if (StringUtils.isBlank(manualUploadVo.getSubCustomer())) {
            errorMessageSb.append("subCustomer").append(FIELD_IS_EMPTY).append(ENTER);
        }
        if (manualUploadVo.getFileId() == null) {
            errorMessageSb.append("fileId").append(FIELD_IS_EMPTY).append(ENTER);
        }
        if (StringUtils.isBlank(manualUploadVo.getFileName())) {
            errorMessageSb.append("fileName").append(FIELD_IS_EMPTY).append(ENTER);
        }
        if (manualUploadVo.getFileOwner() == null) {
            errorMessageSb.append("fileOwner").append(FIELD_IS_EMPTY).append(ENTER);
        }
        if (manualUploadVo.getFileCategory() != FileCategory.STDF) {
            if (Arrays.asList(ParseRule.SHMOO_SCRIPT, ParseRule.RAW_DATA_SCRIPT).contains(manualUploadVo.getParseRule())) {
                manualUploadVo.setFileCategory(manualUploadVo.getParseRule() == ParseRule.SHMOO_SCRIPT ? FileCategory.SHMOO : FileCategory.RAW_DATA);
                if (StringUtils.isBlank(manualUploadVo.getScriptHdfsPath())) {
                    errorMessageSb.append("scriptHdfsPath").append(FIELD_IS_EMPTY).append(ENTER);
                }
            } else if (manualUploadVo.getParseRule() == ParseRule.RAW_DATA_FIELD_MAPPING) {
                manualUploadVo.setFileCategory(FileCategory.RAW_DATA);
                if (manualUploadVo.getFieldMapping() == null || manualUploadVo.getFieldMapping().isEmpty()) {
                    errorMessageSb.append("fieldMapping").append(FIELD_IS_EMPTY).append(ENTER);
                }
            }
        }
        if (StringUtils.isBlank(manualUploadVo.getHdfsPath()) && StringUtils.isBlank(manualUploadVo.getFilePath())) {
            errorMessageSb.append("hdfsPath&filePath").append(FIELD_IS_EMPTY).append(ENTER);
        }
        if (manualUploadVo.getParseRule() == null) {
            errorMessageSb.append("parseRule").append(FIELD_IS_EMPTY).append(ENTER);
        }
        if (manualUploadVo.getFileCategory() != FileCategory.SHMOO) {
            List<ManualFileFieldVo> manualFileFieldVos = manualUploadVo.getManualFileFieldVos();
            if (CollectionUtils.isNotEmpty(manualFileFieldVos)) {
                Set<ManualEditField> manualEditFields = manualFileFieldVos.stream().map(ManualFileFieldVo::getManualEditField).collect(Collectors.toSet());
                KEY_EDIT_FIELD_LIST.stream().filter(t -> !manualEditFields.contains(t)).forEach(t -> errorMessageSb.append(FileLoadExceptionInfo.KEY_FIELD_NULL_EXCEPTION.getMessage().replace(KEY_FIELD, t.getField())).append(ENTER));
                manualFileFieldVos.stream().filter(t -> MANUAL_EDIT_FIELD_CHECK_AND_FILL_MAP.containsKey(t.getManualEditField())).forEach(t -> {
                    String checkRes = MANUAL_EDIT_FIELD_CHECK_AND_FILL_MAP.get(t.getManualEditField()).getLeft().apply(manualUploadVo, t.getModifyValue());
                    if (StringUtils.isNotBlank(checkRes)) {
                        errorMessageSb.append(t.getManualEditField().getField()).append(checkRes).append(ENTER);
                    }
                });
            } else {
                KEY_EDIT_FIELD_LIST.forEach(t -> errorMessageSb.append(FileLoadExceptionInfo.KEY_FIELD_NULL_EXCEPTION.getMessage().replace(KEY_FIELD, t.getField())).append(ENTER));
            }
        }

        String errorMessage = errorMessageSb.toString();
        if (!errorMessage.isEmpty()) {
            throw new FileLoadException(FileLoadExceptionInfo.PARAM_CKECK_FAIL, errorMessage, null).updateExceptionMessage(errorMessage);
        }
    }
}
