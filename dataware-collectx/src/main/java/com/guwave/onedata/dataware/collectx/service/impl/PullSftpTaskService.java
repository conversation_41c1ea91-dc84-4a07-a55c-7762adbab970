package com.guwave.onedata.dataware.collectx.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.collectx.core.FTPSyncCore;
import com.guwave.onedata.dataware.collectx.core.IFTPSyncCore;
import com.guwave.onedata.dataware.collectx.core.LocalSyncCore;
import com.guwave.onedata.dataware.collectx.core.SFTPSyncCore;
import com.guwave.onedata.dataware.collectx.entity.FTPFileAttr;
import com.guwave.onedata.dataware.collectx.manager.redis.SftpFileRedisManager;
import com.guwave.onedata.dataware.collectx.manager.thread.*;
import com.guwave.onedata.dataware.collectx.util.ThreadPoolUtils;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.util.AESUtil;
import com.guwave.onedata.dataware.common.util.DateTimeConvertUtil;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;
import static com.guwave.onedata.dataware.common.util.DateTimeConvertUtil.NORM_DATETIME_PATTERN;

@Slf4j
@Service
public class PullSftpTaskService {

    public static final String CAN_NOT_EMPTY = "不能为空";

    public static final Map<FtpType, Supplier<IFTPSyncCore>> ftpSyncCoreMap = new HashMap<FtpType, Supplier<IFTPSyncCore>>() {{
        put(FtpType.FTP, FTPSyncCore::new);
        put(FtpType.SFTP, SFTPSyncCore::new);
        put(FtpType.LOCAL, LocalSyncCore::new);
    }};

    private final static String THREAD_PREFIX = "pull_sftp_task";
    private final AtomicInteger threadActiveCount = new AtomicInteger(0);
    private ThreadPoolExecutor THREAD_POOL_EXECUTOR;

    @Value("${spring.pullTask.convertCorePoolSize}")
    private Integer convertCorePoolSize;

    @Autowired
    private SftpCollectRuleRepository sftpCollectRuleRepository;
    @Autowired
    private SftpBatchInfoRepository sftpBatchInfoRepository;
    @Autowired
    private SftpFileDetailRepository sftpFileDetailRepository;
    @Autowired
    private PullFileHandler pullFileHandler;
    @Autowired
    private FileLoadingLogRepository fileLoadingLogRepository;
    @Autowired
    private FactoryEnumRepository factoryEnumRepository;
    @Autowired
    private SftpSubCustomerRuleRepository sftpSubCustomerRuleRepository;
    @Autowired
    private FileRenameRuleRepository fileRenameRuleRepository;
    @Autowired
    private SftpFileConvertRecordRepository sftpFileConvertRecordRepository;
    @Autowired
    private LotMetaDataDetailRepository lotMetaDataDetailRepository;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private SftpLogRepository sftpLogRepository;

    @PostConstruct
    public void init() {
        THREAD_POOL_EXECUTOR = ThreadPoolUtils.getNewThreadPoolExecutor(
                THREAD_PREFIX, convertCorePoolSize, convertCorePoolSize, convertCorePoolSize
        );
    }

    public void pullTask() {
        if (threadActiveCount.get() >= THREAD_POOL_EXECUTOR.getCorePoolSize()) {
            log.info("{} 线程池已达最大处理数！", THREAD_PREFIX);
            return;
        }

        // 获取需要拉取的任务
        SftpCollectRule sftpCollectRule = getTask();
        if (sftpCollectRule == null) {
            return;
        }

        // 执行拉取任务
        threadActiveCount.incrementAndGet();
        THREAD_POOL_EXECUTOR.execute(() -> {
                    try {
                        runTask(sftpCollectRule);
                    } finally {
                        threadActiveCount.decrementAndGet();
                    }
                }
        );
    }

    private SftpCollectRule getTask() {
        String collectRuleId = SftpFileRedisManager.pullOneCollectRule();
        if (StringUtils.isBlank(collectRuleId)) {
            log.info("pullTask 没有待拉取任务");
            return null;
        }

        Optional<SftpCollectRule> sftpCollectRuleOption = sftpCollectRuleRepository.findById(Long.valueOf(collectRuleId));
        if (!sftpCollectRuleOption.isPresent() || !Objects.equals(0, sftpCollectRuleOption.get().getDeleteFlag())) {
            log.info("collectRuleId : {} 对应的mysql记录不存在", collectRuleId);
            return null;
        }

        SftpCollectRule sftpCollectRule = sftpCollectRuleOption.get();
        if (!Integer.valueOf(1).equals(sftpCollectRule.getStatus())) {
            log.info("collectRuleId : {} 未开启", collectRuleId);
            return null;
        }

        return sftpCollectRule;
    }

    public void fillSftpCollectRule(SftpCollectRule sftpCollectRule, boolean saveSftpBatchInfoFlag) {
        Long collectRuleId = sftpCollectRule.getId();
        // 此collectRuleId最新的拉取记录
        List<SftpBatchInfo> sftpBatchInfos = sftpBatchInfoRepository.findAllByCollectRuleIdOrderByIdDesc(collectRuleId, Pageable.ofSize(1));
        SftpBatchInfo latestSftpBatchInfo = null;
        if (CollectionUtils.isNotEmpty(sftpBatchInfos)) {
            latestSftpBatchInfo = sftpBatchInfos.get(0);
        }
        long currentTime = System.currentTimeMillis();
        if (latestSftpBatchInfo != null) {
            long sftpBatchInfoExpireTime = 24L * 60L * 60L * 1000L;
            if (latestSftpBatchInfo.getBatchStatus() == BatchInfoStatus.PROCESSING) {
                long difTime = currentTime - latestSftpBatchInfo.getCreateTime().getTime();
                if (difTime < sftpBatchInfoExpireTime) {
                    String errorMsg = "collectRuleId : " + collectRuleId + " 长时间在运行中 已运行 " + difTime / 1000 + " 秒";
                    throw new FileLoadException(FileLoadExceptionInfo.PARAM_CKECK_FAIL, errorMsg, null).updateExceptionMessage(errorMsg);
                } else {
                    List<FileLoadingLog> fileLoadingLogs = fileLoadingLogRepository.findAllByBatchIdOrderByIdDesc(latestSftpBatchInfo.getId(), Pageable.ofSize(1));
                    if (CollectionUtils.isNotEmpty(fileLoadingLogs)) {
                        FileLoadingLog fileLoadingLog = fileLoadingLogs.get(0);
                        difTime = currentTime - (fileLoadingLog.getStepEndTime() != null ? fileLoadingLog.getStepEndTime() : fileLoadingLog.getCreateTime()).getTime();
                        if (difTime < sftpBatchInfoExpireTime) {
                            String errorMsg = "collectRuleId : " + collectRuleId + " 可能还正在运行中 距离上一次拉取的文件间隔时间为 " + difTime / 1000 + " 秒";
                            throw new FileLoadException(FileLoadExceptionInfo.PARAM_CKECK_FAIL, errorMsg, null).updateExceptionMessage(errorMsg);
                        }
                    }
                }
            }
        }

        // 校验拉取文件规则
        validateSftpCollectRule(sftpCollectRule, saveSftpBatchInfoFlag);

        // 设置本次实际拉取文件的起止时间
        long realStartTime = sftpCollectRule.getFileModifiedStartDate().getTime();
        long realEndTime = Math.min(currentTime - sftpCollectRule.getBeforeSeconds() * 1000L, sftpCollectRule.getFileModifiedEndDate().getTime());
        if (sftpCollectRule.getEachTimeDownAllFileFlag() == 1) {
            // 全量拉取时
        } else {
            // 滚动窗口拉取时
            if (latestSftpBatchInfo != null && latestSftpBatchInfo.getSftpFileLastMtime() != null) {
                // 此sftpCollectRule存在拉取的记录,从上次结束的时间开始拉取
                realStartTime = Math.max(realStartTime, latestSftpBatchInfo.getSftpFileLastMtime().getTime());
            } else {
                // 此sftpCollectRule第一次拉取
            }
            realEndTime = Math.min(realEndTime, realStartTime + sftpCollectRule.getDaysForOneScheduler() * 24L * 60L * 60L * 1000L);
        }
        if (realStartTime >= realEndTime) {
            String errorMsg = "collectRuleId : " + collectRuleId + " 真实的起始拉取时间 " + realStartTime + " 不小于 真实的结束拉取时间 " + realEndTime + " 不进行拉取";
            throw new FileLoadException(FileLoadExceptionInfo.PARAM_CKECK_FAIL, errorMsg, null).updateExceptionMessage(errorMsg);
        }

        sftpCollectRule
                .setFileModifiedRealStartDate(new Date(realStartTime))
                .setFileModifiedRealEndDate(new Date(realEndTime))
                .setLatestSftpBatchInfo(latestSftpBatchInfo)
        ;
        SftpBatchInfo newSftpBatchInfo = new SftpBatchInfo()
                .setCollectRuleId(collectRuleId)
                .setCustomer(sftpCollectRule.getCustomer())
                .setFactory(sftpCollectRule.getFactory())
                .setFactorySite(sftpCollectRule.getFactorySite())
                .setFab(sftpCollectRule.getFab())
                .setFabSite(sftpCollectRule.getFabSite())
                .setTestArea(sftpCollectRule.getTestArea())
                .setFtpIp(sftpCollectRule.getServer())
                .setSftpFileLastMtime(new Date(realStartTime))
                .setBatchStatus(BatchInfoStatus.PROCESSING)
                .setCollectRuleSnapshot(JSON.toJSONString(sftpCollectRule))
                .setCreateTime(new Date(currentTime))
                .setUpdateTime(new Date(currentTime))
                .setCreateUser(SYSTEM)
                .setUpdateUser(SYSTEM);
        sftpCollectRule.setNewSftpBatchInfo(newSftpBatchInfo);
    }

    private void validateSftpCollectRule(SftpCollectRule sftpCollectRule, boolean saveSftpBatchInfoFlag) {
        StringBuilder errorMessageSb = new StringBuilder();

        if (sftpCollectRule.getExtInfo() == null) {
            sftpCollectRule.setExtInfo(new HashMap<>());
        }
        if (StringUtils.isBlank(sftpCollectRule.getCustomer())) {
            errorMessageSb.append("customer").append(CAN_NOT_EMPTY).append(ENTER);
        } else {
            String validateLicense = sftpCollectRule.findExtProp("validateLicense");
            if (StringUtils.isBlank(validateLicense) || !"false".equalsIgnoreCase(validateLicense)) {
                // 校验license
                Long dataSize = getDataSize(sftpCollectRule.getCustomer());
                Long waferCnt = getWaferCnt(sftpCollectRule.getCustomer());
                licenseService.check(sftpCollectRule.getCustomer(), dataSize, waferCnt, errorMessageSb);
            }
        }
        if (StringUtils.isBlank(sftpCollectRule.getFactory())) {
            errorMessageSb.append("factory").append(CAN_NOT_EMPTY).append(ENTER);
        }
        if (StringUtils.isBlank(sftpCollectRule.getFactorySite())) {
            errorMessageSb.append("factorySite").append(CAN_NOT_EMPTY).append(ENTER);
        }

        TestArea testArea = sftpCollectRule.getTestArea();
        if (testArea == null) {
            errorMessageSb.append("testArea").append(CAN_NOT_EMPTY).append(ENTER);
        } else if (testArea == TestArea.CP || testArea == TestArea.FT) {
            sftpCollectRule.setFab(EMPTY).setFabSite(EMPTY);
        } else if (testArea == TestArea.WAT) {
            if (StringUtils.isBlank(sftpCollectRule.getFab())) {
                errorMessageSb.append("WAT时fab").append(CAN_NOT_EMPTY).append(ENTER);
            }
            if (StringUtils.isBlank(sftpCollectRule.getFabSite())) {
                errorMessageSb.append("WAT时fabSite").append(CAN_NOT_EMPTY).append(ENTER);
            }
        } else {
            errorMessageSb.append("testArea").append("只能是 : CP、FT、WAT").append(ENTER);
        }

        FtpType type = sftpCollectRule.getType();
        if (type == null) {
            errorMessageSb.append("文件服务器类型").append(CAN_NOT_EMPTY).append(ENTER);
        } else if (type != FtpType.LOCAL) {
            if (type == FtpType.FTP) {
                if (sftpCollectRule.getUseTls() == null) {
                    sftpCollectRule.setUseTls(0);
                }
            } else {
                sftpCollectRule.setUseTls(0);
            }
            if (StringUtils.isBlank(sftpCollectRule.getServer())) {
                errorMessageSb.append("文件服务器类型非LOCAL时,IP地址").append(CAN_NOT_EMPTY).append(ENTER);
            }
            if (sftpCollectRule.getPort() == null) {
                errorMessageSb.append("文件服务器类型非LOCAL时,端口").append(CAN_NOT_EMPTY).append(ENTER);
            }
            if (StringUtils.isBlank(sftpCollectRule.getUsername())) {
                errorMessageSb.append("文件服务器类型非LOCAL时,用户名").append(CAN_NOT_EMPTY).append(ENTER);
            }
            sftpCollectRule.setDecryptPassword(AESUtil.Decrypt(sftpCollectRule.getPassword()));
            if (StringUtils.isBlank(sftpCollectRule.getPassword()) || StringUtils.isBlank(sftpCollectRule.getDecryptPassword())) {
                errorMessageSb.append("文件服务器类型非LOCAL时,密码").append(CAN_NOT_EMPTY).append(ENTER);
            }
            if (sftpCollectRule.getTimeout() == null || sftpCollectRule.getTimeout() <= 0) {
                sftpCollectRule.setTimeout(180);
            } else if (sftpCollectRule.getTimeout() > 600) {
                sftpCollectRule.setTimeout(600);
            }
            if (sftpCollectRule.getReconnectCount() == null || sftpCollectRule.getReconnectCount() <= 0) {
                sftpCollectRule.setReconnectCount(5);
            } else if (sftpCollectRule.getReconnectCount() > 20) {
                sftpCollectRule.setReconnectCount(20);
            }
            if (sftpCollectRule.getTimeDifference() == null) {
                sftpCollectRule.setTimeDifference(0L);
            }
        } else {
            if (StringUtils.isBlank(sftpCollectRule.getServer())) {
                errorMessageSb.append("文件服务器类型是LOCAL时,IP地址").append(CAN_NOT_EMPTY).append(" ,将用在匹配脚本等相关用到此值的地方").append(ENTER);
            }
            sftpCollectRule.setPort(0).setUsername(EMPTY).setPassword(EMPTY).setTimeout(0).setReconnectCount(0).setTimeDifference(0L);
        }

        if (sftpCollectRule.getFileModifiedStartDate() == null) {
            errorMessageSb.append("待拉取的文件的起始时间").append(CAN_NOT_EMPTY).append(ENTER);
        }
        if (sftpCollectRule.getFileModifiedEndDate() == null) {
            errorMessageSb.append("待拉取的文件的结束时间").append(CAN_NOT_EMPTY).append(ENTER);
        }
        if (sftpCollectRule.getBeforeSeconds() == null || sftpCollectRule.getBeforeSeconds() < 120) {
            sftpCollectRule.setBeforeSeconds(120);
        }
        if (sftpCollectRule.getIgnoreSuccessFileFlag() == null) {
            sftpCollectRule.setIgnoreSuccessFileFlag(1);
        } else if (sftpCollectRule.getIgnoreSuccessFileFlag() != 0 && sftpCollectRule.getIgnoreSuccessFileFlag() != 1) {
            errorMessageSb.append("是否忽略已经成功拉取过的文件只能是 : 不忽略、忽略").append(ENTER);
        }

        Integer eachTimeDownAllFileFlag = sftpCollectRule.getEachTimeDownAllFileFlag();
        if (eachTimeDownAllFileFlag == null) {
            errorMessageSb.append("文件拉取方式").append(CAN_NOT_EMPTY).append(ENTER);
        } else if (eachTimeDownAllFileFlag == 0) {
            // 滚动窗口拉取
            sftpCollectRule.setEachTimeDownAllWaitTime(0L).setMoveFileAfterDownAllFileFlag(0).setMoveFileAfterDownAllFileToDirectory(EMPTY);
            if (sftpCollectRule.getDaysForOneScheduler() == null || sftpCollectRule.getDaysForOneScheduler() <= 0) {
                sftpCollectRule.setDaysForOneScheduler(30);
            } else if (sftpCollectRule.getDaysForOneScheduler() > 365) {
                sftpCollectRule.setDaysForOneScheduler(365);
            }
        } else if (eachTimeDownAllFileFlag == 1) {
            // 全量拉取
            sftpCollectRule.setDaysForOneScheduler(365000);
            if (sftpCollectRule.getEachTimeDownAllWaitTime() == null || sftpCollectRule.getEachTimeDownAllWaitTime() < 10) {
                sftpCollectRule.setEachTimeDownAllWaitTime(10L);
            } else if (sftpCollectRule.getEachTimeDownAllWaitTime() > 600) {
                sftpCollectRule.setEachTimeDownAllWaitTime(600L);
            }

            Integer moveFileAfterDownAllFileFlag = sftpCollectRule.getMoveFileAfterDownAllFileFlag();
            if (moveFileAfterDownAllFileFlag == null) {
                errorMessageSb.append("全量拉取文件时，是否移动文件到备份目录").append(CAN_NOT_EMPTY).append(ENTER);
            } else if (moveFileAfterDownAllFileFlag == 0) {
                // 全量拉取文件,不移动文件到备份目录
                sftpCollectRule.setMoveFileAfterDownAllFileToDirectory(EMPTY);
            } else if (moveFileAfterDownAllFileFlag == 1) {
                // 全量拉取文件,移动文件到备份目录
                if (StringUtils.isBlank(sftpCollectRule.getMoveFileAfterDownAllFileToDirectory())) {
                    errorMessageSb.append("全量拉取文件且移动文件到备份目录时，备份目录的前缀路径").append(CAN_NOT_EMPTY).append(ENTER);
                } else if (!sftpCollectRule.getMoveFileAfterDownAllFileToDirectory().startsWith(SLASH) || sftpCollectRule.getMoveFileAfterDownAllFileToDirectory().equals(SLASH)) {
                    errorMessageSb.append("全量拉取文件且移动文件到备份目录时，备份目录的前缀路径必须以 / 开头且不能只有 /").append(ENTER);
                }
            } else {
                errorMessageSb.append("全量拉取文件时，是否移动文件到备份目录只能是 : 不移动、移动").append(ENTER);
            }
        } else {
            errorMessageSb.append("文件拉取方式只能是 : 滚动时间窗口拉取、全量拉取").append(ENTER);
        }

        if (StringUtils.isBlank(sftpCollectRule.getNeedDecompressionTypes())) {
            sftpCollectRule.setNeedDecompressionTypes(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getRemotePaths())) {
            errorMessageSb.append("待拉取的文件服务器的路径").append(CAN_NOT_EMPTY).append(ENTER);
        }
        if (StringUtils.isBlank(sftpCollectRule.getStdfFileRemoteFullNameRegex())) {
            sftpCollectRule.setStdfFileRemoteFullNameRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getStdfFileRegex())) {
            sftpCollectRule.setStdfFileRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getRawDataFileRemoteFullNameRegex())) {
            sftpCollectRule.setRawDataFileRemoteFullNameRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getRawDataFileRegex())) {
            sftpCollectRule.setRawDataFileRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getWatFileRemoteFullNameRegex())) {
            sftpCollectRule.setWatFileRemoteFullNameRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getWatFileRegex())) {
            sftpCollectRule.setWatFileRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getSummaryFileRemoteFullNameRegex())) {
            sftpCollectRule.setSummaryFileRemoteFullNameRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getSummaryFileRegex())) {
            sftpCollectRule.setSummaryFileRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getLotRelationFileRemoteFullNameRegex())) {
            sftpCollectRule.setLotRelationFileRemoteFullNameRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getLotRelationFileRegex())) {
            sftpCollectRule.setLotRelationFileRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getLogFileRemoteFullNameRegex())) {
            sftpCollectRule.setLogFileRemoteFullNameRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getLogFileRegex())) {
            sftpCollectRule.setLogFileRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getMapFileRemoteFullNameRegex())) {
            sftpCollectRule.setMapFileRemoteFullNameRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getMapFileRegex())) {
            sftpCollectRule.setMapFileRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getZipSplitFileRemoteFullNameRegex())) {
            sftpCollectRule.setZipSplitFileRemoteFullNameRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getZipSplitFileRegex())) {
            sftpCollectRule.setZipSplitFileRegex(EMPTY);
        }
        if (StringUtils.isBlank(sftpCollectRule.getBlackFileRemoteFullNameRegex())) {
            sftpCollectRule.setBlackFileRemoteFullNameRegex(EMPTY);
        }

        String errorMessage = errorMessageSb.toString();
        if (!errorMessage.isEmpty()) {
            FileLoadException exception = new FileLoadException(FileLoadExceptionInfo.PARAM_CKECK_FAIL, errorMessage, null).updateExceptionMessage(errorMessage);
            if (saveSftpBatchInfoFlag) {
                SftpBatchInfo newSftpBatchInfo = new SftpBatchInfo()
                        .setCollectRuleId(sftpCollectRule.getId())
                        .setCustomer(sftpCollectRule.getCustomer())
                        .setFactory(sftpCollectRule.getFactory())
                        .setFactorySite(sftpCollectRule.getFactorySite())
                        .setFab(sftpCollectRule.getFab())
                        .setFabSite(sftpCollectRule.getFabSite())
                        .setTestArea(sftpCollectRule.getTestArea())
                        .setFtpIp(sftpCollectRule.getServer())
                        .setSftpFileLastMtime(null)
                        .setBatchStatus(BatchInfoStatus.FAIL)
                        .setCollectRuleSnapshot(JSON.toJSONString(sftpCollectRule))
                        .setExceptionType(exception.getExceptionType())
                        .setExceptionMessage(exception.getExceptionMessage())
                        .setErrorMessage(exception.getErrorMessage())
                        .setCreateTime(new Date())
                        .setUpdateTime(new Date())
                        .setCreateUser(SYSTEM)
                        .setUpdateUser(SYSTEM);
                sftpBatchInfoRepository.save(newSftpBatchInfo);
            }
            throw exception;
        }
    }

    private Long getDataSize(String customer) {
        return sftpFileDetailRepository.queryDataSize(customer) / (1024L * 1024L * 1024L);
    }

    private Long getWaferCnt(String customer) {
        return lotMetaDataDetailRepository.queryWaferCnt(customer);
    }

    private void runTask(SftpCollectRule sftpCollectRule) {
        boolean lockCollectRuleId = SftpFileRedisManager.lockCollectRuleId(sftpCollectRule.getId().toString());
        if (!lockCollectRuleId) {
            log.info("collectRuleId : {} 已被上锁处理", sftpCollectRule.getId());
            return;
        }
        try {
            // 填充必要的拉取设置
            fillSftpCollectRule(sftpCollectRule, true);

            // 开始拉取
            executeTask(sftpCollectRule);
        } catch (Exception e) {
            log.error("collectRuleId : {} 异常", sftpCollectRule.getId(), e);
        } finally {
            // 解锁
            SftpFileRedisManager.unLockCollectRuleId(sftpCollectRule.getId().toString());
        }
    }

    public void executeTask(SftpCollectRule sftpCollectRule) {
        IFTPSyncCore ftpSyncCore = null;
        try {
            log.info("开始执行拉取任务：{}", sftpCollectRule.getNewSftpBatchInfo().getCollectRuleSnapshot());

            // 设置配置
            SftpCollectRuleThreadManager.setSftpCollectRule(sftpCollectRule);
            SftpConnectConfThreadManager.setSftpConnectConf(sftpCollectRule);
            FactoryEnumThreadLocalUtil.setAllFactoy(Lists.newArrayList(factoryEnumRepository.findAll()));
            SftpSubCustomerThreadLocalUtil.setSubCustomers(sftpSubCustomerRuleRepository.findAllByCustomerAndFtpIp(sftpCollectRule.getCustomer(), sftpCollectRule.getServer()));
            FileRenameRuleThreadLocalUtil.setStdfFileRenameRules(fileRenameRuleRepository.findAllByCustomerAndFtpIp(sftpCollectRule.getCustomer(), sftpCollectRule.getServer()));
            ftpSyncCore = ftpSyncCoreMap.get(sftpCollectRule.getType()).get();

            // 新建一条此sftpCollectRule对应的拉取记录
            sftpBatchInfoRepository.save(sftpCollectRule.getNewSftpBatchInfo());


            SftpLog sftpLog = generateSftpLog(sftpCollectRule.getNewSftpBatchInfo());
            SftpLogThreadLocalUtil.setSftpLog(sftpLog);

            // 获取需要拉取的文件
            List<FTPFileAttr> needPullFiles = pullFileHandler.findNeedPullFiles(ftpSyncCore);
            Collections.sort(needPullFiles);
            if (sftpCollectRule.getEachTimeDownAllFileFlag() == 1 && CollectionUtils.isNotEmpty(needPullFiles)) {
                // 全量拉取等待文件上传完成的时间
                Thread.sleep(sftpCollectRule.getEachTimeDownAllWaitTime() * 1000L);
            }

            ArrayList<FTPFileAttr> successLoadFiles = new ArrayList<>();
            Long firstFailMtime = null;
            int queryCount = 100;
            Set<String> existsSuccessFileLoadingLogs = new HashSet<>();
            Map<String, String> fileConvertRecordMap = new HashMap<>();
            for (int i = 0; i < needPullFiles.size(); i++) {
                if (i % queryCount == 0) {
                    ArrayList<String> queryFullFileNames = new ArrayList<>();
                    for (int j = i; j < needPullFiles.size() && j < i + queryCount; j++) {
                        queryFullFileNames.add(needPullFiles.get(j).getFullName());
                    }
                    if (sftpCollectRule.getIgnoreSuccessFileFlag() == 1) {
                        // 查询已经成功下载过的文件
                        existsSuccessFileLoadingLogs = fileLoadingLogRepository.findAllByFileNameInAndStepAndProcessStatus(queryFullFileNames, StepType.STEP_TYPE_1000.getStep(), ProcessStatus.SUCCESS)
                                .stream().map(FileLoadingLog::getFileName)
                                .collect(Collectors.toSet());
                    }
                    if (sftpCollectRule.getType() == FtpType.LOCAL) {
                        // 查询文件转换记录
                        fileConvertRecordMap = sftpFileConvertRecordRepository.findAllByFileNameIn(queryFullFileNames)
                                .stream().collect(
                                        Collectors.groupingBy(SftpFileConvertRecord::getFileName,
                                                Collectors.collectingAndThen(
                                                        Collectors.maxBy(Comparator.comparingLong(SftpFileConvertRecord::getId)),
                                                        t -> t.orElse(new SftpFileConvertRecord()).getSourceFileNames())
                                        ));
                    }
                }

                FTPFileAttr ftpFileAttr = needPullFiles.get(i);
                if (existsSuccessFileLoadingLogs.contains(ftpFileAttr.getFullName())) {
                    log.info("{} 已经成功下载过的，跳过处理", ftpFileAttr.getFullName());
                    SftpLogThreadLocalUtil.appendLogTxt("文件：" + ftpFileAttr.getFullName() + " 已经成功下载过的，跳过处理");
                    successLoadFiles.add(ftpFileAttr);
                    continue;
                }

                log.info("开始处理 {} ", ftpFileAttr.getFullName());
                // 拉取文件
                ftpFileAttr.setSourceFileNames(fileConvertRecordMap.get(ftpFileAttr.getFullName()));
                boolean pullFlag = pullFileHandler.pullFile(ftpFileAttr, ftpSyncCore);
                if (pullFlag) {
                    successLoadFiles.add(ftpFileAttr);
                } else if (sftpCollectRule.getEachTimeDownAllFileFlag() != 1 && firstFailMtime == null) {
                    firstFailMtime = ftpFileAttr.getMTime();
                }
            }
            log.info("从 {} 个文件中成功拉取 {} 个文件", needPullFiles.size(), successLoadFiles.size());

            // 记录本次拉取结束的时间
            sftpCollectRule.getNewSftpBatchInfo()
                    .setBatchTotalLoadFiles(needPullFiles.size())
                    .setBatchSuccessLoadFiles(successLoadFiles.size())
                    .setBatchStatus(BatchInfoStatus.SUCCESS);
            if (firstFailMtime == null) {
                sftpCollectRule.getNewSftpBatchInfo().setSftpFileLastMtime(sftpCollectRule.getFileModifiedRealEndDate());
            } else if (firstFailMtime == sftpCollectRule.getFileModifiedRealStartDate().getTime()) {
                sftpCollectRule.getNewSftpBatchInfo().setSftpFileLastMtime(new Date(firstFailMtime + 1L));
            } else {
                sftpCollectRule.getNewSftpBatchInfo().setSftpFileLastMtime(new Date(firstFailMtime));
            }
            // 更新此batchId对应拉取的文件的batch_status-->SUCCESS
            sftpFileDetailRepository.updateSftpBatchState(sftpCollectRule.getNewSftpBatchInfo().getId(), SftpBatchStatus.SUCCESS);

            // 移动文件到另外的目录
            moveSuccessLoadSourceFiles(ftpSyncCore, successLoadFiles);

            sftpCollectRule.getNewSftpBatchInfo().setUpdateTime(new Date());
            sftpBatchInfoRepository.save(sftpCollectRule.getNewSftpBatchInfo());
            log.info("拉取任务成功：{} ", sftpCollectRule.getNewSftpBatchInfo().getCollectRuleSnapshot());

            SftpLogThreadLocalUtil.appendLogTxt(String.format("拉取任务成功, 从 %s中成功拉取 %s 个文件, 扫描的时间范围[%s, %s]", sftpCollectRule.getRemotePaths(), successLoadFiles.size(),
                    DateTimeConvertUtil.dateToString(sftpCollectRule.getFileModifiedRealStartDate(), NORM_DATETIME_PATTERN), DateTimeConvertUtil.dateToString(sftpCollectRule.getFileModifiedRealEndDate(), NORM_DATETIME_PATTERN)));
            sftpLogRepository.save(SftpLogThreadLocalUtil.getSftpLog().setCreateTime(new Date()).setUpdateTime(new Date()));
        } catch (Exception e) {
            log.error("拉取任务异常：{} ", sftpCollectRule.getNewSftpBatchInfo().getCollectRuleSnapshot(), e);
            sftpCollectRule.getNewSftpBatchInfo()
                    .setBatchStatus(BatchInfoStatus.FAIL)
                    .setSftpFileLastMtime(sftpCollectRule.getFileModifiedRealStartDate())
                    .setExceptionType(FileLoadExceptionInfo.OTHER_EXCEPTION.getType())
                    .setExceptionMessage(FileLoadExceptionInfo.OTHER_EXCEPTION.getMessage())
                    .setErrorMessage(ExceptionUtils.getStackTrace(e))
                    .setUpdateTime(new Date());
            if (sftpCollectRule.getNewSftpBatchInfo().getId() != null) {
                // 更新此batchId对应拉取的文件的batch_status-->SUCCESS
                sftpFileDetailRepository.updateSftpBatchState(sftpCollectRule.getNewSftpBatchInfo().getId(), SftpBatchStatus.SUCCESS);
            }
            sftpBatchInfoRepository.save(sftpCollectRule.getNewSftpBatchInfo());

            SftpLogThreadLocalUtil.appendLogTxt("拉取任务异常：" + sftpCollectRule.getNewSftpBatchInfo().getErrorMessage());
            sftpLogRepository.save(SftpLogThreadLocalUtil.getSftpLog().setCreateTime(new Date()).setUpdateTime(new Date()));
        } finally {
            // 清除配置
            SftpCollectRuleThreadManager.clear();
            FactoryEnumThreadLocalUtil.clear();
            SftpSubCustomerThreadLocalUtil.clear();
            FileRenameRuleThreadLocalUtil.clear();
            SftpConnectConfThreadManager.clear();
            SftpLogThreadLocalUtil.clear();
            // 关闭连接
            if (ftpSyncCore != null) {
                ftpSyncCore.closeConnect();
            }
        }
    }

    private void moveSuccessLoadSourceFiles(IFTPSyncCore ftpSyncCore, ArrayList<FTPFileAttr> successLoadFiles) {
        try {
            SftpCollectRule sftpCollectRule = SftpCollectRuleThreadManager.getSftpCollectRule();
            //全量拉取文件时，且把远程服务器上的文件移到另外的目录时，移动文件到另外的目录
            if (sftpCollectRule.getEachTimeDownAllFileFlag() == 1 && sftpCollectRule.getMoveFileAfterDownAllFileFlag() == 1) {
                if (!sftpCollectRule.getMoveFileAfterDownAllFileToDirectory().startsWith(Constant.SLASH) || sftpCollectRule.getMoveFileAfterDownAllFileToDirectory().equals(Constant.SLASH)) {
                    return;
                }
                String remoteTargetPathPrefix = sftpCollectRule.getMoveFileAfterDownAllFileToDirectory();
                if (remoteTargetPathPrefix.endsWith(Constant.SLASH)) {
                    remoteTargetPathPrefix = remoteTargetPathPrefix.substring(0, remoteTargetPathPrefix.length() - 1);
                }
                for (FTPFileAttr successLoadFile : successLoadFiles) {
                    String remoteSourcePath = successLoadFile.getFullName();
                    String remoteTargetPath = remoteTargetPathPrefix + remoteSourcePath;
                    log.info("{} 移动到 {} 开始...", remoteSourcePath, remoteTargetPath);
                    try {
                        String remoteParentDir = remoteTargetPath.substring(0, remoteTargetPath.lastIndexOf(Constant.SLASH));
                        if (StringUtils.isNotEmpty(remoteParentDir)) {
                            log.info("创建文件夹 {} ", remoteParentDir);
                            ftpSyncCore.mkdir(remoteParentDir);
                        }
                        boolean moveFlag = ftpSyncCore.moveFile(remoteSourcePath, remoteTargetPath);
                        if (!moveFlag) {
                            throw new RuntimeException("移动文件失败");
                        }
                        log.info("{} 移动到 {} 成功！", remoteSourcePath, remoteTargetPath);
                    } catch (Exception e) {
                        log.info("{} 移动到 {} 失败！", remoteSourcePath, remoteTargetPath, e);
                    }
                }
            }
        } catch (Exception e) {
            log.info("moveSuccessLoadSourceFiles异常：", e);
        }
    }

    private SftpLog generateSftpLog(SftpBatchInfo sftpBatchInfo) {
        return new SftpLog()
                .setSftpBatchInfoId(sftpBatchInfo.getId())
                .setCustomer(sftpBatchInfo.getCustomer())
                .setFactory(sftpBatchInfo.getFactory())
                .setFactorySite(sftpBatchInfo.getFactorySite())
                .setFab(sftpBatchInfo.getFab())
                .setFabSite(sftpBatchInfo.getFabSite())
                .setTestArea(sftpBatchInfo.getTestArea())
                .setFtpIp(sftpBatchInfo.getFtpIp())
                .setRemotePaths(SftpCollectRuleThreadManager.getSftpCollectRule().getRemotePaths())
                .setCreateUser(Constant.SYSTEM)
                .setUpdateUser(Constant.SYSTEM);
    }

}
