package com.guwave.onedata.dataware.collectx.common;

/**
 *响应码枚举
 */
public enum ResponseStatus {

    Success("Success", "00000001"),
    RequestParameterErro("Request message error", "00000002"),
    USER_AUTH_ERROR("用户鉴权异常", "00000004"),
    USER_NOT_LOGIN_IN("用户未登录", "00000401"),
    FORBIDDEN("禁止访问", "000000403"),
    NotFound("Not Found", "00000404"),
    USER_NOT("用户没有该权限", "00000405"),



    Fail("系统异常", "00000009");

    private final String msg;
    private final String code;

    ResponseStatus(String msg, String code) {
        this.msg = msg;
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public String getCode() {
        return code;
    }
}