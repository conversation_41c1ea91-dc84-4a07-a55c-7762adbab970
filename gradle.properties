mavenReleaseUrl=http://nexus.guwave.com/repository/maven-releases
mavenSnapshotUrl=http://nexus.guwave.com/repository/maven-snapshots
mavenPublicUrl=http://nexus.guwave.com/repository/maven-public
nexusUserName=devops
nexusUserPwd=devops@guwave
gradleVersion=Gradle 7.2
jdkVersion=1.8

gdpCommonVersion=1.12.0
nextComputeVersion=1.4.2
skyeyeVersion=1.0.0


springBootVersion=2.7.8
dubboVersion=3.2.11
curatorVersion=2.8.0
slf4jVersion=1.7.30
mysqlVersion=8.0.23
jodaTimeVersion=2.10.1
commonsCodecVersion=1.15
commonsLang3Version=3.11
commonsIoVersion=2.10.0
clickhouseNativeJdbcVersion=2.6.5
springKafkaVersion=2.8.11
protostuffVersion=1.7.0
avroVersion=1.10.0
scalaVersion=2.12.17
scalaBinaryVersion=2.12
sparkVersion=3.2.3
fastJsonVersion=1.2.76
jacksonVersion=2.11.4
hadoopVersion=*******.1.4.0-315
commonsCompressVersion=1.21
easyExcelVersion=3.0.2
junitVersion=4.13.2
log4jVersion=1.2.17
slf4jlog4j12Version=1.7.32
flinkVersion=1.14.6
parquetAvroVersion=1.12.2
dataCommonVersion=0.6.0
shadowVersion=4.0.0
clickhouseJdbcVersion=0.3.1
kafkaVersion=*******.1.4.0-315
jodaConvertVersion=2.1.2
gsonVersion=2.8.5
commonsMathVersion=3.6.1
lombokVersion=1.18.22
tukaaniXzVersion=1.9
sevenzipjbindingVersion=16.02-2.01
commonsCollectionsVersion=3.2.2
beanutilsVersion=1.8.3
tikaVersion=2.9.0
seatunnelVersion=2.3.113
aspectjVersion=1.9.7
rhinoVersion=1.7.14
feignVersion=13.2.1
feignFormVersion=3.8.0
guavaVersion=28.0-jre
commonsMath3Version=3.6.1
redissonVersion=3.23.3
minIOVersion=8.3.9
jschVersion=0.1.72