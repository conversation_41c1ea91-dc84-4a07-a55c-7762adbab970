package com.guwave.onedata.dataware.scheduler.service;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import com.guwave.onedata.dataware.common.model.summary.dwd.DwdMesBinDetail;
import com.guwave.onedata.dataware.common.util.WaferUtil;
import com.guwave.onedata.dataware.dao.ck.domain.dw.dwd.DwdMesBinDetailSink;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DwTable;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.MesDetail;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.DwTableRepository;
import com.guwave.onedata.dataware.parser.stdf.util.DateUtil;
import com.guwave.onedata.dataware.repair.common.model.Wafer;
import com.guwave.onedata.dataware.scheduler.provider.CkProvider;
import com.guwave.onedata.dataware.scheduler.sink.KafkaSink;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Component
public class MesRequestCkService {
    private static final Logger LOGGER = LoggerFactory.getLogger(MesRequestCkService.class);


    @Value("${spring.handler.sink.batchSize}")
    private Integer batchSize;
    @Value("${spring.kafka.calculateEndFlagTopic}")
    private String calculateEndFlagTopic;
    @Value("${spring.data.clickhouse.cluster}")
    private String clusterName;
    @Autowired
    private DwTableRepository dwTableRepository;
    @Autowired
    private DwdMesBinDetailSink dwdMesBinDetailSink;
    @Autowired
    private KafkaSink kafkaSink;
    @Autowired
    private CkProvider ckProvider;

    private final static String DELETE_TABLE_SQL_TEMPLATE = "ALTER TABLE {DB_NAME}.{TABLE_NAME} ON CLUSTER {CLUSTER} UPDATE IS_DELETE = 1 {IN_PARTITION_CONDITION} WHERE "
            + " IS_DELETE = '0' AND CREATE_TIME < toDateTime('{CURRENT_TIME}')";

    private final static String LOT_ID_CONDITION = " CUSTOMER = '{CUSTOMER}' "
            + " AND TEST_AREA = '{TEST_AREA}' "
            + " AND FACTORY = '{FACTORY}' "
            + " AND DEVICE_ID = '{DEVICE_ID}' "
            + " AND LOT_ID = '{LOT_ID}' "
            + " AND LOT_TYPE = '{LOT_TYPE}' "
            + " AND TEST_STAGE = '{TEST_STAGE}' ";
    private final static String WAFER_NO_CONDITION = "AND WAFER_NO = '{WAFER_NO}' ";
    private final static String SBLOT_ID_CONDITION = "AND SBLOT_ID = '{SBLOT_ID}' ";
    private final static String UPLOAD_TYPE_CONDITION = " AND UPLOAD_TYPE = '{UPLOAD_TYPE}' ";
    private final static String DATA_SOURCE_CONDITION = " AND DATA_SOURCE = '{DATA_SOURCE}' ";
    private final static String IN_PARTITION_CONDITION = " in partition {PARTITION}";

    private final static String IN_PARTITION = "{IN_PARTITION_CONDITION}";
    private final String YMS_SUMMARY_DATA_SOURCE = "MES";

    Map<String, BiConsumer<String, DwdMesBinDetail>> MES_BIN_DETAIL_CONSUMER_MAP = new HashMap<String, BiConsumer<String, DwdMesBinDetail>>() {{
        put("bin", (str, dwdMesBinDetail) -> dwdMesBinDetail.setBinNum(Long.valueOf(str)));
        put("binName", (str, dwdMesBinDetail) -> dwdMesBinDetail.setBinNam(str));
        put("cnt", (str, dwdMesBinDetail) -> dwdMesBinDetail.setFinalBinCnt(Long.valueOf(str)));
        put("binPf", (str, dwdMesBinDetail) -> dwdMesBinDetail.setBinPf(str));
    }};

    private final static Map<Pair<FileCategory, String>, Function<String, String>> EXTRA_CONDITION_MAP = new HashMap<Pair<FileCategory, String>, Function<String, String>>() {{
        put(Pair.of(FileCategory.SUMMARY, TestArea.CP.getTestScope()), value -> WAFER_NO_CONDITION.replace(WAFER_NO, value));
        put(Pair.of(FileCategory.SUMMARY, TestArea.FT.getTestScope()), value -> SBLOT_ID_CONDITION.replace(SBLOT_ID, value));
    }};

    public void dealMesDetail(List<MesDetail> mesDetails) {
        Long dataVersion = System.currentTimeMillis();

        List<DwdMesBinDetail> dwdMesBinDetails = buildMesBinDetail(mesDetails, dataVersion);
        if (CollectionUtils.isNotEmpty(dwdMesBinDetails)) {
            // 删除ck
            Set<Wafer> wafers = dwdMesBinDetails
                    .stream()
                    .map(t -> new Wafer(t.getCustomer(), t.getSubCustomer(), t.getTestArea(), t.getFactory(), t.getDeviceId(),
                            t.getLotType(), t.getTestStage(), t.getLotId(), t.getWaferNo(), t.getSblotId(), t.getFileCategory()))
                    .collect(Collectors.toSet());
            List<DwTable> dwTableList = dwTableRepository.findAllByUploadTypeAndClusterNameAndCalculateFlag(
                    UploadType.AUTO,
                    dwdMesBinDetailSink.getTableName().replace(Constant.LOCAL_TABLE, Constant.CLUSTER_TABLE),
                    1);
            LOGGER.info("删除ck数据，刪除的表：{}, 需要删除的维度信息 = {}", dwTableList.stream().map(DwTable::getClusterName).collect(Collectors.toList()), wafers);
            deleteCk(wafers, UploadType.AUTO, dwTableList);
            // 写入ck
            LOGGER.info("写入ck数据，dataSize = {}", dwdMesBinDetails.size());
            splitList(dwdMesBinDetails, batchSize).forEach(subListData -> dwdMesBinDetailSink.doHandle(subListData));
            sendMesSaveCkMessage(dwdMesBinDetails, dataVersion);
        } else {
            LOGGER.info("写入ck数据，dataSize = 0");
        }
    }

    private List<DwdMesBinDetail> buildMesBinDetail(List<MesDetail> mesDetails, Long dataVersion) {
        if (CollectionUtils.isEmpty(mesDetails)) {
            LOGGER.info("mesDetails is empty");
            return Collections.emptyList();
        }
        Long now = System.currentTimeMillis();
        String createHourKey = DateUtil.getDayHour(now);
        String createDayKey = DateUtil.getDay(now);

        return mesDetails.stream().flatMap(mesDetail -> {
                    List<DwdMesBinDetail> dwdMesBinDetails;
                    try {
                        List<Map> hbinInfoMapList = StringUtils.isBlank(mesDetail.getHbinInfo()) ? Collections.emptyList() : JSON.parseArray(mesDetail.getHbinInfo(), Map.class);
                        List<Map> sbinInfoMapList = StringUtils.isBlank(mesDetail.getSbinInfo()) ? Collections.emptyList() : JSON.parseArray(mesDetail.getSbinInfo(), Map.class);

                        dwdMesBinDetails = new ArrayList<>(hbinInfoMapList.size() + sbinInfoMapList.size());

                        Map<String, List<Map>> binInfoMap = new HashMap<>(2);
                        binInfoMap.put(Constant.HBIN_NAME_PREFIX, hbinInfoMapList);
                        binInfoMap.put(Constant.SBIN_NAME_PREFIX, sbinInfoMapList);

                        binInfoMap.forEach((binType, binInfoMapList) -> {
                            for (Map map : binInfoMapList) {
                                DwdMesBinDetail dwdMesBinDetail = new DwdMesBinDetail().setBinType(binType);
                                MES_BIN_DETAIL_CONSUMER_MAP.forEach((key, value) -> {
                                    if (map.containsKey(key)) {
                                        try {
                                            value.accept(String.valueOf(map.get(key)), dwdMesBinDetail);
                                        } catch (Exception ex) {
                                            String errorMessage = String.format("mesDetail binInfo转换异常, binType: %s, binInfo: %s", binType, map);
                                            LOGGER.error(errorMessage, ex);
                                            throw new RuntimeException(errorMessage, ex);
                                        }
                                    }
                                });
                                if (dwdMesBinDetail.getBinNum() != null) {
                                    dwdMesBinDetails.add(dwdMesBinDetail);
                                } else {
                                    LOGGER.info("未解析到binNum");
                                }
                            }
                        });
                    } catch (Exception ex) {
                        String errorMessage = String.format("mesDetail binInfo转换异常, HbinInfo: %s, SbinInfo: %s", mesDetail.getHbinInfo(), mesDetail.getSbinInfo());
                        LOGGER.error(errorMessage, ex);
                        throw new FileLoadException(FileLoadExceptionInfo.RAW_DATA_FILE_CONVERT_EXCEPTION, errorMessage + ExceptionUtils.getStackTrace(ex), null);
                    }

                    long startTime;
                    long endTime;
                    if (mesDetail.getStartTime() == null && mesDetail.getEndTime() == null) {
                        startTime = 0L;
                        endTime = 0L;
                    } else {
                        startTime = mesDetail.getStartTime() != null ? mesDetail.getStartTime().getTime() : mesDetail.getEndTime().getTime();
                        endTime = mesDetail.getEndTime() != null ? mesDetail.getEndTime().getTime() : mesDetail.getStartTime().getTime();
                    }

                    String startHourKey = DateUtil.getDayHour(startTime);
                    String startDayKey = DateUtil.getDay(startTime);
                    String endHourKey = DateUtil.getDayHour(endTime);
                    String endDayKey = DateUtil.getDay(endTime);

                    return dwdMesBinDetails.stream().map(dwdMesBinDetail -> dwdMesBinDetail
                            .setCustomer(mesDetail.getCustomer())
                            .setSubCustomer(mesDetail.getSubCustomer())
                            .setUploadType(UploadType.AUTO)
                            .setFactory(mesDetail.getFactory())
                            .setFactorySite(mesDetail.getFactorySite())
                            .setFab(EMPTY)
                            .setFabSite(EMPTY)
                            .setLotType(mesDetail.getLotType())
                            .setTestArea(mesDetail.getTestArea())
                            .setTestStage(mesDetail.getTestStage())
                            .setFileCategory(mesDetail.getFileCategory())
                            .setFileId(0L)
                            .setFileName(mesDetail.getFileName())
                            .setTestProgram(mesDetail.getTestProgram())
                            .setTestHead(null)
                            .setTesterName(mesDetail.getTesterName())
                            .setStartTime(startTime)
                            .setEndTime(endTime)
                            .setStartHourKey(startHourKey)
                            .setStartDayKey(startDayKey)
                            .setEndHourKey(endHourKey)
                            .setEndDayKey(endDayKey)
                            .setProberHandlerTyp(EMPTY)
                            .setProberHandlerId(mesDetail.getProberHandlerId())
                            .setProbecardLoadboardTyp(EMPTY)
                            .setProbecardLoadboardId(mesDetail.getProbecardLoadboardId())
                            .setDeviceId(mesDetail.getDeviceId())
                            .setLotId(mesDetail.getLotId())
                            .setSblotId(mesDetail.getSblotId())
                            .setWaferLotId((TestArea.CP.getTestScope().equals(mesDetail.getTestArea().getTestScope()) ? WaferUtil.formatWaferLotId(mesDetail.getLotId(), mesDetail.getWaferId()) : WaferUtil.formatLotId(mesDetail.getSblotId())))
                            .setWaferId(mesDetail.getWaferId())
                            .setOriginWaferId(mesDetail.getOriginWaferId())
                            .setWaferNo(mesDetail.getWaferNo())
                            .setStdfFiles(mesDetail.getStdfFiles())
                            .setBin(buildBin(dwdMesBinDetail.getBinType(), dwdMesBinDetail.getBinNum(), dwdMesBinDetail.getBinNam()))
                            .setTotalCnt(mesDetail.getTotalCnt() == null ? null : Long.valueOf(mesDetail.getTotalCnt()))
                            .setPassCnt(mesDetail.getPassCnt() == null ? null : Long.valueOf(mesDetail.getPassCnt()))
                            .setFailCnt(mesDetail.getFailCnt() == null ? null : Long.valueOf(mesDetail.getFailCnt()))
                            .setExtraInfo(mesDetail.getExtraInfo())
                            .setCreateHourKey(createHourKey)
                            .setCreateDayKey(createDayKey)
                            .setCreateTime(now)
                            .setCreateUser(mesDetail.getCreateUser())
                            .setUploadTime(now)
                            .setDataVersion(dataVersion)
                            .setIsDelete(0));
                })
                .collect(Collectors.toList());
    }

    /**
     * 构造HBIN/SBIN：HBIN/SBIN + binNum + "-" + binNam
     */
    private String buildBin(String prefix, Long binNum, String binNam) {
        return binNum != null ? prefix + binNum + MIDDLE_LINE + binNam : EMPTY;
    }

    @Async
    public void deleteCk(Set<Wafer> wafers, UploadType uploadType, List<DwTable> needDeleteCkTables) {
        Set<String> dbNames = needDeleteCkTables.stream().map(t -> SINGLE_QUOTATION + t.getDb() + SINGLE_QUOTATION).collect(Collectors.toSet());
        Set<String> localTableNames = needDeleteCkTables.stream().map(t -> SINGLE_QUOTATION + t.getLocalName() + SINGLE_QUOTATION).collect(Collectors.toSet());
        Map<String, String> partitionKeyMap = wafers.size() == 1 ? ckProvider.getTablePartitionKeyMap(dbNames, localTableNames) : new HashMap<>();

        String conditions = wafers.stream()
                .map(wafer -> {
                    String condition = LOT_ID_CONDITION.replace(CUSTOMER, wafer.getCustomer())
                            .replace(TEST_AREA, wafer.getTestArea().getArea())
                            .replace(FACTORY, wafer.getFactory())
                            .replace(DEVICE_ID, wafer.getDeviceId())
                            .replace(LOT_ID, wafer.getLotId())
                            .replace(LOT_TYPE, wafer.getLotType().getType())
                            .replace(TEST_STAGE, wafer.getTestStage());
                    Function<String, String> extraConditionFunction = EXTRA_CONDITION_MAP.get(Pair.of(wafer.getFileCategory(), wafer.getTestArea().getTestScope()));
                    if (extraConditionFunction != null) {
                        String extraValue = TestArea.CP.getTestScope().equals(wafer.getTestArea().getTestScope()) ? wafer.getWaferNo() : wafer.getSblotId();
                        condition = condition + extraConditionFunction.apply(extraValue);
                    }
                    if (uploadType != null) {
                        condition = condition + UPLOAD_TYPE_CONDITION.replace(UPLOAD_TYPE, uploadType.getType());
                    }
                    return LEFT_SMALL_BRACKETS + condition + RIGHT_SMALL_BRACKETS;
                }).collect(Collectors.joining(Constant.OR));
        if (StringUtils.isNotBlank(conditions)) {
            List<String> deleteSqls = needDeleteCkTables.stream()
                    .flatMap(t -> wafers.stream().map(wafer -> {
                        String sql = DELETE_TABLE_SQL_TEMPLATE
                                .replace(DB_NAME, t.getDb())
                                .replace(TABLE_NAME, t.getLocalName())
                                .replace(CLUSTER_NAME, clusterName)
                                .replace(IN_PARTITION, partitionKeyMap.isEmpty() ? EMPTY : getPartitionExpr(partitionKeyMap, t.getLocalName(), uploadType, wafer))
                                .replace(CURRENT_TIME, System.currentTimeMillis() / 1000 + EMPTY);
                        String ymsDataSourceCondition = t.getCalculateScope() == CalculateScope.YMS ? DATA_SOURCE_CONDITION.replace(DATA_SOURCE, YMS_SUMMARY_DATA_SOURCE) : EMPTY;
                        return sql + Constant.AND + LEFT_SMALL_BRACKETS + conditions + RIGHT_SMALL_BRACKETS + ymsDataSourceCondition;
                    }))
                    .distinct()
                    .collect(Collectors.toList());
            ckProvider.executeSqls(deleteSqls);
        }
    }

    private <T> List<List<T>> splitList(List<T> originalList, int batchSize) {
        if (CollectionUtils.isEmpty(originalList) || originalList.size() <= batchSize || batchSize <= 0) {
            return Collections.singletonList(originalList);
        }
        // 计算需要拆分的批次数
        int numberOfBatches = (int) Math.ceil(originalList.size() / (double) batchSize);

        // 使用IntStream.range()生成批次索引，并映射到对应的子列表
        return IntStream.range(0, numberOfBatches)
                .mapToObj(batchIndex -> {
                    int start = batchIndex * batchSize;
                    int end = Math.min(start + batchSize, originalList.size());
                    return originalList.subList(start, end);
                })
                .collect(Collectors.toList());
    }

    private void sendMesSaveCkMessage(List<DwdMesBinDetail> dwdMesBinDetails, Long dataVersion) {
        if (CollectionUtils.isEmpty(dwdMesBinDetails)) {
            LOGGER.info("dwdMesBinDetails is empty");
            return;
        }
        List<CalculateEndFlag> calculateEndFlags = dwdMesBinDetails
                .stream()
                .map(t -> new Wafer()
                        .setCustomer(t.getCustomer())
                        .setSubCustomer(t.getSubCustomer())
                        .setTestArea(t.getTestArea())
                        .setFactory(t.getFactory())
                        .setFactorySite(t.getFactorySite())
                        .setDeviceId(t.getDeviceId())
                        .setLotId(t.getLotId())
                        .setWaferNo(TestArea.getCPList().contains(t.getTestArea()) ? t.getWaferNo() : EMPTY)
                        .setSblotId(TestArea.getFTList().contains(t.getTestArea()) ? t.getSblotId() : EMPTY)
                        .setTestStage(t.getTestStage())
                        .setFileCategory(t.getFileCategory())
                        .setLotType(t.getLotType()))
                .distinct()
                .map(t -> buildCalculateEndFlag(t, dataVersion))
                .collect(Collectors.toList());
        for (CalculateEndFlag calculateEndFlagMessage : calculateEndFlags) {
            String message = JSON.toJSONString(calculateEndFlagMessage);
            LOGGER.info("mes解析写入Ck完成,发送消息通知. {}", message);
            kafkaSink.send(calculateEndFlagTopic, message);
        }
    }

    private CalculateEndFlag buildCalculateEndFlag(Wafer wafer, Long dataVersion) {
        return new CalculateEndFlag()
                .setCustomer(wafer.getCustomer())
                .setSubCustomer(wafer.getSubCustomer())
                .setTestArea(wafer.getTestArea())
                .setFactory(wafer.getFactory())
                .setFactorySite(wafer.getFactorySite())
                .setDeviceId(wafer.getDeviceId())
                .setLotId(wafer.getLotId())
                .setWaferNo(TestArea.getCPList().contains(wafer.getTestArea()) ? wafer.getWaferNo() : EMPTY)
                .setSblotId(TestArea.getFTList().contains(wafer.getTestArea()) ? wafer.getSblotId() : EMPTY)
                .setTestStage(wafer.getTestStage())
                .setFileCategory(wafer.getFileCategory())
                .setLotType(wafer.getLotType())
                .setPlatform(Platform.CK)
                .setProcessStatus(ProcessStatus.SUCCESS)
                .setTs(System.currentTimeMillis())
                .setDataVersion(dataVersion)
                ;

    }

    private String getPartitionExpr(Map<String, String> partitionKeyMap, String localTableName, UploadType uploadType, Wafer wafer) {
        String partitionKey = partitionKeyMap.getOrDefault(localTableName, EMPTY);
        if (StringUtils.isBlank(partitionKey)) {
            return EMPTY;
        } else {
            boolean hasSmallBracketsFlag = partitionKey.contains(LEFT_SMALL_BRACKETS) && partitionKey.contains(RIGHT_SMALL_BRACKETS);
            partitionKey = hasSmallBracketsFlag ? partitionKey.replace(LEFT_SMALL_BRACKETS, EMPTY).replace(RIGHT_SMALL_BRACKETS, EMPTY) : partitionKey;

            String formatPartitionKey = Stream.of(partitionKey.split(COMMA)).map(t ->
                    SINGLE_QUOTATION + LEFT_CURLY_BRACE + t.trim() + RIGHT_CURLY_BRACE + SINGLE_QUOTATION
            ).collect(Collectors.joining(COMMA));

            formatPartitionKey = (hasSmallBracketsFlag ? (LEFT_SMALL_BRACKETS + formatPartitionKey + RIGHT_SMALL_BRACKETS) : formatPartitionKey)
                    .replace(CUSTOMER, wafer.getCustomer())
                    .replace(SUB_CUSTOMER, wafer.getSubCustomer())
                    .replace(DATA_SOURCE, YMS_SUMMARY_DATA_SOURCE)
                    .replace(FACTORY, wafer.getFactory())
                    .replace(DEVICE_ID, wafer.getDeviceId())
                    .replace(UPLOAD_TYPE, uploadType == null ? EMPTY : uploadType.getType())
                    .replace(TEST_AREA, wafer.getTestArea() == null ? EMPTY : wafer.getTestArea().getArea())
                    .replace(LOT_ID, wafer.getLotId())
                    .replace(LOT_BUCKET, String.valueOf(Math.abs(wafer.getLotId().hashCode()) % 6))
                    .replace(FILE_CATEGORY, wafer.getFileCategory() == null ? EMPTY : wafer.getFileCategory().getCategory())
            ;

            return IN_PARTITION_CONDITION.replace(Constant.PARTITION, formatPartitionKey);
        }
    }

}
