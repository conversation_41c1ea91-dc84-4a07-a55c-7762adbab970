package com.guwave.onedata.dataware.source.agent.common.util;

import com.guwave.onedata.dataware.common.contant.ManualFileSystem;
import com.guwave.onedata.dataware.source.agent.common.configuration.SourceAgentCommonConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;

import static com.guwave.onedata.dataware.common.contant.Constant.SLASH;

@Component
public class LocalfsUtil implements FileSystemUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(LocalfsUtil.class);
    @Autowired
    private SourceAgentCommonConfiguration sourceAgentCommonConfiguration;

    @Value("${spring.ods.local.prefix}")
    private String odsPathPrefix;

    private static final int allRetryCount = 5;

    @Override
    public void upload(String filePath, String targetPath, int retryCount) {
        targetPath = (odsPathPrefix.endsWith(SLASH) ? odsPathPrefix : odsPathPrefix + SLASH) + targetPath;
        targetPath = targetPath.endsWith(SLASH) ? targetPath : targetPath + SLASH;
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                LOGGER.error("文件不存在: {}", filePath);
                throw new RuntimeException(filePath + "文件不存在");
            }
            FileUtil.mkdir(targetPath);
            FileUtil.moveFile(filePath, targetPath + file.getName());
        } catch (Exception e) {
            LOGGER.info("{} -> {} 移动至目标目录失败", filePath, targetPath, e);
            if (retryCount > allRetryCount) {
                throw new RuntimeException(e);
            } else {
                LOGGER.info("move 第{}次尝试重新连接......{}", retryCount, e.getMessage());
                try {
                    Thread.sleep(retryCount * 5000L);
                } catch (InterruptedException ex) {
                    //
                }
                upload(filePath, targetPath, ++retryCount);
            }
        }
    }

    @Override
    public void downloadToLocal(boolean delSrc, String targetPath, String filePath) throws IOException {
        try {
            LOGGER.info("从local下载文件到本地, path: {},path: {}", targetPath, filePath);
            FileUtil.copyFile(targetPath, filePath);
        } catch (Exception e) {
            LOGGER.error("从local下载文件到本地失败, path: {},path: {}", targetPath, filePath);
            throw new RuntimeException(e);
        }
        if (delSrc) {
            FileUtil.deleteFile(filePath, true);
        }
    }

    @Override
    public void deleteFile(String targetPath, Boolean recursive) {
        targetPath = (odsPathPrefix.endsWith(SLASH) ? odsPathPrefix : odsPathPrefix + SLASH) + targetPath;
        try {
            LOGGER.info("从local删除文件, path: {}", targetPath);
            FileUtil.deleteFile(targetPath, recursive);
        } catch (Exception e) {
            LOGGER.error("从local删除文件失败, path: {}", targetPath, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public ManualFileSystem getManualFileSystem() {
        return ManualFileSystem.LOCAL;
    }

    public SourceAgentCommonConfiguration getSourceAgentCommonConfiguration() {
        return sourceAgentCommonConfiguration;
    }

}
