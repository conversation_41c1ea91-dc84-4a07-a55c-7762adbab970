package com.guwave.onedata.dataware.source.agent.manual.handler.impl;

import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.contant.ManualType;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.manual.ManualDeleteVo;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingFinishMessage;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingMessage;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualDeleteTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualMessage;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualDeleteTaskRepository;
import com.guwave.onedata.dataware.source.agent.manual.handler.ManualTypehandler;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Component
public class ManualDeleteHandler implements ManualTypehandler {


    @Autowired
    private ManualDeleteTaskRepository manualDeleteTaskRepository;

    @Override
    public Boolean support(ManualType manualType) {
        return manualType == ManualType.DELETE;
    }

    @Override
    public void generateTask(ManualMessage manualMessage, ManualWarehousingMessage manualWarehousingMessage) {
        ManualDeleteVo manualDeleteVo = manualWarehousingMessage.getManualDeleteVo();

        // 校验参数
        checkParam(manualDeleteVo);

        // 生成删除任务
        Date date = new Date();
        ManualDeleteTask manualDeleteTask = new ManualDeleteTask()
                .setMessageId(manualMessage.getId())
                .setDeleteFileIds(manualDeleteVo.getFileIds().stream().map(t -> t + EMPTY).distinct().collect(Collectors.joining(COMMA)))
                .setProcessStatus(ProcessStatus.CREATE)
                .setCreateUser(SYSTEM)
                .setUpdateUser(SYSTEM)
                .setCreateTime(date)
                .setUpdateTime(date);

        // 保存 manual_delete_task
        manualDeleteTaskRepository.save(manualDeleteTask);
    }

    @Override
    public void fillManualWarehousingFinishMessage(ManualWarehousingFinishMessage manualWarehousingFinishMessage, ManualWarehousingMessage manualWarehousingMessage) {
        manualWarehousingFinishMessage.setManualDeleteVo(manualWarehousingMessage.getManualDeleteVo());
    }

    private void checkParam(ManualDeleteVo manualDeleteVo) {
        StringBuilder errorMessageSb = new StringBuilder();
        if (CollectionUtils.isEmpty(manualDeleteVo.getFileIds())) {
            errorMessageSb.append("fileIds").append(FIELD_IS_EMPTY).append(ENTER);
        }

        String errorMessage = errorMessageSb.toString();
        if (!errorMessage.isEmpty()) {
            throw new FileLoadException(FileLoadExceptionInfo.PARAM_CKECK_FAIL, errorMessage, null).updateExceptionMessage(errorMessage);
        }
    }
}
