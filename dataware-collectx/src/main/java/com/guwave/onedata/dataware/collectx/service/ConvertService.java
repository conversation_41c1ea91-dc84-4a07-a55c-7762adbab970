package com.guwave.onedata.dataware.collectx.service;

import com.guwave.onedata.dataware.collectx.util.HDFSUtil;
import com.guwave.onedata.dataware.common.contant.*;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.File;
import java.io.IOException;

public interface ConvertService {
    Logger log = LoggerFactory.getLogger(ConvertService.class);

    String CRC_FILE_SUFFIX = "crc";

    default void downloadAndUncompressHdfsFile(String fileName, String localRootPath, String hdfsPath) throws IOException {
        HDFSUtil hdfsUtil = getHdfsUtil();
        String localPath = localRootPath + fileName;
        log.info("从hdfs 下载不删除原文件: {} --> {}", hdfsPath, localPath);
        // 此处下载不删除源文件
        clean(localRootPath, fileName);
        hdfsUtil.downloadToLocal(false, hdfsPath, localPath);
        // 解压下载的文件
        hdfsUtil.unCompress(removeFileSuffix(localPath, FileType.ZIP), localPath);
    }

    /**
     * 清理处理完成的文件
     *
     * @param readPath 文件路径
     * @param fileName 文件名
     */
    default void clean(String readPath, String fileName) {
        if (!readPath.endsWith(Constant.SLASH)) {
            readPath = readPath + Constant.SLASH;
        }

        // 清理文件
        String filePath = readPath + fileName;
        File file = new File(filePath);
        if (file.exists()) {
            try {
                // 删除文件
                log.info("删除当前文件：{} ", filePath);
                FileUtils.deleteQuietly(file);
            } catch (Exception e) {
                log.info("文件异常", e);
            }
        }

        // 清理.crc文件
        String crcFilePath = readPath + Constant.POINT + fileName + Constant.POINT + CRC_FILE_SUFFIX;
        File crcFile = new File(crcFilePath);
        if (crcFile.exists()) {
            log.info("删除当前文件：{} ", crcFilePath);
            try {
                // 删除文件
                FileUtils.deleteQuietly(crcFile);
                log.info("删除文件成功");
            } catch (Exception e) {
                log.info("删除文件异常", e);
            }
        }

        deleteUnCompressFile(readPath, fileName, FileType.ZIP);
    }


    /**
     * 删除解压缩文件
     *
     * @param readPath 被压缩的原文件路径
     * @param fileName 被压缩的文件名
     * @param fileType 压缩形式
     */
    default void deleteUnCompressFile(String readPath, String fileName, FileType fileType) {
        if (!readPath.endsWith(Constant.SLASH)) {
            readPath = readPath + Constant.SLASH;
        }
        fileName = removeFileSuffix(fileName, fileType);
        // 清理解压缩文件
        String unCompressFilePath = readPath + fileName;
        File unCompressFile = new File(unCompressFilePath);
        if (unCompressFile.exists()) {
            try {
                // 删除文件
                log.info("删除当前文件：{} ", unCompressFilePath);
                FileUtils.deleteQuietly(unCompressFile);
            } catch (Exception e) {
                log.info("文件异常", e);
            }
        }

        // 清理crc文件
        String crcUnCompressFilePath = readPath + Constant.POINT + fileName + Constant.POINT + CRC_FILE_SUFFIX;
        File crcUnCompressFile = new File(crcUnCompressFilePath);
        if (crcUnCompressFile.exists()) {
            log.info("删除当前文件：{} ", crcUnCompressFilePath);
            try {
                // 删除文件
                FileUtils.deleteQuietly(crcUnCompressFile);
                log.info("删除文件成功");
            } catch (Exception e) {
                log.info("删除文件异常", e);
            }
        }
    }

    /**
     * 压缩本地的文件并上传
     *
     * @param localFilePath 本地文件路径
     * @param hdfsDirPath   hdfs文件夹路径
     */
    default void compressLocalFileAndUpload(String localFilePath, String hdfsDirPath) {
        HDFSUtil hdfsUtil = getHdfsUtil();
        String localStdfFileZipPath = localFilePath + Constant.POINT + FileType.ZIP.getType();
        hdfsUtil.compress(localFilePath, localStdfFileZipPath);
        hdfsUtil.uploadToHDFSByFileSystem(localStdfFileZipPath, hdfsDirPath, 1);
    }

    default String removeFileSuffix(String fileName, FileType fileType) {
        String suffix = Constant.POINT + fileType.getType();
        if (fileName.endsWith(suffix)) {
            fileName = fileName.substring(0, fileName.length() - suffix.length());
        }
        return fileName;
    }

    default void cleanDir(File file) {
        FileUtils.deleteQuietly(file);
        file.mkdirs();
    }

    HDFSUtil getHdfsUtil();
}
