package com.guwave.onedata.dataware.source.agent.common.util;

import com.guwave.gdp.common.util.ReflectFieldUtil;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.DataClearRuleType;
import com.guwave.onedata.dataware.common.model.stdf.StdfCommon;
import com.guwave.onedata.dataware.parser.stdf.model.ClearRule;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/7 13:58
 * @description CommonDataClearUtil
 */
public class CommonDataClearUtil {

    public static StdfCommon doClear(StdfCommon obj, List<ClearRule> rules) {
        rules.forEach(rule -> {
            DataClearRuleType ruleType = rule.getRuleType();
            switch (ruleType) {
                case SWAP:
                    CommonDataClearUtil.swap(obj, rule.getRule());
                    break;
                case REPLACE:
                    CommonDataClearUtil.replace(obj, rule.getRule());
                    break;
                case REPLACE_APPEND:
                    CommonDataClearUtil.replaceAppend(obj, rule.getRule());
                    break;
                case SPECIAL_REPLACE:
                    CommonDataClearUtil.specialReplace(obj, rule.getRule());
                    break;
                case CONTAIN_REPLACE:
                    CommonDataClearUtil.containReplace(obj, rule.getRule());
                    break;
                case CONTAIN_REPLACE_SWAP:
                    CommonDataClearUtil.containReplaceSwap(obj, rule.getRule());
                    break;
                case UNIT_TO_SCALE:
                    CommonDataClearUtil.unitToScale(obj, rule.getRule());
                    break;
                case INTERSECTION:
            }
        });
        return obj;

    }

    /**
     * 字段交换
     */
    private static void swap(StdfCommon obj, String rule) {
        String[] detail = rule.split(Constant.PLUS_PLUS);
        String leftField = detail[0];
        String rightField = detail[1];

        String left = ReflectFieldUtil.getValueForJava(obj, leftField);
        String right = ReflectFieldUtil.getValueForJava(obj, rightField);
        ReflectFieldUtil.setValue(obj, leftField, right);
        ReflectFieldUtil.setValue(obj, rightField, left);
    }

    /**
     * 字段用另一个字段替换
     *
     * @param obj  Object
     * @param rule rule
     */
    private static void replace(StdfCommon obj, String rule) {
        String[] detail = rule.split(Constant.PLUS_PLUS);
        String leftField = detail[0];
        String rightField = detail[1];

        String newValue = ReflectFieldUtil.getValueForJava(obj, rightField);
        ReflectFieldUtil.setValue(obj, leftField, newValue);
    }

    /**
     * 字段用另一个字段替换并补值
     *
     * @param obj  Object
     * @param rule rule
     */
    private static void replaceAppend(StdfCommon obj, String rule) {
        String[] detail = rule.split(Constant.PLUS_PLUS);
        String leftField = detail[0];
        String rightField = detail[1];
        String appendValue = detail[2];

        String newValue = ReflectFieldUtil.getValueForJava(obj, rightField) + appendValue;
        ReflectFieldUtil.setValue(obj, leftField, newValue);
    }

    /**
     * 字段用特定值替换
     *
     * @param obj  Object
     * @param rule rule
     */
    private static void specialReplace(StdfCommon obj, String rule) {
        String[] detail = rule.split(Constant.PLUS_PLUS);
        String filed = detail[0];
        String value = detail[1];

        ReflectFieldUtil.setValue(obj, filed, value);
    }

    /**
     * 字段部分用特定值替换
     *
     * @param obj  Object
     * @param rule rule
     */
    private static void containReplace(StdfCommon obj, String rule) {
        String[] detail = rule.split(Constant.PLUS_PLUS);
        String filed = detail[0];
        String replaceValue = detail[1];
        String value = detail[2];

        String oldValue = ReflectFieldUtil.getValueForJava(obj, filed);
        ReflectFieldUtil.setValue(obj, filed, oldValue.replace(replaceValue, value));
    }

    /**
     * 字段部分用特定值替换后交换位置
     *
     * @param obj  Object
     * @param rule rule
     */
    private static void containReplaceSwap(StdfCommon obj, String rule) {
        String[] detail = rule.split(Constant.PLUS_PLUS);
        String filed = detail[0];
        String replaceValue = detail[1];
        String value = detail[2];

        String oldValue = ReflectFieldUtil.getValueForJava(obj, filed);
        Integer index = oldValue.indexOf(replaceValue);
        if (oldValue.contains(replaceValue)) {
            ReflectFieldUtil.setValue(obj, filed, value + oldValue.substring(0, index));
        }
    }

    /**
     * 转标准单位（kHz:3;MHz:6;GHz:9;kV:3;mV:-3;uV:-6;nV:-9;pV:-12;fV:-15;kA:3;mA:-3;uA:-6;nA:-9;pA:-12;fA:-15;mF:-3;uF:-6;nF:-9;pF:-12;fF:-15;aF:-18;kOhm:3;MOhm:6;kW:3;mW:-3;uW:-6;nW:-9;ms:-3;us:-6）
     *
     * @param obj  Object
     * @param rule rule
     */
    private static void unitToScale(StdfCommon obj, String rule) {
        String[] detail = rule.split(Constant.PLUS_PLUS);
        String leftField = detail[0];
        String rightField = detail[1];
        Map<String, Integer> alternateUnits = CommonDataClearUtil.getAlternateUnits(detail[2]);

        String unitValue = ReflectFieldUtil.getValueForJava(obj, leftField);
        Integer scale = alternateUnits.getOrDefault(unitValue.toUpperCase(), null);
        if (null != scale) {
            ReflectFieldUtil.setValue(obj, rightField, scale);
        }
    }

    /**
     * 计算换算单位
     *
     * @return Map[String, Int]
     */
    private static Map<String, Integer> getAlternateUnits(String rule) {
        Map<String, Integer> result = new HashMap<>();
        String[] items = rule.split(Constant.SEMICOLON);
        for (String item : items) {
            String[] detail = item.split(Constant.COLON);
            result.put(detail[0], Integer.valueOf(detail[1]));
        }
        return result;
    }


}
