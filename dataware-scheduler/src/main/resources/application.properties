spring.module.name=${module.name}
spring.main.web-application-type=none
# jpa config
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=jdbc:mysql://${database.address}/${database.name}?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&failOverReadOnly=false&useSSL=false&allowPublicKeyRetrieval=true&useTimezone=true&serverTimezone=Asia/Shanghai&allowMultiQueries=true&rewriteBatchedStatements=true
spring.datasource.username=${database.username}
spring.datasource.password=${database.password}
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.read-only=false
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=HikariCP
spring.datasource.hikari.max-lifetime=60000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1

spring.data.jpa.repositories.enabled=true
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.generate-ddl=false
spring.jpa.database=MYSQL
spring.jpa.show-sql=false
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.jdbc.batch_size=10000
spring.jpa.database-platform=org.hibernate.dialect.MySQL5InnoDBDialect

# clickhouse
spring.data.clickhouse.address=${data.clickhouse.address}
spring.data.clickhouse.username=${data.clickhouse.username}
spring.data.clickhouse.password=${data.clickhouse.password}
spring.data.clickhouse.cluster=${data.clickhouse.cluster}
spring.sink.ck.ods.dbName=${sink.ck.ods.dbName}
spring.sink.ck.dwd.dbName=${sink.ck.dwd.dbName}
spring.data.clickhouse.dwdDbName=${data.clickhouse.dwdDbName}
spring.data.clickhouse.dwsDbName=${data.clickhouse.dwsDbName}
spring.data.clickhouse.maxMutationsCnt=${data.clickhouse.maxMutationsCnt}

# kafka
spring.kafka.bootstrap-servers=${kafka.bootstrapServers}
spring.kafka.consumer.properties.group.id=${kafka.consumer.consumeGroup}
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.consumer.auto-commit-interval=${kafka.consumer.autoCommitInterval}
spring.kafka.consumer.auto-offset-reset=${kafka.consumer.autoOffsetReset}
spring.kafka.consumer.properties.session.timeout.ms=120000
spring.kafka.consumer.properties.request.timeout.ms=180000
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.ByteArrayDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.ByteArrayDeserializer
spring.kafka.consumer.max-poll-records=${kafka.consumer.maxPollRecords}
spring.kafka.listener.missing-topics-fatal=false
spring.kafka.listener.concurrency=${kafka.listener.concurrency}
spring.kafka.listener.ack-mode=RECORD
spring.kafka.listener.type=SINGLE
spring.kafka.listener.poll-timeout=5000
spring.kafka.producer.retries=0
spring.kafka.producer.acks=1
spring.kafka.producer.batch-size=${kafka.producer.batchSize}
spring.kafka.producer.properties.linger.ms=${kafka.producer.lingerMs}
spring.kafka.producer.buffer-memory=${kafka.producer.bufferMemory}
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.ByteArraySerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.ByteArraySerializer
spring.kafka.properties.max.request.size=104857600
spring.kafka.calculateEndFlagTopic=${kafka.calculateEndFlagTopic}
spring.kafka.calculatePatchFileTopic=${kafka.calculatePatchFileTopic}
spring.kafka.loadEndFlagTopic=${kafka.loadEndFlagTopic}
spring.kafka.repairFinishTopic=${kafka.repairFinishTopic}
spring.kafka.repairRecalculateTopic=${kafka.repairRecalculateTopic}
spring.kafka.manualFinishTopic=${kafka.manualFinishTopic}
spring.kafka.computeResultTopic=${kafka.computeResultTopic}
spring.kafka.ruleCalculateEndFlagTopic=${kafka.ruleCalculateEndFlagTopic}
spring.kafka.testItemFinishTopic=${kafka.testItemFinishTopic}
spring.kafka.ymsCalculateEndFlagTopic=${kafka.ymsCalculateEndFlagTopic}

# redis
spring.redis.host=${redis.host}
spring.redis.port=${redis.port}
spring.redis.database=${redis.database}
spring.redis.password=${redis.password}
spring.redis.lockExpireTime=${redis.lockExpireTime}
spring.redis.unlockExpireTime=${redis.unlockExpireTime}
spring.redis.timeout=3000
spring.redis.jedis.pool.min-idle=1
spring.redis.jedis.pool.max-idle=10
spring.redis.jedis.pool.max-active=20
spring.redis.jedis.pool.max-wait=-1
spring.data.redis.repositories.enabled=false

# task
spring.task.execution.thread-name-prefix=listener-kafka-
spring.task.execution.pool.queue-capacity=0
spring.task.execution.pool.max-size=50
spring.task.execution.pool.core-size=10
spring.task.execution.pool.keep-alive=1s
spring.task.execution.pool.allow-core-thread-timeout=true
spring.task.execution.shutdown.await-termination=true
spring.task.execution.shutdown.await-termination-period=60

# scheduling
spring.task.scheduling.pool.size=${task.scheduling.pool.size}
spring.task.scheduling.shutdown.await-termination=true
spring.task.scheduling.shutdown.await-termination-period=60
spring.task.scheduling.thread-name-prefix=listener-spark-
spring.scheduler.polling.milliseconds=${scheduler.polling.milliseconds}

# buffer pool
spring.spark.buffer.pool.allMaxRunningSize=${spark.buffer.pool.allMaxRunningSize}
spring.spark.buffer.pool.odsDelaySecond=${spark.buffer.pool.odsDelaySecond}

# manual buffer pool
spring.spark.buffer.pool.manualCpDwdMaxRunningSize=${spark.buffer.pool.manualCpDwdMaxRunningSize}
spring.spark.buffer.pool.manualFtDwdMaxRunningSize=${spark.buffer.pool.manualFtDwdMaxRunningSize}
spring.spark.buffer.pool.manualAllMaxRunningSize=${spark.buffer.pool.manualAllMaxRunningSize}

# noticeBi
spring.noticeBi.open=${noticeBi.open}
spring.noticeBi.url=${noticeBi.url}
spring.noticeBi.headers=${noticeBi.headers}

# mesRequest
spring.mesRequest.open=${mesRequest.open}
spring.mesRequest.url=${mesRequest.url}

# mesDataLossRecord过期天数
spring.mesDataLossRecord.expireDay=${mesDataLossRecord.expireDay}

# run mode
spring.runMode.standaloneThreshold=${runMode.standaloneThreshold}
spring.runMode.manualThreshold=${runMode.manualThreshold}

# checkCkSinkTimeout
spring.ck.checkCkSinkTimeout=${ck.checkCkSinkTimeout}
spring.handler.sink.batchSize=${handler.sink.batchSize}

# dubbo config
dubbo.application.name=${module.name}
dubbo.application.serialize-check-status=WARN
dubbo.application.qos-enable=false
dubbo.registry.address=zookeeper://${zookeeper.address}
dubbo.consumer.check=false
dubbo.consumer.timeout=${rpc.timeout}
dubbo.consumer.retries=0
dubbo.consumer.group=${environment.group}
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.registry.protocol=zookeeper
dubbo.provider.timeout=${rpc.timeout}
dubbo.provider.retries=0
dubbo.provider.group=${environment.group}
dubbo.provider.filter=customerProviderFilter
