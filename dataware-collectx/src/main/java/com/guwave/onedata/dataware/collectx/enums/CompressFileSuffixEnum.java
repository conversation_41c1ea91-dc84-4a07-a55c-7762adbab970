package com.guwave.onedata.dataware.collectx.enums;

public enum CompressFileSuffixEnum {
    GZ("GZ", "application/gzip"),
    ZIP("ZIP", "application/zip"),
    SEVENZ("7Z", "application/x-7z-compressed"),
    Z("Z", "application/x-compress"),
    TAR("TAR", "application/x-tar"),
    RAR("RAR", "application/x-rar-compressed; version=5"),
    RAR4("RAR4", "application/x-rar-compressed; version=4"),
    TGZ("TGZ", "application/x-gtar");

    private final String value;
    private final String type;

    CompressFileSuffixEnum(String value, String type) {
        this.value = value;
        this.type = type;
    }

    public String value() {
        return this.value;
    }

    public static CompressFileSuffixEnum of(String value) {
        for (CompressFileSuffixEnum compressFileSuffixEnum : CompressFileSuffixEnum.values()) {
            if (compressFileSuffixEnum.value().equalsIgnoreCase(value)) {
                return compressFileSuffixEnum;
            }
        }
        return null;
    }

    public static CompressFileSuffixEnum ofType(String type) {
        for (CompressFileSuffixEnum compressFileSuffixEnum : CompressFileSuffixEnum.values()) {
            if (compressFileSuffixEnum.type.equalsIgnoreCase(type)) {
                return compressFileSuffixEnum;
            }
        }
        return null;
    }
}
