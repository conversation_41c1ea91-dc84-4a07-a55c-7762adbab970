package com.guwave.onedata.dataware.collectx.entity;

import com.guwave.onedata.dataware.common.contant.FileCategory;
import lombok.Data;

@Data
public class FileAttr {
    private String filePath;
    private String fileName;
    private String originalFileName;
    private String fullName;
    private long size;
    private long originalSize;

    private FileCategory fileCategory;
    // 解压后会拼上压缩包的路径
    private String remoteFileFullPath;
    // hdfs上的全路径
    private String hdfsFileFullName;

}
