package com.guwave.onedata.dataware.scheduler.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.LotStockingDetail;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.MesDataLossRecord;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.MesParseLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SparkAppConfig;
import com.guwave.onedata.dataware.dao.mysql.manager.SftpFileDetailManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.*;
import com.guwave.onedata.next.compute.api.iface.IComputeRpcService;
import com.guwave.onedata.next.compute.api.vo.response.ComputeResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.guwave.gdp.common.constant.Constant.MIDDLE_LINE;

@Component
public class MesDwsPatchScheduleService {
    private static final Logger LOGGER = LoggerFactory.getLogger(MesDwsPatchScheduleService.class);

    @Autowired
    private MesParseLogRepository mesParseLogRepository;
    @Autowired
    private MesDataLossRecordRepository mesDataLossRecordRepository;
    @Autowired
    private SparkAppConfigRepository sparkAppConfigRepository;
    @Autowired
    private MesDetailRepository mesDetailRepository;
    @Autowired
    private RedisService redisService;
    @DubboReference
    private IComputeRpcService computeRpcService;
    @Autowired
    private LotStockingDetailRepository lotStockingDetailRepository;

    @ScheduleSwitch
    @Scheduled(fixedDelayString = "${spring.scheduler.polling.milliseconds}")
    public void runTask() {
        dealMesParseLog();
    }


    private void dealMesParseLog() {
        List<MesParseLog> processingMesParseLogs = mesParseLogRepository.findAllByProcessStatusOrderByUpdateTimeAsc(
                ProcessStatus.PROCESSING, Pageable.ofSize(1)
        );
        if (!CollectionUtils.isEmpty(processingMesParseLogs)) {
            LOGGER.info("存在正在处理的MesParseLog...");
            return;
        }

        List<MesParseLog> needDealMesParseLogs = mesParseLogRepository.findAllByProcessStatusOrderByUpdateTimeAsc(
                ProcessStatus.CREATE, Pageable.ofSize(1)
        );
        if (CollectionUtils.isEmpty(needDealMesParseLogs)) {
            LOGGER.info("没有待处理的MesParseLog");
            return;
        }
        MesParseLog needDealMesParseLog = needDealMesParseLogs.get(0);
        // 所有同样的 mesParseLog
        List<MesParseLog> mesParseLogs = mesParseLogRepository.findAllByCustomerAndFactoryAndFactorySiteAndTestAreaAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoAndProcessStatus(
                needDealMesParseLog.getCustomer(), needDealMesParseLog.getFactory(), needDealMesParseLog.getFactorySite(), needDealMesParseLog.getTestArea(), needDealMesParseLog.getDeviceId(), needDealMesParseLog.getLotType(), needDealMesParseLog.getTestStage(), needDealMesParseLog.getLotId(), needDealMesParseLog.getWaferNo(), ProcessStatus.CREATE
        );

        Date date = new Date();
        boolean lockDwDieTask = redisService.lockDwDieTask(
                Lists.newArrayList(FileCategory.OTHER),
                needDealMesParseLog.getCustomer(), needDealMesParseLog.getTestArea(), needDealMesParseLog.getFactory(), needDealMesParseLog.getDeviceId(), needDealMesParseLog.getLotType(), needDealMesParseLog.getTestStage(), needDealMesParseLog.getLotId(), needDealMesParseLog.getWaferNo(),
                RedisService.lockDwDieMesPatchValue
        );
        if (lockDwDieTask) {
            LOGGER.info("{} 上锁成功 MES_DWS_PATCH", JSON.toJSONString(needDealMesParseLog));
            boolean runTaskFlag = false;
            try {
                // 是否存在mesDataLossRecord记录（无关状态）
                List<MesDataLossRecord> existsMesDataLossRecords = mesDataLossRecordRepository.findAllByCustomerAndFactoryAndFactorySiteAndTestAreaAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoOrderByIdDesc(
                        needDealMesParseLog.getCustomer(), needDealMesParseLog.getFactory(), needDealMesParseLog.getFactorySite(), needDealMesParseLog.getTestArea(), needDealMesParseLog.getDeviceId(), needDealMesParseLog.getLotType(), needDealMesParseLog.getTestStage(), needDealMesParseLog.getLotId(), needDealMesParseLog.getWaferNo(), Pageable.ofSize(1)
                );

                if (CollectionUtils.isEmpty(existsMesDataLossRecords)) {
                    LOGGER.info("{} 对应的MesDataLossRecord 为空", JSON.toJSONString(needDealMesParseLog));
                    mesParseLogs.forEach(t -> t.setProcessStatus(ProcessStatus.SUCCESS)
                            .setComment(t.getComment() + DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss") + "\t对应的MesDataLossRecord 为空，不处理\n")
                            .setUpdateTime(date));
                    mesParseLogRepository.saveAll(mesParseLogs);
                    return;
                }
                MesDataLossRecord latestMesDataLossRecord = existsMesDataLossRecords.get(0);

                // 所有同样的 CREATE 状态的 mesDataLossRecord
                List<MesDataLossRecord> mesDataLossRecords = mesDataLossRecordRepository.findAllByCustomerAndFactoryAndFactorySiteAndTestAreaAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoAndProcessStatus(
                        needDealMesParseLog.getCustomer(), needDealMesParseLog.getFactory(), needDealMesParseLog.getFactorySite(), needDealMesParseLog.getTestArea(), needDealMesParseLog.getDeviceId(), needDealMesParseLog.getLotType(), needDealMesParseLog.getTestStage(), needDealMesParseLog.getLotId(), needDealMesParseLog.getWaferNo(), ProcessStatus.CREATE
                );

                SparkAppConfig sparkAppConfig = sparkAppConfigRepository.findByFileCategoryAndUploadTypeAndTestAreaAndDwLayer(
                        FileCategory.STDF,
                        UploadType.AUTO,
                        TestArea.of(needDealMesParseLog.getTestArea().getTestScope()),
                        DwLayer.MES_DWS_PATCH
                );
                if (sparkAppConfig == null) {
                    LOGGER.info("{} sparkAppConfig没有MES_DWS_PATCH配置信息", JSON.toJSONString(needDealMesParseLog));
                    mesParseLogs.forEach(t -> t.setProcessStatus(ProcessStatus.FAIL)
                            .setComment(t.getComment() + DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss") + "\tsparkAppConfig没有MES_DWS_PATCH配置信息\n")
                            .setUpdateTime(date));
                    mesParseLogRepository.saveAll(mesParseLogs);
                    mesDataLossRecords.forEach(t -> t.setProcessStatus(ProcessStatus.FAIL)
                            .setUpdateTime(date));
                    mesDataLossRecordRepository.saveAll(mesDataLossRecords);
                    return;
                }

                int mesDetailCount = mesDetailRepository.countAllByCustomerAndFactoryAndFactorySiteAndTestAreaAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNo(
                        needDealMesParseLog.getCustomer(), needDealMesParseLog.getFactory(), needDealMesParseLog.getFactorySite(), needDealMesParseLog.getTestArea(), needDealMesParseLog.getDeviceId(), needDealMesParseLog.getLotType(), needDealMesParseLog.getTestStage(), needDealMesParseLog.getLotId(), needDealMesParseLog.getWaferNo()
                );
                if (mesDetailCount <= 0) {
                    LOGGER.info("{} 不存在对应的mesDetail", JSON.toJSONString(needDealMesParseLog));
                    mesParseLogs.forEach(t -> t.setProcessStatus(ProcessStatus.SUCCESS)
                            .setComment(t.getComment() + DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss") + "\t不存在对应的mesDetail，不处理\n")
                            .setUpdateTime(date));
                    mesParseLogRepository.saveAll(mesParseLogs);
                    mesDataLossRecords.forEach(t -> t.setProcessStatus(ProcessStatus.SUCCESS)
                            .setUpdateTime(date));
                    mesDataLossRecordRepository.saveAll(mesDataLossRecords);
                    return;
                }

                try {
                    LOGGER.info("{} 开始执行MES_DWS_PATCH任务！", JSON.toJSONString(needDealMesParseLog));
                    mesParseLogs.forEach(t -> t.setProcessStatus(ProcessStatus.PROCESSING)
                            .setComment(t.getComment() + DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss") + "\tMesDwsPatch补充mes信息开始\n")
                            .setUpdateTime(date));
                    mesParseLogRepository.saveAll(mesParseLogs);
                    mesDataLossRecords.forEach(t -> t.setProcessStatus(ProcessStatus.PROCESSING)
                            .setUpdateTime(date));
                    mesDataLossRecordRepository.saveAll(mesDataLossRecords);

                    // 构造mesDwsPatch spark参数
                    Map<String, String> params = new HashMap<String, String>() {{
                        put("customer", latestMesDataLossRecord.getCustomer());
                        put("subCustomer", latestMesDataLossRecord.getSubCustomer());
                        put("factory", latestMesDataLossRecord.getFactory());
                        put("factorySite", latestMesDataLossRecord.getFactorySite());
                        put("testArea", latestMesDataLossRecord.getTestArea().getArea());
                        put("deviceId", latestMesDataLossRecord.getDeviceId());
                        put("lotId", latestMesDataLossRecord.getLotId());
                        put("waferNo", latestMesDataLossRecord.getWaferNo());
                        put("lotType", latestMesDataLossRecord.getLotType().getType());
                        put("testStage", latestMesDataLossRecord.getTestStage());
                        put("executeMode", ExecuteMode.AUTO.getMode());
                    }};
                    List<LotStockingDetail> lotStockingDetails = lotStockingDetailRepository.findAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNo(
                            latestMesDataLossRecord.getCustomer(),
                            latestMesDataLossRecord.getTestArea(),
                            latestMesDataLossRecord.getFactory(),
                            latestMesDataLossRecord.getFactorySite(),
                            latestMesDataLossRecord.getDeviceId(),
                            latestMesDataLossRecord.getLotType(),
                            latestMesDataLossRecord.getTestStage(),
                            latestMesDataLossRecord.getLotId(),
                            latestMesDataLossRecord.getWaferNo()
                    );
                    List<LotStockingDetail> supportLotStockingDetails = lotStockingDetails.stream()
                            .filter(lotStockingDetail -> lotStockingDetail.getFileCategory() != FileCategory.BIT_MEM)
                            .collect(Collectors.toList());
                    Long dieCnt = SftpFileDetailManager.calDieCnt(supportLotStockingDetails);
                    Long testItemCnt = dieCnt;

                    ComputeResponse computeResponse = computeRpcService.submit(sparkAppConfig.getMainClass(), generateAppName(sparkAppConfig.getMainClass(), latestMesDataLossRecord), dieCnt, testItemCnt, params);
                    if (!ComputeResponse.SUCCESS.equals(computeResponse.getCode())) {
                        throw new RuntimeException(JSON.toJSONString(computeResponse));
                    }
                    mesParseLogs.forEach(t -> t.setUniqueId(computeResponse.getUniqueId()).setUpdateTime(date));
                    mesParseLogRepository.saveAll(mesParseLogs);
                    runTaskFlag = true;
                } catch (Exception e) {
                    LOGGER.error("{} 提交spark任务异常：", JSON.toJSONString(latestMesDataLossRecord), e);
                    // 改成 CREATE，重试
                    mesParseLogs.forEach(t -> t.setUniqueId(null).setProcessStatus(ProcessStatus.CREATE)
                            .setComment(t.getComment() + DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss") + "\tMesDwsPatch提交spark任务异常:" + e.getMessage() + "\n")
                            .setUpdateTime(date));
                    mesParseLogRepository.saveAll(mesParseLogs);
                    mesDataLossRecords.forEach(t -> t.setProcessStatus(ProcessStatus.CREATE)
                            .setUpdateTime(date));
                    mesDataLossRecordRepository.saveAll(mesDataLossRecords);
                }
            } finally {
                if (runTaskFlag) {
                    // 任务结束后才解锁
                } else {
                    // 未运行任务立马解锁
                    redisService.unlockDwDieTask(
                            Lists.newArrayList(FileCategory.OTHER),
                            needDealMesParseLog.getCustomer(), needDealMesParseLog.getTestArea(), needDealMesParseLog.getFactory(), needDealMesParseLog.getDeviceId(), needDealMesParseLog.getLotType(), needDealMesParseLog.getTestStage(), needDealMesParseLog.getLotId(), needDealMesParseLog.getWaferNo(),
                            RedisService.lockDwDieMesPatchValue
                    );
                }
            }
        } else {
            LOGGER.info("{} 存在die任务正在执行！", JSON.toJSONString(needDealMesParseLog));
            mesParseLogs.forEach(t -> t.setUpdateTime(date));
            mesParseLogRepository.saveAll(mesParseLogs);
        }
    }

    private String generateAppName(String prefix, MesDataLossRecord record) {
        StringBuilder builder = new StringBuilder(prefix + MIDDLE_LINE)
                .append("/TEST_AREA=").append(record.getTestArea().getArea())
                .append("/CUSTOMER=").append(record.getCustomer())
                .append("/FACTORY=").append(record.getFactory())
                .append("/DEVICE_ID=").append(record.getDeviceId())
                .append("/LOT_ID=").append(record.getLotId());
        if (StringUtils.isNotBlank(record.getWaferNo())) {
            builder.append("/WAFER_NO=").append(record.getWaferNo());
        }
        builder
                .append("/TEST_STAGE=").append(record.getTestStage())
                .append("/LOT_TYPE=").append(record.getLotType().getType());
        return builder.toString();
    }

    public void afterDealMesParseLog(List<MesParseLog> mesParseLogs, List<MesDataLossRecord> mesDataLossRecords, ProcessStatus processStatus) {
        try {
            Date date = new Date();
            mesParseLogs.forEach(t -> t.setProcessStatus(processStatus)
                    .setComment(t.getComment() + DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss") + "\tMesDwsPatch补充mes信息 " + (processStatus == ProcessStatus.SUCCESS ? "成功" : "失败") + "\n")
                    .setUpdateTime(date));
            mesParseLogRepository.saveAll(mesParseLogs);
            mesDataLossRecords.forEach(t -> t.setProcessStatus(processStatus)
                    .setUpdateTime(date));
            mesDataLossRecordRepository.saveAll(mesDataLossRecords);
        } finally {

        }
    }
}
