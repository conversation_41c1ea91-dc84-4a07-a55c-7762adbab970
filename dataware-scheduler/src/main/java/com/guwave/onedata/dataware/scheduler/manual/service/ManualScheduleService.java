package com.guwave.onedata.dataware.scheduler.manual.service;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SparkAppConfig;
import com.guwave.onedata.dataware.dao.mysql.manager.ManualFileInfoManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualCalculateTaskRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualMessageRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SparkAppConfigRepository;
import com.guwave.onedata.next.compute.api.iface.IComputeRpcService;
import com.guwave.onedata.next.compute.api.vo.response.ComputeResponse;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.*;

import static com.guwave.gdp.common.constant.Constant.MIDDLE_LINE;
import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;

public interface ManualScheduleService {
    Logger LOGGER = LoggerFactory.getLogger(ManualScheduleService.class);

    void runTask();

    DwLayer getDwLayer();

    List<TestArea> getTestArea();

    default Long getManualAllProcessingTask() {
        return getManualCalculateTaskRepository().countAllByDwLayerAndProcessStatus(getDwLayer(), ProcessStatus.PROCESSING);
    }

    default Long getManualCurrentTestAreaProcessingTask() {
        return getManualCalculateTaskRepository().countAllByTestAreaInAndDwLayerAndProcessStatus(getTestArea(), getDwLayer(), ProcessStatus.PROCESSING);
    }

    default SparkAppConfig getSparkAppConfig(FileCategory fileCategory, UploadType uploadType, TestArea testArea, DwLayer dwLayer) {
        return getSparkAppConfigRepository().findByFileCategoryAndUploadTypeAndTestAreaAndDwLayer(fileCategory, uploadType, TestArea.of(testArea.getTestScope()), dwLayer);
    }

    default ManualCalculateTask getTask() {
        List<ManualCalculateTask> manualTasks = getManualCalculateTaskRepository().findAllByTestAreaInAndDwLayerAndRunModeAndProcessStatusOrderByPriorityDescIdAsc(getTestArea(), getDwLayer(), RunMode.DISTRIBUTED, ProcessStatus.CREATE, Pageable.ofSize(1));
        return CollectionUtils.isNotEmpty(manualTasks) ? manualTasks.get(0) : null;
    }


    default void executeSparkTask() {
        LOGGER.info("开始执行Manual Spark Runner");

        // 查询总任务上限
        if (getManualAllProcessingTask() >= getManualAllMaxRunningTaskCount()) {
            LOGGER.info("运行任务达到总上限！");
            return;
        }

        // 查询当前testArea任务上限
        if (getManualCurrentTestAreaProcessingTask() >= getManualMaxRunningSize()) {
            LOGGER.info("当前testArea运行任务达到上限！");
            return;
        }

        // 查询待执行任务
        ManualCalculateTask manualCalculateTask = getTask();
        if (Objects.isNull(manualCalculateTask)) {
            LOGGER.info("当前没有找到需要执行的任务");
            return;
        }

        try {
            LOGGER.info("开始执行: {}", JSON.toJSONString(manualCalculateTask));
            ManualFileInfoManager.RunTaskFilesContext runTaskFilesInfo = getManualFileInfoManager().getRunTaskFilesInfo(manualCalculateTask.getNeedReadFileIds());

            if (runTaskFilesInfo.getManualFileSystem() != ManualFileSystem.HDFS) {
                LOGGER.info("文件来源不在HDFS，不在此处处理！");
                return;
            }

            // 更新任务状态
            manualCalculateTask
                    .setProcessStatus(ProcessStatus.PROCESSING)
                    .setUpdateTime(new Date())
            ;
            getManualCalculateTaskRepository().save(manualCalculateTask);
            // 设置spark任务参数
            SparkAppConfig config = getSparkAppConfig(manualCalculateTask.getFileCategory(), manualCalculateTask.getUploadType(), manualCalculateTask.getTestArea(), manualCalculateTask.getDwLayer());
            Map<String, String> params = new HashMap<String, String>() {{
                put("customer", manualCalculateTask.getCustomer());
                put("subCustomer", manualCalculateTask.getSubCustomer());
                put("factory", manualCalculateTask.getFactory());
                put("factorySite", manualCalculateTask.getFactorySite());
                put("testArea", manualCalculateTask.getTestArea().getArea());
                put("deviceId", manualCalculateTask.getDeviceId());
                put("lotId", manualCalculateTask.getLotId());
                put("waferNo", manualCalculateTask.getWaferNo());
                put("lotType", manualCalculateTask.getLotType().getType());
                put("testStage", manualCalculateTask.getTestStage());
                put("fileCategory", manualCalculateTask.getFileCategory().getCategory());
                put("uploadType", manualCalculateTask.getUploadType().getType());
                put("fileId", manualCalculateTask.getFileId().toString());
                put("fileName", manualCalculateTask.getFileName());
                put("needReadFileIds", manualCalculateTask.getNeedReadFileIds());
                put("manualType", manualCalculateTask.getManualType().getType());
                put("fileOwner", manualCalculateTask.getFileOwner());
                put("uploadTime", runTaskFilesInfo.getUploadTime().toString());
                put("dataVersion", System.currentTimeMillis() + EMPTY);
                put("executeMode", ExecuteMode.AUTO.getMode());
                put("originFileModifyFields", manualCalculateTask.getOriginFileModifyFields());
            }};
            fillTaskParams(params, manualCalculateTask, runTaskFilesInfo);
            ComputeResponse computeResponse = getComputeRpcService().submit(config.getMainClass(), generateAppName(config.getMainClass(), manualCalculateTask), runTaskFilesInfo.getDieCnt(), runTaskFilesInfo.getTestItemCnt(), params);
            if (!ComputeResponse.SUCCESS.equals(computeResponse.getCode())) {
                throw new RuntimeException(JSON.toJSONString(computeResponse));
            }
            manualCalculateTask.setUniqueId(computeResponse.getUniqueId()).setUpdateTime(new Date());
            getManualCalculateTaskRepository().save(manualCalculateTask);
        } catch (Exception e) {
            LOGGER.error("{} 提交spark任务异常：", JSON.toJSONString(manualCalculateTask), e);
            submitTaskFail(manualCalculateTask);
            // 改成 CREATE，重试
            manualCalculateTask.setUniqueId(null).setProcessStatus(ProcessStatus.CREATE).setUpdateTime(new Date());
            getManualCalculateTaskRepository().save(manualCalculateTask);
        }
    }

    void submitTaskFail(ManualCalculateTask manualCalculateTask);

    default String generateAppName(String prefix, ManualCalculateTask task) {
        StringBuilder builder = new StringBuilder(prefix + MIDDLE_LINE)
                .append(task.getFileCategory().getCategory())
                .append("/MANUAL_TYPE=").append(task.getManualType().getType())
                .append("/TEST_AREA=").append(task.getTestArea().getArea())
                .append("/CUSTOMER=").append(task.getCustomer())
                .append("/FILE_ID=").append(task.getFileId())
                .append("/NEED_READ_FILE_IDS=").append(task.getNeedReadFileIds());
        return builder.toString();
    }

    void fillTaskParams(Map<String, String> params, ManualCalculateTask manualCalculateTask, ManualFileInfoManager.RunTaskFilesContext runTaskFilesInfo);

    ManualFileInfoManager getManualFileInfoManager();

    IComputeRpcService getComputeRpcService();

    Long getManualAllMaxRunningTaskCount();

    Long getManualMaxRunningSize();

    SparkAppConfigRepository getSparkAppConfigRepository();

    ManualCalculateTaskRepository getManualCalculateTaskRepository();

    ManualMessageRepository getManualMessageRepository();

    String getManualFinishTopic();

    KafkaTemplate<byte[], byte[]> getKafkaTemplate();

}
