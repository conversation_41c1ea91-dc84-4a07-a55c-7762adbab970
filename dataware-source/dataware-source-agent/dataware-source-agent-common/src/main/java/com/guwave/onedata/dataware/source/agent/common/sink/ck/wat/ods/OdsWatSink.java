package com.guwave.onedata.dataware.source.agent.common.sink.ck.wat.ods;

import com.guwave.onedata.dataware.common.model.wat.ods.OdsWat;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

/**
 * Copyright (C), 2022, guwave
 * <p>
 * OdsWatSink
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2022-03-14 11:34:47
 */
@Component
public class OdsWatSink implements CkSink<OdsWat> {

    @Value("${spring.sink.ck.ods.dbName}")
    private String dbName;

    @Override
    public String getTableName() {
        return "ods_wat_cluster";
    }

    @Override
    public void handle(PreparedStatement statement, List<OdsWat> items) throws SQLException {
        for (OdsWat item : items) {
            statement.setObject(1, item.getCustomer());
            statement.setObject(2, item.getFactory());
            statement.setObject(3, item.getFab());
            statement.setObject(4, item.getLotId());
            statement.setObject(5, item.getWaferId());
            statement.setObject(6, this.toTimestamp(item.getStartTime()));
            statement.setObject(7, this.toTimestamp(item.getEndTime()));
            statement.setObject(8, item.getReticleX());
            statement.setObject(9, item.getReticleY());
            statement.setObject(12, item.getSiteId());
            statement.setObject(13, item.getWfFlat());
            statement.setObject(15, item.getDeviceId());
            statement.setObject(22, item.getTestProgram());
            statement.setObject(25, item.getTestTemperature());
            statement.setObject(28, item.getTesterName());
            statement.setObject(30, item.getOperatorName());
            statement.setObject(31, item.getProcess());
            statement.setObject(35, this.toBigDecimal(item.getHiLimit()));
            statement.setObject(36, this.toBigDecimal(item.getLoLimit()));
            statement.setObject(37, item.getUnits());
            statement.setObject(38, this.toBigDecimal(item.getTestValue()));
            statement.setObject(39, new Timestamp(System.currentTimeMillis()));
            // TEST_NUM
            statement.setObject(40, item.getTestNum());
            // IS_FIRST_TEST
            statement.setObject(41, item.getIsFirstTest());
            // IS_FINAL_TEST
            statement.setObject(42, item.getIsFinalTest());
            // IS_DUP_FIRST_TEST
            statement.setObject(43, item.getIsDupFirstTest());
            // IS_DUP_FINAL_TEST
            statement.setObject(44, item.getIsDupFinalTest());
            // TEST_STAGE
            statement.setObject(45, item.getTestStage());
            statement.setObject(46, item.getSubCustomer());
            statement.addBatch();
        }
    }

    @Override
    public List<String> getColumns() {
        return Arrays.asList(
                "CUSTOMER",
                "FACTORY",
                "FAB",
                "LOT_ID",
                "WAFER_ID",
                "START_TIME",
                "END_TIME",
                "REC_X",
                "REC_Y",
                "SITE_ID",
                "ORIENTATION",
                "PRODUCT_ID",
                "TEST_PROGRAM",
                "TEMPERATURE",
                "TESTER",
                "OPERATOR",
                "PROCESS",
                "HI_LIMIT",
                "LO_LIMIT",
                "UNIT",
                "RESULT",
                "CREATE_TIME",
                "TEST_NUM",
                "IS_FIRST_TEST",
                "IS_FINAL_TEST",
                "IS_DUP_FIRST_TEST",
                "IS_DUP_FINAL_TEST",
                "TEST_STAGE",
                "SUB_CUSTOMER");
    }

    @Override
    public String getDbName() {
        return dbName;
    }
}
