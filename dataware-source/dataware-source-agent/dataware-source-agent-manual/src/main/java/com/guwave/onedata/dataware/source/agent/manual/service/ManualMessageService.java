package com.guwave.onedata.dataware.source.agent.manual.service;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingFinishMessage;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingMessage;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualMessage;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualMessageRepository;
import com.guwave.onedata.dataware.source.agent.common.sink.impl.KafkaSink;
import com.guwave.onedata.dataware.source.agent.manual.handler.ManualTypehandler;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
public class ManualMessageService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ManualMessageService.class);


    @Value("${spring.kafka.manualFinishTopic}")
    private String manualFinishTopic;

    @Autowired
    private ManualMessageRepository manualMessageRepository;
    @Autowired
    private List<ManualTypehandler> manualTypehandlers;
    @Autowired
    private KafkaSink kafkaSink;

    public void distributeManualMessage(String jsonStr) {
        ManualWarehousingMessage manualWarehousingMessage = JSON.parseObject(jsonStr, ManualWarehousingMessage.class);
        Date date = new Date();
        ManualMessage manualMessage = new ManualMessage()
                .setManualType(manualWarehousingMessage.getManualType())
                .setManualStartMessage(jsonStr)
                .setProcessStatus(ProcessStatus.CREATE)
                .setCreateTime(date)
                .setUpdateTime(date)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateUser(Constant.SYSTEM);
        Optional<ManualTypehandler> manualTypehandlerOptional = manualTypehandlers.stream().filter(t -> t.support(manualMessage.getManualType())).findFirst();
        if (manualTypehandlerOptional.isPresent()) {
            manualMessage.setProcessStatus(ProcessStatus.PROCESSING);
            manualMessageRepository.save(manualMessage);
            // 生成任务
            try {
                manualTypehandlerOptional.get().generateTask(manualMessage, manualWarehousingMessage);
            } catch (Exception e) {
                LOGGER.error("生成任务异常：{}", jsonStr, e);
                // 生成任务异常
                FileLoadException exception = e instanceof FileLoadException ? (FileLoadException) e : new FileLoadException(FileLoadExceptionInfo.OTHER_EXCEPTION, ExceptionUtils.getStackTrace(e), null).updateExceptionMessage(e.getMessage());
                manualMessage
                        .setProcessStatus(ProcessStatus.FAIL)
                        .setExceptionType(exception.getExceptionType())
                        .setExceptionMessage(exception.getExceptionMessage())
                        .setErrorMessage(ExceptionUtils.getStackTrace(exception));

                ManualWarehousingFinishMessage manualWarehousingFinishMessage = new ManualWarehousingFinishMessage()
                        .setManualType(manualMessage.getManualType())
                        .setProcessStatus(manualMessage.getProcessStatus())
                        .setExceptionType(manualMessage.getExceptionType())
                        .setExceptionMessage(manualMessage.getExceptionMessage());
                manualTypehandlerOptional.get().fillManualWarehousingFinishMessage(manualWarehousingFinishMessage, manualWarehousingMessage);
                String manualFinishMessageStr = JSON.toJSONString(manualWarehousingFinishMessage);
                manualMessage.setManualFinishMessage(manualFinishMessageStr);

                manualMessageRepository.save(manualMessage);
                // 发送结束消息
                kafkaSink.send(manualFinishTopic, manualMessage.getManualType().getType(), manualMessage.getManualFinishMessage());
            }
        } else {
            // 此类型没有对应的处理器
            LOGGER.error("messageId: {},manualType: {},没有对应的处理器！", manualMessage.getId(), manualMessage.getManualType());
            manualMessage
                    .setProcessStatus(ProcessStatus.FAIL)
                    .setExceptionType(FileLoadExceptionInfo.IGNORE_FAIL.getType())
                    .setExceptionMessage(FileLoadExceptionInfo.IGNORE_FAIL.getMessage())
                    .setErrorMessage("没有对应的处理器")
            ;
            manualMessageRepository.save(manualMessage);
        }
    }

}
