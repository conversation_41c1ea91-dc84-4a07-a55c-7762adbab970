package com.guwave.onedata.dataware.source.agent.common.service;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.model.key.LotWaferPrimaryKey;
import com.guwave.onedata.dataware.source.agent.common.vo.FileProcessProgress;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.guwave.onedata.dataware.common.contant.Constant.MIDDLE_LINE;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * FileProcessProgressService
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-14 09:54:31
 */
@Service
@SuppressWarnings("unchecked")
public class FileProcessProgressService {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileProcessProgressService.class);

    private static final String SINGLE_FILE_FETCHER_FILE_KEY = "SINGLE_FILE_FETCHER_FILE";
    private static final String FETCHER_LOT_AND_WAFER_KEY = "FETCHER_LOT_AND_WAFER";
    private static final String SINGLE_FETCHER_WAFERMAP_CONFIG_KEY = "SINGLE_FETCHER_WAFERMAP_CONFIG";
    private static final String ORIGIN_FILE_PATH_KEY = "ORIGIN_FILE_PATH_KEY";
    private static final String GENERATE_CUSTOMIZE_WAFER_NO_KEY = "GENERATE_CUSTOMIZE_WAFER_NO";
    private static final String PARSE_LOT_RELATION_KEY = "PARSE_LOT_RELATION";
    private static final String WIP_API_KEY = "WIP_API";

    private static final List<TestArea> SUPPORT_CP_TEST_AREA_LIST = TestArea.getCPList();


    @Value("${spring.handler.file.lockExpireTime}")
    private long lockExpireTime;
    @Value("${spring.handler.file.unlockExpireTime}")
    private long unlockExpireTime;

    private final RedisTemplate<String, FileProcessProgress> redisTemplate;

    private static final FileProcessProgress finalFileProcessProgress = new FileProcessProgress();

    public FileProcessProgressService(RedisTemplate<String, FileProcessProgress> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void clearAll() {
        clearLockOriginFilePath();
        clearLockFetcherLotAndWafer();
        clearLockSingleFileFetcherFile();
        clearLockGenerateCustomizeWaferNo();
        clearLockParseLotRelation();
        clearLockWipApi();
        clearLockUpdateLotWaferPrimaryData();
    }

    public void clearKey(String key) {
        key = key + Constant.MULTIPLICATION_SIGN;
        Set<String> keys = redisTemplate.keys(key);
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }
        keys.forEach(t -> {
            LOGGER.info("删除redis key：{}", t);
            redisTemplate.delete(t);
        });
    }

    /**
     * SingleFileFetcher 对文件上锁 (设置锁最大超时时间)
     *
     * @param fileName fileName
     */
    public boolean lockSingleFileFetcherFile(String fileName) {
        boolean lock;
        String key = SINGLE_FILE_FETCHER_FILE_KEY + Constant.UNDER_LINE + fileName;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            LOGGER.info("lock处理中: {}", key);
            lock = false;
        } else {
            lock = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, finalFileProcessProgress, lockExpireTime, TimeUnit.SECONDS));
        }
        return lock;
    }

    /**
     * SingleFileFetcher 对文件解锁    设置解锁过期时间   不直接删除
     *
     * @param fileName fileName
     */
    public void unlockSingleFileFetcherFile(String fileName) {
        String key = SINGLE_FILE_FETCHER_FILE_KEY + Constant.UNDER_LINE + fileName;
        redisTemplate.expire(key, unlockExpireTime, TimeUnit.SECONDS);
    }

    public void clearLockSingleFileFetcherFile() {
        clearKey(SINGLE_FILE_FETCHER_FILE_KEY);
    }

    /**
     * 对FT:lot和CP:wafer上锁 (设置锁最大超时时间)
     */
    public boolean lockFetcherLotAndWafer(String customer, TestArea testArea, String factory, String factorySite, String lotId, String waferNo, String testStage, LotType lotType) {
        boolean lock;
        String key = FETCHER_LOT_AND_WAFER_KEY + Constant.UNDER_LINE + customer + Constant.UNDER_LINE + testArea.getArea() + Constant.UNDER_LINE + factory + Constant.UNDER_LINE + factorySite + Constant.UNDER_LINE + lotId + Constant.UNDER_LINE + testStage + Constant.UNDER_LINE + LotType.of(lotType);
        if (SUPPORT_CP_TEST_AREA_LIST.contains(testArea)) {
            key += Constant.UNDER_LINE + waferNo;
        }
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            LOGGER.info("lock处理中: {}", key);
            lock = false;
        } else {
            lock = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, finalFileProcessProgress, lockExpireTime, TimeUnit.SECONDS));
        }
        return lock;
    }

    /**
     * 对FT:lot和CP:wafer上锁    设置解锁过期时间   不直接删除
     */
    public void unlockFetcherLotAndWafer(String customer, TestArea testArea, String factory, String factorySite, String lotId, String waferNo, String testStage, LotType lotType) {
        String key = FETCHER_LOT_AND_WAFER_KEY + Constant.UNDER_LINE + customer + Constant.UNDER_LINE + testArea.getArea() + Constant.UNDER_LINE + factory + Constant.UNDER_LINE + factorySite + Constant.UNDER_LINE + lotId + Constant.UNDER_LINE + testStage + Constant.UNDER_LINE + LotType.of(lotType);
        if (SUPPORT_CP_TEST_AREA_LIST.contains(testArea)) {
            key += Constant.UNDER_LINE + waferNo;
        }
        redisTemplate.expire(key, unlockExpireTime, TimeUnit.SECONDS);
    }

    public void clearLockFetcherLotAndWafer() {
        clearKey(FETCHER_LOT_AND_WAFER_KEY);
    }

    /**
     * 对文件的远程原始文件夹上锁
     */
    public boolean lockOriginFilePath(String customer, TestArea testArea, String factory, String factorySite, String originFilePath) {
        boolean lock;
        String key = ORIGIN_FILE_PATH_KEY + Constant.UNDER_LINE + customer + Constant.UNDER_LINE + testArea.getArea() + Constant.UNDER_LINE + factory + Constant.UNDER_LINE + factorySite + Constant.UNDER_LINE + originFilePath;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            LOGGER.info("lock处理中: {}", key);
            lock = false;
        } else {
            lock = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, finalFileProcessProgress, lockExpireTime, TimeUnit.SECONDS));
        }
        return lock;
    }

    /**
     * 对文件的远程原始文件夹解锁
     */
    public void unlockOriginFilePath(String customer, TestArea testArea, String factory, String factorySite, String originFilePath) {
        String key = ORIGIN_FILE_PATH_KEY + Constant.UNDER_LINE + customer + Constant.UNDER_LINE + testArea.getArea() + Constant.UNDER_LINE + factory + Constant.UNDER_LINE + factorySite + Constant.UNDER_LINE + originFilePath;
        redisTemplate.expire(key, unlockExpireTime, TimeUnit.SECONDS);
    }

    public void clearLockOriginFilePath() {
        clearKey(ORIGIN_FILE_PATH_KEY);
    }

    /**
     * SingleFetcher 对wafermapId上锁
     *
     * @param wafermapConfigId wafermapConfigId
     */
    public boolean lockSingleFetcherWafermapId(Long wafermapConfigId) {
        boolean lock;
        String key = SINGLE_FETCHER_WAFERMAP_CONFIG_KEY + Constant.UNDER_LINE + wafermapConfigId;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            LOGGER.info("lock处理中: {}", key);
            lock = false;
        } else {
            lock = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, finalFileProcessProgress, lockExpireTime, TimeUnit.SECONDS));
        }
        return lock;
    }

    /**
     * SingleFetcher 对wafermapId解锁    设置过期时间   不直接删除
     *
     * @param wafermapConfigId wafermapConfigId
     */
    public void unlockSingleFetcherWafermapId(Long wafermapConfigId) {
        String key = SINGLE_FETCHER_WAFERMAP_CONFIG_KEY + Constant.UNDER_LINE + wafermapConfigId;
        redisTemplate.expire(key, unlockExpireTime, TimeUnit.SECONDS);
    }


    /**
     * 对cp自定义生成waferNo上锁
     */
    public boolean lockGenerateCustomizeWaferNo(String customer, TestArea testArea, String factory, String factorySite, String lotId) {
        boolean lock;
        String key = GENERATE_CUSTOMIZE_WAFER_NO_KEY + Constant.UNDER_LINE + customer + Constant.UNDER_LINE + testArea.getArea() + Constant.UNDER_LINE + factory + Constant.UNDER_LINE + factorySite + Constant.UNDER_LINE + lotId;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            LOGGER.info("lock处理中: {}", key);
            lock = false;
        } else {
            lock = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, finalFileProcessProgress, lockExpireTime, TimeUnit.SECONDS));
        }
        return lock;
    }

    /**
     * 对cp自定义生成waferNo解锁
     */
    public void unlockGenerateCustomizeWaferNo(String customer, TestArea testArea, String factory, String factorySite, String lotId) {
        String key = GENERATE_CUSTOMIZE_WAFER_NO_KEY + Constant.UNDER_LINE + customer + Constant.UNDER_LINE + testArea.getArea() + Constant.UNDER_LINE + factory + Constant.UNDER_LINE + factorySite + Constant.UNDER_LINE + lotId;
        redisTemplate.delete(key);
    }

    public void clearLockGenerateCustomizeWaferNo() {
        clearKey(GENERATE_CUSTOMIZE_WAFER_NO_KEY);
    }

    /**
     * 对ft解析lotRelation上锁
     */
    public boolean lockParseLotRelation(String customer, TestArea testArea, String factory, String factorySite) {
        boolean lock;
        String key = PARSE_LOT_RELATION_KEY + Constant.UNDER_LINE + customer + Constant.UNDER_LINE + testArea.getArea() + Constant.UNDER_LINE + factory + Constant.UNDER_LINE + factorySite;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            LOGGER.info("lock处理中: {}", key);
            lock = false;
        } else {
            lock = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, finalFileProcessProgress, lockExpireTime, TimeUnit.SECONDS));
        }
        return lock;
    }

    /**
     * 对ft解析lotRelation解锁
     */
    public void unlockParseLotRelation(String customer, TestArea testArea, String factory, String factorySite) {
        String key = PARSE_LOT_RELATION_KEY + Constant.UNDER_LINE + customer + Constant.UNDER_LINE + testArea.getArea() + Constant.UNDER_LINE + factory + Constant.UNDER_LINE + factorySite;
        redisTemplate.expire(key, unlockExpireTime, TimeUnit.SECONDS);
    }

    public void clearLockParseLotRelation() {
        clearKey(PARSE_LOT_RELATION_KEY);
    }

    public boolean lockWipApi(String url) {
        boolean lock;
        String key = WIP_API_KEY + Constant.UNDER_LINE + url;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            LOGGER.info("lock处理中: {}", key);
            lock = false;
        } else {
            lock = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, finalFileProcessProgress, lockExpireTime, TimeUnit.SECONDS));
        }
        return lock;
    }

    public void unlockWipApi(String url) {
        String key = WIP_API_KEY + Constant.UNDER_LINE + url;
        redisTemplate.expire(key, unlockExpireTime, TimeUnit.SECONDS);
    }

    public void clearLockWipApi() {
        clearKey(WIP_API_KEY);
    }

    /**
     * 更新主批次表
     */
    public boolean lockUpdateLotWaferPrimaryData(LotWaferPrimaryKey lotWaferPrimaryKey) {
        boolean lock;
        String key = "UpdateLotWaferPrimaryDataLock:" +
                lotWaferPrimaryKey.getCustomer()
                + MIDDLE_LINE
                + lotWaferPrimaryKey.getSubCustomer()
                + MIDDLE_LINE
                + lotWaferPrimaryKey.getFactory()
                + MIDDLE_LINE
                + lotWaferPrimaryKey.getFactorySite()
                + MIDDLE_LINE
                + TestArea.of(lotWaferPrimaryKey.getTestArea())
                + MIDDLE_LINE
                + lotWaferPrimaryKey.getDeviceId()
                + MIDDLE_LINE + lotWaferPrimaryKey.getLotId()
                + MIDDLE_LINE + lotWaferPrimaryKey.getSblotId();
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            LOGGER.info("lock处理中: {}", key);
            lock = false;
        } else {
            lock = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, finalFileProcessProgress, lockExpireTime, TimeUnit.SECONDS));
        }
        return lock;
    }

    /**
     * 更新主批次表
     */
    public void unlockUpdateLotWaferPrimaryData(LotWaferPrimaryKey lotWaferPrimaryKey) {
        String key = "UpdateLotWaferPrimaryDataLock:" +
                lotWaferPrimaryKey.getCustomer()
                + MIDDLE_LINE
                + lotWaferPrimaryKey.getSubCustomer()
                + MIDDLE_LINE
                + lotWaferPrimaryKey.getFactory()
                + MIDDLE_LINE
                + lotWaferPrimaryKey.getFactorySite()
                + MIDDLE_LINE
                + TestArea.of(lotWaferPrimaryKey.getTestArea())
                + MIDDLE_LINE
                + lotWaferPrimaryKey.getDeviceId()
                + MIDDLE_LINE + lotWaferPrimaryKey.getLotId()
                + MIDDLE_LINE + lotWaferPrimaryKey.getSblotId();

        redisTemplate.expire(key, unlockExpireTime, TimeUnit.SECONDS);
    }

    public void clearLockUpdateLotWaferPrimaryData() {
        clearKey("UpdateLotWaferPrimaryDataLock");
    }

}
