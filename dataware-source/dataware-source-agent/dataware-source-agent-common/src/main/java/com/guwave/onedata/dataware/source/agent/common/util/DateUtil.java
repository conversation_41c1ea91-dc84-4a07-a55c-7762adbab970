package com.guwave.onedata.dataware.source.agent.common.util;

import org.joda.time.DateTime;
import org.joda.time.Seconds;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.Date;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * 日期工具类
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2021-10-14 19:20:53
 */
public class DateUtil {

    private static final String YYYYMMDD = "yyyyMMdd";
    public static final String YYYYMMDDHH = "yyyyMMddHH";

    /**
     * 获取当前day
     *
     * @param ts 当前ts
     * @return String
     */
    public static String getDate(Long ts) {
        DateTime date = new DateTime(ts);
        return date.toString(YYYYMMDD);
    }


    /**
     * 计算dayHour
     *
     * @param ts 时间戳
     * @return String
     */
    public static String getDayHour(Long ts) {
        DateTime dateTime = new DateTime(ts);
        return dateTime.toString(YYYYMMDDHH);
    }

    /**
     * 标准format
     *
     * @param date   date
     * @param format format
     * @return Date
     */
    public static Date getDate(String date, String format) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(format);
        DateTime dateTime = dateTimeFormatter.parseDateTime(date);
        return dateTime.toDate();
    }

    /**
     * 计算两个日期相差seconds
     *
     * @param from start date
     * @param to   end date
     * @return diff
     */
    public static Long timeDiff(Date from, Date to) {
        DateTime start = new DateTime(from);
        DateTime end = new DateTime(to);
        return (long) Seconds.secondsBetween(start, end).getSeconds();
    }
}
