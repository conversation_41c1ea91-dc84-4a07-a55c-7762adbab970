include 'dataware-common'
include 'dataware-dao:dataware-dao-ck', 'dataware-dao:dataware-dao-mysql'
include 'dataware-parser'
include 'dataware-scheduler'
include 'dataware-collectx'
include 'dataware-source:dataware-source-common'
include 'dataware-source:dataware-source-agent:dataware-source-agent-common', 'dataware-source:dataware-source-agent:dataware-source-agent-cp', 'dataware-source:dataware-source-agent:dataware-source-agent-ft', 'dataware-source:dataware-source-agent:dataware-source-agent-wat', 'dataware-source:dataware-source-agent:dataware-source-agent-manual'
include 'dataware-dw:dataware-dw-common', 'dataware-dw:dataware-dw-manual', 'dataware-dw:dataware-dw-standalone', 'dataware-dw:dataware-dw-recovery', 'dataware-dw:dataware-dw-die', 'dataware-dw:dataware-dw-test-item'
include 'dataware-repair:dataware-repair-api', 'dataware-repair:dataware-repair-engine', 'dataware-repair:dataware-repair-common'
include 'dataware-sdk'
include 'dataware-quality'
include 'dataware-bridge:dataware-bridge-api'

rootProject.name = 'dataware'
