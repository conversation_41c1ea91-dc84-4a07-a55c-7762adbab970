package com.guwave.onedata.dataware.scheduler.model.key;

public class BiRequest {
    private String customer;
    private String subCustomer;
    private String factory;
    private String factorySite;
    private String testStage;
    private String waferId;
    private String deviceId;

    public String getSubCustomer() {
        return subCustomer;
    }

    public BiRequest setSubCustomer(String subCustomer) {
        this.subCustomer = subCustomer;
        return this;
    }

    public String getCustomer() {
        return customer;
    }

    public BiRequest setCustomer(String customer) {
        this.customer = customer;
        return this;
    }

    public String getFactory() {
        return factory;
    }

    public BiRequest setFactory(String factory) {
        this.factory = factory;
        return this;
    }

    public String getFactorySite() {
        return factorySite;
    }

    public BiRequest setFactorySite(String factorySite) {
        this.factorySite = factorySite;
        return this;
    }

    public String getTestStage() {
        return testStage;
    }

    public BiRequest setTestStage(String testStage) {
        this.testStage = testStage;
        return this;
    }

    public String getWaferId() {
        return waferId;
    }

    public BiRequest setWaferId(String waferId) {
        this.waferId = waferId;
        return this;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public BiRequest setDeviceId(String deviceId) {
        this.deviceId = deviceId;
        return this;
    }
}
