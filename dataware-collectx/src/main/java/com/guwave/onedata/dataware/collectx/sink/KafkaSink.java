package com.guwave.onedata.dataware.collectx.sink;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DatawareFailMessageRecord;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileLoadingLog;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.DatawareFailMessageRecordRepository;
import org.apache.tika.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.guwave.onedata.dataware.common.contant.Constant.SYSTEM;

/**
 * Copyright (C), 2021, guwave
 * <p>
 * KafkaSink
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2021-10-18 15:41:29
 */
@Component
public class KafkaSink {

    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaSink.class);

    private final KafkaTemplate<byte[], byte[]> kafkaTemplate;

    @Value("${spring.kafka.loadEndFlagTopic}")
    private String loadEndFlagTopic;

    private final DatawareFailMessageRecordRepository datawareFailMessageRecordRepository;

    public KafkaSink(KafkaTemplate<byte[], byte[]> kafkaTemplate, DatawareFailMessageRecordRepository datawareFailMessageRecordRepository) {
        this.kafkaTemplate = kafkaTemplate;
        this.datawareFailMessageRecordRepository = datawareFailMessageRecordRepository;

    }

    /**
     * 发送消息
     * @param topic topic
     * @param value value
     */
    public void send(String topic, String value) {
        byte[] message = value.getBytes(StandardCharsets.UTF_8);
        this.kafkaTemplate.send(topic, message).addCallback(success -> {
            // 消息发送到的topic
            assert success != null;
            // 消息发送到的分区
            int partition = success.getRecordMetadata().partition();
            // 消息在分区内的offset
            long offset = success.getRecordMetadata().offset();
            LOGGER.info("消息发送成功, topic: {}, partition: {}, offset: {}, message: {}", topic, partition, offset, value);
        }, fail -> {
            DatawareFailMessageRecord datawareFailMessageRecord = new DatawareFailMessageRecord()
                    .setProject(ProjectEnum.DATAWARE)
                    .setModule(ModuleEnum.DATAWARE_COLLECTX)
                    .setTopic(topic)
                    .setKey(null)
                    .setValue(value)
                    .setProcessStatus(ProcessStatus.FAIL)
                    .setDeleteFlag(0)
                    .setCreateUser(SYSTEM)
                    .setUpdateUser(SYSTEM)
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date());

            datawareFailMessageRecordRepository.save(datawareFailMessageRecord);
            LOGGER.error("发送消息失败, topic: {}, message: {}，将该条消息记入dw_dataware_fail_message_record", topic, value, fail);

        });
    }

    public void sendLoadEndFlagFailMessage(FileLoadingLog fileLoadingLog) {
        // 发送处理失败通知
        LOGGER.info("发送处理失败通知, fileName: {}", fileLoadingLog.getFileName());
        if (fileLoadingLog.getProcessStatus() == ProcessStatus.FAIL) {
            List<Long> cleanupTaskIds = StringUtils.isBlank(fileLoadingLog.getCleanupTaskIds()) ? null : Stream.of(fileLoadingLog.getCleanupTaskIds().split(Constant.COMMA)).flatMap(t -> Stream.of(t.split(Constant.COMMA)).map(s -> Long.parseLong(s.trim())))
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
            CalculateEndFlag calculateEndFlagMessage = new CalculateEndFlag()
                    .setCleanupTaskIds(cleanupTaskIds)
                    .setPlatform(Platform.CK)
                    .setExceptionType(fileLoadingLog.getExceptionType())
                    .setExceptionMessage(fileLoadingLog.getExceptionMessage())
                    .setErrorMessage(fileLoadingLog.getFileName() + Constant.ENTER + fileLoadingLog.getErrorMessage())
                    .setProcessStatus(ProcessStatus.FAIL)
                    .setTs(System.currentTimeMillis());
            this.send(loadEndFlagTopic, JSON.toJSONString(calculateEndFlagMessage));
        }
    }

}
