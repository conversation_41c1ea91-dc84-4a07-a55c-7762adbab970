package com.guwave.onedata.dataware.scheduler.request;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.guwave.onedata.dataware.common.contant.RequestEncodedType;
import com.guwave.onedata.dataware.common.contant.RequestType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

public class RequestParmBuilder {
    private static final Logger LOGGER = LoggerFactory.getLogger(RequestParmBuilder.class);

    private String url;

    private RequestType requestType;

    private RequestEncodedType requestEncodedType;

    private String requestParmaterMapping;

    private MultiValueMap<String, String> headerMap;

    private Integer pageFlag;

    private String pageKey;

    private String pageSizeKey;

    private HttpEntity request;

    private Map<String, Object> paramMap;

    private String JSON_ELEM = "\"{key}\":\"{value}\"";
    private String FORM_ELEM = "{key}={value}";

    public RequestParmBuilder(String url, RequestType requestType, RequestEncodedType requestEncodedType, String requestParmaterMapping, MultiValueMap<String, String> headerMap, Integer pageFlag, String pageKey, String pageSizeKey, Map<String, Object> paramMap) {
        this.url = url;
        this.requestType = requestType;
        this.requestEncodedType = requestEncodedType;
        this.requestParmaterMapping = requestParmaterMapping;
        this.headerMap = headerMap;
        this.pageFlag = pageFlag;
        this.pageKey = pageKey;
        this.pageSizeKey = pageSizeKey;
        this.paramMap = paramMap;
        init();
    }

    /**
     * 初始化请求参数
     */
    private void init() {
        if (requestType== null || requestType.getType() == null) {
            throw new RuntimeException("请求类型不合法");
        }
        if (requestEncodedType == null) {
            LOGGER.info("请求编码类型为空，使用默认编码类型application/json");
            requestEncodedType = RequestEncodedType.JSON_ENCODED;
        }
        if (pageFlag != null && pageFlag == 1 && (pageKey == null || pageSizeKey == null)) {
            throw new RuntimeException("分页参数不完整");
        }
        HttpHeaders headers = new HttpHeaders();
        headers.putAll(headerMap);
        switch (requestType) {
            case GET:
                if (requestParmaterMapping != null || pageFlag == 1) {
                    this.url = url + QUESTION_MARK + splicingFormParameters();
                }
                request = new HttpEntity(new HttpHeaders());
                break;
            case POST:

                headers.add("Content-Type", requestEncodedType.getType());
                switch (requestEncodedType) {
                    case URL_ENCODED:
                        request = new HttpEntity(splicingFormParameters(), headers);
                        break;
                    case JSON_ENCODED:
                        request = new HttpEntity(splicingJSONParameters(), headers);
                        break;
                    case XML_ENCODED:
                        try {
                            request = new HttpEntity(splicingXmlParameters(), headers);
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException("xml参数拼接异常", e);
                        }
                        break;
                    default:
                        request = new HttpEntity(splicingJSONParameters(), headers);
                        break;
                }
                break;
        }
    }

    public String getUrl() {
        return url;
    }

    public HttpEntity getRequest() {
        return request;
    }

    /**
     * 拼接XML参数
     *
     * @return xml参数
     * @throws JsonProcessingException 解析异常
     */
    private String splicingXmlParameters() throws JsonProcessingException {
        String jsonParameters = splicingJSONParameters();
        ObjectMapper jsonMapper = new ObjectMapper();
        XmlMapper xmlMapper = new XmlMapper();
        return xmlMapper.writeValueAsString(jsonMapper.readValue(jsonParameters, Object.class));
    }

    /**
     * 拼接JSON参数
     *
     * @return JSON参数
     */
    private String splicingJSONParameters() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(LEFT_CURLY_BRACE);
        if (StringUtils.isNotBlank(requestParmaterMapping)) {
            String[] mappings = requestParmaterMapping.split(COMMA);
            for (int i = 0; i < mappings.length; i++) {
                String[] mapping = mappings[i].split(COLON);
                if (mapping.length == 2) {
                    stringBuilder.append(JSON_ELEM.replace("{key}", mapping[0]).replace("{value}", LEFT_CURLY_BRACE + mapping[1] + RIGHT_CURLY_BRACE));
                    if (i < mappings.length - 1) {
                        stringBuilder.append(COMMA);
                    }
                }
            }
        }
        if (pageFlag == 1) {
            if (StringUtils.isNotBlank(requestParmaterMapping)) {
                stringBuilder.append(COMMA);
            }
            stringBuilder.append("\"").append(pageKey).append("\"").append(COLON).append("\"" + PAGE + "\"");
            stringBuilder.append(COMMA).append("\"").append(pageSizeKey).append("\"").append(COLON).append("\"" + PAGE_SIZE + "\"");
        }
        stringBuilder.append(RIGHT_CURLY_BRACE);
        return insertParm(stringBuilder.toString());
    }

    /**
     * 拼接表单参数
     *
     * @return 表单参数
     */
    private String splicingFormParameters() {
        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(requestParmaterMapping)) {
            String[] mappings = requestParmaterMapping.split(COMMA);
            for (int i = 0; i < mappings.length; i++) {
                String[] mapping = mappings[i].split(COLON);
                if (mapping.length == 2) {
                    stringBuilder.append(FORM_ELEM.replace("{key}", mapping[0]).replace("{value}", LEFT_CURLY_BRACE + mapping[1] + RIGHT_CURLY_BRACE));
                    if (i < mappings.length - 1) {
                        stringBuilder.append(BITWISE_AND);
                    }
                }
            }
        }
        if (pageFlag == 1) {
            if (StringUtils.isNotBlank(requestParmaterMapping)) {
                stringBuilder.append(BITWISE_AND);
            }
            stringBuilder.append(pageKey).append(EQUAL_SIGN).append(PAGE);
            stringBuilder.append(BITWISE_AND).append(pageSizeKey).append(EQUAL_SIGN).append(PAGE_SIZE);
        }
        return insertParm(stringBuilder.toString());
    }

    private String insertParm(String elem) {
        List<String> keys = new ArrayList<>(paramMap.keySet());
        for (String key : keys) {
            if (paramMap.get(key) != null) {
                elem = elem.replace(key, paramMap.get(key).toString());
            }
        }

        return elem;
    }

    public RequestType getRequestType() {
        return requestType;
    }

}
