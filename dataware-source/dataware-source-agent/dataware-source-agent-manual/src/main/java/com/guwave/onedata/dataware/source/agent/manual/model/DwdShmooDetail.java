package com.guwave.onedata.dataware.source.agent.manual.model;

import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.contant.UploadType;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;

public class DwdShmooDetail {
    /**
     * dwd_shmoo_detail_cluster.FILE_ID
     */
    private Long fileId;

    /**
     * dwd_shmoo_detail_cluster.FILE_NAME
     */
    private String fileName;

    /**
     * dwd_shmoo_detail_cluster.CUSTOMER
     */
    private String customer;

    /**
     * dwd_shmoo_detail_cluster.UPLOAD_TYPE
     */
    private UploadType uploadType;

    /**
     * dwd_shmoo_detail_cluster.ECID
     */
    private String ecid = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.TEST_PROGRAM
     */
    private String testProgram = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.TESTER_NAME
     */
    private String testerName = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.TEST_AREA
     */
    private TestArea testArea;

    /**
     * dwd_shmoo_detail_cluster.DEVICE_ID
     */
    private String deviceId = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.LOT_ID
     */
    private String lotId = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.PROBER_HANDLER_ID
     */
    private String proberHandlerId = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.PROBER_HANDLER_TYP
     */
    private String proberHandlerTyp = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.TEST_ITEM
     */
    private String testItem = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.PART_ID
     */
    private String partId = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.SHMOO_NAME
     */
    private String shmooName = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.SITE_NUMBER
     */
    private Integer siteNumber;

    /**
     * dwd_shmoo_detail_cluster.TEST_SUITE
     */
    private String testSuite = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.TEST_PATTERN
     */
    private String testPattern = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.SPECIFICATION
     */
    private String specification = EMPTY;

     /**
     * dwd_shmoo_detail_cluster.TEST_ORDER
     */

    private Long TestOrder;

    /**
     * dwd_shmoo_detail_cluster.X_LABEL
     */
    private String xLabel = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.Y_LABEL
     */
    private String yLabel = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.XRESOURCE_TYPE
     */
    private String xresourceType = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.YRESOURCE_TYPE
     */
    private String yresourceType = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.X_START
     */
    private BigDecimal xStart;

    /**
     * dwd_shmoo_detail_cluster.X_END
     */
    private BigDecimal xEnd;

    /**
     * dwd_shmoo_detail_cluster.Y_START
     */
    private BigDecimal yStart;

    /**
     * dwd_shmoo_detail_cluster.Y_END
     */
    private BigDecimal yEnd;

    /**
     * dwd_shmoo_detail_cluster.X_STEP
     */
    private Integer xStep;

    /**
     * dwd_shmoo_detail_cluster.Y_STEP
     */
    private Integer yStep;

    /**
     * dwd_shmoo_detail_cluster.SHMOO_JUDGE_RESULT
     */
    private String shmooJudgeResult = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.SUBLOOP
     */
    private String subloop = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.OPERATING_SEQUENC
     */
    private String operatingSequenc = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.TESTMETHOD
     */
    private String testmethod = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.AXISNUM
     */
    private Integer axisnum;

    /**
     * dwd_shmoo_detail_cluster.ERRCNTFLAG
     */
    private Integer errcntflag;

    /**
     * dwd_shmoo_detail_cluster.X_VALUE
     */
    private BigDecimal xValue;

    /**
     * dwd_shmoo_detail_cluster.Y_VALUE
     */
    private BigDecimal yValue;

    /**
     * dwd_shmoo_detail_cluster.SHMOO_RESULT
     */
    private Short shmooResult;

    /**
     * dwd_shmoo_detail_cluster.SHMOO_RESULT_NAME
     */
    private String shmooResultName;

    /**
     * dwd_shmoo_detail_cluster.IS_PRIMARY
     */
    private Short isPrimary;

    /**
     * dwd_shmoo_detail_cluster.STDF_PART_TXT
     */
    private String stdfPartTxt = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.CHAR_START_EVENTID
     */
    private Integer charStartEventid;

    /**
     * dwd_shmoo_detail_cluster.PROCESS
     */
    private String process = EMPTY;


    /**
     * dwd_shmoo_detail_cluster.X_UNIT
     */
    private String xUnit = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.Y_UNIT
     */
    private String yUnit = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.C_PART_ID
     */
    private Long cPartId;

    /**
     * dwd_shmoo_detail_cluster.START_TIME
     */
    private Long startTime;

    /**
     * dwd_shmoo_detail_cluster.END_TIME
     */
    private Long endTime;

    /**
     * dwd_shmoo_detail_cluster.PRIMARY_X
     */
    private BigDecimal primaryX;

    /**
     * dwd_shmoo_detail_cluster.PRIMARY_Y
     */
    private BigDecimal primaryY;

    /**
     * dwd_shmoo_detail_cluster.IS_HOLE_FLAG
     */
    private Short isHoleFlag;

    /**
     * dwd_shmoo_detail_cluster.TEST_TEMPERATURE
     */
    private String testTemperature = EMPTY;

    /**
     * dwd_shmoo_detail_cluster.CREATE_TIME
     */
    private Long createTime;

    /**
     * dwd_shmoo_detail_cluster.CREATE_USER
     */
    private String createUser;

    private Long uploadTime;

    /**
     * dwd_shmoo_detail_cluster.LOT_BUCKET
     */
    private String lotBucket;

    /**
     * DATA_TYPE
     */
    private String dataType = EMPTY;
    /**
     * LOT_TYPE
     */
    private LotType lotType = LotType.EMPTY;
    /**
     * WAFER_ID
     */
    private String waferId = EMPTY;
    /**
     * EXTRA_INFO
     */
    private Map<String, String>  extraInfo = new HashMap<>();

    public Long getFileId() {
        return fileId;
    }

    public DwdShmooDetail setFileId(Long fileId) {
        this.fileId = fileId;
        return this;
    }

    public String getFileName() {
        return fileName;
    }

    public DwdShmooDetail setFileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public String getCustomer() {
        return customer;
    }

    public DwdShmooDetail setCustomer(String customer) {
        this.customer = customer;
        return this;
    }

    public UploadType getUploadType() {
        return uploadType;
    }

    public DwdShmooDetail setUploadType(UploadType uploadType) {
        this.uploadType = uploadType;
        return this;
    }

    public String getEcid() {
        return ecid;
    }

    public DwdShmooDetail setEcid(String ecid) {
        this.ecid = ecid;
        return this;
    }

    public String getTestProgram() {
        return testProgram;
    }

    public DwdShmooDetail setTestProgram(String testProgram) {
        this.testProgram = testProgram;
        return this;
    }

    public String getTesterName() {
        return testerName;
    }

    public DwdShmooDetail setTesterName(String testerName) {
        this.testerName = testerName;
        return this;
    }

    public TestArea getTestArea() {
        return testArea;
    }

    public DwdShmooDetail setTestArea(TestArea testArea) {
        this.testArea = testArea;
        return this;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public DwdShmooDetail setDeviceId(String deviceId) {
        this.deviceId = deviceId;
        return this;
    }

    public String getLotId() {
        return lotId;
    }

    public DwdShmooDetail setLotId(String lotId) {
        this.lotId = lotId;
        return this;
    }

    public String getProberHandlerId() {
        return proberHandlerId;
    }

    public DwdShmooDetail setProberHandlerId(String proberHandlerId) {
        this.proberHandlerId = proberHandlerId;
        return this;
    }

    public String getProberHandlerTyp() {
        return proberHandlerTyp;
    }

    public DwdShmooDetail setProberHandlerTyp(String proberHandlerTyp) {
        this.proberHandlerTyp = proberHandlerTyp;
        return this;
    }

    public String getTestItem() {
        return testItem;
    }

    public DwdShmooDetail setTestItem(String testItem) {
        this.testItem = testItem;
        return this;
    }

    public String getPartId() {
        return partId;
    }

    public DwdShmooDetail setPartId(String partId) {
        this.partId = partId;
        return this;
    }

    public String getShmooName() {
        return shmooName;
    }

    public DwdShmooDetail setShmooName(String shmooName) {
        this.shmooName = shmooName;
        return this;
    }

    public Integer getSiteNumber() {
        return siteNumber;
    }

    public DwdShmooDetail setSiteNumber(Integer siteNumber) {
        this.siteNumber = siteNumber;
        return this;
    }

    public String getTestSuite() {
        return testSuite;
    }

    public DwdShmooDetail setTestSuite(String testSuite) {
        this.testSuite = testSuite;
        return this;
    }

    public String getTestPattern() {
        return testPattern;
    }

    public DwdShmooDetail setTestPattern(String testPattern) {
        this.testPattern = testPattern;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public DwdShmooDetail setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public Long getTestOrder() {
        return TestOrder;
    }

    public DwdShmooDetail setTestOrder(Long testOrder) {
        TestOrder = testOrder;
        return this;
    }

    public String getxLabel() {
        return xLabel;
    }

    public DwdShmooDetail setxLabel(String xLabel) {
        this.xLabel = xLabel;
        return this;
    }

    public String getyLabel() {
        return yLabel;
    }

    public DwdShmooDetail setyLabel(String yLabel) {
        this.yLabel = yLabel;
        return this;
    }

    public String getXresourceType() {
        return xresourceType;
    }

    public DwdShmooDetail setXresourceType(String xresourceType) {
        this.xresourceType = xresourceType;
        return this;
    }

    public String getYresourceType() {
        return yresourceType;
    }

    public DwdShmooDetail setYresourceType(String yresourceType) {
        this.yresourceType = yresourceType;
        return this;
    }

    public BigDecimal getxStart() {
        return xStart;
    }

    public DwdShmooDetail setxStart(BigDecimal xStart) {
        this.xStart = xStart;
        return this;
    }

    public BigDecimal getxEnd() {
        return xEnd;
    }

    public DwdShmooDetail setxEnd(BigDecimal xEnd) {
        this.xEnd = xEnd;
        return this;
    }

    public BigDecimal getyStart() {
        return yStart;
    }

    public DwdShmooDetail setyStart(BigDecimal yStart) {
        this.yStart = yStart;
        return this;
    }

    public BigDecimal getyEnd() {
        return yEnd;
    }

    public DwdShmooDetail setyEnd(BigDecimal yEnd) {
        this.yEnd = yEnd;
        return this;
    }

    public Integer getxStep() {
        return xStep;
    }

    public DwdShmooDetail setxStep(Integer xStep) {
        this.xStep = xStep;
        return this;
    }

    public Integer getyStep() {
        return yStep;
    }

    public DwdShmooDetail setyStep(Integer yStep) {
        this.yStep = yStep;
        return this;
    }

    public String getShmooJudgeResult() {
        return shmooJudgeResult;
    }

    public DwdShmooDetail setShmooJudgeResult(String shmooJudgeResult) {
        this.shmooJudgeResult = shmooJudgeResult;
        return this;
    }

    public String getSubloop() {
        return subloop;
    }

    public DwdShmooDetail setSubloop(String subloop) {
        this.subloop = subloop;
        return this;
    }

    public String getOperatingSequenc() {
        return operatingSequenc;
    }

    public DwdShmooDetail setOperatingSequenc(String operatingSequenc) {
        this.operatingSequenc = operatingSequenc;
        return this;
    }

    public String getTestmethod() {
        return testmethod;
    }

    public DwdShmooDetail setTestmethod(String testmethod) {
        this.testmethod = testmethod;
        return this;
    }

    public Integer getAxisnum() {
        return axisnum;
    }

    public DwdShmooDetail setAxisnum(Integer axisnum) {
        this.axisnum = axisnum;
        return this;
    }

    public Integer getErrcntflag() {
        return errcntflag;
    }

    public DwdShmooDetail setErrcntflag(Integer errcntflag) {
        this.errcntflag = errcntflag;
        return this;
    }

    public BigDecimal getxValue() {
        return xValue;
    }

    public DwdShmooDetail setxValue(BigDecimal xValue) {
        this.xValue = xValue;
        return this;
    }

    public BigDecimal getyValue() {
        return yValue;
    }

    public DwdShmooDetail setyValue(BigDecimal yValue) {
        this.yValue = yValue;
        return this;
    }

    public Short getShmooResult() {
        return shmooResult;
    }

    public DwdShmooDetail setShmooResult(Short shmooResult) {
        this.shmooResult = shmooResult;
        return this;
    }

    public String getShmooResultName() {
        return shmooResultName;
    }

    public DwdShmooDetail setShmooResultName(String shmooResultName) {
        this.shmooResultName = shmooResultName;
        return this;
    }

    public Short getIsPrimary() {
        return isPrimary;
    }

    public DwdShmooDetail setIsPrimary(Short isPrimary) {
        this.isPrimary = isPrimary;
        return this;
    }

    public String getStdfPartTxt() {
        return stdfPartTxt;
    }

    public DwdShmooDetail setStdfPartTxt(String stdfPartTxt) {
        this.stdfPartTxt = stdfPartTxt;
        return this;
    }

    public Integer getCharStartEventid() {
        return charStartEventid;
    }

    public DwdShmooDetail setCharStartEventid(Integer charStartEventid) {
        this.charStartEventid = charStartEventid;
        return this;
    }

    public String getProcess() {
        return process;
    }

    public DwdShmooDetail setProcess(String process) {
        this.process = process;
        return this;
    }

    public String getxUnit() {
        return xUnit;
    }

    public DwdShmooDetail setxUnit(String xUnit) {
        this.xUnit = xUnit;
        return this;
    }

    public String getyUnit() {
        return yUnit;
    }

    public DwdShmooDetail setyUnit(String yUnit) {
        this.yUnit = yUnit;
        return this;
    }

    public Long getcPartId() {
        return cPartId;
    }

    public DwdShmooDetail setcPartId(Long cPartId) {
        this.cPartId = cPartId;
        return this;
    }

    public DwdShmooDetail setStartTime(Long startTime) {
        this.startTime = startTime;
        return this;
    }

    public Long getStartTime() {
        return startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public DwdShmooDetail setEndTime(Long endTime) {
        this.endTime = endTime;
        return this;
    }

    public BigDecimal getPrimaryX() {
        return primaryX;
    }

    public DwdShmooDetail setPrimaryX(BigDecimal primaryX) {
        this.primaryX = primaryX;
        return this;
    }

    public BigDecimal getPrimaryY() {
        return primaryY;
    }

    public DwdShmooDetail setPrimaryY(BigDecimal primaryY) {
        this.primaryY = primaryY;
        return this;
    }

    public Short getIsHoleFlag() {
        return isHoleFlag;
    }

    public DwdShmooDetail setIsHoleFlag(Short isHoleFlag) {
        this.isHoleFlag = isHoleFlag;
        return this;
    }

    public String getTestTemperature() {
        return testTemperature;
    }

    public DwdShmooDetail setTestTemperature(String testTemperature) {
        this.testTemperature = testTemperature;
        return this;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public DwdShmooDetail setCreateTime(Long createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public DwdShmooDetail setCreateUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public Long getUploadTime() {
        return uploadTime;
    }

    public DwdShmooDetail setUploadTime(Long uploadTime) {
        this.uploadTime = uploadTime;
        return this;
    }

    public String getLotBucket() {
        return lotBucket;
    }

    public DwdShmooDetail setLotBucket(String lotBucket) {
        this.lotBucket = lotBucket;
        return this;
    }

    public String getDataType() {
        return dataType;
    }

    public DwdShmooDetail setDataType(String dataType) {
        this.dataType = dataType;
        return this;
    }

    public LotType getLotType() {
        return lotType;
    }

    public DwdShmooDetail setLotType(LotType lotType) {
        this.lotType = lotType;
        return this;
    }

    public String getWaferId() {
        return waferId;
    }

    public DwdShmooDetail setWaferId(String waferId) {
        this.waferId = waferId;
        return this;
    }

    public Map<String, String> getExtraInfo() {
        return extraInfo;
    }

    public DwdShmooDetail setExtraInfo(Map<String, String> extraInfo) {
        this.extraInfo = extraInfo;
        return this;
    }
}