package com.guwave.onedata.dataware.collectx.manager.thread;

import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpSubCustomerRule;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;


public class SftpSubCustomerThreadLocalUtil {

    private static final ThreadLocal<List<SftpSubCustomerRule>> SUBCUSTOMER_THREAD_LOCAL = new ThreadLocal<>();

    public static List<SftpSubCustomerRule> getSubCustomers(){
        return SUBCUSTOMER_THREAD_LOCAL.get();
    }

    public static void setSubCustomers(List<SftpSubCustomerRule> subCustomerData) {
        if(CollectionUtils.isNotEmpty(subCustomerData)){
            SUBCUSTOMER_THREAD_LOCAL.set(subCustomerData);
        }
        else{
           SUBCUSTOMER_THREAD_LOCAL.set(new ArrayList<>());
        }
    }

    public static void clear(){SUBCUSTOMER_THREAD_LOCAL.remove();}
}
