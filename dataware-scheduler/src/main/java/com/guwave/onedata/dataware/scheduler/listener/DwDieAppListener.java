package com.guwave.onedata.dataware.scheduler.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.key.LotWaferPrimaryInputCntKey;
import com.guwave.onedata.dataware.common.model.key.LotWaferPrimaryKey;
import com.guwave.onedata.dataware.common.model.key.LotWaferPrimaryWaferLotIdKey;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import com.guwave.onedata.dataware.common.model.message.RuleCalculateEndFlagMessage;
import com.guwave.onedata.dataware.common.model.message.YmsCalculateEndFlagMessage;
import com.guwave.onedata.dataware.common.util.WaferUtil;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import com.guwave.onedata.dataware.dao.mysql.manager.FileWarehousingRecordManager;
import com.guwave.onedata.dataware.dao.mysql.manager.LotBatchSummaryManager;
import com.guwave.onedata.dataware.dao.mysql.manager.LotWaferPrimaryDataManager;
import com.guwave.onedata.dataware.dao.mysql.manager.WarehousingTaskRecordManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.*;
import com.guwave.onedata.dataware.repair.common.service.DataRepairService;
import com.guwave.onedata.dataware.scheduler.model.value.LotWaferData;
import com.guwave.onedata.dataware.scheduler.provider.KafKaProvider;
import com.guwave.onedata.dataware.scheduler.repository.DieDetailRepository;
import com.guwave.onedata.dataware.scheduler.repository.LotWaferRepository;
import com.guwave.onedata.dataware.scheduler.service.BiNoticeService;
import com.guwave.onedata.dataware.scheduler.service.RedisService;
import com.guwave.onedata.dataware.scheduler.util.ComputeUtil;
import com.guwave.onedata.next.compute.common.message.ComputeResultMessage;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.time.Duration;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;
import static com.guwave.onedata.dataware.common.contant.Constant.SYSTEM;

@Component
public class DwDieAppListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(DwDieAppListener.class);

    String RULE_EXPRESSION = "{RULE_EXPRESSION}";

    private static final List<TestArea> SUPPORT_CP_TEST_AREA_LIST = TestArea.getCPList();

    private final static Set<FileCategory> REQUEST_BI_FILE_CATEGORY_SET = Sets.newHashSet(FileCategory.STDF, FileCategory.RAW_DATA);
    private final static Set<FileCategory> UPDATE_LOT_BATCH_SUMMARY_FILE_CATEGORY_SET = Sets.newHashSet(FileCategory.STDF, FileCategory.RAW_DATA);
    private final static Set<FileCategory> SEND_RULE_CALCULATE_END_FLAG_FILE_CATEGORY_SET = Sets.newHashSet(FileCategory.STDF, FileCategory.RAW_DATA);

    @Value("${spring.kafka.loadEndFlagTopic}")
    private String loadEndFlagTopic;
    @Value("${spring.kafka.ruleCalculateEndFlagTopic}")
    private String ruleCalculateEndFlagTopic;
    @Value("${spring.data.clickhouse.dwdDbName}")
    private String dwdDbName;
    @Value("${spring.data.clickhouse.dwsDbName}")
    private String dwsDbName;

    @Autowired
    private LotStockingDetailRepository lotStockingDetailRepository;
    @Autowired
    private LayerCalculatePoolRepository layerCalculatePoolRepository;
    @Autowired
    private LotWaferCalStatusRepository lotWaferCalStatusRepository;
    @Autowired
    private LotWaferCalRecordRepository lotWaferCalRecordRepository;
    @Autowired
    private FileLoadingLogRepository fileLoadingLogRepository;
    @Autowired
    private RepairRecalculateRecordRepository repairRecalculateRecordRepository;
    @Autowired
    private LotWaferWarehousingRecordRepository lotWaferWarehousingRecordRepository;
    @Autowired
    private LotMetaDataDetailRepository lotMetaDataDetailRepository;
    @Autowired
    private TestItemSwitchRepository testItemSwitchRepository;
    @Autowired
    private FileWarehousingRecordManager fileWarehousingRecordManager;
    @Autowired
    private WarehousingTaskRecordManager warehousingTaskRecordManager;
    @Autowired
    private LotBatchSummaryManager lotBatchSummaryManager;
    @Autowired
    private RedisService redisService;
    @Autowired
    private DataRepairService dataRepairService;
    @Autowired
    private BiNoticeService biNoticeService;
    @Autowired
    private KafKaProvider kafKaProvider;
    @Autowired
    private LotWaferPrimaryDataManager lotWaferPrimaryDataManager;
    @Autowired
    private DieDetailRepository dieDetailRepository;
    @Autowired
    private LotWaferRepository lotWaferRepository;

    public void stateChanged(LayerCalculatePool task, ComputeResultMessage computeResultMessage) {
        try {
            ProcessStatus processStatus = ComputeUtil.convertProcessStatus(computeResultMessage.getProcessStatus());
            Date updateTime = new Date();
            task
                    .setProcessStatus(processStatus)
                    .setUpdateTime(updateTime)
                    .setExceptionType(ComputeUtil.convertExceptionType(computeResultMessage.getExceptionType()))
                    .setExceptionMessage(computeResultMessage.getExceptionMessage())
                    .setErrorMessage(processStatus == ProcessStatus.FAIL ? computeResultMessage.getExceptionMessage() + Constant.ENTER + computeResultMessage.getErrorMessage() : null)
                    .setAppId(computeResultMessage.getAppId())
                    .setNumExecutors(computeResultMessage.getNumExecutors())
                    .setExecutorCores(computeResultMessage.getExecutorCores())
                    .setExecutorMemory(computeResultMessage.getExecutorMemory())
                    .setDriverMemory(computeResultMessage.getDriverMemory())
                    .setParallelism(computeResultMessage.getParallelism())
                    .setTestItemDetailResultPartition(computeResultMessage.getHdfsResultPartition())
                    .setExtraConf(computeResultMessage.getExtraConf())
                    .setVersion(computeResultMessage.getVersion())
                    .setSinkType(task.getSinkType() == null ? ComputeUtil.convertCkSinkType(computeResultMessage.getSinkType()) : task.getSinkType())
                    .setFailCnt(task.getFailCnt() == null ? computeResultMessage.getFailCnt() : task.getFailCnt() + computeResultMessage.getFailCnt());

            List<LotStockingDetail> lotStockingDetails;
            List<String> fileNames;
            List<String> sblotIdList;
            LotWaferCalStatus lotWaferCalStatus;
            RLock updateDwTaskTableLock = redisService.getUpdateDwTaskTableLock(task.getFileCategory(), task.getCustomer(), task.getTestArea(), task.getFactory(), task.getDeviceId(), task.getLotType(), task.getTestStage(), task.getLotId(), task.getWaferNo());
            updateDwTaskTableLock.lock();
            try {
                // 更新lotWaferCalStatus
                lotWaferCalStatus = updateLotWaferCalStatus(task);

                // 查询tesItemSwitch，更新calTestItemFlag
                updateCalTestItemFlagByTestItemSwitch(lotWaferCalStatus, task);

                // 查询dw_lot_meta_data_detail,如果存在同维度的create/processing任务，则更新更新calTestItemFlag为0
                updateCalTestItemFlagByLotMetaDataDetail(lotWaferCalStatus, task);

                // 更新lotWaferCalRecord
                updateLotWaferCalRecord(task, lotWaferCalStatus);
                lotStockingDetails = lotStockingDetailRepository.findAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoAndFileCategory(
                        task.getCustomer(),
                        task.getTestArea(),
                        task.getFactory(),
                        task.getFactorySite(),
                        task.getDeviceId(),
                        task.getLotType(),
                        task.getTestStage(),
                        task.getLotId(),
                        task.getWaferNo(),
                        task.getFileCategory()
                );
                fileNames = lotStockingDetails.stream().map(LotStockingDetail::getFileName).collect(Collectors.toList());
                sblotIdList = TestArea.getCPList().contains(task.getTestArea()) ? Collections.emptyList() : lotStockingDetails.stream()
                        .map(LotStockingDetail::getSblotId)
                        .filter(StringUtils::isNotBlank)
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList());
                // 记录fileLoadingLog 和 fileWarehousingRecord
                updateFileLoadingLogs(task, fileNames);
                // 更新WarehousingTaskRecord
                warehousingTaskRecordManager.updateWarehousingTaskRecord(task);
                // 更新LotWaferWarehousingRecord
                updateLotWaferWarehousingRecord(task, fileNames);
                // 更新lotStockingDetail
                updateLotStockingDetail(lotStockingDetails, task);
                // request bi
                requestBi(task, lotStockingDetails);
                // 更新LotBatchSummary表
                updateLotBatchSummary(task, fileNames);
                // 更新TestRawDataLotWaferPrimaryData
                try {
                    updateTestRawDataLotWaferPrimaryData(lotStockingDetails);
                } catch (Exception ex) {
                    LOGGER.info("更新TestRawDataLotWaferPrimaryData失败", ex);
                }
            } finally {
                updateDwTaskTableLock.unlock();
            }

            // 更新LayerCalculatePool
            List<LayerCalculatePool> needSaveLayerCalculatePools = new ArrayList<>();
            needSaveLayerCalculatePools.add(task);
            LayerCalculatePool lastTask = task;
            if (task.getProcessStatus() == ProcessStatus.SUCCESS) {
                needSaveLayerCalculatePools.add(generateNextLayerCalculatePool(task, DwLayer.DWS, DwLayer.ADS, updateTime));
                lastTask = generateNextLayerCalculatePool(task, DwLayer.ADS, DwLayer.NONE, updateTime);
                needSaveLayerCalculatePools.add(lastTask);
            }
            layerCalculatePoolRepository.saveAll(needSaveLayerCalculatePools);

            // die任务结束发送消息
            sendLoadEndFlag(computeResultMessage, processStatus, lastTask, lotWaferCalStatus);

            // 发送rule消息
            sendRuleCalculateEndFlag(lastTask, lotWaferCalStatus, sblotIdList);
        } finally {
            // 任务结束解锁die任务
            redisService.unlockDwDieTask(
                    task.getFileCategory() == FileCategory.BIT_MEM ? Lists.newArrayList(FileCategory.BIT_MEM) : Lists.newArrayList(FileCategory.OTHER),
                    task.getCustomer(), task.getTestArea(), task.getFactory(), task.getDeviceId(), task.getLotType(), task.getTestStage(), task.getLotId(), task.getWaferNo(),
                    RedisService.lockDwDieValue
            );
        }
    }

    private void updateCalTestItemFlagByLotMetaDataDetail(LotWaferCalStatus lotWaferCalStatus, LayerCalculatePool task) {
        if (Objects.equals(task.getCalculateDwTestItem(), 1)) {
            Long unfinishedFile =  lotMetaDataDetailRepository.countByCustomerAndTestAreaAndFactoryAndLotIdAndWaferNoAndDeviceIdAndTestStageAndLotTypeAndFileCategoryAndProcessStatusIn(
                    lotWaferCalStatus.getCustomer(),
                    lotWaferCalStatus.getTestArea(),
                    lotWaferCalStatus.getFactory(),
                    lotWaferCalStatus.getLotId(),
                    lotWaferCalStatus.getWaferNo(),
                    lotWaferCalStatus.getDeviceId(),
                    lotWaferCalStatus.getTestStage(),
                    lotWaferCalStatus.getLotType(),
                    lotWaferCalStatus.getFileCategory(),
                    Arrays.asList(ProcessStatus.CREATE, ProcessStatus.PROCESSING)
            );
            if (unfinishedFile > 0) {
                LOGGER.info("lotId:{}, waferNo:{}, testStage:{}存在未完成的4100任务,更新calTestItemFlag为0,不跑测项任务", lotWaferCalStatus.getLotId(), lotWaferCalStatus.getWaferNo(), lotWaferCalStatus.getTestStage());
                task.setCalculateDwTestItem(0);

                lotWaferCalStatus
                        .setCalculateDwTestItem(0)
                        .setCalculateYmsTestItem(0)
                        .setUpdateTime(new Date());
                lotWaferCalStatusRepository.save(lotWaferCalStatus);
            }
        }
    }

    private void sendLoadEndFlag(ComputeResultMessage computeResultMessage, ProcessStatus processStatus, LayerCalculatePool lastTask, LotWaferCalStatus lotWaferCalStatus) {
        CalculateEndFlag calculateEndFlag = buildCalculateEndFlag(lastTask, computeResultMessage, lotWaferCalStatus, Platform.GDP);
        LOGGER.info("任务处理 {},{}", processStatus, JSON.toJSONString(calculateEndFlag));
        kafKaProvider.sendKafka(loadEndFlagTopic, JSON.toJSONString(calculateEndFlag));
        CalculateEndFlag ckCalculateEndFlag = buildCalculateEndFlag(lastTask, computeResultMessage, lotWaferCalStatus, Platform.CK);
        kafKaProvider.sendKafka(loadEndFlagTopic, JSON.toJSONString(ckCalculateEndFlag));
    }


    private void sendRuleCalculateEndFlag(LayerCalculatePool task, LotWaferCalStatus lotWaferCalStatus, List<String> sblotIdList) {
        if (!SEND_RULE_CALCULATE_END_FLAG_FILE_CATEGORY_SET.contains(task.getFileCategory())) {
            return;
        }
        RuleCalculateEndFlagMessage ruleCalculateEndFlagMessage = new RuleCalculateEndFlagMessage()
                .setCustomer(task.getCustomer())
                .setSubCustomer(task.getSubCustomer())
                .setTestArea(task.getTestArea())
                .setFactory(task.getFactory())
                .setFactorySite(task.getFactorySite())
                .setDeviceId(task.getDeviceId())
                .setLotId(task.getLotId())
                .setWaferNo(task.getWaferNo())
                .setSblotId(CollectionUtils.isEmpty(sblotIdList) ? EMPTY : String.join(COMMA, sblotIdList))
                .setTestStage(task.getTestStage())
                .setFileCategory(task.getFileCategory())
                .setLotType(task.getLotType())
                .setProcessStatus(task.getProcessStatus())
                .setStartTime(lotWaferCalStatus.getDieStartTime().getTime())
                .setTs(System.currentTimeMillis())
                .setDataVersion(task.getDataVersion())
                .setExceptionMessage(task.getExceptionMessage())
                .setErrorMessage(task.getErrorMessage());
        String ruleMessage = JSON.toJSONString(ruleCalculateEndFlagMessage);
        LOGGER.info("发送rule消息：{}", ruleMessage);
        kafKaProvider.sendKafka(ruleCalculateEndFlagTopic, ruleMessage);
    }

    private void requestBi(LayerCalculatePool task, List<LotStockingDetail> lotStockingDetails) {
        if (SUPPORT_CP_TEST_AREA_LIST.contains(task.getTestArea()) && REQUEST_BI_FILE_CATEGORY_SET.contains(task.getFileCategory()) && task.getProcessStatus() == ProcessStatus.SUCCESS) {
            lotStockingDetails.stream()
                    .collect(Collectors.groupingBy(LotStockingDetail::getTestStage))
                    .forEach((key, value) -> biNoticeService.requestBi(value.get(0)));
        }
    }

    private void updateLotStockingDetail(List<LotStockingDetail> lotStockingDetails, LayerCalculatePool task) {
        Date date = new Date();
        lotStockingDetails.forEach(t -> t.setContinueFlag(1).setUpdateTime(date));
        if (task.getProcessStatus() == ProcessStatus.SUCCESS) {
            lotStockingDetails.forEach(t -> t.setDieFinishFlag(1));
        }
        lotStockingDetailRepository.saveAll(lotStockingDetails);
    }

    private static LayerCalculatePool generateNextLayerCalculatePool(LayerCalculatePool task, DwLayer dwLayer, DwLayer nextDwLayer, Date updateTime) {
        LayerCalculatePool newLayerCalculatePool = new LayerCalculatePool();
        BeanUtils.copyProperties(task, newLayerCalculatePool);
        return newLayerCalculatePool
                .setId(null)
                .setDwLayer(dwLayer)
                .setNextDwLayer(nextDwLayer)
                .setCreateTime(updateTime)
                .setUniqueId(null)
                .setAppId(null);
    }

    private LotWaferCalStatus updateLotWaferCalStatus(LayerCalculatePool task) {
        Date date = new Date();
        LotWaferCalStatus lotWaferCalStatus = lotWaferCalStatusRepository.findFirstByCustomerAndTestAreaAndFileCategoryAndFactoryAndDeviceIdAndLotIdAndWaferNoAndTestStageAndLotTypeOrderByUpdateTimeDesc(
                task.getCustomer(), task.getTestArea(), task.getFileCategory(), task.getFactory(), task.getDeviceId(), task.getLotId(), task.getWaferNo(), task.getTestStage(), task.getLotType()
        );
        lotWaferCalStatus
                .setDieStatus(task.getProcessStatus())
                .setDieEndTime(date)
                .setDieCalTime((lotWaferCalStatus.getDieEndTime().getTime() - lotWaferCalStatus.getDieStartTime().getTime()) / 1000L)
                .setTestItemStatus(null)
                .setTestItemStartTime(null)
                .setTestItemEndTime(null)
                .setTestItemCalTime(null)
                .setUpdateTime(date)
                .setUpdateUser(SYSTEM);
        lotWaferCalStatusRepository.save(lotWaferCalStatus);
        return lotWaferCalStatus;
    }

    private void updateLotWaferCalRecord(LayerCalculatePool task, LotWaferCalStatus lotWaferCalStatus) {
        Date updateTime = new Date();
        LotWaferCalRecord lotWaferCalRecord = lotWaferCalRecordRepository.findFirstByCustomerAndFactoryAndTestAreaAndFileCategoryAndDeviceIdAndLotIdAndWaferNoAndTestStageAndLotTypeAndLatestFlagOrderByUpdateTimeDesc(
                task.getCustomer(), task.getFactory(), task.getTestArea(), task.getFileCategory(), task.getDeviceId(), task.getLotId(), task.getWaferNo(), task.getTestStage(), task.getLotType(), 1
        );
        lotWaferCalRecord
                .setFinalEndTime(task.getProcessStatus() == ProcessStatus.FAIL ? lotWaferCalStatus.getDieEndTime() : null)
                .setDieCalTime(Lists.newArrayList(lotWaferCalStatus.getDieStartTime(), lotWaferCalStatus.getDieEndTime()).stream().map(t -> DateFormatUtils.format(t, DEFAULT_TIME_FORMAT)).collect(Collectors.joining(WAVY_LINE)))
                .setDieCalConsume(lotWaferCalStatus.getDieCalTime())
                .setDieStatus(lotWaferCalStatus.getDieStatus())
                .setCalculateDwTestItem(lotWaferCalStatus.getCalculateDwTestItem())
                .setCalculateYmsTestItem(lotWaferCalStatus.getCalculateYmsTestItem())
                .setRuleCalTime(lotWaferCalRecord.getDieCalTime())
                .setRuleCalConsume(lotWaferCalRecord.getDieCalConsume())
                .setRuleStatus(lotWaferCalRecord.getDieStatus())
                .setUpdateTime(updateTime)
                .setUpdateUser(SYSTEM);
        lotWaferCalRecordRepository.save(lotWaferCalRecord);
    }

    public void updateFileLoadingLogs(LayerCalculatePool task, List<String> fileNames) {
        Date date = new Date();
        List<FileLoadingLog> stageFileLoadingLogs = fileLoadingLogRepository.findAllByFileNameInAndStep(fileNames, StepType.STEP_TYPE_5100.getStep());
        stageFileLoadingLogs.forEach(stageFileLoadingLog -> {
            stageFileLoadingLog
                    .setProcessStatus(task.getProcessStatus())
                    .setStepEndTime(date)
                    .setUpdateTime(date)
                    .setFailedType(null)
                    .setFailedFields(null)
                    .setExceptionType(null)
                    .setExceptionMessage(null)
                    .setErrorMessage(null);
            if (task.getProcessStatus() == ProcessStatus.FAIL) {
                FileLoadException fileLoadException = new FileLoadException(FileLoadExceptionInfo.DWD_PROCESS_FAIL_EXCEPTION, task.getErrorMessage(), null)
                        .updateExceptionMessage(task.getExceptionMessage());
                stageFileLoadingLog
                        .setFailedType(fileLoadException.getFailedType())
                        .setFailedFields(fileLoadException.getFailedFields())
                        .setExceptionType(fileLoadException.getExceptionType())
                        .setExceptionMessage(fileLoadException.getExceptionMessage())
                        .setErrorMessage(fileLoadException.getErrorMessage());
            }
        });
        List<FileLoadingLog> needSaveFileLoadingLogs = new ArrayList<>(stageFileLoadingLogs);
        List<FileLoadingLog> latestStageFileLoadingLogs = stageFileLoadingLogs;
        if (task.getProcessStatus() == ProcessStatus.SUCCESS) {
            needSaveFileLoadingLogs.addAll(stageFileLoadingLogs.stream().map(t -> generateNextStepFileloadingLog(t, date, StepType.STEP_TYPE_6100, ProcessStatus.SUCCESS)).collect(Collectors.toList()));
            latestStageFileLoadingLogs = stageFileLoadingLogs.stream().map(t -> generateNextStepFileloadingLog(t, date, StepType.STEP_TYPE_7100, ProcessStatus.PROCESSING)).collect(Collectors.toList());
            needSaveFileLoadingLogs.addAll(latestStageFileLoadingLogs);
        }
        fileLoadingLogRepository.saveAll(needSaveFileLoadingLogs);
        fileWarehousingRecordManager.updateFileWarehousingStatus(latestStageFileLoadingLogs);
    }

    private void updateLotWaferWarehousingRecord(LayerCalculatePool task, List<String> fileNames) {
        // 更新数据修复相关表
        if (task.getProcessStatus() == ProcessStatus.SUCCESS) {
            // 任务成功，更新LotWaferWarehousing
            lotWaferWarehousingRecordRepository.updateProcessStatusForDie(
                    task.getCustomer(),
                    task.getTestArea(),
                    task.getFactory(),
                    task.getDeviceId(),
                    task.getLotType(),
                    task.getTestStage(),
                    task.getLotId(),
                    task.getWaferNo(),
                    task.getFileCategory(),
                    ProcessStatus.PROCESSING,
                    new Date(),
                    task.getProcessStatus()
            );
            // 查询dw_repair_recalculate_record，如果修复的flowId被修改，发送消息通知
            List<RepairRecalculateRecord> recalculateRecords = repairRecalculateRecordRepository.findAllByCustomerAndFactoryAndFactorySiteAndTestAreaAndDeviceIdAndLotTypeAndTestStageAndLotIdAndWaferNoAndFileCategoryAndPushMessageFlagOrderByUpdateTimeDesc(
                    task.getCustomer(), task.getFactory(), task.getFactorySite(), task.getTestArea(), task.getDeviceId(), task.getLotType(), task.getTestStage(), task.getLotId(), task.getWaferNo(), task.getFileCategory(), 0
            );
            if (CollectionUtils.isNotEmpty(recalculateRecords)) {
                LOGGER.info("修复的值被重计算");
                // 发送消息
                dataRepairService.sendRepairRecalculateMessage(recalculateRecords);
                recalculateRecords.forEach(repairRecalculateRecord -> repairRecalculateRecordRepository.save(repairRecalculateRecord.setPushMessageFlag(1).setUpdateTime(new Date())));
            }
        } else {
            // 任务失败，更新LotWaferWarehousing
            lotWaferWarehousingRecordRepository.updateProcessStatusForDie(
                    task.getCustomer(),
                    task.getTestArea(),
                    task.getFactory(),
                    task.getDeviceId(),
                    task.getLotType(),
                    task.getTestStage(),
                    task.getLotId(),
                    task.getWaferNo(),
                    task.getFileCategory(),
                    task.getProcessStatus(),
                    new Date(),
                    task.getProcessStatus()
            );
            if (task.getWarehousingMode() == WarehousingMode.REPAIR) {
                LOGGER.info("任务失败，修复结束");
                FileLoadExceptionInfo exceptionInfo = FileLoadExceptionInfo.DWD_PROCESS_FAIL_EXCEPTION;
                dataRepairService.repairFinish(task.getRepairLotWaferId(), fileNames, ProcessStatus.FAIL, exceptionInfo.getType(), task.getExceptionMessage(), task.getErrorMessage());
            }
        }
    }

    private static FileLoadingLog generateNextStepFileloadingLog(FileLoadingLog fileLoadingLog, Date date, StepType stepType, ProcessStatus processStatus) {
        FileLoadingLog nextStepFileLoadingLog = new FileLoadingLog();
        BeanUtils.copyProperties(fileLoadingLog, nextStepFileLoadingLog);
        nextStepFileLoadingLog
                .setId(null)
                .setStep(stepType.getStep())
                .setStepStartTime(date)
                .setStepEndTime(processStatus == ProcessStatus.PROCESSING ? null : date)
                .setProcessStatus(processStatus)
                .setCreateTime(date)
                .setUpdateTime(date)
                .setCreateUser(SYSTEM)
                .setUpdateUser(SYSTEM);
        return nextStepFileLoadingLog;
    }

    private CalculateEndFlag buildCalculateEndFlag(LayerCalculatePool layerCalculatePool, ComputeResultMessage computeResultMessage, LotWaferCalStatus lotWaferCalStatus, Platform platform) {
        return new CalculateEndFlag()
                .setCustomer(layerCalculatePool.getCustomer())
                .setSubCustomer(layerCalculatePool.getSubCustomer())
                .setFactory(layerCalculatePool.getFactory())
                .setFactorySite(layerCalculatePool.getFactorySite())
                .setTestArea(layerCalculatePool.getTestArea())
                .setLotType(layerCalculatePool.getLotType())
                .setDeviceId(layerCalculatePool.getDeviceId())
                .setTestStage(layerCalculatePool.getTestStage())
                .setLotId(layerCalculatePool.getLotId())
                .setWaferNo(layerCalculatePool.getWaferNo())
                .setFileCategory(layerCalculatePool.getFileCategory())
                .setDwLayer(layerCalculatePool.getDwLayer())
                .setNextDwLayer(layerCalculatePool.getNextDwLayer())
                .setPriority(layerCalculatePool.getPriority())
                .setPlatform(platform)
                .setWarehousingMode(layerCalculatePool.getWarehousingMode())
                .setRepairLotWaferId(layerCalculatePool.getRepairLotWaferId())
                .setCleanupTaskIds(layerCalculatePool.getCleanupTaskIds())
                .setProcessStatus(layerCalculatePool.getProcessStatus())
                .setAppId(layerCalculatePool.getAppId())
                .setExceptionType(layerCalculatePool.getExceptionType())
                .setExceptionMessage(layerCalculatePool.getExceptionMessage())
                .setErrorMessage(layerCalculatePool.getErrorMessage())
                .setProcessConsume(computeResultMessage.getExecuteTime())
                .setTs(System.currentTimeMillis())
                .setDataVersion(layerCalculatePool.getDataVersion())
                .setDieCount(lotWaferCalStatus.getDieTotalCnt())
                .setTestItemCount(lotWaferCalStatus.getTestItemTotalCnt())
                .setCalculateDwTestItem(lotWaferCalStatus.getCalculateDwTestItem())
                .setCalculateYmsTestItem(lotWaferCalStatus.getCalculateYmsTestItem());
    }

    private void updateLotBatchSummary(LayerCalculatePool task, List<String> fileNames) {
        if (!UPDATE_LOT_BATCH_SUMMARY_FILE_CATEGORY_SET.contains(task.getFileCategory()) || task.getProcessStatus() == ProcessStatus.FAIL) {
            return;
        }
        LOGGER.info("ads层处理完成,准备更新dw_lot_batch_summary表");
        List<LotMetaDataDetail> lotMetaDataDetail = lotMetaDataDetailRepository.findAllByFileNameIn(fileNames);
        if (SUPPORT_CP_TEST_AREA_LIST.contains(task.getTestArea())) {
            LOGGER.info("是CP数据，开始更新dw_lot_batch_summary表");
            // 将lotMetaDataDetail 按customer,subCustomer,factory,testArea,testStage,deviceId,lotType,lotId,WaferId分组，并遍历每组，处理每组数据
            lotMetaDataDetail.stream().collect(Collectors.groupingBy(t -> new StringBuilder(t.getCustomer())
                    .append(Constant.UNDER_LINE)
                    .append(t.getSubCustomer())
                    .append(Constant.UNDER_LINE)
                    .append(t.getFactory())
                    .append(Constant.UNDER_LINE)
                    .append(t.getTestArea().getArea())
                    .append(Constant.UNDER_LINE)
                    .append(t.getTestStage())
                    .append(Constant.UNDER_LINE)
                    .append(t.getDeviceId())
                    .append(Constant.UNDER_LINE)
                    .append(t.getLotType().getType())
                    .append(Constant.UNDER_LINE)
                    .append(t.getLotId())
                    .append(Constant.UNDER_LINE)
                    .append(t.getOriginWaferId())
                    .toString())).forEach((key, value) -> {
                LOGGER.info("开始处理每组数据，key = {}, lotMetaDataDetail = {}", key, JSON.toJSONString(lotMetaDataDetail));
                LotMetaDataDetail latestLotMetaDataDetail = value.stream().sorted(Comparator.comparing(LotMetaDataDetail::getStartT).reversed()).findFirst().orElse(null);
                String factorySite = latestLotMetaDataDetail.getFactorySite();
                String sblotId = latestLotMetaDataDetail.getSblotId();
                String waferId = latestLotMetaDataDetail.getWaferId();
                String waferLotId = WaferUtil.formatWaferLotId(latestLotMetaDataDetail.getLotId(), waferId);
                String latest_program = latestLotMetaDataDetail.getTestProgram();
                String programList = value.stream().map(LotMetaDataDetail::getTestProgram).distinct().collect(Collectors.joining(Constant.COMMA));
                Long minStartT = value.stream().map(detail -> Long.parseLong(detail.getStartT())).min(Long::compare).orElse(null);
                Long maxFinishT = value.stream().map(detail -> Long.parseLong(detail.getFinishT())).max(Long::compare).orElse(null);
                Date startTime = minStartT != null ? new Date(minStartT * 1000) : null;
                Date endTime = maxFinishT != null ? new Date(maxFinishT * 1000) : null;
                LotBatchSummary lotBatchSummary = new LotBatchSummary()
                        .setCustomer(task.getCustomer())
                        .setSubCustomer(task.getSubCustomer())
                        .setFactory(task.getFactory())
                        .setTestArea(task.getTestArea())
                        .setTestStage(task.getTestStage())
                        .setDeviceId(task.getDeviceId())
                        .setLotType(task.getLotType())
                        .setLotId(task.getLotId())
                        .setSblotId(sblotId)
                        .setWaferId(waferId)
                        .setWaferLotId(waferLotId)
                        .setStartTime(startTime)
                        .setEndTime(endTime)
                        .setFactorySite(factorySite)
                        .setStdfLatestProgram(latest_program)
                        .setStdfTestProgramList(programList);
                LOGGER.info("获取的lotbatchsummary数据为 = {}", JSON.toJSONString(lotBatchSummary));
                lotBatchSummaryManager.updateLotBatchSummaryStdfCp(lotBatchSummary);
                LOGGER.info("dw_lot_batch_summary表更新成功！");
            });
        } else {
            LOGGER.info("是FT数据，开始更新dw_lot_batch_summary表");
            // 将lotMetaDataDetail 按customer,subCustomer,factory,testArea,testStage,deviceId,lotType,lotId,SblotId分组，并遍历每组，处理每组数据
            lotMetaDataDetail.stream().collect(Collectors.groupingBy(t -> new StringBuilder(t.getCustomer())
                    .append(Constant.UNDER_LINE)
                    .append(t.getSubCustomer())
                    .append(Constant.UNDER_LINE)
                    .append(t.getFactory())
                    .append(Constant.UNDER_LINE)
                    .append(t.getTestArea().getArea())
                    .append(Constant.UNDER_LINE)
                    .append(t.getTestStage())
                    .append(Constant.UNDER_LINE)
                    .append(t.getDeviceId())
                    .append(Constant.UNDER_LINE)
                    .append(t.getLotType().getType())
                    .append(Constant.UNDER_LINE)
                    .append(t.getLotId())
                    .append(Constant.UNDER_LINE)
                    .append(t.getSblotId())
                    .toString())).forEach((key, value) -> {
                LOGGER.info("开始处理每组数据，key = {}, lotMetaDataDetail = {}", key, JSON.toJSONString(lotMetaDataDetail));
                LotMetaDataDetail latestLotMetaDataDetail = value.stream().sorted(Comparator.comparing(LotMetaDataDetail::getStartT).reversed()).findFirst().orElse(null);
                String factorySite = latestLotMetaDataDetail.getFactorySite();
                String sblotId = latestLotMetaDataDetail.getSblotId();
                String waferId = latestLotMetaDataDetail.getWaferId();
                String waferLotId = WaferUtil.formatLotId(sblotId);
                String latest_program = latestLotMetaDataDetail.getTestProgram();
                String programList = value.stream().map(LotMetaDataDetail::getTestProgram).distinct().collect(Collectors.joining(Constant.COMMA));
                Long minStartT = value.stream().map(detail -> Long.parseLong(detail.getStartT())).min(Long::compare).orElse(null);
                Long maxFinishT = value.stream().map(detail -> Long.parseLong(detail.getFinishT())).max(Long::compare).orElse(null);
                Date startTime = minStartT != null ? new Date(minStartT * 1000) : null;
                Date endTime = maxFinishT != null ? new Date(maxFinishT * 1000) : null;
                LotBatchSummary lotBatchSummary = new LotBatchSummary()
                        .setCustomer(task.getCustomer())
                        .setSubCustomer(task.getSubCustomer())
                        .setFactory(task.getFactory())
                        .setTestArea(task.getTestArea())
                        .setTestStage(task.getTestStage())
                        .setDeviceId(task.getDeviceId())
                        .setLotType(task.getLotType())
                        .setLotId(task.getLotId())
                        .setSblotId(sblotId)
                        .setWaferId(waferId)
                        .setWaferLotId(waferLotId)
                        .setStartTime(startTime)
                        .setEndTime(endTime)
                        .setFactorySite(factorySite)
                        .setStdfLatestProgram(latest_program)
                        .setStdfTestProgramList(programList);
                LOGGER.info("获取的lotbatchsummary数据为 = {}", JSON.toJSONString(lotBatchSummary));
                lotBatchSummaryManager.updateLotBatchSummaryStdfFt(lotBatchSummary);
                LOGGER.info("dw_lot_batch_summary表更新成功！");
            });
        }
    }

    private void updateTestRawDataLotWaferPrimaryData(List<LotStockingDetail> lotStockingDetails) {
        if (CollectionUtils.isEmpty(lotStockingDetails)) {
            LOGGER.info("updateTestRawDataLotWaferPrimaryData, lotStockingDetails is empty");
            return;
        }
        LotStockingDetail head = lotStockingDetails.get(0);
        if (TestArea.CP.getTestScope().equals(head.getTestArea().getTestScope())) {
            LotWaferPrimaryKey lotWaferPrimaryKey = new LotWaferPrimaryKey()
                    .setCustomer(head.getCustomer())
                    .setSubCustomer(head.getSubCustomer())
                    .setFactory(head.getFactory())
                    .setFactorySite(head.getFactorySite())
                    .setTestArea(TestArea.CP)
                    .setDeviceId(head.getDeviceId())
                    .setLotId(head.getLotId())
                    .setSblotId(EMPTY);

            List<LotWaferPrimaryInputCntKey> inputCntList = dieDetailRepository.getInputCount(dwdDbName, head.getCustomer(), head.getSubCustomer(), head.getFactory(), head.getTestArea(), head.getDeviceId(), head.getLotType(), head.getTestStage(), head.getLotId(), head.getWaferNo());
            Integer inputCount = CollectionUtils.isEmpty(inputCntList) ? 0 : inputCntList.get(0).getCount();

            try {
                LOGGER.info("LotWaferPrimaryData数据更新, lotWaferPrimaryKey:{}, waferNo:{}", lotWaferPrimaryKey, head.getWaferNo());
                updateLotWaferPrimaryData(lotWaferPrimaryKey, lotStockingDetails, Collections.singletonList(WaferUtil.formatLotId(head.getLotId())), Collections.singletonList(head.getWaferNo()), inputCount);
            } catch (InterruptedException e) {
                LOGGER.info("LotWaferPrimaryData数据更新失败!", e);
            }
        } else if (TestArea.FT.getTestScope().equals(head.getTestArea().getTestScope())) {
            Map<LotWaferPrimaryKey, List<LotStockingDetail>> lotWaferPrimaryKeyListMap = lotStockingDetails.stream()
                    .collect(Collectors.groupingBy(t -> new LotWaferPrimaryKey()
                                    .setCustomer(t.getCustomer())
                                    .setSubCustomer(t.getSubCustomer())
                                    .setFactory(t.getFactory())
                                    .setFactorySite(t.getFactorySite())
                                    .setTestArea(t.getTestArea())
                                    .setDeviceId(t.getDeviceId())
                                    .setLotId(t.getLotId())
                                    .setSblotId(t.getSblotId())
                            , Collectors.toList()));

            Map<LotWaferPrimaryKey, List<LotWaferPrimaryWaferLotIdKey>> waferLotIdMap =
                    dieDetailRepository.getWaferLotIdList(dwdDbName, head.getCustomer(), head.getSubCustomer(), head.getFactory(), head.getTestArea(), head.getDeviceId(), head.getLotType(), head.getTestStage(), head.getLotId())
                            .stream()
                            .filter(elem -> Objects.equals(elem.getIsStandardEcid(), 1) || !Objects.equals(elem.getWaferLotId(), WaferUtil.formatLotId(elem.getSblotId())))
                            .collect(Collectors.groupingBy(t -> new LotWaferPrimaryKey()
                                            .setCustomer(t.getCustomer())
                                            .setSubCustomer(t.getSubCustomer())
                                            .setFactory(t.getFactory())
                                            .setFactorySite(t.getFactorySite())
                                            .setTestArea(t.getTestArea())
                                            .setDeviceId(t.getDeviceId())
                                            .setLotId(t.getLotId())
                                            .setSblotId(t.getSblotId())
                                    , Collectors.toList()));

            Map<LotWaferPrimaryKey, Integer> inputCntMap =
                    dieDetailRepository.getInputCount(dwdDbName, head.getCustomer(), head.getSubCustomer(), head.getFactory(), head.getTestArea(), head.getDeviceId(), head.getLotType(), head.getTestStage(), head.getLotId(), EMPTY)
                            .stream()
                            .collect(Collectors.toMap(t -> new LotWaferPrimaryKey()
                                    .setCustomer(t.getCustomer())
                                    .setSubCustomer(t.getSubCustomer())
                                    .setFactory(t.getFactory())
                                    .setFactorySite(t.getFactorySite())
                                    .setTestArea(t.getTestArea())
                                    .setDeviceId(t.getDeviceId())
                                    .setLotId(t.getLotId())
                                    .setSblotId(t.getSblotId()), LotWaferPrimaryInputCntKey::getCount, Integer::max));

            lotWaferPrimaryKeyListMap.forEach((lotWaferPrimaryKey, lotStockingDetailList) -> {
                List<LotWaferPrimaryWaferLotIdKey> lotWaferPrimaryWaferLotIdKeys = waferLotIdMap.getOrDefault(lotWaferPrimaryKey, new ArrayList<>());
                List<String> waferLotIds = lotWaferPrimaryWaferLotIdKeys.stream().map(LotWaferPrimaryWaferLotIdKey::getWaferLotId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                List<String> waferNos = lotWaferPrimaryWaferLotIdKeys.stream().map(LotWaferPrimaryWaferLotIdKey::getWaferNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                Integer inputCount = inputCntMap.getOrDefault(lotWaferPrimaryKey, 0);
                try {
                    updateLotWaferPrimaryData(lotWaferPrimaryKey, lotStockingDetailList, waferLotIds, waferNos, inputCount);
                } catch (InterruptedException e) {
                    LOGGER.info("LotWaferPrimaryData数据更新失败!", e);
                }
            });
            LOGGER.info("LotWaferPrimaryData数据更新成功!");
        }
    }

    private void updateLotWaferPrimaryData(LotWaferPrimaryKey lotWaferPrimaryKey, List<LotStockingDetail> lotStockingDetailList, List<String> waferLotIds, List<String> waferNos, Integer inputCount) throws InterruptedException {
        LocalTime start = LocalTime.now();
        boolean getLock = false;
        RLock lock = redisService.getUpdateLotWaferPrimaryDataLock(lotWaferPrimaryKey);
        while (!getLock && Duration.between(start, LocalTime.now()).toMinutes() <= 5) {
            LOGGER.info("未获取到锁");
            try {
                lock.lock();
                getLock = true;
                LOGGER.info("获取锁成功, lotWaferPrimaryKey:{}", lotWaferPrimaryKey);
            } catch (Exception e) {
                LOGGER.info("获取锁失败, lotWaferPrimaryKey:{}", lotWaferPrimaryKey, e);
                Thread.sleep(200L);
            }
        }

        if (getLock) {
            try {
                LOGGER.info("LotWaferPrimaryData数据更新开始!");
                lotWaferPrimaryDataManager.saveTestRawDataLotWaferPrimaryData(lotWaferPrimaryKey, lotStockingDetailList, waferLotIds, waferNos, inputCount);
            } finally {
                lock.unlock();
                LOGGER.info("释放锁成功, lotWaferPrimaryKey:{}", lotWaferPrimaryKey);
            }

        }
    }

    private void updateCalTestItemFlagByTestItemSwitch(LotWaferCalStatus lotWaferCalStatus, LayerCalculatePool task) {
        if (TestArea.getCpMapDataSourceList().contains(task.getTestArea()) || TestArea.getCpInklessMapDataDourceList().contains(task.getTestArea())) {
            LOGGER.info("testArea:{}不跑测项，不需要查询tesItemSwitch更新calTestItemFlag", TestArea.of(task.getTestArea()));
            return;
        }
        LOGGER.info("查询tesItemSwitch，更新calTestItemFlag");

        Optional<TestItemSwitch> testItemSwitchOptional = testItemSwitchRepository.findFirstByDeleteFlagOrderByUpdateTimeDesc(0);
        if (testItemSwitchOptional.isPresent()) {
            // 代码不为空且执行结果result为1,计算dw测项相关表
            LOGGER.info("tesItemSwitch存在，执行rule expression更新CalTestItemFlag");
            int calculateDwTestItem = 0;
            int calculateYmsTestItem = 0;
            // 应对工厂客户测项按照条件触发计算
            TestItemSwitch testItemSwitch = testItemSwitchOptional.get();
            String dwTestItemSwitchRuleExpression = testItemSwitch.getDwTestItemSwitchRuleExpression();
            String ymsTestItemSwitchRuleExpression = testItemSwitch.getYmsTestItemSwitchRuleExpression();
            if (StringUtils.isNotBlank(dwTestItemSwitchRuleExpression) || StringUtils.isNotBlank(ymsTestItemSwitchRuleExpression)) {
                LOGGER.debug("dw_test_item_switch表配置了规则,按照条件触发跑测项表,dwTestItemSwitchRuleExpression:{},\n ymsTestItemSwitchRuleExpression:{}",
                        dwTestItemSwitchRuleExpression, ymsTestItemSwitchRuleExpression);
                // 查询die任务信息
                List<LotWaferData> lotWaferData = lotWaferRepository.getLotWaferData(
                        dwsDbName,
                        lotWaferCalStatus.getCustomer(),
                        lotWaferCalStatus.getFactory(),
                        lotWaferCalStatus.getTestArea(),
                        lotWaferCalStatus.getDeviceId(),
                        lotWaferCalStatus.getLotType(),
                        lotWaferCalStatus.getTestStage(),
                        lotWaferCalStatus.getLotId(),
                        lotWaferCalStatus.getWaferNo()
                );

                LOGGER.info("执行dwTestItemSwitchRuleExpression");
                calculateDwTestItem = getCalTestItemFlag(testItemSwitch.getDwTestItemSwitchRuleExpression(), lotWaferData) ? 1 : 0;
                if (calculateDwTestItem == 1) {
                    LOGGER.info("执行ymsTestItemSwitchRuleExpression");
                    calculateYmsTestItem = getCalTestItemFlag(testItemSwitch.getYmsTestItemSwitchRuleExpression(), lotWaferData) ? 1 : 0;
                }
            }
            LOGGER.info("按照条件触发跑测项表,calculateDwTestItem:{}, calculateYmsTestItem:{}", calculateDwTestItem, calculateYmsTestItem);

            task.setCalculateDwTestItem(calculateDwTestItem);

            lotWaferCalStatus
                    .setCalculateDwTestItem(calculateDwTestItem)
                    .setCalculateYmsTestItem(calculateYmsTestItem)
                    .setUpdateTime(new Date());
            lotWaferCalStatusRepository.save(lotWaferCalStatus);
        } else {
            LOGGER.info("tesItemSwitch表没有数据,不更新CalTestItemFlag");
        }
    }

    private boolean getCalTestItemFlag(String ruleExpression, List<LotWaferData> lotWaferDataVo) {
        if (StringUtils.isBlank(ruleExpression) || CollectionUtils.isEmpty(lotWaferDataVo)) {
            return false;
        }

        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName(JS_ENGINE_NAME);
        String lotWaferData = JSON.toJSONString(lotWaferDataVo);

        String jsFun = "function getData(lotWaferData) {\n" +
                "        var result = null\n" +
                "        if (lotWaferData) {\n" +
                "            lotWaferData = JSON.parse(lotWaferData)\n" +
                "            {RULE_EXPRESSION}\n" +
                "        }\n" +
                "        if (result || result === 0) {\n" +
                "            return result\n" +
                "        } else {\n" +
                "            return null\n" +
                "        }\n" +
                "    }";

        Object result = getResultFromJs(engine, lotWaferData, jsFun.replace(RULE_EXPRESSION, ruleExpression));
        return Objects.equals(String.valueOf(result), "1");
    }

    private Object getResultFromJs(ScriptEngine engine, String lotWaferData, String completeJsFun) {
        Object result = null;
        try {
            // 注册js方法
            engine.eval(completeJsFun);
            Invocable invo = (Invocable) engine;

            //执行脚本中方法
            result = invo.invokeFunction("getData", lotWaferData);
            LOGGER.info("执行js脚本； lotWaferData: {} \n result: {}", lotWaferData, result);
        } catch (Throwable e) {
            String errorMessage = String.format("执行js脚本异常； completeJsFun : %s \n lotWaferData: %s \n %s", completeJsFun, lotWaferData, e.getMessage());
            LOGGER.error(errorMessage);
        }
        return result;
    }
}
