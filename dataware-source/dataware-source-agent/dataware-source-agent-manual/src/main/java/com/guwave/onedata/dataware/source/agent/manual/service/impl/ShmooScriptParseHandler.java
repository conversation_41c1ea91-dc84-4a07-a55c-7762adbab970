package com.guwave.onedata.dataware.source.agent.manual.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.manual.ManualCalculateFinishVo;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingFinishMessage;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualMessage;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualCalculateTaskRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualFileInfoRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualMessageRepository;
import com.guwave.onedata.dataware.source.agent.common.sink.impl.KafkaSink;
import com.guwave.onedata.dataware.source.agent.common.util.ProcessLogThreadLocalUtil;
import com.guwave.onedata.dataware.source.agent.manual.listener.ExcelConsumer;
import com.guwave.onedata.dataware.source.agent.manual.listener.ExcelLineListener;
import com.guwave.onedata.dataware.source.agent.manual.model.DwdShmooDetail;
import com.guwave.onedata.dataware.source.agent.manual.model.DwdShmooVminVmaxDetail;
import com.guwave.onedata.dataware.source.agent.manual.model.ManualConvertFileInfo;
import com.guwave.onedata.dataware.source.agent.manual.model.ManualConvertResult;
import com.guwave.onedata.dataware.source.agent.manual.service.ParseFileHandler;
import com.guwave.onedata.dataware.source.agent.manual.sink.DwdShmooDetailSink;
import com.guwave.onedata.dataware.source.agent.manual.sink.DwdShmooVminVmaxDetailSink;
import com.guwave.onedata.dataware.source.agent.manual.util.ShmooDataCommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;
import static com.guwave.onedata.dataware.source.agent.manual.util.RawDataCommonUtil.getMapValue;

@Component
public class ShmooScriptParseHandler implements ParseFileHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ShmooScriptParseHandler.class);

    @Value("${spring.kafka.manualFinishTopic}")
    private String manualFinishTopic;
    @Value("${spring.handler.sink.batchSize}")
    private Long batchSize;

    @Autowired
    private DwdShmooDetailSink dwdShmooDetailSink;
    @Autowired
    private DwdShmooVminVmaxDetailSink dwdShmooVminVmaxDetailSink;
    @Autowired
    private ManualFileInfoRepository manualFileInfoRepository;
    @Autowired
    private ManualCalculateTaskRepository manualCalculateTaskRepository;
    @Autowired
    private ManualMessageRepository manualMessageRepository;

    @Autowired
    private KafkaSink kafkaSink;

    @Override
    public Boolean support(FileCategory fileCategory, ParseRule parseRule) {
        return fileCategory == FileCategory.SHMOO && parseRule == ParseRule.SHMOO_SCRIPT;
    }

    @Override
    public boolean hasNext() {
        return false;
    }

    private Map<String, String> overWriteFieldMap = new HashMap<>();

    Map<String, Predicate<DwdShmooDetail>> SHMOO_DETAIL_CHECK_MAP = new LinkedHashMap<String, Predicate<DwdShmooDetail>>() {{
        put("fileId", dwdShmooDetail -> dwdShmooDetail.getFileId() != null);
        put("fileName", dwdShmooDetail -> StringUtils.isNotEmpty(dwdShmooDetail.getFileName()));
        put("customer", dwdShmooDetail -> StringUtils.isNotEmpty(dwdShmooDetail.getCustomer()));
        put("getuploadType", dwdShmooDetail -> dwdShmooDetail.getUploadType() != null);
        put("ecid", dwdShmooDetail -> StringUtils.isNotEmpty(dwdShmooDetail.getEcid()));
        put("siteNumber", dwdShmooDetail -> dwdShmooDetail.getSiteNumber() != null);
        put("testPattern", dwdShmooDetail -> StringUtils.isNotEmpty(dwdShmooDetail.getTestPattern()));
        put("testOrder", dwdShmooDetail -> dwdShmooDetail.getTestOrder() != null);
        put("xLabel", dwdShmooDetail -> StringUtils.isNotEmpty(dwdShmooDetail.getxLabel()));
        put("xStart", dwdShmooDetail -> dwdShmooDetail.getxStart() != null);
        put("xEnd", dwdShmooDetail -> dwdShmooDetail.getxEnd() != null);
        put("xValue", dwdShmooDetail -> dwdShmooDetail.getxValue() != null);
        put("shmooResult", dwdShmooDetail -> dwdShmooDetail.getShmooResult() != null);
        put("shmooResultName", dwdShmooDetail -> StringUtils.isNotEmpty(dwdShmooDetail.getShmooResultName()));
        put("isPrimary", dwdShmooDetail -> dwdShmooDetail.getIsPrimary() != null);
        put("createUser", dwdShmooDetail -> StringUtils.isNotEmpty(dwdShmooDetail.getCreateUser()));
        put("createTime", dwdShmooDetail -> dwdShmooDetail.getCreateTime() != null);
    }};

    Map<String, Predicate<DwdShmooVminVmaxDetail>> SHMOO_VMIN_VMAX_DETAIL_CHECK_MAP = new LinkedHashMap<String, Predicate<DwdShmooVminVmaxDetail>>() {{

        put("fileId", dwdShmooVminVmaxDetail -> dwdShmooVminVmaxDetail.getFileId() != null);
        put("fileName", dwdShmooVminVmaxDetail -> StringUtils.isNotEmpty(dwdShmooVminVmaxDetail.getFileName()));
        put("customer", dwdShmooVminVmaxDetail -> StringUtils.isNotEmpty(dwdShmooVminVmaxDetail.getCustomer()));
        put("getuploadType", dwdShmooVminVmaxDetail -> dwdShmooVminVmaxDetail.getUploadType() != null);
        put("ecid", dwdShmooVminVmaxDetail -> StringUtils.isNotEmpty(dwdShmooVminVmaxDetail.getEcid()));
        put("siteNumber", dwdShmooVminVmaxDetail -> dwdShmooVminVmaxDetail.getSiteNumber() != null);
        put("testPattern", dwdShmooVminVmaxDetail -> StringUtils.isNotEmpty(dwdShmooVminVmaxDetail.getTestPattern()));
        put("testOrder", dwdShmooVminVmaxDetail -> dwdShmooVminVmaxDetail.getTestOrder() != null);
        put("executionType", dwdShmooVminVmaxDetail -> StringUtils.isNotEmpty(dwdShmooVminVmaxDetail.getExecutionType()));
        put("executionLabel", dwdShmooVminVmaxDetail -> StringUtils.isNotEmpty(dwdShmooVminVmaxDetail.getExecutionLabel()));
        put("executionValue", dwdShmooVminVmaxDetail -> dwdShmooVminVmaxDetail.getExecutionValue() != null);
        put("shmooResult", dwdShmooVminVmaxDetail -> dwdShmooVminVmaxDetail.getShmooResult() != null);
        put("shmooResultName", dwdShmooVminVmaxDetail -> StringUtils.isNotEmpty(dwdShmooVminVmaxDetail.getShmooResultName()));
        put("vminResult", dwdShmooVminVmaxDetail -> dwdShmooVminVmaxDetail.getVminResult() != null);
        put("vmaxResult", dwdShmooVminVmaxDetail -> dwdShmooVminVmaxDetail.getVmaxResult() != null);
        put("createUser", dwdShmooDetail -> StringUtils.isNotEmpty(dwdShmooDetail.getCreateUser()));
        put("createTime", dwdShmooDetail -> dwdShmooDetail.getCreateTime() != null);
    }};


    @Override
    public void dealFile(ManualCalculateTask manualCalculateTask, File localFile, ManualFileInfo manualFileInfo, File scriptFile) {
        manualCalculateTask.setProcessStatus(ProcessStatus.PROCESSING);
        // 覆盖字段
        overWriteFieldMap = getMapValue(manualFileInfo.getExtInfo());

        // 填充manualFileInfo并更新
        overWriteFieldMap.forEach((field, value) -> {
            BiConsumer<String, ManualFileInfo> consumer = ShmooDataCommonUtil.MANUAL_FILE_INFO_OVERWRITE_MAP.get(field);
            if (consumer != null) {
                consumer.accept(value, manualFileInfo);
            }
        });
        manualFileInfo.setTestArea(manualFileInfo.getTestArea() == null ? TestArea.NA : manualFileInfo.getTestArea());
        manualFileInfo.setLotType(manualFileInfo.getLotType() == null ? LotType.EMPTY : manualFileInfo.getLotType());

        // manual任务消息
        ManualMessage manualMessage = manualMessageRepository.findById(manualCalculateTask.getMessageId()).get()
                .setExceptionType(null)
                .setExceptionMessage(EMPTY)
                .setErrorMessage(EMPTY)
                .setProcessStatus(ProcessStatus.PROCESSING);

        long start = System.currentTimeMillis();
        LOGGER.info("解析SHMOO开始, 文件: {},  脚本: {}", localFile.getAbsolutePath(), scriptFile.getAbsolutePath());
        List<File> finalDeleteFiles = Lists.newArrayList(localFile, scriptFile);
        try {
            // 调用脚本解析shmoo生成csv
            ManualConvertResult manualConvertResult = convertFile(localFile, scriptFile, false);

            // 发送ODS进度消息
            sendManualWarehousingFinishMessage(manualCalculateTask, DwLayer.ODS, manualMessage.getExceptionType(), manualMessage.getExceptionMessage());

            String convertCode = manualConvertResult.getCode();
            List<ManualConvertFileInfo> manualConvertFileInfos = manualConvertResult.getData();

            if (convertCode.equals(Constant.MANUAL_CONVERT_SUCCESS) && CollectionUtils.isNotEmpty(manualConvertFileInfos)) {
                LOGGER.info("执行脚本: {}解析SHMOO: {} 生成文件: {}", scriptFile.getAbsolutePath(), localFile.getAbsolutePath(), manualConvertFileInfos.stream().map(ManualConvertFileInfo::getFileFullPath).collect(Collectors.joining(Constant.COMMA)));
                finalDeleteFiles.addAll(manualConvertFileInfos.stream().map(convertFile -> new File(convertFile.getFileFullPath())).collect(Collectors.toList()));
                // 读取csv
                ExcelLineListener excelLineListener = null;
                List<DwdShmooDetail> dwdShmooDetails = new ArrayList<>();
                List<DwdShmooVminVmaxDetail> dwdShmooVminVmaxsDetailDetails = new ArrayList<>();
                for (ManualConvertFileInfo manualConvertFileInfo : manualConvertFileInfos) {
                    LOGGER.info("读取SHMOO解析结果文件, fileType: {}, filePath: {}", manualConvertFileInfo.getFileType(), manualConvertFileInfo.getFileFullPath());

                    ManualConvertFileType fileType = manualConvertFileInfo.getFileType();
                    try (InputStream inputStream = Files.newInputStream(Paths.get(manualConvertFileInfo.getFileFullPath()))) {
                        switch (fileType) {
                            case SHMOO_DETAIL_FILE:
                                excelLineListener = new ExcelLineListener(manualFileInfo.getFieldMapping(), getDwdFileShmooDetailConsumer(manualFileInfo, dwdShmooDetails));
                                break;
                            case SHMOO_VMIN_FILE:
                                excelLineListener = new ExcelLineListener(manualFileInfo.getFieldMapping(), getDwdFileShmooVminVmaxConsumer(manualFileInfo, dwdShmooVminVmaxsDetailDetails));
                                break;
                        }
                        ExcelReader reader = EasyExcel.read(inputStream, excelLineListener).headRowNumber(1).excelType(ExcelTypeEnum.CSV).build();
                        try {
                            reader.read(new ReadSheet(0));
                        } finally {
                            reader.finish();
                        }
                    } catch (FileLoadException e) {
                        throw e;
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
            LOGGER.info("解析SHMOO结束, 文件: {}, 脚本: {},  耗时: {}", localFile.getAbsolutePath(), scriptFile.getAbsolutePath(), System.currentTimeMillis() - start);

            // 发送DWD进度消息
            sendManualWarehousingFinishMessage(manualCalculateTask, DwLayer.DWD, manualMessage.getExceptionType(), manualMessage.getExceptionMessage());

            manualCalculateTask.setProcessStatus(ProcessStatus.SUCCESS);
            manualMessage.setProcessStatus(ProcessStatus.SUCCESS);
        } catch (FileLoadException e) {
            throw e;
        } catch (Exception e) {
            manualCalculateTask.setProcessStatus(ProcessStatus.FAIL);
            manualMessage.setProcessStatus(ProcessStatus.FAIL)
                    .setExceptionType(FileLoadExceptionInfo.RAW_DATA_FILE_CONVERT_EXCEPTION.getType())
                    .setExceptionMessage(FileLoadExceptionInfo.RAW_DATA_FILE_CONVERT_EXCEPTION.getMessage())
                    .setErrorMessage(ExceptionUtils.getStackTrace(e));

            LOGGER.error("解析SHMOO文件: {}异常, 脚本: {}", localFile.getAbsolutePath(), scriptFile.getAbsolutePath(), e);
            throw new FileLoadException(FileLoadExceptionInfo.RAW_DATA_FILE_CONVERT_EXCEPTION, ExceptionUtils.getStackTrace(e), null);
        } finally {
            // 发送完成消息
            String manualFinishMessage = sendManualWarehousingFinishMessage(manualCalculateTask, DwLayer.ADS, manualMessage.getExceptionType(), manualMessage.getExceptionMessage());

            Date now = new Date();
            LOGGER.info("更新manualFileInfo {}", manualFileInfo.getId());
            manualFileInfoRepository.save(manualFileInfo.setUpdateTime(now));
            LOGGER.info("更新manualCalculateTask {}", manualCalculateTask.getId());
            manualCalculateTaskRepository.save(manualCalculateTask.setUpdateTime(now));
            LOGGER.info("更新manualMessage {}", manualMessage.getId());
            manualMessageRepository.save(manualMessage.setManualFinishMessage(manualFinishMessage).setUpdateTime(now));

            LOGGER.info("任务 {} 结束 删除文件 {}", manualCalculateTask.getId(), finalDeleteFiles.stream().map(File::getAbsolutePath).collect(Collectors.joining(Constant.COMMA)));
            finalDeleteFiles.forEach(FileUtils::deleteQuietly);
        }
    }

    /**
     * 解析DwdFileShmooDetail Consumer
     *
     * @param manualFileInfo  ManualFileInfo
     * @param dwdShmooDetails dwdFileShmooDetails
     * @return
     */
    public ExcelConsumer<Map<String, List<Pair<String, String>>>> getDwdFileShmooDetailConsumer(ManualFileInfo manualFileInfo, List<DwdShmooDetail> dwdShmooDetails) {
        return new ExcelConsumer<Map<String, List<Pair<String, String>>>>() {
            @Override
            public void dealLine(Map<String, List<Pair<String, String>>> filedMap) {
                DwdShmooDetail dwdShmooDetail = buildDwdFileShmooDetail(manualFileInfo);
                dwdShmooDetails.add(dwdShmooDetail);
                filedMap.forEach((field, pairs) -> {
                    BiConsumer<String, DwdShmooDetail> consumer = ShmooDataCommonUtil.SHMOO_DETAIL_FILE_CONSUMER_MAP.get(field);
                    if (consumer != null) {
                        String value = overWriteFieldMap.getOrDefault(field, formatFieldPairs(pairs));
                        try {
                            consumer.accept(value, dwdShmooDetail);
                        } catch (Exception e) {
                            throw new RuntimeException(String.format("RawData文件%s数据异常, 异常数据: columnName=%s, columnValue=%s", manualFileInfo.getFileName(), field, value), e);
                        }
                    }
                });
                if (dwdShmooDetails.size() >= batchSize) {
                    sinkDwdShmooDetail(manualFileInfo, dwdShmooDetails);
                }
            }

            @Override
            public void after() {
                if (CollectionUtils.isNotEmpty(dwdShmooDetails)) {
                    LOGGER.info("DwdShmooDetail余量数据写入ck, size: {}", dwdShmooDetails.size());
                    sinkDwdShmooDetail(manualFileInfo, dwdShmooDetails);
                }
            }
        };
    }

    /**
     * 解析DwdFileShmooVminVmax Consumer
     *
     * @param manualFileInfo          ManualFileInfo
     * @param dwdShmooVminVmaxDetails dwdFileShmooVminVmaxs
     * @return
     */
    public ExcelConsumer<Map<String, List<Pair<String, String>>>> getDwdFileShmooVminVmaxConsumer(ManualFileInfo manualFileInfo, List<DwdShmooVminVmaxDetail> dwdShmooVminVmaxDetails) {
        return new ExcelConsumer<Map<String, List<Pair<String, String>>>>() {
            @Override
            public void dealLine(Map<String, List<Pair<String, String>>> filedMap) {
                DwdShmooVminVmaxDetail dwdShmooVminVmaxDetail = buildDwdFileShmooVminVmax(manualFileInfo);
                dwdShmooVminVmaxDetails.add(dwdShmooVminVmaxDetail);
                filedMap.forEach((field, pairs) -> {
                    BiConsumer<String, DwdShmooVminVmaxDetail> consumer = ShmooDataCommonUtil.SHMOO_VMIN_FILE_CONSUMER_MAP.get(field);
                    if (consumer != null) {
                        consumer.accept(overWriteFieldMap.getOrDefault(field, formatFieldPairs(pairs)), dwdShmooVminVmaxDetail);
                    }
                });
                if (dwdShmooVminVmaxDetails.size() >= batchSize) {
                    sinkShmooVminVmaxDetail(manualFileInfo, dwdShmooVminVmaxDetails);
                }
            }

            @Override
            public void after() {
                if (CollectionUtils.isNotEmpty(dwdShmooVminVmaxDetails)) {
                    LOGGER.info("DwdShmooVminVmaxDetail余量数据写入ck, size: {}", dwdShmooVminVmaxDetails.size());
                    sinkShmooVminVmaxDetail(manualFileInfo, dwdShmooVminVmaxDetails);
                }
            }
        };
    }

    /**
     * format List<Pair<String, String>> -> String
     *
     * @param pairs pairs
     * @return
     */
    public String formatFieldPairs(List<Pair<String, String>> pairs) {
        String fieldValueStr = Constant.EMPTY;
        if (CollectionUtils.isNotEmpty(pairs)) {
            if (pairs.size() == 1) {
                fieldValueStr = pairs.get(0).getValue();
            } else {
                Map<String, String> pairMap = pairs.stream().collect(Collectors.toMap(Pair::getKey, Pair::getValue));
                fieldValueStr = JSON.toJSONString(pairMap);
            }
        }
        return fieldValueStr;
    }

    /**
     * 构建DwdFileShmooDetail
     *
     * @param manualFileInfo ManualFileInfo
     * @return
     */
    public DwdShmooDetail buildDwdFileShmooDetail(ManualFileInfo manualFileInfo) {
        return new DwdShmooDetail()
                .setFileId(manualFileInfo.getFileId())
                .setFileName(manualFileInfo.getFileName())
                .setCustomer(manualFileInfo.getCustomer())
                .setUploadType(manualFileInfo.getUploadType())
                .setLotType(manualFileInfo.getLotType())
                .setTestArea(manualFileInfo.getTestArea())
                .setDeviceId(manualFileInfo.getDeviceId())
                .setLotId(manualFileInfo.getLotId())
                .setTesterName(manualFileInfo.getTesterName())
                .setTestProgram(manualFileInfo.getTestProgram())
                .setTestTemperature(manualFileInfo.getTestTemperature())
                .setProberHandlerId(manualFileInfo.getProberHandlerId())
                .setProcess(manualFileInfo.getProcess())
                .setStartTime(manualFileInfo.getStartT() == null ? null : manualFileInfo.getStartT().getTime())
                .setEndTime(manualFileInfo.getFinishT() == null ? null : manualFileInfo.getFinishT().getTime())
                .setCreateTime(System.currentTimeMillis())
                .setCreateUser(manualFileInfo.getFileOwner())
                .setUploadTime(manualFileInfo.getCreateTime().getTime());
    }

    /**
     * 构建DwdFileShmooVminVmax
     *
     * @param manualFileInfo ManualFileInfo
     * @return
     */
    public DwdShmooVminVmaxDetail buildDwdFileShmooVminVmax(ManualFileInfo manualFileInfo) {
        return new DwdShmooVminVmaxDetail()
                .setFileId(manualFileInfo.getFileId())
                .setFileName(manualFileInfo.getFileName())
                .setCustomer(manualFileInfo.getCustomer())
                .setUploadType(manualFileInfo.getUploadType())
                .setLotType(manualFileInfo.getLotType())
                .setTestArea(manualFileInfo.getTestArea())
                .setDeviceId(manualFileInfo.getDeviceId())
                .setLotId(manualFileInfo.getLotId())
                .setTesterName(manualFileInfo.getTesterName())
                .setTestProgram(manualFileInfo.getTestProgram())
                .setTestTemperature(manualFileInfo.getTestTemperature())
                .setProberHandlerId(manualFileInfo.getProberHandlerId())
                .setProcess(manualFileInfo.getProcess())
                .setCreateTime(System.currentTimeMillis())
                .setCreateUser(manualFileInfo.getFileOwner())
                .setUploadTime(manualFileInfo.getCreateTime().getTime());
    }

    /**
     * 发送Manual任务进度消息
     *
     * @param manualCalculateTask ManualCalculateTask
     * @param dwLayer             ODS、DWD、ADS表示进度
     * @param exceptionType       ExceptionType
     * @param exceptionMessage    exceptionMessage
     * @return
     */
    private String sendManualWarehousingFinishMessage(ManualCalculateTask manualCalculateTask, DwLayer dwLayer, ExceptionType exceptionType, String exceptionMessage) {
        ManualCalculateFinishVo manualCalculateFinishVo = new ManualCalculateFinishVo()
                .setFileId(manualCalculateTask.getFileId())
                .setFileName(manualCalculateTask.getFileName())
                .setDwLayer(dwLayer);

        ManualWarehousingFinishMessage manualWarehousingFinishMessage = new ManualWarehousingFinishMessage()
                .setManualType(manualCalculateTask.getManualType())
                .setManualDeleteVo(null)
                .setManualCalculateFinishVo(manualCalculateFinishVo)
                .setProcessStatus(manualCalculateTask.getProcessStatus())
                .setExceptionType(exceptionType)
                .setExceptionMessage(exceptionMessage);

        String manualFinishMessageStr = JSON.toJSONString(manualWarehousingFinishMessage);
        kafkaSink.send(manualFinishTopic, manualCalculateTask.getFileId() + EMPTY, manualFinishMessageStr);

        return manualFinishMessageStr;
    }

    /**
     * ShmooDetail关键字校验
     *
     * @param dwdShmooDetails
     * @return
     */
    private Pair<Boolean, Set<String>> checkShmooDetailKeyField(List<DwdShmooDetail> dwdShmooDetails) {
        boolean checkFlag = true;
        Set<String> keyFieldNullList = new HashSet<>();

        for (Map.Entry<String, Predicate<DwdShmooDetail>> entry : SHMOO_DETAIL_CHECK_MAP.entrySet()) {
            String field = entry.getKey();
            Predicate<DwdShmooDetail> predicate = entry.getValue();
            for (DwdShmooDetail dwdShmooDetail : dwdShmooDetails) {
                if (!predicate.test(dwdShmooDetail)) {
                    checkFlag = false;
                    String exceptionMessage = FileLoadExceptionInfo.KEY_FIELD_NULL_EXCEPTION.getMessage().replace(KEY_FIELD, field);
                    ProcessLogThreadLocalUtil.appendErrorMessage(exceptionMessage);
                    keyFieldNullList.add(field);
                }
            }
        }
        return Pair.of(checkFlag, keyFieldNullList);
    }


    /**
     * ShmooVminVmaxDetail关键字校验
     *
     * @param dwdShmooVminVmaxDetails
     * @return
     */
    private Pair<Boolean, Set<String>> checkShmooVminVmaxDetailKeyField(List<DwdShmooVminVmaxDetail> dwdShmooVminVmaxDetails) {
        boolean checkFlag = true;

        Set<String> keyFieldNullList = new HashSet<>();

        for (Map.Entry<String, Predicate<DwdShmooVminVmaxDetail>> entry : SHMOO_VMIN_VMAX_DETAIL_CHECK_MAP.entrySet()) {
            String field = entry.getKey();
            Predicate<DwdShmooVminVmaxDetail> predicate = entry.getValue();
            for (DwdShmooVminVmaxDetail dwdShmooVminVmaxDetail : dwdShmooVminVmaxDetails) {
                if (!predicate.test(dwdShmooVminVmaxDetail)) {
                    checkFlag = false;
                    String exceptionMessage = FileLoadExceptionInfo.KEY_FIELD_NULL_EXCEPTION.getMessage().replace(KEY_FIELD, field);
                    ProcessLogThreadLocalUtil.appendErrorMessage(exceptionMessage);
                    keyFieldNullList.add(field);
                }
            }
        }
        return Pair.of(checkFlag, keyFieldNullList);
    }

    /**
     * 批量写入DWD_SHMOO_DETAIL
     * @param manualFileInfo ManualFileInfo
     * @param dwdShmooDetails DwdShmooDetail
     */
    private void sinkDwdShmooDetail(ManualFileInfo manualFileInfo, List<DwdShmooDetail> dwdShmooDetails) {
        if (CollectionUtils.isNotEmpty(dwdShmooDetails)) {
            // 校验必要字段非空
            Pair<Boolean, Set<String>> checkShmooDetailKeyField = checkShmooDetailKeyField(dwdShmooDetails);
            if (!checkShmooDetailKeyField.getKey()) {
                List<String> keyFieldNullList = new ArrayList<>(checkShmooDetailKeyField.getValue());
                String exceptionMessage = keyFieldNullList.stream().map(field -> FileLoadExceptionInfo.KEY_FIELD_NULL_EXCEPTION.getMessage().replace(KEY_FIELD, field)).collect(Collectors.joining(COMMA));
                LOGGER.error("{} 必要字段为空, exceptionMessage: {}", manualFileInfo.getFileName(), exceptionMessage);
                String errorMessage = "必要字段为空";
                throw new FileLoadException(FileLoadExceptionInfo.KEY_FIELD_NULL_EXCEPTION, errorMessage, keyFieldNullList)
                        .updateExceptionMessage(exceptionMessage);
            }

            LOGGER.info("读取SHMOO解析结果文件写入CK表dwd_shmoo_detail_cluster, dataSize: {}", dwdShmooDetails.size());
            dwdShmooDetailSink.doHandle(dwdShmooDetails);
            dwdShmooDetails.clear();
        }
    }

    /**
     * 批量写入DWD_SHMOO_VMIN_VMAX_DETAIL
     * @param manualFileInfo ManualFileInfo
     * @param dwdShmooVminVmaxsDetailDetails DwdShmooVminVmaxDetail
     */
    private void sinkShmooVminVmaxDetail(ManualFileInfo manualFileInfo, List<DwdShmooVminVmaxDetail> dwdShmooVminVmaxsDetailDetails) {
        if (CollectionUtils.isNotEmpty(dwdShmooVminVmaxsDetailDetails)) {
            // 校验必要字段非空
            Pair<Boolean, Set<String>> checkShmooVminVmaxDetailKeyField = checkShmooVminVmaxDetailKeyField(dwdShmooVminVmaxsDetailDetails);
            if (!checkShmooVminVmaxDetailKeyField.getKey()) {
                List<String> keyFieldNullList = new ArrayList<>(checkShmooVminVmaxDetailKeyField.getValue());
                String exceptionMessage = keyFieldNullList.stream().map(field -> FileLoadExceptionInfo.KEY_FIELD_NULL_EXCEPTION.getMessage().replace(KEY_FIELD, field)).collect(Collectors.joining(COMMA));
                LOGGER.error("{} 必要字段为空, exceptionMessage: {}", manualFileInfo.getFileName(), exceptionMessage);
                String errorMessage = "必要字段为空";
                throw new FileLoadException(FileLoadExceptionInfo.KEY_FIELD_NULL_EXCEPTION, errorMessage, keyFieldNullList)
                        .updateExceptionMessage(exceptionMessage);
            }

            LOGGER.info("读取SHMOO解析结果文件写入CK表dwd_shmoo_vmin_vmax_cluster, dataSize: {}", dwdShmooVminVmaxsDetailDetails.size());
            dwdShmooVminVmaxDetailSink.doHandle(dwdShmooVminVmaxsDetailDetails);
            dwdShmooVminVmaxsDetailDetails.clear();
        }
    }

}
