package com.guwave.onedata.dataware.collectx.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.collectx.configuration.HdfsConfig;
import com.guwave.onedata.dataware.collectx.configuration.SftpConfig;
import com.guwave.onedata.dataware.collectx.core.AbstractFTPSyncCore;
import com.guwave.onedata.dataware.collectx.core.IFTPSyncCore;
import com.guwave.onedata.dataware.collectx.entity.FTPFileAttr;
import com.guwave.onedata.dataware.collectx.entity.FileAttr;
import com.guwave.onedata.dataware.collectx.enums.CompressFileSuffixEnum;
import com.guwave.onedata.dataware.collectx.manager.thread.SftpCollectRuleThreadManager;
import com.guwave.onedata.dataware.collectx.manager.thread.FileRenameRuleThreadLocalUtil;
import com.guwave.onedata.dataware.collectx.manager.thread.SftpLogThreadLocalUtil;
import com.guwave.onedata.dataware.collectx.sink.KafkaSink;
import com.guwave.onedata.dataware.collectx.util.FileUtils;
import com.guwave.onedata.dataware.collectx.util.HDFSUtil;
import com.guwave.onedata.dataware.collectx.manager.thread.SftpSubCustomerThreadLocalUtil;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.dao.mysql.converter.ZipFileCollectionToStringConverter;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import com.guwave.onedata.dataware.dao.mysql.manager.FileWarehousingRecordManager;
import com.guwave.onedata.dataware.dao.mysql.manager.SourceStandardRuleManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.FileLoadingLogRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpFileDetailRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Slf4j
@Service
public class PullFileHandler {

    public static final List<FileCategory> NEED_CONVERT_FILE_CATEGORY_LIST = Lists.newArrayList(
            FileCategory.RAW_DATA,
            FileCategory.SUMMARY,
            FileCategory.LOT_RELATION,
            FileCategory.LOG,
            FileCategory.MAP,
            FileCategory.WAT,
            FileCategory.ZIP_SPLIT,
            FileCategory.WIP,
            FileCategory.BIT_MEM
    );

    @Autowired
    private SftpConfig sftpConfig;
    @Autowired
    private HdfsConfig hdfsConfig;
    @Autowired
    private HDFSUtil hdfsUtil;
    @Autowired
    private SftpFileDetailRepository sftpFileDetailRepository;
    @Autowired
    private FileLoadingLogRepository fileLoadingLogRepository;
    @Autowired
    private KafkaSink kafkaSink;
    @Autowired
    private FileWarehousingRecordManager fileWarehousingRecordManager;
    @Autowired
    private PullFileHandler pullFileHandler;
    @Autowired
    private SourceStandardRuleManager sourceStandardRuleManager;

    public List<FTPFileAttr> findNeedPullFiles(IFTPSyncCore ftpSyncCore) throws Exception {
        SftpCollectRule sftpCollectRule = SftpCollectRuleThreadManager.getSftpCollectRule();
        List<FTPFileAttr> ftpFileAttrs = new ArrayList<>();
        String remotePaths = sftpCollectRule.getRemotePaths();
        for (String remotePath : remotePaths.split(Constant.COMMA)) {
            log.info("准备扫描 {}", remotePath);
            int startSize = ftpFileAttrs.size();
            ftpSyncCore.listFTPFileInfo(remotePath, ftpFileAttrs);
            int endSize = ftpFileAttrs.size();
            log.info("{} 扫描到 {} 个文件", remotePath, endSize - startSize);
            SftpLogThreadLocalUtil.appendLogTxt(String.format("%s 扫描到 %s 个文件", remotePath, endSize - startSize));
        }
        log.info("sftpCollectRuleId : {},sftpBatchInfoId : {} 待拉取 {} 个文件", sftpCollectRule.getId(), sftpCollectRule.getNewSftpBatchInfo().getId(), ftpFileAttrs.size());
        return ftpFileAttrs;
    }

    public boolean pullFile(FTPFileAttr ftpFileAttr, IFTPSyncCore ftpSyncCore) {
        SftpCollectRule sftpCollectRule = SftpCollectRuleThreadManager.getSftpCollectRule();
        String localPath = FileUtils.getFileFullPath(sftpConfig.getLocalPath(), UUID.randomUUID().toString());
        File localPathDir = new File(localPath);
        if (!localPathDir.exists()) {
            localPathDir.mkdirs();
        }

        // 步骤开始时间
        Date allStepStartTime = new Date();
        String remoteFileFullName = ftpFileAttr.getFullName();
        Long remoteFileSize = null;
        try {
            String localFileFullName;
            try {
                // 下载文件到本地
                localFileFullName = ftpSyncCore.loadFile(remoteFileFullName, localPath);
            } catch (Exception e) {
                throw new FileLoadException(FileLoadExceptionInfo.PULL_FILE_FAIL, ExceptionUtils.getStackTrace(e), null);
            }
            remoteFileSize = new File(localFileFullName).length();

            List<FileAttr> fileAttrs = new ArrayList<>();
            Set<CompressFileSuffixEnum> needDecompressTypesConfig = Arrays.stream(sftpCollectRule.getNeedDecompressionTypes().split(COMMA)).map(CompressFileSuffixEnum::of).filter(Objects::nonNull).collect(Collectors.toSet());
            try {
                // 解压文件
                FileUtils.deCompressFile(fileAttrs, new File(localFileFullName), needDecompressTypesConfig);
            } catch (Exception e) {
                throw new FileLoadException(FileLoadExceptionInfo.DECOMPRESSION_FILE_FAIL, ExceptionUtils.getStackTrace(e), null);
            }

            try {
                // 根据配置的重命名规则重命名文件名
                renameFiles(fileAttrs, ftpFileAttr);
            } catch (Exception e) {
                throw new FileLoadException(FileLoadExceptionInfo.RENAME_FILE_FAIL, ExceptionUtils.getStackTrace(e), null);
            }

            HashSet<String> fileNameSet = new HashSet<>();
            Exception childFileDealException = null;
            for (FileAttr fileAttr : fileAttrs) {
                // 步骤开始时间
                Date stepStartTime = new Date();

                String unCompressFileName = fileAttr.getFileName();
                String unCompressFilePath = fileAttr.getFilePath();
                String unCompressFileFullName = fileAttr.getFullName();

                // 本地压缩zip的目录
                String zipCompressFileName = unCompressFileName + Constant.POINT + FileType.ZIP.getType();
                String zipCompressPath = new File(sftpConfig.getLocalZipCompressPath(), UUID.randomUUID().toString()).getAbsolutePath() + File.separator;
                org.apache.commons.io.FileUtils.forceMkdir(new File(zipCompressPath));
                String zipCompressFileFullName = zipCompressPath + zipCompressFileName;

                SftpFileDetail sftpFileDetail = null;
                try {
                    if (!fileNameSet.add(unCompressFileName)) {
                        log.info("{} 中存在重复的 {} ", remoteFileFullName, unCompressFileName);
                        SftpLogThreadLocalUtil.appendLogTxt(String.format("%s 中存在重复的 {} %s！", remoteFileFullName, unCompressFileName));
                        continue;
                    }

                    log.info("开始处理 {} 中的 {}", remoteFileFullName, unCompressFileName);
                    //根据解压文件重新判定数据文件类型
                    FileCategory fileCategory = AbstractFTPSyncCore.matchFileCategoryWithFileName(fileAttr.getOriginalFileName(), sftpCollectRule);
                    fileAttr.setFileCategory(fileCategory);
                    if (AbstractFTPSyncCore.NOT_NEED_PULL_FILE_CATEGORY_LIST.contains(fileCategory)) {
                        // 不处理
                        log.info("{} 中 {} 不进行下载！", remoteFileFullName, unCompressFileName);
                        SftpLogThreadLocalUtil.appendLogTxt(String.format("%s 中 %s, fileCategory=%s 不进行下载！", remoteFileFullName, unCompressFileName, fileCategory));
                        continue;
                    }

                    String tempName = StringUtils.replace(unCompressFileFullName, localPath, "");
                    int subPathIndex = tempName.lastIndexOf(SLASH);
                    //解压文件的子目录
                    String remoteSubPath = "";
                    if (subPathIndex > 0) {
                        remoteSubPath = tempName.substring(0, subPathIndex);
                    }
                    String remoteFileFullPath = FileUtils.getFileFullPath(ftpFileAttr.getFilePath(), remoteSubPath);
                    fileAttr.setRemoteFileFullPath(remoteFileFullPath);

                    // hdfs 上传的目录
                    String hdfsFullPath = String.format(
                            hdfsConfig.getHdfsRootPath(),
                            sftpCollectRule.getCustomer(),
                            sftpCollectRule.getTestArea().getArea(),
                            sftpCollectRule.getFactory(),
                            fileCategory.getCategory(),
                            DateFormatUtils.format(new Date(ftpFileAttr.getMTime()), "yyyyMMdd")
                    );
                    String hdfsFileFullName = FileUtils.getFileFullName(hdfsFullPath, zipCompressFileName);
                    fileAttr.setHdfsFileFullName(hdfsFileFullName);

                    fileAttr.setFileName(zipCompressFileName);
                    fileAttr.setFilePath(zipCompressPath);
                    fileAttr.setFullName(zipCompressFileFullName);

                    try {
                        // 尝试写入sftpFileDetail
                        sftpFileDetail = this.insertSftpFileDetail(ftpFileAttr, fileAttr);
                    } catch (DataIntegrityViolationException e) {
                        log.info("{} 待插入文件已经存在！{}", zipCompressFileName, e.getMessage());
                        SftpLogThreadLocalUtil.appendLogTxt(zipCompressFileName + " 待插入文件已经存在！");
                        continue;
                    }

                    // 把文件压缩到本地zip目录
                    FileUtils.compressFileToZip(unCompressFilePath, unCompressFileName, zipCompressPath);
                    File zipCompressFile = new File(zipCompressFileFullName);
                    if (!zipCompressFile.exists()) {
                        throw new RuntimeException("文件压缩失败！");
                    }
                    fileAttr.setSize(zipCompressFile.length());
                    sftpFileDetail.setFileSize(fileAttr.getSize());

                    // 上传到hdfs
                    hdfsUtil.uploadToHDFSByFileSystem(zipCompressFileFullName, hdfsFullPath, 1);
                    // 删除.crc校验文件
                    String crcFileName = Constant.POINT + zipCompressFileName + Constant.POINT + Constant.CRC_SUFFIX;
                    FileUtils.deleteFile(zipCompressPath, crcFileName);

                    // 1000->文件从ftp下载； 步骤成功
                    this.generateFileLoadingLog(sftpFileDetail, stepStartTime, null);
                    sftpFileDetail
                            .setTransferStatus(TransferStatus.SUCCESS)
                            .setUpdateTime(new Date());
                    sftpFileDetailRepository.save(sftpFileDetail);

                    log.info("{} 中的 {} 拉取完成", remoteFileFullName, unCompressFileName);

                    // 解析文件元数据,解析失败不影响入库主流程
                    if (sftpFileDetail.getConvertFlag() == 0) {
                        sourceStandardRuleManager.parseFileNameMetaData(sftpFileDetail);
                    }
                } catch (Exception e) {
                    if (sftpFileDetail != null) {
                        childFileDealException = new RuntimeException("存在子文件处理失败：" + sftpFileDetail.getLocalFileName());
                        // 说明应该此文件应该存到sftpFileDetail 但是失败了
                        sftpFileDetailRepository.deleteById(sftpFileDetail.getId());
                        // 1000->文件从ftp下载； 步骤失败
                        this.generateFileLoadingLog(sftpFileDetail, stepStartTime, e);
                        log.error("{} 中的 {} 处理异常：", remoteFileFullName, unCompressFileName, e);
                    }
                } finally {
                    FileUtils.deleteFile(new File(unCompressFileFullName));
                    org.apache.commons.io.FileUtils.deleteQuietly(new File(zipCompressPath));
                }
            }

            if (childFileDealException != null) {
                throw childFileDealException;
            }

            // 1000->文件从ftp下载； 步骤成功
            pullFileHandler.generateFileLoadingLog(ftpFileAttr, allStepStartTime, remoteFileSize, null);
            return true;
        } catch (Exception e) {
            log.error("文件：{} 处理失败！", remoteFileFullName, e);
            // 1000->文件从ftp下载； 步骤失败
            pullFileHandler.generateFileLoadingLog(ftpFileAttr, allStepStartTime, remoteFileSize, e);
            return false;
        } finally {
            org.apache.commons.io.FileUtils.deleteQuietly(localPathDir);
        }
    }


    /**
     * 重命名文件
     *
     * @param fileAttrs List<FileAttr>文件属性
     */
    private void renameFiles(List<FileAttr> fileAttrs, FTPFileAttr ftpFileAttr) throws IOException {
        // 获取重命名文件规则
        List<FileRenameRule> fileRenameRules = FileRenameRuleThreadLocalUtil.getFileRenameRules();
        if (CollectionUtils.isNotEmpty(fileRenameRules) && CollectionUtils.isNotEmpty(fileAttrs)) {
            for (FileAttr fileAttr : fileAttrs) {
                for (FileRenameRule fileRenameRule : fileRenameRules) {
                    String ruleExpression = fileRenameRule.getRuleExpression();
                    if (ftpFileAttr.getFullName().matches(ruleExpression)) {
                        String fullName = fileAttr.getFullName();
                        String filePath = fileAttr.getFilePath();
                        String fileName = fileAttr.getFileName();
                        String renameFileName;
                        if (Objects.equals(1, fileRenameRule.getUseTimestamp())) {
                            // 使用时间戳拼接
                            String createTime = String.valueOf(ftpFileAttr.getMTime());
                            renameFileName = FileUtils.renameFile(fileName, EMPTY, UNDER_LINE + createTime);
                        } else {
                            // 使用目录拼接
                            String[] directorys = Arrays.stream(ftpFileAttr.getFilePath().split(SLASH)).filter(str -> !str.isEmpty()).toArray(String[]::new);
                            // 设置的长度大于目录总长度时，使用全目录
                            Integer directoryNum = fileRenameRule.getDirectoryNum();
                            directoryNum = directoryNum > directorys.length ? directorys.length : directoryNum;
                            String[] lastDirectorys = Arrays.copyOfRange(directorys, directorys.length - directoryNum, directorys.length);
                            String prefix = String.join(UNDER_LINE, lastDirectorys) + UNDER_LINE;
                            renameFileName = FileUtils.renameFile(fileName, prefix, EMPTY);
                        }
                        File file = new File(fullName);
                        file.renameTo(new File(FileUtils.getFileFullName(filePath, renameFileName)));
                        FileUtils.setFileAttrInfo(fileAttr, filePath, renameFileName);
                        break;
                    }
                }
            }
        }
    }

    private SftpFileDetail insertSftpFileDetail(FTPFileAttr ftpFileAttr, FileAttr fileAttr) {
        Date date = new Date();
        SftpCollectRule sftpCollectRule = SftpCollectRuleThreadManager.getSftpCollectRule();
        String ftpFileAttrString = JSON.toJSONString(ftpFileAttr);
        int ftpFileAttrLen = Math.min(ftpFileAttrString.length(), 1024);
        String sourceFileNames = ftpFileAttr.getSourceFileNames();
        String cleanupTaskId = sourceFileNames == null ? null :
                sftpFileDetailRepository.findAllByLocalFileNameIn(ZipFileCollectionToStringConverter.getInstance().convertToEntityAttribute(sourceFileNames))
                        .stream()
                        .map(SftpFileDetail::getCleanupTaskIds)
                        .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                        .flatMap(t -> Stream.of(t.split(Constant.COMMA)).map(s -> Long.parseLong(s.trim())))
                        .distinct()
                        .sorted()
                        .map(String::valueOf)
                        .collect(Collectors.joining(Constant.COMMA));
        SftpFileDetail sftpFileDetail = new SftpFileDetail()
                .setCustomer(sftpCollectRule.getCustomer())
                .setSubCustomer(getSubCustomer(ftpFileAttr.getFullName()))
                .setFactory(sftpCollectRule.getFactory())
                .setFactorySite(sftpCollectRule.getFactorySite())
                .setFab(sftpCollectRule.getFab())
                .setFabSite(sftpCollectRule.getFabSite())
                .setTestArea(sftpCollectRule.getTestArea())
                .setBatchId(sftpCollectRule.getNewSftpBatchInfo().getId())
                .setBatchStatus(SftpBatchStatus.PROCESSING)
                .setFtpIp(sftpCollectRule.getServer())
                .setRemoteFileName(ftpFileAttr.getFileName())
                .setRemoteFileMtime(new Date(ftpFileAttr.getMTime()))
                .setLocalFileName(fileAttr.getFileName())
                .setOriginFileName(fileAttr.getOriginalFileName())
                .setFileType(FileType.ZIP)
                .setOriginFileSize(fileAttr.getOriginalSize())
                .setFileSize(fileAttr.getSize())
                .setFileCategory(fileAttr.getFileCategory())
                .setRemoteFilePath(fileAttr.getRemoteFileFullPath())
                .setRemoteOriginalFilePath(ftpFileAttr.getFilePath())
                .setLocalFilePath(fileAttr.getFullName())
                .setValidateStatus(null)
                .setTransferStatus(TransferStatus.PROCESSING)
                .setConvertFlag(matchConvertFlag(fileAttr.getFileCategory()))
                .setSourceSftpFileId(null)
                .setSourceFileNames(ftpFileAttr.getSourceFileNames())
                .setWarehousingMode(WarehousingMode.NORMAL)
                .setRepairLotWaferId(null)
                .setCleanupTaskIds(cleanupTaskId)
                .setProcessStatus(ProcessStatus.CREATE)
                .setRemoteFileAttrDetail(ftpFileAttrString.substring(0, ftpFileAttrLen))
                .setCreateTime(date)
                .setUpdateTime(date)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateUser(Constant.SYSTEM)
                .setHdfsFilePath(fileAttr.getHdfsFileFullName());

        sftpFileDetailRepository.save(sftpFileDetail);
        return sftpFileDetail;
    }

    private String getSubCustomer(String remoteFileOriginalPath) {
        List<SftpSubCustomerRule> sftpSubCustomers = SftpSubCustomerThreadLocalUtil.getSubCustomers();
        for (SftpSubCustomerRule data : sftpSubCustomers) {
            // 如果其路径前缀和data中的directory相同，则对subCustomer赋值
            if (remoteFileOriginalPath.startsWith(data.getDirectory())) {
                return data.getSubCustomer();
            }
        }
        return SftpCollectRuleThreadManager.getSftpCollectRule().getCustomer();
    }

    public static Integer matchConvertFlag(FileCategory fileCategory) {
        return NEED_CONVERT_FILE_CATEGORY_LIST.contains(fileCategory) ? 1 : 0;
    }


    private void generateFileLoadingLog(SftpFileDetail sftpFileDetail, Date stepStartTime, Exception exception) {
        Date date = new Date();
        String remoteOriginalFilePath = FileUtils.getFileFullPath(sftpFileDetail.getRemoteOriginalFilePath(), "");

        FileLoadException fileLoadException = null;
        if (exception != null) {
            fileLoadException = exception instanceof FileLoadException ? ((FileLoadException) exception) : new FileLoadException(FileLoadExceptionInfo.PULL_FTP_FILE_TO_HDFS_FAIL, ExceptionUtils.getStackTrace(exception), null);
        }

        FileLoadingLog fileLoadingLog = new FileLoadingLog()
                .setCustomer(sftpFileDetail.getCustomer())
                .setSubCustomer(sftpFileDetail.getSubCustomer())
                .setTestArea(sftpFileDetail.getTestArea())
                .setFactory(sftpFileDetail.getFactory())
                .setFactorySite(sftpFileDetail.getFactorySite())
                .setFab(sftpFileDetail.getFab())
                .setFabSite(sftpFileDetail.getFabSite())
                .setFileCategory(sftpFileDetail.getFileCategory())
                .setFileName(sftpFileDetail.getLocalFileName())
                .setOriginFileName(sftpFileDetail.getOriginFileName())
                .setOriginFileSize(sftpFileDetail.getOriginFileSize())
                .setFileSize(sftpFileDetail.getFileSize())
                .setConvertFlag(sftpFileDetail.getConvertFlag())
                .setHdfsPath(sftpFileDetail.getHdfsFilePath())
                .setFtpPath(remoteOriginalFilePath + sftpFileDetail.getRemoteFileName())
                .setFtpIp(sftpFileDetail.getFtpIp())
                .setBatchId(sftpFileDetail.getBatchId())
                .setRemoteFileMtime(sftpFileDetail.getRemoteFileMtime())
                .setStep(StepType.STEP_TYPE_1000.getStep())
                .setStepStartTime(stepStartTime)
                .setStepEndTime(date)
                .setSourceFileNames(sftpFileDetail.getSourceFileNames())
                .setWarehousingMode(sftpFileDetail.getWarehousingMode())
                .setRepairLotWaferId(sftpFileDetail.getRepairLotWaferId())
                .setCleanupTaskIds(sftpFileDetail.getCleanupTaskIds())
                .setProcessStatus(fileLoadException == null ? ProcessStatus.SUCCESS : ProcessStatus.FAIL)
                .setFailedType(fileLoadException == null ? null : fileLoadException.getFailedType())
                .setFailedFields(fileLoadException == null ? null : fileLoadException.getFailedFields())
                .setExceptionType(fileLoadException == null ? null : fileLoadException.getExceptionType())
                .setExceptionMessage(fileLoadException == null ? null : fileLoadException.getExceptionMessage())
                .setErrorMessage(fileLoadException == null ? null : ExceptionUtils.getStackTrace(exception))
                .setCreateTime(stepStartTime)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateTime(date)
                .setUpdateUser(Constant.SYSTEM);

        // 查询转换前数据，补充内容
        List<FileLoadingLog> sourceFileLoadingLogs = fileLoadingLogRepository.findAllByFileNameInAndStep(
                ZipFileCollectionToStringConverter.getInstance().convertToEntityAttribute(sftpFileDetail.getSourceFileNames()),
                StepType.STEP_TYPE_1000.getStep());
        if (CollectionUtils.isNotEmpty(sourceFileLoadingLogs)) {
            Set<String> uniqueWarningMessages = new HashSet<>();
            for (FileLoadingLog log : sourceFileLoadingLogs) {
                String warningMessage = log.getWarningMessage();
                if (warningMessage != null && !warningMessage.isEmpty()) {
                    uniqueWarningMessages.add(warningMessage);
                }
            }
            String concatenatedWarningMessages = String.join(Constant.COMMA, uniqueWarningMessages);
            fileLoadingLog.setDataIntegrityFileComment(sourceFileLoadingLogs.get(0).getDataIntegrityFileComment())
                    .setWarningMessage(concatenatedWarningMessages)
                    .setDataIntegrityFileLabel(sourceFileLoadingLogs.get(0).getDataIntegrityFileLabel());
        }
        insertFileLoadingLog(fileLoadingLog);
        fileWarehousingRecordManager.updateFileWarehousingStatus(Collections.singletonList(fileLoadingLog));
        // 发送处理失败通知
        if (fileLoadingLog.getProcessStatus() == ProcessStatus.FAIL) {
            kafkaSink.sendLoadEndFlagFailMessage(fileLoadingLog);
        }
    }

    public void generateFileLoadingLog(FTPFileAttr ftpFileAttr, Date stepStartTime, Long remoteFileSize, Exception exception) {
        SftpCollectRule sftpCollectRule = SftpCollectRuleThreadManager.getSftpCollectRule();
        FileLoadException fileLoadException = null;
        if (exception != null) {
            fileLoadException = exception instanceof FileLoadException ? ((FileLoadException) exception) : new FileLoadException(FileLoadExceptionInfo.PULL_FTP_FILE_TO_HDFS_FAIL, ExceptionUtils.getStackTrace(exception), null);
        }

        Date date = new Date();
        FileLoadingLog fileLoadingLog = new FileLoadingLog()
                .setCustomer(sftpCollectRule.getCustomer())
                .setSubCustomer(getSubCustomer(ftpFileAttr.getFullName()))
                .setTestArea(sftpCollectRule.getTestArea())
                .setFactory(sftpCollectRule.getFactory())
                .setFactorySite(sftpCollectRule.getFactorySite())
                .setFab(sftpCollectRule.getFab())
                .setFabSite(sftpCollectRule.getFabSite())
                .setFileCategory(ftpFileAttr.getFileCategory())
                .setFileName(ftpFileAttr.getFullName())
                .setOriginFileName(ftpFileAttr.getFullName())
                .setFileSize(remoteFileSize)
                .setOriginFileSize(ftpFileAttr.getSize())
                .setConvertFlag(exception == null ? 1 : 0)
                .setFtpPath(ftpFileAttr.getFullName())
                .setFtpIp(sftpCollectRule.getServer())
                .setBatchId(sftpCollectRule.getNewSftpBatchInfo().getId())
                .setRemoteFileMtime(new Date(ftpFileAttr.getMTime()))
                .setStep(StepType.STEP_TYPE_1000.getStep())
                .setStepStartTime(stepStartTime)
                .setStepEndTime(date)
                .setSourceFileNames(ftpFileAttr.getSourceFileNames())
                .setWarehousingMode(WarehousingMode.NORMAL)
                .setProcessStatus(fileLoadException == null ? ProcessStatus.SUCCESS : ProcessStatus.FAIL)
                .setFailedType(fileLoadException == null ? null : fileLoadException.getFailedType())
                .setFailedFields(fileLoadException == null ? null : fileLoadException.getFailedFields())
                .setExceptionType(fileLoadException == null ? null : fileLoadException.getExceptionType())
                .setExceptionMessage(fileLoadException == null ? null : fileLoadException.getExceptionMessage())
                .setErrorMessage(fileLoadException == null ? null : ExceptionUtils.getStackTrace(exception))
                .setCreateTime(stepStartTime)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateTime(date)
                .setUpdateUser(Constant.SYSTEM);
        insertFileLoadingLog(fileLoadingLog);
        fileWarehousingRecordManager.updateFileWarehousingStatus(Collections.singletonList(fileLoadingLog));
    }

    public FileLoadingLog insertFileLoadingLog(FileLoadingLog fileLoadingLog) {
        //插入前先删除 当前以及以后的所有行
        fileLoadingLogRepository.deleteByFileNameAndStep(fileLoadingLog.getFileName(), fileLoadingLog.getStep());
        fileLoadingLogRepository.save(fileLoadingLog);
        return fileLoadingLog;
    }

}
