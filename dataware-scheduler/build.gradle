plugins {
    id 'application'
}

description = 'dataware scheduler'

dependencies {
    implementation project(':dataware-bridge:dataware-bridge-api')
    implementation project(':dataware-common')
    implementation (project(':dataware-parser')) {
        exclude group: 'com.google.guava', module: 'guava'
    }
    api project(':dataware-dao:dataware-dao-ck')
    implementation project(':dataware-dao:dataware-dao-mysql')
    implementation project(':dataware-repair:dataware-repair-common')
    implementation enforcedPlatform("org.springframework.boot:spring-boot-dependencies:$springBootVersion")
    implementation group: 'com.fasterxml.jackson.dataformat', name: 'jackson-dataformat-xml', version: jacksonVersion

    implementation group: 'org.springframework.boot', name: 'spring-boot-starter'
    implementation group: 'org.springframework.boot', name: 'spring-boot-starter-data-redis', version: springBootVersion
    implementation group: 'org.springframework', name: 'spring-web'
    implementation group: 'com.fasterxml.jackson.core', name: 'jackson-databind'
    implementation group: 'org.springframework.kafka', name: 'spring-kafka'

    implementation "org.apache.spark:spark-launcher_$scalaBinaryVersion:$sparkVersion"

    implementation group: 'com.alibaba', name: 'fastjson', version: fastJsonVersion
    implementation group: 'commons-collections', name: 'commons-collections', version: commonsCollectionsVersion
    implementation group: 'org.redisson', name: 'redisson', version: redissonVersion

    implementation("com.guwave.gdp:common:$gdpCommonVersion") {
        transitive = false
    }

    implementation group: 'com.guwave.onedata', name: 'next-compute-api', version: nextComputeVersion
    implementation group: 'com.guwave.onedata', name: 'next-compute-common', version: nextComputeVersion
    implementation group: 'com.guwave.bigbrother', name: 'skyeye-driver-logback', version: skyeyeVersion

    implementation group: 'commons-codec', name: 'commons-codec', version: commonsCodecVersion
    implementation group: 'commons-io', name: 'commons-io', version: commonsIoVersion
    implementation group: 'org.apache.commons', name: 'commons-lang3', version: commonsLang3Version
    implementation group: 'org.apache.commons', name: 'commons-math3', version: commonsMathVersion
    implementation group: 'mysql', name: 'mysql-connector-java', version: mysqlVersion

    testImplementation group: 'org.springframework.boot', name: 'spring-boot-starter-test'
}

configurations {
    compile.exclude group: 'log4j', module: 'log4j'
    compile.exclude group: 'org.hibernate.validator', module: 'hibernate-validator'
    compile.exclude group: 'org.apache.tomcat.embed', module: 'tomcat-embed-websocket'
    compile.exclude group: 'org.slf4j', module: 'slf4j-log4j12'
}

compileJava {
    options.compilerArgs = ["-parameters"]
}

jar {
    enabled true
    manifest.attributes 'Main-Class': 'com.guwave.onedata.dataware.scheduler.Application'
}

application {
    mainClassName = 'com.guwave.onedata.dataware.scheduler.Application'
    applicationDistribution.from('src/main/resources/properties').into('properties')
}

distributions {
    main {
        contents {
            from('src/main/resources/shell/startup.sh') {
                into '.'
            }
        }
    }
}

startScripts {
    doLast {
        unixScript.text = unixScript.text.replaceAll(":\\\$APP_HOME/lib/(.*)\n", ":\\\$APP_HOME/lib/\\*\n")
    }
}
