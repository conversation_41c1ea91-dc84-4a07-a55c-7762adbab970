package com.guwave.onedata.dataware.source.agent.common.sink.ck;

import com.github.housepower.data.IDataType;
import com.github.housepower.data.type.DataTypeUInt32;
import com.github.housepower.data.type.complex.DataTypeFixedString;
import com.github.housepower.data.type.complex.DataTypeString;
import com.github.housepower.jdbc.ClickHouseArray;
import com.guwave.gdp.common.ck.CkOperate;
import com.guwave.onedata.dataware.dao.ck.connection.ClickHouseConnection;
import com.guwave.onedata.dataware.source.agent.common.handler.Handler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.sql.*;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

/**
 * Copyright (C), 2022, guwave
 * <p>
 * Sink
 * <AUTHOR>
 * @version 0.0.1
 * 2022-03-14 11:36:18
 */
public interface CkSink<T> {

    Logger LOGGER = LoggerFactory.getLogger(CkSink.class);

    default ClickHouseArray toArrayFromIntegerList(List<Integer> data) {
        return this.toArray(data, new DataTypeUInt32());
    }

    default ClickHouseArray toArrayFromLongList(List<Long> data) {
        return this.toArray(data, new DataTypeUInt32());
    }

    default ClickHouseArray toArrayFromStringList(List<String> data) {
        return this.toArray(data, new DataTypeString(StandardCharsets.UTF_8));
    }

    default <R> ClickHouseArray toArray(List<R> data, IDataType<?, ?> elementDataType) {
        Object[] elements = new Object[0];
        if (!CollectionUtils.isEmpty(data)) {
            elements = data.toArray();
        }
        return new ClickHouseArray(elementDataType, elements);
    }

    default Timestamp toTimestamp(Long data) {
        if (null == data) {
            return null;
        } else {
            return new Timestamp(data);
        }
    }

    default BigDecimal toBigDecimal(Double data) {
        if (null == data) {
            return null;
        } else {
            return BigDecimal.valueOf(data);
        }
    }

    default String stringValue(Object obj) {
        return (obj == null) ? EMPTY : obj.toString();
    }

    default String getOptimizeTableSql(String partition) {
        return OPTIMIZE_TABLE_TEMPLATE
                .replace(DB_NAME + POINT + TABLE_NAME, this.sql().split(SPLIT_LEFT_SMALL_BRACKETS)[0].split(SPACE)[2])
                .replace(PARTITION, partition);
    }

    /**
     * 处理
     * @param statement PreparedStatement
     * @param items     数据items
     */
    void handle(PreparedStatement statement, List<T> items) throws SQLException;


    default String sql() {
        String sql = String.format(
                "INSERT INTO {DB_NAME}.{TABLE_NAME}(%s) VALUES (%s)",
                String.join(COMMA, getColumns()),
                String.join(COMMA, Collections.nCopies(getColumns().size(), "?")));
        return sql.replace(DB_NAME, getDbName()).replace(TABLE_NAME, getTableName());
    }

    List<String> getColumns();

    String getTableName();

    String getDbName();

    default String getPartitionExpr() {
        return "";
    }

    default boolean sinkFlag() {
        return true;
    }

    default void doHandle(List<T> items) {
        Connection connection = null;
        try {
            connection = ClickHouseConnection.getConnection();
            assert connection != null;
            writeToCk(items, connection);
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    LOGGER.info("close connection failed");
                }
            }
        }
    }

    default void doHandle(List<T> items, String partition) {
        Connection connection = null;
        try {
            connection = ClickHouseConnection.getConnection(partition);
            assert connection != null;
            writeToCk(items, connection);
            optimizeTable(partition, connection);
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    LOGGER.info("close connection failed");
                }
            }
        }
    }

    default void writeToCk(List<T> items, Connection connection) {
        StopWatch sw = new StopWatch();
        sw.start();
        LOGGER.info("开始写入ck");
        PreparedStatement statement = null;
        try {
            assert connection != null;
            connection.setAutoCommit(false);
            statement = connection.prepareStatement(this.sql());
            this.handle(statement, items);
            statement.executeBatch();
            connection.commit();
        } catch (SQLException e) {
            LOGGER.error("sql解析失败", e);
            throw new RuntimeException(e);
        } finally {
            sw.stop();
            LOGGER.info("写入ck结束, 耗时: {}ms", sw.getTotalTimeMillis());
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    LOGGER.info("close statement failed");
                }
            }
        }
    }

    default void optimizeTable(String partition, Connection connection) {
        PreparedStatement statement = null;
        try {
            assert connection != null;
            connection.setAutoCommit(false);
            String sql = this.getOptimizeTableSql(partition);
            LOGGER.info("optimizeTable [{}] 开始执行", sql);
            statement = connection.prepareStatement(sql);
            statement.execute();
            connection.commit();
            LOGGER.info("optimizeTable [{}] 执行结束", sql);
        } catch (SQLException e) {
            LOGGER.error("sql解析失败", e);
        } finally {
            if (statement != null) {
                try {
                    statement.close();
                } catch (SQLException e) {
                    LOGGER.info("close statement failed");
                }
            }
        }
    }

    default Integer getLotBucket(String lotId, Integer lotBucketNum){
        return Math.abs(lotId.hashCode()) % lotBucketNum;
    }

    /**
     * 构造HBIN/SBIN：HBIN/SBIN + binNum + "-" + binNam
     */
    default String buildBin(String prefix, Long binNum, String binNam){
        return binNum != null ? prefix + binNum + MIDDLE_LINE + binNam : EMPTY;
    }

    default void writeCkAfterTombstone(List<T> items, String customer, String factory, String testArea, String lotId, Set<String> waferNos, String deviceId, String lotType, String testStage,
                                       Integer lotBucket, String address, String[] addressList, String userName, String password, Long currentTimeMillis) {
        CkOperate.tombstoneCk(customer, factory, testArea, lotId, lotType, testStage, deviceId, lotBucket, String.join(COMMA, waferNos), getDbName() + POINT + getTableName(), address, addressList, userName, password, getPartitionExpr(), currentTimeMillis);
        doHandle(items);
    }

    default void writeCk(List<T> items) {
        doHandle(items);
    }

    default void writeCkByPartitionAfterTombstone(List<T> items, String partition, String customer, String factory, String testArea, String lotId, Set<String> waferNos, String deviceId, String lotType, String testStage,
                                                  Integer lotBucket, String address, String[] addressList, String userName, String password, Long currentTimeMillis) {
        CkOperate.tombstoneCk(customer, factory, testArea, lotId, lotType, testStage, deviceId, lotBucket, String.join(COMMA, waferNos), getDbName() + POINT + getTableName(), address, addressList, userName, password, getPartitionExpr(), currentTimeMillis);
        doHandle(items, partition);
    }

    default void writeCkByPartition(List<T> items, String partition) {
        doHandle(items, partition);
    }
}
