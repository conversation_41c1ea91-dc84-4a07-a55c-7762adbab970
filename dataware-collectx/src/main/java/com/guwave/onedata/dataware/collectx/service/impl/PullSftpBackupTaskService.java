package com.guwave.onedata.dataware.collectx.service.impl;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.collectx.core.FTPSyncCore;
import com.guwave.onedata.dataware.collectx.core.IFTPSyncCore;
import com.guwave.onedata.dataware.collectx.core.LocalSyncCore;
import com.guwave.onedata.dataware.collectx.core.SFTPSyncCore;
import com.guwave.onedata.dataware.collectx.entity.FTPFileAttr;
import com.guwave.onedata.dataware.collectx.manager.redis.SftpFileRedisManager;
import com.guwave.onedata.dataware.collectx.manager.thread.SftpBackupRuleThreadManager;
import com.guwave.onedata.dataware.collectx.manager.thread.SftpConnectConfThreadManager;
import com.guwave.onedata.dataware.collectx.util.ThreadPoolUtils;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.util.AESUtil;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpBackupBatchInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpBackupFileDetail;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpBackupRule;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpBackupBatchInfoRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpBackupFileDetailRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpBackupRuleRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;
import static com.guwave.onedata.dataware.common.contant.Constant.SYSTEM;

@Slf4j
@Service
public class PullSftpBackupTaskService {
    public static final String CAN_NOT_EMPTY = "不能为空";

    public static final Map<FtpType, Function<FTPServerType, IFTPSyncCore>> ftpSyncCoreMap = new HashMap<FtpType, Function<FTPServerType, IFTPSyncCore>>() {{

        put(FtpType.FTP, FTPSyncCore::new);
        put(FtpType.SFTP, SFTPSyncCore::new);
        put(FtpType.LOCAL, LocalSyncCore::new);
    }};

    private ThreadPoolExecutor THREAD_POOL_EXECUTOR;

    private final static String THREAD_PREFIX = "pull_sftp_backup_task";

    private final AtomicInteger threadActiveCount = new AtomicInteger(0);

    private final Long DAYS_FOR_ONE_SCHEDULER = 1L;

    @Value("${spring.backup.maxFailCnt}")
    private Integer maxFailCnt;

    @Autowired
    private SftpBackupRuleRepository sftpBackupRuleRepository;
    @Autowired
    private SftpBackupBatchInfoRepository sftpBackupBatchInfoRepository;
    @Autowired
    private SftpBackupFileDetailRepository sftpBackupFileDetailRepository;
    @Autowired
    private BackupFileHandler backupFileHandler;

    @Value("${spring.pullBackupTask.backupCorePoolSize}")
    private Integer convertCorePoolSize;

    @PostConstruct
    public void init() {
        THREAD_POOL_EXECUTOR = ThreadPoolUtils.getNewThreadPoolExecutor(
                THREAD_PREFIX, convertCorePoolSize, convertCorePoolSize, convertCorePoolSize
        );
    }

    public void pullTask() {
        if (threadActiveCount.get() >= THREAD_POOL_EXECUTOR.getCorePoolSize()) {
            log.info("{} 线程池已达最大处理数！", THREAD_PREFIX);
            return;
        }

        // 获取备份文件的任务
        SftpBackupRule sftpBackupRule = getTask();
        if (sftpBackupRule == null) {
            return;
        }

        // 执行备份文件任务
        threadActiveCount.incrementAndGet();
        THREAD_POOL_EXECUTOR.execute(() -> {
            try {
                runTask((sftpBackupRule));
            } finally {
                threadActiveCount.decrementAndGet();
            }
        });

    }

    private SftpBackupRule getTask() {
        String backupRuleId = SftpFileRedisManager.pullOneBackupRule();
        if (StringUtils.isBlank(backupRuleId)) {
            log.info("pullTask 没有待备份任务");
            return null;
        }

        Optional<SftpBackupRule> sftpBackupRuleOptional = sftpBackupRuleRepository.findById(Long.valueOf(backupRuleId));
        if (!sftpBackupRuleOptional.isPresent() || !Objects.equals(0, sftpBackupRuleOptional.get().getDeleteFlag())) {
            log.info("BackupRuleId : {} 对应的mysql记录不存在", backupRuleId);
            return null;
        }

        SftpBackupRule sftpBackupRule = sftpBackupRuleOptional.get();
        if (!Integer.valueOf(1).equals(sftpBackupRule.getStatus())) {
            log.info("BackupRuleId : {} 未开启", backupRuleId);
            return null;
        }

        return sftpBackupRule;
    }

    private void runTask(SftpBackupRule sftpBackupRule) {
        boolean lockBackupRuleId = SftpFileRedisManager.lockBackupRuleId(sftpBackupRule.getId().toString());
        if (!lockBackupRuleId) {
            log.info("BackupRuleId : {} 已被上锁处理", sftpBackupRule.getId());
            return;
        }

        try {
            // 填充备份任务相关信息
            fillSftpBackupRule(sftpBackupRule);

            IFTPSyncCore sourceFtpSyncCore = null;
            IFTPSyncCore targetFtpSycnCore = null;
            try {
                log.info("开始执行备份任务: {}", sftpBackupRule.getNewSftpBackupBatchInfo().getBackupRuleSnapshot());

                // 设置配置
                SftpBackupRuleThreadManager.setSftpBackupRule(sftpBackupRule);
                SftpConnectConfThreadManager.setSftpConnectConf(sftpBackupRule);
                sourceFtpSyncCore = ftpSyncCoreMap.get(sftpBackupRule.getSourceFtpType()).apply(FTPServerType.SOURCE);
                targetFtpSycnCore = ftpSyncCoreMap.get(sftpBackupRule.getTargetFtpType()).apply(FTPServerType.TARGET);

                // 新建一条sftpBackupRule的batch记录
                sftpBackupBatchInfoRepository.save(sftpBackupRule.getNewSftpBackupBatchInfo());

                // 获取需要备份的文件
                List<SftpBackupFileDetail> failFiles = sftpBackupFileDetailRepository.findAllByBackupRuleIdAndProcessStatusAndFailCntLessThan(sftpBackupRule.getId(), ProcessStatus.FAIL, maxFailCnt);
                List<FTPFileAttr> needBackupFiles = backupFileHandler.findNeedPullFiles(sourceFtpSyncCore, failFiles).stream()
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList());

                // 开始备份文件
                int successBackupFileCnt = 0;
                for (FTPFileAttr needBackupFile : needBackupFiles) {
                    SftpBackupFileDetail backupFailFileDetail = failFiles.stream()
                            .filter(t -> (t.getSourceFilePath().equals(needBackupFile.getFilePath()) && t.getFileName().equals(needBackupFile.getFileName())))
                            .findAny().orElse(null);

                    boolean backupFlag = backupFileHandler.backupFile(sourceFtpSyncCore, targetFtpSycnCore, needBackupFile, backupFailFileDetail);
                    if (!backupFlag) {
                        log.error("文件:{} 备份失败", needBackupFile.getFilePath() + needBackupFile.getFileName());
                    } else {
                        successBackupFileCnt++;
                    }
                }
                log.info("从 {} 个文件中成功备份 {} 个文件", needBackupFiles.size(), successBackupFileCnt);

                // 更新这次batch中已备份文件的状态
                sftpBackupFileDetailRepository.updateBatchState(sftpBackupRule.getNewSftpBackupBatchInfo().getId(), BatchInfoStatus.SUCCESS);

                // 更新batch
                sftpBackupRule.getNewSftpBackupBatchInfo()
                        .setBatchTotalBackupFiles(needBackupFiles.size())
                        .setBatchSuccessBackupFiles(needBackupFiles.size())
                        .setSftpFileLastMtime(sftpBackupRule.getFileModifiedRealEndDate())
                        .setBatchStatus(BatchInfoStatus.SUCCESS)
                        .setUpdateTime(new Date());
                sftpBackupBatchInfoRepository.save(sftpBackupRule.getNewSftpBackupBatchInfo());

                log.info("备份任务成功：{} ", sftpBackupRule.getNewSftpBackupBatchInfo().getBackupRuleSnapshot());
            } catch (Exception e) {
                log.error("备份任务异常：{} ", sftpBackupRule.getNewSftpBackupBatchInfo().getBackupRuleSnapshot());

                sftpBackupRule.getNewSftpBackupBatchInfo()
                        .setBatchStatus(BatchInfoStatus.FAIL)
                        .setSftpFileLastMtime(sftpBackupRule.getFileModifiedRealStartDate())
                        .setExceptionType(FileLoadExceptionInfo.OTHER_EXCEPTION.getType())
                        .setExceptionMessage(FileLoadExceptionInfo.OTHER_EXCEPTION.getMessage())
                        .setErrorMessage(ExceptionUtils.getStackTrace(e))
                        .setUpdateTime(new Date());

                // 更新此batchId对应备份的文件的batch_status-->SUCCESS
                if (sftpBackupRule.getNewSftpBackupBatchInfo().getId() != null) {
                    sftpBackupFileDetailRepository.updateBatchState(sftpBackupRule.getNewSftpBackupBatchInfo().getId(), BatchInfoStatus.SUCCESS);
                }

                sftpBackupBatchInfoRepository.save(sftpBackupRule.getNewSftpBackupBatchInfo());
            } finally {
                // 清除配置
                SftpBackupRuleThreadManager.clear();
                SftpConnectConfThreadManager.clear();
                // 关闭连接
                if (sourceFtpSyncCore != null) {
                    sourceFtpSyncCore.closeConnect();
                }
                if (targetFtpSycnCore != null && targetFtpSycnCore.isConnected()) {
                    targetFtpSycnCore.closeConnect();
                }
            }

        } catch (Exception e) {
            log.error("BackupRuleId : {} 异常", sftpBackupRule.getId(), e);
        } finally {
            SftpFileRedisManager.unLockBackupRuleId(sftpBackupRule.getId().toString());
        }
    }

    private void fillSftpBackupRule(SftpBackupRule sftpBackupRule) {
        Long backupRuleId = sftpBackupRule.getId();
        List<SftpBackupBatchInfo> stpBackupBatchInfos = sftpBackupBatchInfoRepository.findAllByBackupRuleIdOrderByIdDesc(backupRuleId);
        SftpBackupBatchInfo latestSftpBackupBatchInfo = CollectionUtils.isNotEmpty(stpBackupBatchInfos) ? stpBackupBatchInfos.get(0) : null;

        long currentTime = System.currentTimeMillis();

        // 若已经运行了很久，则抛出异常
        if (latestSftpBackupBatchInfo != null) {
            long sftpBackupBatchInfoExpireTime = 24L * 60L * 60L * 1000L;
            if (latestSftpBackupBatchInfo.getBatchStatus() == BatchInfoStatus.PROCESSING) {
                long difTime = currentTime - latestSftpBackupBatchInfo.getCreateTime().getTime();
                if (difTime > sftpBackupBatchInfoExpireTime) {
                    throw new RuntimeException("backupRuleId : " + backupRuleId + " 长时间在运行中  已运行 : " + difTime / 1000 + "秒");
                }
            }
        }

        // 校验备份文件规则
        validateSftpBackupRule(sftpBackupRule);

        // 设置本次备份文件的起止时间
        long realStartTime = sftpBackupRule.getFileModifiedStartDate().getTime();
        long realEndTime;
        if (sftpBackupRule.getBackupBeforeDays() != null && sftpBackupRule.getFileModifiedEndDate() != null) {
            realEndTime = Math.min(sftpBackupRule.getFileModifiedEndDate().getTime(), currentTime - sftpBackupRule.getBackupBeforeDays() * 24 * 60L * 60L * 1000L);
        } else if (sftpBackupRule.getBackupBeforeDays() != null) {
            realEndTime = currentTime - sftpBackupRule.getBackupBeforeDays() * 24 * 60L * 60L * 1000L;
        } else {
            realEndTime = sftpBackupRule.getFileModifiedEndDate().getTime();
        }
        if (latestSftpBackupBatchInfo != null && latestSftpBackupBatchInfo.getSftpFileLastMtime() != null) {
            // 从上次备份的记录中获取开始时间和结束时间
            realStartTime = Math.max(realStartTime, latestSftpBackupBatchInfo.getSftpFileLastMtime().getTime());
            realEndTime = Math.min(realEndTime, latestSftpBackupBatchInfo.getSftpFileLastMtime().getTime() + DAYS_FOR_ONE_SCHEDULER * 24 * 60L * 60L * 1000L);
        } else {
            // 该rule第一次备份，将时间设置为第一天
            realEndTime = Math.min(realEndTime, realStartTime + DAYS_FOR_ONE_SCHEDULER * 24 * 60L * 60L * 1000L);
        }
        if (realStartTime >= realEndTime) {
            throw new RuntimeException("BackupRuleId : " + backupRuleId + " 真实的起始备份时间 " + realStartTime + " 不小于 真实的结束备份时间 " + realEndTime + " 不进行备份");
        }

        sftpBackupRule
                .setFileModifiedRealStartDate(new Date(realStartTime))
                .setFileModifiedRealEndDate(new Date(realEndTime))
                .setLatestSftpBackupBatchInfo(latestSftpBackupBatchInfo);

        SftpBackupBatchInfo newSftpBackupBatchInfo = new SftpBackupBatchInfo()
                .setBackupRuleId(backupRuleId)
                .setSftpFileLastMtime(new Date(realStartTime))
                .setBackupType(sftpBackupRule.getBackupType())
                .setBatchStatus(BatchInfoStatus.PROCESSING)
                .setBackupRuleSnapshot(JSON.toJSONString(sftpBackupRule))
                .setCreateTime(new Date(currentTime))
                .setUpdateTime(new Date(currentTime))
                .setCreateUser(SYSTEM)
                .setUpdateUser(SYSTEM);

        sftpBackupRule.setNewSftpBackupBatchInfo(newSftpBackupBatchInfo);
    }

    private void validateSftpBackupRule(SftpBackupRule sftpBackupRule) {
        StringBuilder errorMessageSb = new StringBuilder();

        // 校验源服务器的FTP参数
        FtpType sourceFtpType = sftpBackupRule.getSourceFtpType();
        if (sourceFtpType == null) {
            errorMessageSb.append("文件服务器类型").append(CAN_NOT_EMPTY).append(ENTER);
        } else if (sourceFtpType != FtpType.LOCAL) {
            if (sourceFtpType == FtpType.FTP) {
                if (sftpBackupRule.getSourceFtpUseTls() == null) {
                    sftpBackupRule.setSourceFtpUseTls(0);
                }
            } else {
                sftpBackupRule.setSourceFtpUseTls(0);
            }
            if (StringUtils.isBlank(sftpBackupRule.getSourceFtpServer())) {
                errorMessageSb.append("源文件服务器类型非LOCAL时,IP地址").append(CAN_NOT_EMPTY).append(ENTER);
            }
            if (sftpBackupRule.getSourceFtpPort() == null) {
                errorMessageSb.append("源文件服务器类型非LOCAL时,端口").append(CAN_NOT_EMPTY).append(ENTER);
            }
            if (StringUtils.isBlank(sftpBackupRule.getSourceFtpUsername())) {
                errorMessageSb.append("源文件服务器类型非LOCAL时,用户名").append(CAN_NOT_EMPTY).append(ENTER);
            }
            sftpBackupRule.setSourceDecryptPassword(AESUtil.Decrypt(sftpBackupRule.getSourceFtpPassword()));
            if (StringUtils.isBlank(sftpBackupRule.getSourceFtpPassword()) || StringUtils.isBlank(sftpBackupRule.getSourceDecryptPassword())) {
                errorMessageSb.append("源文件服务器类型非LOCAL时,密码").append(CAN_NOT_EMPTY).append(ENTER);
            }
            if (sftpBackupRule.getTimeout() == null || sftpBackupRule.getTimeout() < 0) {
                sftpBackupRule.setTimeout(180);
            } else if (sftpBackupRule.getTimeout() > 600) {
                sftpBackupRule.setTimeout(600);
            }
            if (sftpBackupRule.getReconnectCount() == null || sftpBackupRule.getReconnectCount() < 0) {
                sftpBackupRule.setReconnectCount(5);
            } else if (sftpBackupRule.getReconnectCount() > 20) {
                sftpBackupRule.setReconnectCount(20);
            }
            if (sftpBackupRule.getTimeDifference() == null) {
                sftpBackupRule.setTimeDifference(0L);
            }
        } else {
            if (StringUtils.isBlank(sftpBackupRule.getSourceFtpServer())) {
                errorMessageSb.append("源文件服务器类型是LOCAL时,IP地址").append(CAN_NOT_EMPTY).append(ENTER);
            }
            sftpBackupRule.setSourceFtpPort(0).setSourceFtpUsername(EMPTY).setSourceFtpPassword(EMPTY);
        }

        // 校验备份文件服务器的FTP参数
        FtpType targetFtpType = sftpBackupRule.getTargetFtpType();
        if (targetFtpType == null) {
            errorMessageSb.append("文件服务器类型").append(CAN_NOT_EMPTY).append(ENTER);
        } else if (targetFtpType != FtpType.LOCAL) {
            if (targetFtpType == FtpType.FTP) {
                if (sftpBackupRule.getTargetFtpUseTls() == null) {
                    sftpBackupRule.setTargetFtpUseTls(0);
                }
            } else {
                sftpBackupRule.setTargetFtpUseTls(0);
            }
            if (StringUtils.isBlank(sftpBackupRule.getTargetFtpServer())) {
                errorMessageSb.append("备份文件服务器类型非LOCAL时,IP地址").append(CAN_NOT_EMPTY).append(ENTER);
            }
            if (sftpBackupRule.getTargetFtpPort() == null) {
                errorMessageSb.append("备份文件服务器类型非LOCAL时,端口").append(CAN_NOT_EMPTY).append(ENTER);
            }
            if (StringUtils.isBlank(sftpBackupRule.getTargetFtpUsername())) {
                errorMessageSb.append("备份文件服务器类型非LOCAL时,用户名").append(CAN_NOT_EMPTY).append(ENTER);
            }
            sftpBackupRule.setTargetDecryptPassword(AESUtil.Decrypt(sftpBackupRule.getTargetFtpPassword()));
            if (StringUtils.isBlank(sftpBackupRule.getTargetFtpPassword()) || StringUtils.isBlank(sftpBackupRule.getTargetDecryptPassword())) {
                errorMessageSb.append("备份文件服务器类型非LOCAL时,密码").append(CAN_NOT_EMPTY).append(ENTER);
            }
            if (sftpBackupRule.getTimeout() == null || sftpBackupRule.getTimeout() < 0) {
                sftpBackupRule.setTimeout(180);
            } else if (sftpBackupRule.getTimeout() > 600) {
                sftpBackupRule.setTimeout(600);
            }
            if (sftpBackupRule.getReconnectCount() == null || sftpBackupRule.getReconnectCount() < 0) {
                sftpBackupRule.setReconnectCount(5);
            } else if (sftpBackupRule.getReconnectCount() > 20) {
                sftpBackupRule.setReconnectCount(20);
            }
            if (sftpBackupRule.getTimeDifference() == null) {
                sftpBackupRule.setTimeDifference(0L);
            }
        } else {
            if (StringUtils.isBlank(sftpBackupRule.getTargetFtpServer())) {
                errorMessageSb.append("备份文件服务器类型是LOCAL时,IP地址").append(CAN_NOT_EMPTY).append(ENTER);
            }
            sftpBackupRule.setTargetFtpPort(0).setTargetFtpUsername(EMPTY).setTargetFtpPassword(EMPTY);
        }

        if (sftpBackupRule.getFileModifiedStartDate() == null) {
            errorMessageSb.append("待备份的文件的起始时间").append(CAN_NOT_EMPTY).append(ENTER);
        }
        if (sftpBackupRule.getFileModifiedEndDate() == null && sftpBackupRule.getBackupBeforeDays() == null) {
            errorMessageSb.append("待备份的文件的结束时间和备份n天前的文件不能同时为空").append(ENTER);
        } else if (sftpBackupRule.getBackupBeforeDays() != null && sftpBackupRule.getBackupBeforeDays() < 7) {
            log.info("最多只能备份七天前的文件");
            sftpBackupRule.setBackupBeforeDays(7);
        }

        if (sftpBackupRule.getBackupType() == null) {
            errorMessageSb.append("备份类型").append(CAN_NOT_EMPTY).append(ENTER);
        }

        if (StringUtils.isBlank(sftpBackupRule.getSourcePaths())) {
            errorMessageSb.append("待备份的文件服务器的路径").append(CAN_NOT_EMPTY).append(ENTER);
        }

        String errorMessage = errorMessageSb.toString();
        if (!errorMessage.isEmpty()) {
            FileLoadException exception = new FileLoadException(FileLoadExceptionInfo.PARAM_CKECK_FAIL, errorMessage, null).updateExceptionMessage(errorMessage);

            SftpBackupBatchInfo newSftpBackupBatchInfo = new SftpBackupBatchInfo()
                    .setBackupRuleId(sftpBackupRule.getId())
                    .setBackupType(sftpBackupRule.getBackupType())
                    .setSftpFileLastMtime(null)
                    .setBatchStatus(BatchInfoStatus.FAIL)
                    .setBackupRuleSnapshot(JSON.toJSONString(sftpBackupRule))
                    .setExceptionType(exception.getExceptionType())
                    .setExceptionMessage(exception.getExceptionMessage())
                    .setErrorMessage(exception.getErrorMessage())
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date())
                    .setCreateUser(SYSTEM)
                    .setUpdateUser(SYSTEM);
            sftpBackupBatchInfoRepository.save(newSftpBackupBatchInfo);
            throw exception;
        }
    }
}
