package com.guwave.onedata.dataware.collectx.service;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.collectx.configuration.HdfsConfig;
import com.guwave.onedata.dataware.collectx.core.AbstractFTPSyncCore;
import com.guwave.onedata.dataware.collectx.entity.FileAttr;
import com.guwave.onedata.dataware.collectx.enums.CompressFileSuffixEnum;
import com.guwave.onedata.dataware.collectx.manager.redis.FileConvertRedisManager;
import com.guwave.onedata.dataware.collectx.service.impl.PullFileHandler;
import com.guwave.onedata.dataware.collectx.sink.KafkaSink;
import com.guwave.onedata.dataware.collectx.util.FileUtils;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.dao.mysql.converter.ZipFileCollectionToStringConverter;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.FileLoadingLog;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpBatchInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpCollectRule;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpFileDetail;
import com.guwave.onedata.dataware.dao.mysql.manager.FileWarehousingRecordManager;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.FileLoadingLogRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpBatchInfoRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpFileDetailRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Pageable;

import java.io.*;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.COMMA;

public interface ZipSplitConvertService extends ConvertService {
    Logger log = LoggerFactory.getLogger(ZipSplitConvertService.class);

    default void convertAsync() {
        ThreadPoolExecutor threadPoolExecutor = getThreadPoolExecutor();
        if (getThreadActiveCount().get() >= threadPoolExecutor.getCorePoolSize()) {
            log.info("线程池已达最大处理数！");
            return;
        }

        List<SftpFileDetail> needConvertFiles = getNeedConvertFiles();
        if (CollectionUtils.isEmpty(needConvertFiles)) {
            log.info("没有待处理文件！");
            return;
        }

        SftpFileDetail needConvertFile = needConvertFiles.get(0);

        // 执行转换
        getThreadActiveCount().incrementAndGet();
        threadPoolExecutor.execute(() -> {
                    try {
                        convert(needConvertFile);
                    } finally {
                        getThreadActiveCount().decrementAndGet();
                    }
                }
        );
    }

    default void convert(SftpFileDetail needConvertFile) {
        // 先把更新时间更新
        needConvertFile
                .setUpdateTime(new Date())
                .setUpdateUser(Constant.SYSTEM);
        getSftpFileDetailRepository().updateUpdateTime(needConvertFile.getId(), needConvertFile.getUpdateTime(), needConvertFile.getUpdateUser(), needConvertFile.getProcessStatus());

        boolean lockZipSplitConvert = FileConvertRedisManager.lockZipSplitConvert(needConvertFile.getRemoteOriginalFilePath());
        if (!lockZipSplitConvert) {
            log.info("ZipSplit {} 正在被处理", needConvertFile.getRemoteOriginalFilePath());
            return;
        }

        try {
            needConvertFile = getSftpFileDetailRepository().findById(needConvertFile.getId()).orElse(needConvertFile);
            if (needConvertFile.getProcessStatus() != ProcessStatus.CREATE) {
                log.info(" {} 已被处理", needConvertFile.getLocalFileName());
                return;
            }

            SftpBatchInfo sftpBatchInfo = null;
            List<SftpBatchInfo> sftpBatchInfos = new ArrayList<>();
            Optional<SftpBatchInfo> sftpBatchInfoOptional = getSftpBatchInfoRepository().findById(needConvertFile.getBatchId());
            if (sftpBatchInfoOptional.isPresent()) {
                sftpBatchInfo = sftpBatchInfoOptional.get();
                sftpBatchInfos = getSftpBatchInfoRepository().findAllByCollectRuleIdOrderByIdDesc(sftpBatchInfoOptional.get().getCollectRuleId(), Pageable.ofSize(1));
            }
            if (CollectionUtils.isNotEmpty(sftpBatchInfos)) {
                SftpBatchInfo latestSftpBatchInfo = sftpBatchInfos.get(0);
                if (latestSftpBatchInfo.getSftpFileLastMtime() == null || latestSftpBatchInfo.getSftpFileLastMtime().getTime() - (getZipSplitFileMaxWaitMillion() * 60000L) < needConvertFile.getRemoteFileMtime().getTime()) {
                    // 未达到zipSplit文件的最大等待时间
                    log.info("{} 未达到zipSplit文件的最大等待时间", needConvertFile.getLocalFileName());
                    return;
                }
            }

            String uuid = UUID.randomUUID().toString();
            File needConvertFileDir = new File(getNeedConvertLocalPath(), uuid);
            if (!needConvertFileDir.exists()) {
                needConvertFileDir.mkdirs();
            }
            File convertFileDir = new File(getConvertLocalPath(), uuid);
            if (!convertFileDir.exists()) {
                convertFileDir.mkdirs();
            }

            List<SftpFileDetail> sftpFileDetails = getSftpFileDetailRepository().findAllByCustomerAndTestAreaAndFactoryAndFactorySiteAndFileCategoryAndTransferStatusAndProcessStatusAndConvertFlagAndBatchStatusAndRemoteOriginalFilePathAndFtpIpAndOriginFileNameLike(
                    needConvertFile.getCustomer(),
                    needConvertFile.getTestArea(),
                    needConvertFile.getFactory(),
                    needConvertFile.getFactorySite(),
                    needConvertFile.getFileCategory(),
                    TransferStatus.SUCCESS,
                    ProcessStatus.CREATE,
                    1,
                    SftpBatchStatus.SUCCESS,
                    needConvertFile.getRemoteOriginalFilePath(),
                    needConvertFile.getFtpIp(),
                    needConvertFile.getOriginFileName().substring(0, needConvertFile.getOriginFileName().lastIndexOf(Constant.POINT)) + '%',
                    Pageable.ofSize(2000)
            );

            sftpFileDetails.forEach(item -> item.setProcessStatus(ProcessStatus.PROCESSING).setUpdateTime(new Date()));
            getSftpFileDetailRepository().saveAll(sftpFileDetails);


            sftpFileDetails.sort((left, right) -> StringUtils.compareIgnoreCase(left.getOriginFileName(), right.getOriginFileName()));
            SftpFileDetail tmpSftpFileDetail = sftpFileDetails.get(0);

            // 步骤开始时间
            Date allStepStartTime = new Date();
            String sourceFileNames = null;

            String tmpName = tmpSftpFileDetail.getOriginFileName();
            File mergeZipSplitFile = new File(convertFileDir, tmpName.substring(0, tmpName.lastIndexOf(Constant.POINT)) + Constant.POINT + FileType.ZIP.getType());
            try {
                ArrayList<String> allZipSplitFileName = new ArrayList<>();
                HashSet<String> originFileNameSet = new HashSet<>();
                for (SftpFileDetail sftpFileDetail : sftpFileDetails) {
                    // 开始进行转换
                    if (!originFileNameSet.add(sftpFileDetail.getOriginFileName())) {
                        log.info("{} 分卷文件重复！", sftpFileDetail.getOriginFileName());
                        continue;
                    }

                    //转换前文件名合并
                    sourceFileNames = sourceFileNames == null ? sftpFileDetail.getLocalFileName() : sourceFileNames + Constant.COMMA + sftpFileDetail.getLocalFileName();

                    // 下载并解压源文件
                    File originalCompressFile = new File(needConvertFileDir.getAbsolutePath(), sftpFileDetail.getLocalFileName());
                    downloadAndUncompressHdfsFile(originalCompressFile.getName(), originalCompressFile.getParent() + File.separator, sftpFileDetail.getHdfsFilePath());
                    if (!originalCompressFile.exists()) {
                        log.error("{} 待转换原压缩文件不存在！", originalCompressFile.getAbsolutePath());
                        throw new RuntimeException(originalCompressFile.getAbsolutePath() + "待转换原压缩文件不存在！");
                    }

                    // 待转换的源文件的全路径
                    File originalUncompressFile = new File(originalCompressFile.getParent() + File.separator + removeFileSuffix(originalCompressFile.getName(), FileType.ZIP), removeFileSuffix(originalCompressFile.getName(), FileType.ZIP));
                    if (!originalUncompressFile.exists()) {
                        log.error("{} 待转换的已解压文件不存在！", originalUncompressFile.getAbsolutePath());
                        throw new RuntimeException(originalUncompressFile.getAbsolutePath() + "待转换的已解压文件不存在！");
                    }

                    allZipSplitFileName.add(originalUncompressFile.getAbsolutePath());
                }

                // 合并文件
                try (FileOutputStream out = new FileOutputStream(mergeZipSplitFile)) {
                    for (String zipSplitFileName : allZipSplitFileName) {
                        log.info("{} 追加合并到 {} 中！", zipSplitFileName, mergeZipSplitFile.getAbsolutePath());
                        try (FileInputStream fin = new FileInputStream(zipSplitFileName);
                             BufferedInputStream in = new BufferedInputStream(fin)) {
                            final byte[] buffer = new byte[1024 * 5];
                            int n;
                            while (-1 != (n = in.read(buffer))) {
                                out.write(buffer, 0, n);
                            }
                            out.flush();
                        }
                    }
                } catch (Exception e) {
                    log.error("合并文件异常：mergeZipSplitFile ：{}", mergeZipSplitFile.getAbsolutePath());
                    throw new RuntimeException(e);
                }

                ArrayList<FileAttr> fileAttrs = new ArrayList<>();
                // 解压合并后的文件
                if (sftpBatchInfo == null || StringUtils.isBlank(sftpBatchInfo.getCollectRuleSnapshot())) {
                    throw new RuntimeException("文件对应的拉取规则不存在");
                }
                SftpCollectRule sftpCollectRule = JSON.parseObject(sftpBatchInfo.getCollectRuleSnapshot(), SftpCollectRule.class);
                Set<CompressFileSuffixEnum> needDecompressTypesConfig = Arrays.stream(sftpCollectRule.getNeedDecompressionTypes().split(COMMA)).map(CompressFileSuffixEnum::of).filter(Objects::nonNull).collect(Collectors.toSet());
                FileUtils.deCompressFile(fileAttrs, mergeZipSplitFile, needDecompressTypesConfig);

                HashSet<String> fileNameSet = new HashSet<>();
                for (FileAttr fileAttr : fileAttrs) {
                    // 步骤开始时间
                    Date stepStartTime = new Date();
                    SftpFileDetail loadSftpFileDetail = null;
                    try {
                        if (!fileNameSet.add(fileAttr.getFileName())) {
                            log.info("{} 中存在重复的 {} ", mergeZipSplitFile.getAbsolutePath(), fileAttr.getFileName());
                            continue;
                        }
                        //根据解压文件重新判定数据文件类型
                        FileCategory fileCategory = AbstractFTPSyncCore.matchFileCategoryWithFileName(fileAttr.getOriginalFileName(), sftpCollectRule);
                        fileAttr.setFileCategory(fileCategory);
                        if (AbstractFTPSyncCore.NOT_NEED_PULL_FILE_CATEGORY_LIST.contains(fileCategory)) {
                            // 不处理
                            continue;
                        }

                        File file = new File(fileAttr.getFilePath(), fileAttr.getFileName());
                        // 压缩文件
                        File compressConvertFile = new File(file.getAbsolutePath() + Constant.POINT + FileType.ZIP.getType());
                        FileUtils.compressFileToZip(file.getParent(), file.getName(), compressConvertFile.getParent());

                        if (!compressConvertFile.exists() || !compressConvertFile.isFile()) {
                            log.error("{} 压缩文件不存在", compressConvertFile.getAbsolutePath());
                            throw new RuntimeException("压缩文件不存在：" + compressConvertFile.getAbsolutePath());
                        }
                        // 上传文件HDFS路径
                        String hdfsFullPath = String.format(
                                getHdfsConfig().getHdfsRootPath(),
                                tmpSftpFileDetail.getCustomer(),
                                tmpSftpFileDetail.getTestArea().getArea(),
                                tmpSftpFileDetail.getFactory(),
                                fileCategory.getCategory(),
                                DateFormatUtils.format(tmpSftpFileDetail.getRemoteFileMtime(), "yyyyMMdd")
                        );

                        try {
                            loadSftpFileDetail = insertSftpFileDetail(tmpSftpFileDetail, compressConvertFile, fileAttr, hdfsFullPath, sourceFileNames);
                        } catch (DataIntegrityViolationException e) {
                            log.info("{} 待插入文件已经存在！{}", compressConvertFile.getName(), e.getMessage());
                            continue;
                        }

                        // 上传压缩文件
                        getHdfsUtil().uploadToHDFSByFileSystem(compressConvertFile.getAbsolutePath(), hdfsFullPath, 1);

                        insertFileLoadingLog(loadSftpFileDetail, sourceFileNames, stepStartTime, null);
                        loadSftpFileDetail
                                .setTransferStatus(TransferStatus.SUCCESS)
                                .setUpdateTime(new Date());
                        getSftpFileDetailRepository().save(loadSftpFileDetail);

                    } catch (Exception e) {
                        log.error("zipSplit 转换文件失败！{} ;", fileAttr.getFileName(), e);
                        if (loadSftpFileDetail != null) {
                            getSftpFileDetailRepository().deleteById(loadSftpFileDetail.getId());
                            insertFileLoadingLog(loadSftpFileDetail, sourceFileNames, stepStartTime, e);
                        }
                    }
                }
                Date endDate = new Date();
                insertFileLoadingLog(sftpFileDetails, allStepStartTime, null);
                sftpFileDetails.forEach(item -> item.setProcessStatus(ProcessStatus.SUCCESS).setUpdateTime(endDate));
                getSftpFileDetailRepository().saveAll(sftpFileDetails);
            } catch (Exception e) {
                log.error("zipSplit 转换异常：", e);
                Date endDate = new Date();
                sftpFileDetails.forEach(item -> item.setProcessStatus(ProcessStatus.FAIL).setUpdateTime(endDate));
                getSftpFileDetailRepository().saveAll(sftpFileDetails);
                insertFileLoadingLog(sftpFileDetails, allStepStartTime, e);
            } finally {
                // 删除临时的文件目录
                org.apache.commons.io.FileUtils.deleteQuietly(needConvertFileDir);
                org.apache.commons.io.FileUtils.deleteQuietly(convertFileDir);
            }
        } finally {
            FileConvertRedisManager.unLockZipSplitConvert(needConvertFile.getRemoteOriginalFilePath());
        }
    }

    default void insertFileLoadingLog(List<SftpFileDetail> sftpFileDetails, Date allStepStartTime, Exception exception) {
        List<String> fileNames = sftpFileDetails.stream().map(SftpFileDetail::getLocalFileName).collect(Collectors.toList());
        int step = StepType.STEP_TYPE_2200.getStep();
        getFileLoadingLogRepository().deleteAllByFileNameInAndStepGreaterThanEqual(fileNames, step);
        Date date = new Date();

        FileLoadException fileLoadException = null;
        if (exception != null) {
            fileLoadException = exception instanceof FileLoadException ? (FileLoadException) exception : new FileLoadException(FileLoadExceptionInfo.ZIP_SPLIT_FILE_CONVERT_EXCEPTION, ExceptionUtils.getStackTrace(exception), null);
        }
        ProcessStatus processStatus = fileLoadException == null ? ProcessStatus.SUCCESS : ProcessStatus.FAIL;
        String failedType = fileLoadException == null ? null : fileLoadException.getFailedType();
        String failedFields = fileLoadException == null ? null : fileLoadException.getFailedFields();
        ExceptionType exceptionType = fileLoadException == null ? null : fileLoadException.getExceptionType();
        String exceptionMessage = fileLoadException == null ? null : fileLoadException.getExceptionMessage();
        String errorMessage = fileLoadException == null ? null : ExceptionUtils.getStackTrace(exception);

        List<FileLoadingLog> fileLoadingLogs = sftpFileDetails.stream().map(sftpFileDetail -> new FileLoadingLog()
                .setCustomer(sftpFileDetail.getCustomer())
                .setSubCustomer(sftpFileDetail.getSubCustomer())
                .setTestArea(sftpFileDetail.getTestArea())
                .setFactory(sftpFileDetail.getFactory())
                .setFactorySite(sftpFileDetail.getFactorySite())
                .setFab(sftpFileDetail.getFab())
                .setFabSite(sftpFileDetail.getFabSite())
                .setFileCategory(sftpFileDetail.getFileCategory())
                .setFileName(sftpFileDetail.getLocalFileName())
                .setOriginFileName(sftpFileDetail.getOriginFileName())
                .setOriginFileSize(sftpFileDetail.getOriginFileSize())
                .setFileSize(sftpFileDetail.getFileSize())
                .setConvertFlag(sftpFileDetail.getConvertFlag())
                .setHdfsPath(sftpFileDetail.getHdfsFilePath())
                .setFtpPath(FileUtils.getFileFullPath(sftpFileDetail.getRemoteOriginalFilePath(), "") + sftpFileDetail.getRemoteFileName())
                .setFtpIp(sftpFileDetail.getFtpIp())
                .setRemoteFileMtime(sftpFileDetail.getRemoteFileMtime())
                .setStep(step)
                .setStepStartTime(allStepStartTime)
                .setStepEndTime(date)
                .setWarehousingMode(sftpFileDetail.getWarehousingMode())
                .setRepairLotWaferId(sftpFileDetail.getRepairLotWaferId())
                .setCleanupTaskIds(sftpFileDetail.getCleanupTaskIds())
                .setProcessStatus(processStatus)
                .setFailedType(failedType)
                .setFailedFields(failedFields)
                .setExceptionType(exceptionType)
                .setExceptionMessage(exceptionMessage)
                .setErrorMessage(errorMessage)
                .setCreateTime(allStepStartTime)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateTime(date)
                .setUpdateUser(Constant.SYSTEM)).collect(Collectors.toList());

        // 查询step1000数据，补充内容
        List<FileLoadingLog> firstStepFileLoadingLogs = getFileLoadingLogRepository().findAllByFileNameAndStep(
                fileNames.get(0),
                StepType.STEP_TYPE_1000.getStep());
        if (CollectionUtils.isNotEmpty(firstStepFileLoadingLogs)) {
            Set<String> uniqueWarningMessages = new HashSet<>();
            for (FileLoadingLog log : firstStepFileLoadingLogs) {
                String warningMessage = log.getWarningMessage();
                if (warningMessage != null && !warningMessage.isEmpty()) {
                    uniqueWarningMessages.add(warningMessage);
                }
            }
            String concatenatedWarningMessages = String.join(Constant.COMMA, uniqueWarningMessages);
            fileLoadingLogs.forEach(fileLoadingLog ->
                    fileLoadingLog.setDataIntegrityFileComment(firstStepFileLoadingLogs.get(0).getDataIntegrityFileComment())
                            .setDataIntegrityFileLabel(firstStepFileLoadingLogs.get(0).getDataIntegrityFileLabel())
                            .setWarningMessage(concatenatedWarningMessages)
            );
        }
        getFileLoadingLogRepository().saveAll(fileLoadingLogs);
        getFileWarehousingRecordManager().updateFileWarehousingStatus(fileLoadingLogs);

        if (processStatus == ProcessStatus.FAIL) {
            // 发送处理失败通知
            fileLoadingLogs.forEach(fileLoadingLog -> getKafkaSink().sendLoadEndFlagFailMessage(fileLoadingLog));
        }
    }

    default SftpFileDetail insertSftpFileDetail(SftpFileDetail tmpSftpFileDetail, File compressConvertFile, FileAttr fileAttr, String hdfsFullPath, String sourceFileNames) {
        Date createTime = new Date();
        SftpFileDetail loadSftpFileDetail = new SftpFileDetail()
                .setCustomer(tmpSftpFileDetail.getCustomer())
                .setSubCustomer(tmpSftpFileDetail.getSubCustomer())
                .setFtpIp(tmpSftpFileDetail.getFtpIp())
                .setFactory(tmpSftpFileDetail.getFactory())
                .setFactorySite(tmpSftpFileDetail.getFactorySite())
                .setFab(tmpSftpFileDetail.getFab())
                .setFabSite(tmpSftpFileDetail.getFabSite())
                .setTestArea(tmpSftpFileDetail.getTestArea())
                .setBatchId(0L)
                .setBatchStatus(SftpBatchStatus.SUCCESS)
                .setRemoteFileName(tmpSftpFileDetail.getRemoteFileName())
                .setRemoteFileMtime(tmpSftpFileDetail.getRemoteFileMtime())
                .setLocalFileName(compressConvertFile.getName())
                .setOriginFileName(fileAttr.getOriginalFileName())
                .setOriginFileSize(fileAttr.getOriginalSize())
                .setSourceFileNames(sourceFileNames)
                .setFileType(FileType.ZIP)
                .setFileSize(compressConvertFile.length())
                .setFileCategory(fileAttr.getFileCategory())
                .setRemoteFilePath(tmpSftpFileDetail.getRemoteFilePath())
                .setRemoteOriginalFilePath(tmpSftpFileDetail.getRemoteOriginalFilePath())
                .setLocalFilePath(compressConvertFile.getAbsolutePath())
                .setWarehousingMode(tmpSftpFileDetail.getWarehousingMode())
                .setRepairLotWaferId(tmpSftpFileDetail.getRepairLotWaferId())
                .setCleanupTaskIds(tmpSftpFileDetail.getCleanupTaskIds())
                .setTransferStatus(TransferStatus.PROCESSING)
                .setProcessStatus(ProcessStatus.CREATE)
                .setRemoteFileAttrDetail(tmpSftpFileDetail.getRemoteFileAttrDetail())
                .setCreateTime(createTime)
                .setUpdateTime(createTime)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateUser(Constant.SYSTEM)
                .setHdfsFilePath(hdfsFullPath + compressConvertFile.getName())
                .setConvertFlag(PullFileHandler.matchConvertFlag(fileAttr.getFileCategory()));
        getSftpFileDetailRepository().save(loadSftpFileDetail);
        return loadSftpFileDetail;
    }

    default void insertFileLoadingLog(SftpFileDetail sftpFileDetail, String sourceFileNames, Date stepStartTime, Exception exception) {
        Date date = new Date();
        String remoteOriginalFilePath = FileUtils.getFileFullPath(sftpFileDetail.getRemoteOriginalFilePath(), "");

        FileLoadException fileLoadException = null;
        if (exception != null) {
            fileLoadException = exception instanceof FileLoadException ? (FileLoadException) exception : new FileLoadException(FileLoadExceptionInfo.ZIP_SPLIT_FILE_CONVERT_EXCEPTION, ExceptionUtils.getStackTrace(exception), null);
        }

        FileLoadingLog fileLoadingLog = new FileLoadingLog()
                .setCustomer(sftpFileDetail.getCustomer())
                .setSubCustomer(sftpFileDetail.getSubCustomer())
                .setTestArea(sftpFileDetail.getTestArea())
                .setFactory(sftpFileDetail.getFactory())
                .setFactorySite(sftpFileDetail.getFactorySite())
                .setFab(sftpFileDetail.getFab())
                .setFabSite(sftpFileDetail.getFabSite())
                .setFileCategory(sftpFileDetail.getFileCategory())
                .setFileName(sftpFileDetail.getLocalFileName())
                .setOriginFileName(sftpFileDetail.getOriginFileName())
                .setOriginFileSize(sftpFileDetail.getOriginFileSize())
                .setFileSize(sftpFileDetail.getFileSize())
                .setConvertFlag(sftpFileDetail.getConvertFlag())
                .setSourceFileNames(sourceFileNames)
                .setHdfsPath(sftpFileDetail.getHdfsFilePath())
                .setFtpPath(remoteOriginalFilePath + sftpFileDetail.getRemoteFileName())
                .setFtpIp(sftpFileDetail.getFtpIp())
                .setRemoteFileMtime(sftpFileDetail.getRemoteFileMtime())
                .setStep(StepType.STEP_TYPE_1000.getStep())
                .setStepStartTime(stepStartTime)
                .setStepEndTime(date)
                .setWarehousingMode(sftpFileDetail.getWarehousingMode())
                .setRepairLotWaferId(sftpFileDetail.getRepairLotWaferId())
                .setCleanupTaskIds(sftpFileDetail.getCleanupTaskIds())
                .setProcessStatus(fileLoadException == null ? ProcessStatus.SUCCESS : ProcessStatus.FAIL)
                .setFailedType(fileLoadException == null ? null : fileLoadException.getFailedType())
                .setFailedFields(fileLoadException == null ? null : fileLoadException.getFailedFields())
                .setExceptionType(fileLoadException == null ? null : fileLoadException.getExceptionType())
                .setExceptionMessage(fileLoadException == null ? null : fileLoadException.getExceptionMessage())
                .setErrorMessage(fileLoadException == null ? null : ExceptionUtils.getStackTrace(exception))
                .setCreateTime(stepStartTime)
                .setCreateUser(Constant.SYSTEM)
                .setUpdateTime(date)
                .setUpdateUser(Constant.SYSTEM);

        // 查询转换前数据，补充内容
        List<FileLoadingLog> sourceFileLoadingLogs = getFileLoadingLogRepository().findAllByFileNameInAndStep(
                getZipFileCollectionToStringConverter().convertToEntityAttribute(sourceFileNames),
                StepType.STEP_TYPE_1000.getStep());
        if (CollectionUtils.isNotEmpty(sourceFileLoadingLogs)) {
            Set<String> uniqueWarningMessages = new HashSet<>();
            for (FileLoadingLog log : sourceFileLoadingLogs) {
                String warningMessage = log.getWarningMessage();
                if (warningMessage != null && !warningMessage.isEmpty()) {
                    uniqueWarningMessages.add(warningMessage);
                }
            }
            String concatenatedWarningMessages = String.join(Constant.COMMA, uniqueWarningMessages);
            fileLoadingLog.setDataIntegrityFileComment(sourceFileLoadingLogs.get(0).getDataIntegrityFileComment())
                    .setWarningMessage(concatenatedWarningMessages)
                    .setDataIntegrityFileLabel(sourceFileLoadingLogs.get(0).getDataIntegrityFileLabel());
        }
        getFileLoadingLogRepository().deleteByFileNameAndStep(fileLoadingLog.getFileName(), fileLoadingLog.getStep());
        getFileLoadingLogRepository().save(fileLoadingLog);
        getFileWarehousingRecordManager().updateFileWarehousingStatus(Collections.singletonList(fileLoadingLog));
        // 发送处理失败通知
        if (fileLoadingLog.getProcessStatus() == ProcessStatus.FAIL) {
            getKafkaSink().sendLoadEndFlagFailMessage(fileLoadingLog);
        }
    }

    SftpBatchInfoRepository getSftpBatchInfoRepository();

    Long getZipSplitFileMaxWaitMillion();

    List<SftpFileDetail> getNeedConvertFiles();

    AtomicInteger getThreadActiveCount();

    String getNeedConvertLocalPath();

    String getConvertLocalPath();

    SftpFileDetailRepository getSftpFileDetailRepository();

    FileLoadingLogRepository getFileLoadingLogRepository();

    FileWarehousingRecordManager getFileWarehousingRecordManager();

    HdfsConfig getHdfsConfig();

    ThreadPoolExecutor getThreadPoolExecutor();

    KafkaSink getKafkaSink();

    ZipFileCollectionToStringConverter getZipFileCollectionToStringConverter();

}
