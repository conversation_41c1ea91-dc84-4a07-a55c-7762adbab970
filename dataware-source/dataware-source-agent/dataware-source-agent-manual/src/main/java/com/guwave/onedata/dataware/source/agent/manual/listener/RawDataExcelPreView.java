package com.guwave.onedata.dataware.source.agent.manual.listener;


import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.model.raw.TestItemData;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.dataware.source.agent.manual.util.RawDataCommonUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

public class RawDataExcelPreView implements ExcelConsumer<Map<String, List<Pair<String, String>>>> {

    private ManualFileInfo manualFileInfo;
    private TestItemData testItemData = new TestItemData();
    private long num = 0L;

    public RawDataExcelPreView(ManualFileInfo manualFileInfo) {
        this.manualFileInfo = manualFileInfo;
    }

    @Override
    public void dealLine(Map<String, List<Pair<String, String>>> fieldWithContentListMap) {
        num++;
        if (num <= 1) {
            fieldWithContentListMap.forEach((field, contentList) -> {
                BiConsumer<String, TestItemData> commonTestItemDataConsumer = RawDataCommonUtil.RAW_DATA_COMMON_FIELD_CONSUMER_MAP.get(field);
                if (commonTestItemDataConsumer != null) {
                    try {
                        commonTestItemDataConsumer.accept(contentList.get(0).getValue(), testItemData);
                    } catch (Exception e) {
                        throw new RuntimeException(String.format("RawData文件%s数据异常, 异常数据: columnName=%s, columnValue=%s", manualFileInfo.getFileName(), field, contentList.get(0).getValue()), e);
                    }
                }
            });
        }
    }

    @Override
    public void after() {
        // do nothing
    }

    public FileCategory getRealFileCategory() {
        if (this.manualFileInfo.getTestArea() == TestArea.WAT) {
            return FileCategory.WAT;
        } else if (TestArea.WAT == TestArea.of(this.testItemData.getTestArea())) {
            return FileCategory.WAT;
        }
        return this.manualFileInfo.getFileCategory();
    }
}
