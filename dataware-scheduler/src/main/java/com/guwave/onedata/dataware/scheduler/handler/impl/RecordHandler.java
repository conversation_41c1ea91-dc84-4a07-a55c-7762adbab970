package com.guwave.onedata.dataware.scheduler.handler.impl;

import com.guwave.onedata.dataware.common.contant.ExceptionType;
import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.model.message.CalculateEndFlag;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SchedulerEndLog;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SchedulerEndLogRepository;
import com.guwave.onedata.dataware.scheduler.handler.Handler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class RecordHandler implements Handler {
    private static final Logger LOGGER = LoggerFactory.getLogger(RecordHandler.class);

    private static final String SYSTEM = "System";

    @Autowired
    private SchedulerEndLogRepository schedulerEndLogRepository;


    @Override
    public void doHandle(CalculateEndFlag endFlag) {
        if (!this.isSupport(endFlag)) {
            LOGGER.info("当前Handler不支持处理该记录");
            return;
        }
        Date now = new Date();
        SchedulerEndLog schedulerEndLog = new SchedulerEndLog();
        schedulerEndLog
                .setCustomer(endFlag.getCustomer())
                .setSubCustomer(endFlag.getSubCustomer())
                .setExecuteMode(endFlag.getExecuteMode())
                .setFactory(endFlag.getFactory())
                .setFactorySite(endFlag.getFactorySite())
                .setTestArea(endFlag.getTestArea())
                .setLotType(endFlag.getLotType())
                .setDeviceId(endFlag.getDeviceId())
                .setTestStage(endFlag.getTestStage())
                .setLotId(endFlag.getLotId())
                .setWaferNo(endFlag.getWaferNo())
                .setFileCategory(endFlag.getFileCategory())
                .setDwLayer(endFlag.getDwLayer())
                .setNextDwLayer(endFlag.getNextDwLayer())
                .setPlatform(endFlag.getPlatform())
                .setWarehousingMode(endFlag.getWarehousingMode())
                .setRepairLotWaferId(endFlag.getRepairLotWaferId())
                .setCleanupTaskIds(endFlag.getCleanupTaskIds())
                .setProcessStatus(endFlag.getProcessStatus())
                .setAppId(endFlag.getAppId())
                .setErrorMessage(endFlag.getErrorMessage())
                .setProcessConsume(endFlag.getProcessConsume())
                .setPriority(endFlag.getPriority())
                .setCreateTime(now)
                .setUpdateTime(now)
                .setCreateUser(SYSTEM)
                .setUpdateUser(SYSTEM);

        schedulerEndLogRepository.save(schedulerEndLog);
    }

    @Override
    public boolean isSupport(CalculateEndFlag endFlag) {
        return true;
    }

    @Override
    public FileLoadExceptionInfo getExceptionInfo(ExceptionType exceptionType) {
        return null;
    }
}
