package com.guwave.onedata.dataware.collectx.service.impl;

import com.guwave.onedata.dataware.collectx.configuration.SftpConfig;
import com.guwave.onedata.dataware.collectx.core.IFTPSyncCore;
import com.guwave.onedata.dataware.collectx.entity.FTPFileAttr;
import com.guwave.onedata.dataware.collectx.manager.thread.SftpBackupRuleThreadManager;
import com.guwave.onedata.dataware.collectx.util.FileUtils;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpBackupFileDetail;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.SftpBackupRule;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpBackupFileDetailRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Slf4j
@Service
public class BackupFileHandler {
    @Autowired
    private SftpConfig sftpConfig;

    @Autowired
    private SftpBackupFileDetailRepository sftpBackupFileDetailRepository;

    public List<FTPFileAttr> findNeedPullFiles(IFTPSyncCore ftpSyncCore, List<SftpBackupFileDetail> failFiles) throws Exception {
        SftpBackupRule sftpBackupRule = SftpBackupRuleThreadManager.getSftpBackupRule();

        List<FTPFileAttr> ftpFileAttrs = new ArrayList<>();
        String remotePaths = sftpBackupRule.getSourcePaths();
        for (String remotePath : remotePaths.split(Constant.COMMA)) {
            log.info("准备扫描 {}", remotePath);
            int startSize = ftpFileAttrs.size();
            ftpSyncCore.listFTPFileInfo(remotePath, ftpFileAttrs);
            int endSize = ftpFileAttrs.size();
            log.info("{} 扫描到 {} 个文件", remotePath, endSize - startSize);
        }
        log.info("此backupRule共有 {} 个备份失败文件尝试重新备份", failFiles.size());
        for (SftpBackupFileDetail failFile : failFiles) {
            ftpFileAttrs.add(ftpSyncCore.ftpFileInfo(failFile.getSourceFilePath(), failFile.getFileName()));
        }

        List<FTPFileAttr> finalFtpFileAttrs = ftpFileAttrs.stream().filter(Objects::nonNull).filter(t -> isBackupFile(t.getFullName()) && !isBlackFile(t.getFullName())).collect(Collectors.toList());

        log.info("sftpBackupRuleId : {},sftpBatchInfoId : {} 待备份 {} 个文件", sftpBackupRule.getId(), sftpBackupRule.getNewSftpBackupBatchInfo().getId(), finalFtpFileAttrs.size());
        return finalFtpFileAttrs;
    }

    public boolean backupFile(IFTPSyncCore sourceFtpSyncCore, IFTPSyncCore targetFtpSyncCore, FTPFileAttr ftpFileAttr, SftpBackupFileDetail backupFailFileDetail) {
        SftpBackupRule sftpBackupRule = SftpBackupRuleThreadManager.getSftpBackupRule();

        String localPath = FileUtils.getFileFullPath(sftpConfig.getLocalPath(), UUID.randomUUID().toString());
        File localPathDir = new File(localPath);
        if (!localPathDir.exists()) {
            localPathDir.mkdirs();
        }

        //
        SftpBackupFileDetail backupFileDetail = null;
        if (backupFailFileDetail == null) {
            // 生成一条新的sftpBackupFileDetail
            backupFileDetail = getSftpFileDetail(ftpFileAttr);
        } else {
            backupFileDetail = backupFailFileDetail.setProcessStatus(ProcessStatus.CREATE)
                    .setBackupBatchInfoId(sftpBackupRule.getNewSftpBackupBatchInfo().getId())
                    .setFileMtime(new Date(ftpFileAttr.getMTime()))
                    .setErrorMessage(null)
                    .setExceptionMessage(null)
                    .setExceptionType(null)
                    .setUpdateTime(new Date())
                    .setUpdateUser(SYSTEM);
        }
        sftpBackupFileDetailRepository.save(backupFileDetail);

        String targetPath = sftpBackupRule.getBackupTargetDirectory();

        String sourceFileFullName = ftpFileAttr.getFullName();
        String localFileFullName;

        String sourceDir = sourceFileFullName.replace(sftpBackupRule.getSourcePaths(), EMPTY).replace(ftpFileAttr.getFileName(), EMPTY);
        try {
            try {
                // 下载文件到本地
                localFileFullName = sourceFtpSyncCore.loadFile(sourceFileFullName, localPath);
            } catch (Exception e) {
                throw new FileLoadException(FileLoadExceptionInfo.PULL_FILE_TO_LOCAL_FAIL, ExceptionUtils.getStackTrace(e), null);
            }

            try {
                // 上传到目标服务器
                targetFtpSyncCore.uploadFile(localFileFullName, targetPath + sourceDir);
            } catch (Exception e) {
                throw new FileLoadException(FileLoadExceptionInfo.PUSH_FILE_TO_TARGET_FTP_FAIL, ExceptionUtils.getStackTrace(e), null);
            }

            try {
                if (sftpBackupRule.getBackupType() == BackupType.MOVE) {
                    // 删除原文件
                    sourceFtpSyncCore.deleteFile(sourceFileFullName);

                }
            } catch (Exception e) {
                throw new FileLoadException(FileLoadExceptionInfo.DELETE_FILE_FAIL, ExceptionUtils.getStackTrace(e), null);
            }

            backupFileDetail.setProcessStatus(ProcessStatus.SUCCESS)
                    .setUpdateTime(new Date())
                    .setUpdateUser(SYSTEM);
        } catch (Exception e) {
            FileLoadException fileLoadException = e instanceof FileLoadException ? ((FileLoadException) e) : new FileLoadException(FileLoadExceptionInfo.BACKUP_FILE_FAIL, ExceptionUtils.getStackTrace(e), null);
            backupFileDetail.setFailCnt(backupFileDetail.getFailCnt() + 1)
                    .setProcessStatus(ProcessStatus.FAIL)
                    .setErrorMessage(ExceptionUtils.getStackTrace(e))
                    .setExceptionMessage(fileLoadException.getExceptionMessage())
                    .setExceptionType(fileLoadException.getExceptionType())
                    .setUpdateTime(new Date())
                    .setUpdateUser(SYSTEM);
            return false;
        } finally {
            sftpBackupFileDetailRepository.save(backupFileDetail);
            org.apache.commons.io.FileUtils.deleteQuietly(localPathDir);
        }
        return true;
    }

    private SftpBackupFileDetail getSftpFileDetail(FTPFileAttr ftpFileAttr) {
        SftpBackupRule sftpBackupRule = SftpBackupRuleThreadManager.getSftpBackupRule();
        SftpBackupFileDetail fileDetail = sftpBackupFileDetailRepository.findByBackupRuleIdAndSourceFilePathAndFileName(sftpBackupRule.getId(), ftpFileAttr.getFilePath(), ftpFileAttr.getFileName());
        if (fileDetail == null) {
            return new SftpBackupFileDetail()
                    .setBackupRuleId(sftpBackupRule.getId())
                    .setBackupType(sftpBackupRule.getBackupType())
                    .setBackupBatchInfoId(sftpBackupRule.getNewSftpBackupBatchInfo().getId())
                    .setFileName(ftpFileAttr.getFileName())
                    .setSourceFilePath(ftpFileAttr.getFilePath())
                    .setFileMtime(new Date(ftpFileAttr.getMTime()))
                    .setBatchStatus(BatchInfoStatus.PROCESSING)
                    .setProcessStatus(ProcessStatus.CREATE)
                    .setFailCnt(0)
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date())
                    .setCreateUser(SYSTEM)
                    .setUpdateUser(SYSTEM);
        } else {
            return fileDetail.setBackupBatchInfoId(sftpBackupRule.getNewSftpBackupBatchInfo().getId())
                    .setFileMtime(new Date(ftpFileAttr.getMTime()))
                    .setBatchStatus(BatchInfoStatus.PROCESSING)
                    .setProcessStatus(ProcessStatus.CREATE)
                    .setFailCnt(0)
                    .setUpdateTime(new Date())
                    .setUpdateUser(SYSTEM);
        }
    }

    public static boolean isBackupFile(String remotePath) {
        SftpBackupRule sftpBackupRule = SftpBackupRuleThreadManager.getSftpBackupRule();

        return (StringUtils.hasLength(sftpBackupRule.getSourceFileFullNameRegex()) && Pattern.compile(sftpBackupRule.getSourceFileFullNameRegex(), Pattern.CASE_INSENSITIVE).matcher(remotePath).find()) ||
                StringUtils.hasLength(sftpBackupRule.getSourceFileRegex()) && Pattern.compile(sftpBackupRule.getSourceFileRegex(), Pattern.CASE_INSENSITIVE).matcher(remotePath).find();
    }

    public static boolean isBlackFile(String remotePath) {
        SftpBackupRule sftpBackupRule = SftpBackupRuleThreadManager.getSftpBackupRule();

        return (StringUtils.hasLength(sftpBackupRule.getBlackFileFullNameRegex()) && Pattern.compile(sftpBackupRule.getBlackFileFullNameRegex(), Pattern.CASE_INSENSITIVE).matcher(remotePath).find());

    }
}
