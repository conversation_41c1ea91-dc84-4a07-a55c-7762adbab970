package com.guwave.onedata.dataware.collectx.util;

import com.guwave.onedata.dataware.collectx.common.OneDataException;
import com.guwave.onedata.dataware.collectx.common.OneDataNotFoundException;

/**
 * 
 * <AUTHOR>
 *
 */
public class JAssert {
    
    public static void notNull(Object val, String msg) {
        if(null == val) {
            throw new OneDataException(msg);
        }
    }
    
    public static void pass(boolean ret, String msg) {
        if(!ret) {
            throw new OneDataException(msg);
        }
    }
    
    /**
     * - 如果对象不存在，抛404错误码
     * @param ret 是否存在的结果
     * @param msg
     */
    public static void found(boolean ret, String msg) {
        if(!ret) {
            throw new OneDataNotFoundException(msg);
        }
    }
    
    public static void justFail(String msg) {
        throw new OneDataException(msg);
    }
    
    public static void justFail(String code, String msg) {
        throw new OneDataException(code, msg, null);
    }
    
    public static void justFail(String code, String msg, Throwable t) {
        throw new OneDataException(code, msg, t);
    }
    
    public static void justFail(String msg, Throwable t) {
        throw new OneDataException(msg, t);
    }
    

}
