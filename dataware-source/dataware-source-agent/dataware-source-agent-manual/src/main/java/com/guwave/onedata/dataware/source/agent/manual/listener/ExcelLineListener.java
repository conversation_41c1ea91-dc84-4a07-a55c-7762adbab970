package com.guwave.onedata.dataware.source.agent.manual.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.guwave.onedata.dataware.common.contant.Constant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ExcelLineListener extends AnalysisEventListener<Map<Integer, String>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(ExcelLineListener.class);

    private final Map<String, String> fieldMapping;

    // Map<fieldName,[<index,showName>,...]>
    private final Map<String, List<Pair<Integer, String>>> fieldIndexMapping = new HashMap<>();
    // Map<fieldName,[<showName,Value>,...]>
    private final ExcelConsumer<Map<String, List<Pair<String, String>>>> fieldWithContentListConsumer;

    public ExcelLineListener(Map<String, String> fieldMapping, ExcelConsumer<Map<String, List<Pair<String, String>>>> fieldWithContentListConsumer) {
        this.fieldMapping = fieldMapping;
        this.fieldWithContentListConsumer = fieldWithContentListConsumer;
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        headMap.forEach((index, showName) -> {
            String field = fieldMapping.get(specialUnicode(showName));
            if (field != null) {
                fieldIndexMapping.compute(field, (k, oldValue) -> {
                    if (CollectionUtils.isEmpty(oldValue)) {
                        oldValue = new ArrayList<>();
                    }
                    oldValue.add(Pair.of(index, showName));
                    return oldValue;
                });
            }
        });
        if (fieldIndexMapping.isEmpty()) {
            throw new RuntimeException("文件没有读取到需要的字段");
        }
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        if (null == data || data.size() == 0) {
            return;
        }

        if (fieldWithContentListConsumer != null) {
            HashMap<String, List<Pair<String, String>>> lineMap = new HashMap<>();
            fieldIndexMapping.forEach((field, indexWithShowNameList) -> {
                indexWithShowNameList.forEach(indexWithShowName -> {
                    lineMap.compute(field, (k, oldValue) -> {
                        if (CollectionUtils.isEmpty(oldValue)) {
                            oldValue = new ArrayList<>();
                        }
                        String str = data.get(indexWithShowName.getKey());
                        if (StringUtils.isBlank(str)) {
                            str = Constant.EMPTY;
                        }
                        oldValue.add(Pair.of(indexWithShowName.getValue(), str));
                        return oldValue;
                    });
                });
            });
            if (!lineMap.isEmpty()) {
                fieldWithContentListConsumer.dealLine(lineMap);
            }
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (fieldWithContentListConsumer != null) {
            fieldWithContentListConsumer.after();
        }
    }

    private static String specialUnicode(String str) {
        if (str.startsWith("\uFEFF")) {
            str = str.replace("\uFEFF", "");
        } else if (str.endsWith("\uFEFF")) {
            str = str.replace("\uFEFF", "");
        }
        return str;
    }
}
