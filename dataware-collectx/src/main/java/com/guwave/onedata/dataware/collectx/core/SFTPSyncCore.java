package com.guwave.onedata.dataware.collectx.core;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.guwave.onedata.dataware.collectx.entity.FTPFileAttr;
import com.guwave.onedata.dataware.collectx.manager.thread.SftpCollectRuleThreadManager;
import com.guwave.onedata.dataware.collectx.manager.thread.SftpConnectConfThreadManager;
import com.guwave.onedata.dataware.collectx.manager.thread.SftpLogThreadLocalUtil;
import com.guwave.onedata.dataware.collectx.util.FileUtils;
import com.guwave.onedata.dataware.common.contant.FTPServerType;
import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.model.collectx.SftpConnectConf;
import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.util.List;
import java.util.Properties;
import java.util.Vector;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Slf4j
public class SFTPSyncCore extends AbstractFTPSyncCore implements IFTPSyncCore {

    private ChannelSftp sftp = new ChannelSftp();

    public SFTPSyncCore() {
        this(FTPServerType.NORMAL);
    }

    public SFTPSyncCore(FTPServerType type) {
        setServerType(type);
    }

    /**
     * 连接到服务器
     */
    public void connectServer() throws Exception {
        SftpConnectConf sftpConnectConf = SftpConnectConfThreadManager.getSftpConnectConf(getServerType());
        if (!sftp.isConnected()) {
            String privateKey = null;
            JSch jsch = new JSch();
            if (privateKey != null) {
                jsch.addIdentity(privateKey);// 设置私钥
            }
            Session session = jsch.getSession(sftpConnectConf.getUsername(), sftpConnectConf.getServer(), sftpConnectConf.getPort());
            if (sftpConnectConf.getPassword() != null) {
                session.setPassword(sftpConnectConf.getPassword());
            }
            Properties config = new Properties();
            String sessionConfig = sftpConnectConf.findExtProp("sessionConfig");
            if (StringUtils.isNotBlank(sessionConfig)){
                JSONObject sessionConfigJson = JSON.parseObject(sessionConfig);
                config.putAll(sessionConfigJson);
            }

            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            session.connect(sftpConnectConf.getTimeout() * 1000);
            session.setTimeout(sftpConnectConf.getTimeout() * 1000);
            Channel channel = session.openChannel("sftp");
            channel.connect(sftpConnectConf.getTimeout() * 1000);

            sftp = (ChannelSftp) channel;
        }
    }

    /**
     * 下载文件
     *
     * @param remoteFileName --服务器上的文件名
     * @param localPath      --本地文件名
     * @return true 下载成功，false 下载失败
     */
    public String loadFile(String remoteFileName, String localPath) throws Exception {
        // 下载文件
        String remoteFileShortName = FileUtils.getFileName(remoteFileName);
        String downFileFullName = FileUtils.getFileFullName(localPath, remoteFileShortName);
        boolean flag = downloadFile(downFileFullName, remoteFileName, 1);
        if (!flag) {
            throw new Exception("文件下载失败！");
        }
        return downFileFullName;
    }

    private boolean downloadFile(String localFilePath, String remoteFilePath, int retryCount) {
        SftpConnectConf sftpConnectConf = SftpConnectConfThreadManager.getSftpConnectConf(getServerType());
        File localFile = new File(localFilePath);
        if (localFile.exists()) {
            org.apache.commons.io.FileUtils.deleteQuietly(localFile);
        }
        boolean flag = false;
        try (OutputStream out = new FileOutputStream(localFilePath);
             BufferedOutputStream buffOut = new BufferedOutputStream(out)) {
            connectServer();
            sftp.get(remoteFilePath, buffOut);
            out.flush();
            flag = true;
            log.info("sftp file:{} is loaded and saved as local file:{}.", remoteFilePath, localFilePath);
        } catch (Exception e) {
            log.error("从 {} 下载到 {} 异常！", remoteFilePath, localFilePath, e);
            flag = false;
        }
        if (!flag && retryCount <= sftpConnectConf.getReconnectCount()) {
            // 下载失败 且未到最大重试次数   重试
            log.info("downloadFile {} 第{}次尝试重新连接......", remoteFilePath, retryCount);
            this.closeConnect();
            try {
                Thread.sleep(retryCount * 5000L);
            } catch (InterruptedException e) {
                //
            }
            flag = downloadFile(localFilePath, remoteFilePath, ++retryCount);
        }
        return flag;
    }

    public void listFTPFileInfo(String absoluteDir, List<FTPFileAttr> fileInfoList) throws Exception {
        SftpConnectConf sftpConnectConf = SftpConnectConfThreadManager.getSftpConnectConf(getServerType());
        log.debug("listFTPFileInfo>>> SFTP ls dir: " + absoluteDir);
        Vector vfiles = listFiles(absoluteDir, 1);
        log.info("listFTPFileInfo>>> SFTP ls dir: " + absoluteDir + " " + vfiles.size());
        SftpLogThreadLocalUtil.appendLogTxt(String.format("listFTPFileInfo>>> SFTP ls dir: %s %s", absoluteDir, vfiles.size()));
        for (Object item : vfiles) {
            FTPFileAttr ftpFileAttr = new FTPFileAttr();
            if (item instanceof ChannelSftp.LsEntry) {
                ChannelSftp.LsEntry lsEntry = (ChannelSftp.LsEntry) item;

                String fileName = lsEntry.getFilename();

                String subDir = "";
                if (lsEntry.getAttrs().isDir() && !(fileName.equals(".") || fileName.equals(".."))) {
                    if (!absoluteDir.endsWith("/")) {
                        absoluteDir = absoluteDir + "/";
                    }
                    subDir = absoluteDir + fileName + "/";

                    //根据黑名单目录配置优化
                    if (isBlackRemotePath(subDir, sftpConnectConf.getBlackFileRemoteFullNameRegex())) {
                        continue;
                    }

                    listFTPFileInfo(subDir, fileInfoList);

                } else {

                    if (fileName.equals(".") || fileName.equals("..")) {
                        continue;
                    }
                    SftpATTRS sftpATTRS = lsEntry.getAttrs();
                    int uid = sftpATTRS.getUId();
                    int gid = sftpATTRS.getGId();
                    int permissions = sftpATTRS.getPermissions();
                    long size = sftpATTRS.getSize();
                    long mTime = sftpATTRS.getMTime() * 1000L + sftpConnectConf.getTimeDifference() * 1000L;
                    long aTime = sftpATTRS.getATime() * 1000L + sftpConnectConf.getTimeDifference() * 1000L;
                    boolean isDir = sftpATTRS.isDir();
                    String fileSuffix = "";
                    if (!isDir) {
                        if (fileName.lastIndexOf(".") + 1 < fileName.length()) {
                            fileSuffix = fileName.substring(fileName.lastIndexOf(".") + 1);
                        } else {
                            fileSuffix = "";
                        }
                    }

                    ftpFileAttr.setFileName(fileName);
                    ftpFileAttr.setFilePath(absoluteDir);
                    if (absoluteDir.endsWith("/")) {
                        ftpFileAttr.setFullName(absoluteDir + fileName);
                    } else {
                        ftpFileAttr.setFullName(absoluteDir + "/" + fileName);
                    }
                    ftpFileAttr.setGid(gid);
                    ftpFileAttr.setUid(uid);
                    ftpFileAttr.setPermissions(permissions);
                    ftpFileAttr.setSize(size);
                    ftpFileAttr.setMTime(mTime);
                    ftpFileAttr.setATime(aTime);
                    ftpFileAttr.setDir(isDir);
                    ftpFileAttr.setFileSuffix(fileSuffix);

                    if (mTime < sftpConnectConf.getFileModifiedRealStartDate().getTime() || mTime > sftpConnectConf.getFileModifiedRealEndDate().getTime()) {
                        continue;
                    }
                    if (getServerType() == FTPServerType.NORMAL) {
                        FileCategory fileCategory = matchFileCategoryWithRemoteFileFullName(ftpFileAttr.getFullName(), SftpCollectRuleThreadManager.getSftpCollectRule());
                        if (!NOT_NEED_PULL_FILE_CATEGORY_LIST.contains(fileCategory)) {
                            ftpFileAttr.setFileCategory(fileCategory);
                        } else {
                            SftpLogThreadLocalUtil.appendLogTxt("文件名：" + ftpFileAttr.getFullName() + "匹配的fileCategory: " + fileCategory + " 不进行下载！");
                            continue;
                        }
                    } else {
                        ftpFileAttr.setFileCategory(FileCategory.OTHER);
                    }
                    fileInfoList.add(ftpFileAttr);
                }
            }
        }
    }

    @Override
    public FTPFileAttr ftpFileInfo(String absoluteDir, String sourceFileName) throws Exception {
        SftpConnectConf sftpConnectConf = SftpConnectConfThreadManager.getSftpConnectConf(getServerType());
        log.debug("ftpFileInfo >>> SFTP ls dir: " + absoluteDir);
        Vector vfiles = listFiles(absoluteDir, 1);
        log.info("ftpFileInfo >>> SFTP ls dir: " + absoluteDir + " " + vfiles.size());
        for (Object item : vfiles) {
            FTPFileAttr ftpFileAttr = new FTPFileAttr();
            if (item instanceof ChannelSftp.LsEntry) {
                ChannelSftp.LsEntry lsEntry = (ChannelSftp.LsEntry) item;

                String fileName = lsEntry.getFilename();

                String subDir = "";
                if (lsEntry.getAttrs().isDir() && !(fileName.equals(POINT) || fileName.equals(".."))) {
                    if (!absoluteDir.endsWith(SLASH)) {
                        absoluteDir = absoluteDir + SLASH;
                    }
                    subDir = absoluteDir + fileName + SLASH;

                    //根据黑名单目录配置优化
                    if (isBlackRemotePath(subDir, sftpConnectConf.getBlackFileRemoteFullNameRegex())) {
                        continue;
                    }

                    ftpFileInfo(subDir, fileName);

                } else {

                    if (fileName.equals(POINT) || fileName.equals("..") || !fileName.equals(sourceFileName)) {
                        continue;
                    }
                    SftpATTRS sftpATTRS = lsEntry.getAttrs();
                    int uid = sftpATTRS.getUId();
                    int gid = sftpATTRS.getGId();
                    int permissions = sftpATTRS.getPermissions();
                    long size = sftpATTRS.getSize();
                    long mTime = sftpATTRS.getMTime() * 1000L + sftpConnectConf.getTimeDifference() * 1000L;
                    long aTime = sftpATTRS.getATime() * 1000L + sftpConnectConf.getTimeDifference() * 1000L;
                    boolean isDir = sftpATTRS.isDir();
                    String fileSuffix = "";
                    if (!isDir) {
                        if (fileName.lastIndexOf(POINT) + 1 < fileName.length()) {
                            fileSuffix = fileName.substring(fileName.lastIndexOf(POINT) + 1);
                        } else {
                            fileSuffix = EMPTY;
                        }
                    }

                    ftpFileAttr.setFileName(fileName);
                    ftpFileAttr.setFilePath(absoluteDir);
                    if (absoluteDir.endsWith(SLASH)) {
                        ftpFileAttr.setFullName(absoluteDir + fileName);
                    } else {
                        ftpFileAttr.setFullName(absoluteDir + SLASH + fileName);
                    }
                    ftpFileAttr.setGid(gid);
                    ftpFileAttr.setUid(uid);
                    ftpFileAttr.setPermissions(permissions);
                    ftpFileAttr.setSize(size);
                    ftpFileAttr.setMTime(mTime);
                    ftpFileAttr.setATime(aTime);
                    ftpFileAttr.setDir(isDir);
                    ftpFileAttr.setFileSuffix(fileSuffix);

                    ftpFileAttr.setFileCategory(FileCategory.OTHER);
                    return ftpFileAttr;
                }
            }
        }
        return null;
    }

    @Override
    public boolean moveFile(String remoteSourcePath, String remoteTargetPath) throws Exception {
        connectServer();
        sftp.rename(remoteSourcePath, remoteTargetPath);
        return true;
    }

    @Override
    public void mkdir(String remotePath) throws Exception {
        connectServer();
        String[] dirs = remotePath.split(SLASH);
        String dir = "";
        for (String s : dirs) {
            if (StringUtils.isNotEmpty(s)) {
                dir += SLASH + s;
                if (!checkPathExists(dir)) {
                    log.info("创建目录 {} ", dir);
                    sftp.mkdir(dir);
                }
            }
        }
    }

    @Override
    public boolean deleteFile(String sourceFilePath) throws Exception {
        connectServer();
        try {
            sftp.rm(sourceFilePath);
            return true;
        } catch (SftpException e) {
            if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                return false;
            } else {
                throw e;
            }
        }
    }

    @Override
    public boolean uploadFile(String localFilePath, String targetFilePath) throws Exception {
        connectServer();
        mkdir(targetFilePath);
        String localFileShortName = FileUtils.getFileName(localFilePath);
        String uploadFileFullName = FileUtils.getFileFullName(targetFilePath, localFileShortName);
        deleteFile(uploadFileFullName);
        try (InputStream in = new FileInputStream(localFilePath);
             BufferedInputStream buffIn = new BufferedInputStream(in)) {
            sftp.put(buffIn, uploadFileFullName);
        } catch (Exception e) {
            log.error("上传 {} 到 {} 异常！", localFilePath, localFilePath, e);
            throw e;
        }
        return true;
    }

    private boolean checkPathExists(String path) throws Exception {
        try {
            sftp.stat(path);
            return true;
        } catch (SftpException e) {
            if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                return false;
            } else {
                throw e;
            }
        }
    }

    private Vector listFiles(String absoluteDir, int retryCount) throws Exception {
        SftpConnectConf sftpConnectConf = SftpConnectConfThreadManager.getSftpConnectConf(getServerType());
        try {
            connectServer();
            return sftp.ls(absoluteDir);
        } catch (Exception e) {
            if (e instanceof SftpException && ((SftpException) e).id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                log.info("listFiles {} 不合法，放弃处理", absoluteDir);
                SftpLogThreadLocalUtil.appendLogTxt("listFiles " + absoluteDir + " 不合法，放弃处理");
                return new Vector();
            }
            if (retryCount > sftpConnectConf.getReconnectCount()) {
                throw e;
            } else {
                log.info("listFiles {} 第{}次尝试重新连接......{}", absoluteDir, retryCount, e.getMessage());
                this.closeConnect();
                try {
                    Thread.sleep(retryCount * 5000L);
                } catch (InterruptedException interruptedException) {
                    //
                }
                return listFiles(absoluteDir, ++retryCount);
            }
        }
    }

    /**
     * 判断是否连接
     */
    public boolean isConnected() {
        return sftp.isConnected();
    }

    /**
     * 关闭连接
     */
    public void closeConnect() {
        try {
            sftp.disconnect();
            Session session = sftp.getSession();
            if (session != null && session.isConnected()) {
                session.disconnect();
            }
            sftp.quit();
        } catch (Exception e) {
            log.error(e.toString(), e);
        } finally {
            try {
                if (sftp.isConnected()) {
                    sftp.disconnect();
                    sftp.quit();
                }
            } catch (Exception e) {
                log.error(e.toString(), e);
            }
        }
    }
}
