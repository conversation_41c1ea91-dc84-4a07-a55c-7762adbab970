package com.guwave.onedata.dataware.source.agent.manual.handler;

import com.alibaba.fastjson.JSON;
import com.guwave.onedata.dataware.common.contant.ProcessStatus;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualMessage;
import com.guwave.onedata.dataware.parser.stdf.util.CommonStdfParseUtil;
import com.guwave.onedata.dataware.source.agent.manual.model.ManualLotWafer;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

public interface ManualCalculateTaskHandler {


    default ManualFileInfo fillManualFileInfoFromManualLotWafer(ManualLotWafer manualLotWafer, ManualFileInfo manualFileInfo) {
        Date date = new Date();
        HashMap<String, String> extInfo = new HashMap<>();
        manualLotWafer.getOriginFileInfos().forEach(t -> extInfo.putAll(JSON.parseObject(t.getExtInfo(), HashMap.class)));
        Map<String, Long> testItemCntMap = new HashMap<>();
        manualLotWafer.getOriginFileInfos().forEach(t -> {
            CommonStdfParseUtil.calculateTestItemCntMap(t.getMinRecordTestItem(), t.getMinRecordTestItemCnt(), testItemCntMap);
            CommonStdfParseUtil.calculateTestItemCntMap(t.getMaxRecordTestItem(), t.getMaxRecordTestItemCnt(), testItemCntMap);
        });
        List<Map.Entry<String, Long>> testItemCntSort = testItemCntMap.entrySet().stream().sorted(Comparator.comparingLong(Map.Entry::getValue)).collect(Collectors.toList());
        Map.Entry<String, Long> testItemCntMin = testItemCntSort.get(0);
        Map.Entry<String, Long> testItemCntMax = testItemCntSort.get(testItemCntSort.size() - 1);

        return manualFileInfo
                .setCustomer(manualLotWafer.getCustomer())
                .setSubCustomer(manualLotWafer.getSubCustomer())
                .setFileCategory(manualLotWafer.getFileCategory())
                .setFileSize(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getFileSize).reduce(0L, Long::sum))
                .setOriginFileIds(manualLotWafer.getOriginFileInfos().stream().map(t -> t.getFileId() + EMPTY).collect(Collectors.joining(COMMA)))
                .setTestArea(manualLotWafer.getTestArea())
                .setFactory(manualLotWafer.getFactory())
                .setFactorySite(manualLotWafer.getFactorySite())
                .setDeviceId(manualLotWafer.getDeviceId())
                .setLotId(manualLotWafer.getLotId())
                .setWaferNo(manualLotWafer.getWaferNo())
                .setLotType(manualLotWafer.getLotType())
                .setSblotId(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getSblotId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setWaferId(manualLotWafer.getOriginFileInfos().get(0).getWaferId())
                .setOriginWaferId(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getOriginWaferId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setTestStage(manualLotWafer.getTestStage())
                .setTestCod(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getTestCod).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setFlowId(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getFlowId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setTesterName(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getTesterName).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setTesterType(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getTesterType).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setTestProgram(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getTestProgram).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setTestProgramVersion(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getTestProgramVersion).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setTestTemperature(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getTestTemperature).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setProberHandlerId(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getProberHandlerId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setPkgTyp(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getPkgTyp).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setRetestBinNum(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getRetestBinNum).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setPosX(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getPosX).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setPosY(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getPosY).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setNotch(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getNotch).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setFloorId(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getFloorId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setProcess(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getProcess).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(COMMA)))
                .setStartT(new Date(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getStartT).filter(Objects::nonNull).mapToLong(Date::getTime).min().orElse(0L)))
                .setFinishT(new Date(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getFinishT).filter(Objects::nonNull).mapToLong(Date::getTime).max().orElse(0L)))
                .setExtInfo(JSON.toJSONString(extInfo))
                .setDieDataCount(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getDieDataCount).filter(Objects::nonNull).reduce(0L, Long::sum))
                .setTestItemDataCount(manualLotWafer.getOriginFileInfos().stream().map(ManualFileInfo::getTestItemDataCount).filter(Objects::nonNull).reduce(0L, Long::sum))
                .setMaxRecordTestItem(testItemCntMax.getKey())
                .setMaxRecordTestItemCnt(testItemCntMax.getValue())
                .setMinRecordTestItem(testItemCntMin.getKey())
                .setMinRecordTestItemCnt(testItemCntMin.getValue())
                .setUpdateUser(SYSTEM)
                .setUpdateTime(date);
    }

    default ManualCalculateTask generateManualCalculateTaskFromManualFileInfo(ManualMessage manualMessage, ManualFileInfo manualFileInfo) {
        Date date = new Date();
        return new ManualCalculateTask()
                .setMessageId(manualMessage.getId())
                .setFileId(manualFileInfo.getFileId())
                .setFileName(manualFileInfo.getFileName())
                .setFileCategory(manualFileInfo.getFileCategory())
                .setUploadType(manualFileInfo.getUploadType())
                .setFileOwner(manualFileInfo.getFileOwner())
                .setCustomer(manualFileInfo.getCustomer())
                .setSubCustomer(manualFileInfo.getSubCustomer())
                .setTestArea(manualFileInfo.getTestArea())
                .setFactory(manualFileInfo.getFactory())
                .setFactorySite(manualFileInfo.getFactorySite())
                .setDeviceId(manualFileInfo.getDeviceId())
                .setLotId(manualFileInfo.getLotId())
                .setWaferNo(manualFileInfo.getWaferNo())
                .setLotType(manualFileInfo.getLotType())
                .setTestStage(manualFileInfo.getTestStage())
                .setPriority(0)
                .setProcessStatus(ProcessStatus.CREATE)
                .setCreateUser(SYSTEM)
                .setUpdateUser(SYSTEM)
                .setCreateTime(date)
                .setUpdateTime(date);
    }

}
