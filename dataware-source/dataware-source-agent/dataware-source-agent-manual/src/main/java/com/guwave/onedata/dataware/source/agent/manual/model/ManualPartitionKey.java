package com.guwave.onedata.dataware.source.agent.manual.model;

import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.ManualType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.contant.UploadType;

import java.util.Objects;

import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;

public class ManualPartitionKey {
    private String customer = EMPTY;

    private String subCustomer = EMPTY;

    private TestArea testArea;

    private String factory = EMPTY;

    private String deviceId = EMPTY;

    private String lotId = EMPTY;

    private LotType lotType;

    private UploadType uploadType;

    private String testStage = EMPTY;

    private String waferId = EMPTY;

    private Long fileId;


    public ManualPartitionKey(String customer, String subCustomer, TestArea testArea, String lotId, String factory, String deviceId, UploadType uploadType) {
        this.customer = customer;
        this.subCustomer = subCustomer;
        this.testArea = testArea;
        this.lotId = lotId;
        this.factory = factory;
        this.deviceId = deviceId;
        this.uploadType = uploadType;
    }


    public ManualPartitionKey(String customer, String subCustomer, TestArea testArea, String lotId, String factory, String deviceId, UploadType uploadType, Long fileId) {
        this.customer = customer;
        this.subCustomer = subCustomer;
        this.testArea = testArea;
        this.lotId = lotId;
        this.factory = factory;
        this.deviceId = deviceId;
        this.uploadType = uploadType;
        this.fileId = fileId;
    }


    public ManualPartitionKey(String customer, String subCustomer, TestArea testArea, String factory, String deviceId, String lotId, LotType lotType, UploadType uploadType, String testStage, String waferId, Long fileId) {
        this.customer = customer;
        this.subCustomer = subCustomer;
        this.testArea = testArea;
        this.factory = factory;
        this.deviceId = deviceId;
        this.lotId = lotId;
        this.lotType = lotType;
        this.uploadType = uploadType;
        this.testStage = testStage;
        this.waferId = waferId;
        this.fileId = fileId;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getSubCustomer() {
        return subCustomer;
    }

    public void setSubCustomer(String subCustomer) {
        this.subCustomer = subCustomer;
    }

    public TestArea getTestArea() {
        return testArea;
    }

    public void setTestArea(TestArea testArea) {
        this.testArea = testArea;
    }

    public String getFactory() {
        return factory;
    }

    public void setFactory(String factory) {
        this.factory = factory;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getLotId() {
        return lotId;
    }

    public void setLotId(String lotId) {
        this.lotId = lotId;
    }

    public LotType getLotType() {
        return lotType;
    }

    public void setLotType(LotType lotType) {
        this.lotType = lotType;
    }

    public String getTestStage() {
        return testStage;
    }

    public void setTestStage(String testStage) {
        this.testStage = testStage;
    }

    public UploadType getUploadType() {
        return uploadType;
    }

    public void setUploadType(UploadType uploadType) {
        this.uploadType = uploadType;
    }

    public String getWaferId() {
        return waferId;
    }

    public void setWaferId(String waferId) {
        this.waferId = waferId;
    }

    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ManualPartitionKey that = (ManualPartitionKey) o;
        return Objects.equals(customer, that.customer) && Objects.equals(subCustomer, that.subCustomer) && testArea == that.testArea && Objects.equals(factory, that.factory) && Objects.equals(deviceId, that.deviceId) && Objects.equals(lotId, that.lotId) && Objects.equals(lotType, that.lotType) && uploadType == that.uploadType && Objects.equals(testStage, that.testStage) && Objects.equals(waferId, that.waferId) && Objects.equals(fileId, that.fileId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(customer, subCustomer, testArea, factory, deviceId, lotId, lotType, uploadType, testStage, waferId, fileId);
    }
}
