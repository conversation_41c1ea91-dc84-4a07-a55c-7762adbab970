package com.guwave.onedata.dataware.collectx.entity;

import com.guwave.onedata.dataware.common.contant.FileCategory;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 */
@Data
public class FTPFileAttr implements Comparable {
    private String filePath;
    private String fileName;
    private String fullName;
    private int uid;
    private int gid;
    private int permissions;
    private long size;
    private long aTime;
    private long mTime;

    private boolean isDir;
    private String fileSuffix;

    private FileCategory fileCategory;
    private String sourceFileNames;

    //for FTP type
    private String group;
    private String user;
    private String rawListingString;

    @Override
    public int compareTo(Object o) {
        if (this == null && o == null) {
            return 0;
        } else if (this == null) {
            return -1;
        } else if (o == null) {
            return 1;
        } else if (o instanceof FTPFileAttr) {
            if (this.getMTime() > ((FTPFileAttr) o).getMTime()) {
                return 1;
            } else if (this.getMTime() < ((FTPFileAttr) o).getMTime()) {
                return -1;
            } else {
                return 0;
            }
        } else {
            return 0;
        }
    }

    public static void main(String[] args) {
        FTPFileAttr ftpFileAttr = new FTPFileAttr();
        FTPFileAttr ftpFileAttr2 = new FTPFileAttr();
        FTPFileAttr ftpFileAttr3 = new FTPFileAttr();
        ftpFileAttr.setMTime(1);
        System.out.println(ftpFileAttr2.getMTime());

        List ftpFileAttrs = new ArrayList();
        ftpFileAttrs.add(ftpFileAttr);
        ftpFileAttrs.add(ftpFileAttr2);
        ftpFileAttrs.add(ftpFileAttr3);
        Collections.sort(ftpFileAttrs);
        System.out.println(ftpFileAttr.compareTo(ftpFileAttr2));
    }
}
