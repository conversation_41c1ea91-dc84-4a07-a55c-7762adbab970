package com.guwave.onedata.dataware.source.agent.manual.listener;

import com.google.common.collect.Sets;
import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.common.contant.FileLoadExceptionInfo;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.raw.DieData;
import com.guwave.onedata.dataware.common.model.raw.TestItemData;
import com.guwave.onedata.dataware.common.util.TestItemUtil;
import com.guwave.onedata.dataware.parser.stdf.model.FileMainData;
import com.guwave.onedata.dataware.parser.stdf.util.CommonRawDataParseUtil;
import com.guwave.onedata.dataware.parser.stdf.util.CommonStdfParseUtil;
import com.guwave.onedata.dataware.parser.stdf.util.StdfMultiThreadParseUtil;
import com.guwave.onedata.dataware.parser.stdf.visitor.Visitor;
import com.guwave.onedata.dataware.source.agent.common.visitor.MultiThreadVisitor;
import com.guwave.onedata.dataware.source.agent.manual.util.RawDataCommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.guwave.onedata.dataware.common.contant.Constant.ALL;
import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;

public class RawDataExcelReader implements ExcelConsumer<Map<String, List<Pair<String, String>>>> {

    private Long noCPartIdCnt = 0L;
    private Long testOrder = 0L;

    private Visitor visitor;
    private Function<TestArea, TestArea> testAreaFunction;
    private final Long maxNoCPartIdCnt;
    private final Map<String, BiConsumer<String, TestItemData>> otherFieldConsumerMap;
    private final Map<String, BiConsumer<Pair<String, String>, TestItemData>> mapFieldConsumerMap;

    private final FileMainData fileMainData = new FileMainData();
    private final Map<Integer, DieData> dieDataMap = new HashMap<>();

    public RawDataExcelReader(Visitor visitor, Function<TestArea, TestArea> testAreaFunction, Long maxNoCPartIdCnt, Map<String, BiConsumer<String, TestItemData>> otherFieldConsumerMap, Map<String, BiConsumer<Pair<String, String>, TestItemData>> mapFieldConsumerMap) {
        this.visitor = visitor;
        this.testAreaFunction = testAreaFunction;
        this.maxNoCPartIdCnt = maxNoCPartIdCnt;
        this.otherFieldConsumerMap = otherFieldConsumerMap;
        this.mapFieldConsumerMap = mapFieldConsumerMap;
    }

    @Override
    public void dealLine(Map<String, List<Pair<String, String>>> fieldWithContentListMap) {
        TestItemData testItemData = new TestItemData();
        fieldWithContentListMap.forEach((field, contentList) -> {
            BiConsumer<String, TestItemData> commonTestItemDataConsumer = RawDataCommonUtil.RAW_DATA_COMMON_FIELD_CONSUMER_MAP.get(field);
            if (commonTestItemDataConsumer != null) {
                try {
                    commonTestItemDataConsumer.accept(contentList.get(0).getValue(), testItemData);
                } catch (Exception e) {
                    throw new RuntimeException(String.format("RawData文件%s数据异常, 异常数据: columnName=%s, columnValue=%s", fileMainData.getFileName(), field, contentList.get(0).getValue()), e);
                }
            }
            BiConsumer<String, TestItemData> otherTestItemDataConsumer = otherFieldConsumerMap.get(field);
            if (otherTestItemDataConsumer != null) {
                otherTestItemDataConsumer.accept(contentList.get(0).getValue(), testItemData);
            }
            BiConsumer<Pair<String, String>, TestItemData> mapFieldConsumer = mapFieldConsumerMap.get(field);
            if (mapFieldConsumer != null) {
                contentList.forEach(contentPair -> {
                    mapFieldConsumer.accept(contentPair, testItemData);
                });
            }
        });

        testItemData.setTestOrder(this.testOrder++);
        testItemData.setTestItem(TestItemUtil.buildTestItem(testItemData.getTestNum(), testItemData.getTestTxt()));
        if (testItemData.getcPartId() == null && StringUtils.isNotBlank(testItemData.getPartId())) {
            testItemData.setcPartId(RawDataCommonUtil.getIntegerValue(testItemData.getPartId()));
        }
        if (testItemData.getcPartId() == null) {
            this.noCPartIdCnt++;
            testItemData.setcPartId(testItemData.getTestOrder().intValue());
        }
        if (testItemData.getOnlineRetest() == null) {
            testItemData.setOnlineRetest(0);
        }
        if (StringUtils.isBlank(testItemData.getTestitemType())) {
            testItemData.setTestitemType(Constant.P);
        }
        if (testItemData.getTestResult() == null) {
            testItemData.setTestResult(1);
        }
        if (noCPartIdCnt > maxNoCPartIdCnt) {
            throw new RuntimeException("请补充PART_ID或者C_PART_ID信息");
        }
        if (this.testOrder == 1L) {
            // 此时是第一条记录
            initFileMainData(testItemData);
            visitor.beforeFile(fileMainData);
            fileMainData.valid();
        }

        // 通用信息填充
        CommonRawDataParseUtil.fillTestItemData(testItemData, fileMainData);
        testItemData.setProberHandlerId(fileMainData.getProberHandlerId());
        Map<String, String> conditionSet = fileMainData.getConditionSet();
        if (conditionSet != null && !conditionSet.isEmpty()) {
            testItemData.setConditionSet(conditionSet);
        }

        if (StdfMultiThreadParseUtil.CP_TEST_AREA_LIST.contains(fileMainData.getTestArea())) {
            if (testItemData.getxCoord() == null || testItemData.getyCoord() == null) {
                throw new RuntimeException("CP类型的文件 X、Y 坐标不能为空");
            }
        }

        setDefaultValue(testItemData);

        // 若dieData不存在则构建
        dieDataMap.putIfAbsent(testItemData.getcPartId(), CommonRawDataParseUtil.buildDieData(testItemData));

        // 处理testItemData
        visitor.handleTestItemData(testItemData);
    }

    private void setDefaultValue(TestItemData testItemData) {
        if (!MultiThreadVisitor.NOT_CAL_TEST_ITEM_TEST_AREAS.contains(fileMainData.getTestArea())) {
            if (testItemData.getTestNum() == null && StringUtils.isBlank(testItemData.getTestTxt())) {
                throw new FileLoadException(FileLoadExceptionInfo.STDF_NOT_FOUND_TEST_DATA_EXCEPTION, "TEST_NUM和TEST_TXT不能都为空", null);
            }
            if (testItemData.getTestNum() == null) {
                testItemData.setTestNum(0L);
            }
            if (StringUtils.isBlank(testItemData.getTestTxt())) {
                testItemData.setTestTxt(testItemData.getTestNum() + EMPTY);
            }
            testItemData.setTestItem(TestItemUtil.buildTestItem(testItemData.getTestNum(), testItemData.getTestTxt()));
        }

        if (testItemData.getHbinNum() == null) {
            testItemData.setHbinNum(65535L);
        }
        if (StringUtils.isBlank(testItemData.getHbinPf())) {
            testItemData.setHbinPf(Constant.U);
        }
        if (StringUtils.isBlank(testItemData.getHbinNam())) {
            testItemData.setHbinNam(Constant.HBIN_NAME_PREFIX + testItemData.getHbinNum());
        }

        if (testItemData.getSbinNum() == null) {
            testItemData.setSbinNum(65535L);
        }
        if (StringUtils.isBlank(testItemData.getSbinPf())) {
            testItemData.setSbinPf(Constant.U);
        }
        if (StringUtils.isBlank(testItemData.getSbinNam())) {
            testItemData.setSbinNam(Constant.SBIN_NAME_PREFIX + testItemData.getSbinNum());
        }
    }

    private void initFileMainData(TestItemData testItemData) {
        HashSet<TestArea> blackAreas = Sets.newHashSet(TestArea.WAT);
        List<TestArea> testAreas = Stream.of(TestArea.values())
                .filter(item -> !blackAreas.contains(item))
                .collect(Collectors.toList());
        TestArea testArea = TestArea.of(testItemData.getTestArea());
        if (!testAreas.contains(testArea)) {
            testArea = TestArea.NA;
        }
        testArea = testAreaFunction.apply(testArea);

        String testStage = testItemData.getTestStage();
        if (StringUtils.isBlank(testStage)) {
            testStage = testItemData.getTestCod();
        }
        if (StringUtils.isBlank(testStage)) {
            testStage = testArea.getArea() + "1";
        }
        if (testArea == TestArea.NA) {
            testStage = testArea.getArea();
        }

        String waferNo = convertNullStr(testItemData.getWaferNo());
        if (StringUtils.isBlank(waferNo) && StdfMultiThreadParseUtil.CP_TEST_AREA_LIST.contains(testArea)) {
            waferNo = CommonStdfParseUtil.predictWaferNo(testItemData.getLotId(), testItemData.getWaferId());
        }


        String startT = testItemData.getStartTime() == null ? Constant.EMPTY : testItemData.getStartTime() + Constant.EMPTY;
        String finishT = testItemData.getEndTime() == null ? Constant.EMPTY : testItemData.getEndTime() + Constant.EMPTY;
        if (StringUtils.isEmpty(startT)) {
            startT = finishT;
        }
        if (StringUtils.isEmpty(finishT)) {
            finishT = startT;
        }
        if (StringUtils.isEmpty(startT)) {
            startT = 0L + EMPTY;
            finishT = 0L + EMPTY;
        }

        LotType lotType = LotType.of(testItemData.getLotType());
        if (lotType == null) {
            lotType = LotType.EMPTY;
        }

        String retestBinNum = testItemData.getRetestBinNum();
        if (StringUtils.isBlank(retestBinNum)) {
            retestBinNum = ALL;
        }

        fileMainData
                .setTestArea(testArea)
                .setTestStage(testStage)
                .setFactory(convertNullStr(testItemData.getFactory()))
                .setFactorySite(convertNullStr(testItemData.getFactorySite()))
                .setLotType(lotType)
                .setDeviceId(convertNullStr(testItemData.getDeviceId()))
                .setLotId(convertNullStr(testItemData.getLotId()))
                .setOriginWaferId(convertNullStr(testItemData.getWaferId()))
                .setWaferNo(waferNo)
                .setSblotId(convertNullStr(testItemData.getSblotId()))
                .setTestProgram(convertNullStr(testItemData.getTestProgram()))
                .setTestTemperature(convertNullStr(testItemData.getTestTemperature()))
                .setTestProgramVersion(convertNullStr(testItemData.getTestProgramVersion()))
                .setTesterName(convertNullStr(testItemData.getTesterName()))
                .setTesterType(convertNullStr(testItemData.getTesterType()))
                .setProberHandlerId(convertNullStr(testItemData.getProberHandlerId()))
                .setProbecardLoadboardId(convertNullStr(testItemData.getProbecardLoadboardId()))
                .setStartT(startT)
                .setFinishT(finishT)
                .setTestCod(convertNullStr(testItemData.getTestCod()))
                .setPkgTyp(convertNullStr(testItemData.getPkgTyp()))
                .setFloorId(convertNullStr(testItemData.getFloorId()))
                .setRetestBinNum(retestBinNum)
                .setPosX(convertNullStr(testItemData.getPosX()))
                .setPosY(convertNullStr(testItemData.getPosY()))
                .setNotch(convertNullStr(testItemData.getWfFlat()))
                .setProcess(convertNullStr(testItemData.getProcess()))
                .setOfflineRetest(testItemData.getOfflineRetest())
                .setOfflineRetestIgnoreTp(testItemData.getOfflineRetestIgnoreTp())
                .setDupRetest(testItemData.getDupRetest())
                .setDupRetestIgnoreTp(testItemData.getDupRetestIgnoreTp())
                .setBatchNum(testItemData.getBatchNum())
                .setBatchNumIgnoreTp(testItemData.getBatchNumIgnoreTp())
                .setInterrupt(testItemData.getInterrupt())
                .setInterruptIgnoreTp(testItemData.getInterruptIgnoreTp())
        ;
    }

    @Override
    public void after() {
        // do nothing
    }

    public void dealRemain() {
        // 计算waferCnt
        CommonRawDataParseUtil.calculateWaferCnt(dieDataMap, fileMainData);

        for (DieData dieData : dieDataMap.values()) {
            // 处理die_data
            visitor.handleDieData(dieData);
        }

        visitor.afterFile();
    }


    private String convertNullStr(String str) {
        if (str == null) {
            return Constant.EMPTY;
        }
        return str;
    }
}
