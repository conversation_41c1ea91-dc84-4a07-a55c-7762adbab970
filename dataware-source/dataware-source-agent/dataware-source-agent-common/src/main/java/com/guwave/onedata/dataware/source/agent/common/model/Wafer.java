package com.guwave.onedata.dataware.source.agent.common.model;

import com.guwave.onedata.dataware.common.contant.FileCategory;
import com.guwave.onedata.dataware.common.contant.LotType;
import com.guwave.onedata.dataware.common.contant.TestArea;

import java.util.Objects;

/**
 * 2023/11/29 14:27
 * Wafer
 * <AUTHOR>
 */
public class Wafer {
    private String customer;
    private String subCustomer;
    private TestArea testArea;
    private String factory;
    private String factorySite;
    private String deviceId;
    private String lotId;
    private String waferNo;
    private String testStage;
    private FileCategory fileCategory;
    private LotType lotType;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Wafer wafer = (Wafer) o;
        return Objects.equals(customer, wafer.customer) && Objects.equals(subCustomer, wafer.subCustomer) && testArea == wafer.testArea && Objects.equals(factory, wafer.factory) && Objects.equals(factorySite, wafer.factorySite) && Objects.equals(deviceId, wafer.deviceId) && Objects.equals(lotId, wafer.lotId) && Objects.equals(waferNo, wafer.waferNo) && Objects.equals(testStage, wafer.testStage) && fileCategory == wafer.fileCategory && lotType == wafer.lotType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(customer, subCustomer, testArea, factory, factorySite, deviceId, lotId, waferNo, testStage, fileCategory, lotType);
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("Wafer{");
        sb.append("customer='").append(customer).append('\'');
        sb.append(", subCustomer='").append(subCustomer).append('\'');
        sb.append(", testArea=").append(testArea);
        sb.append(", factory='").append(factory).append('\'');
        sb.append(", factorySite='").append(factorySite).append('\'');
        sb.append(", deviceId='").append(deviceId).append('\'');
        sb.append(", lotId='").append(lotId).append('\'');
        sb.append(", waferNo='").append(waferNo).append('\'');
        sb.append(", testStage='").append(testStage).append('\'');
        sb.append(", fileCategory=").append(fileCategory);
        sb.append(", lotType=").append(lotType);
        sb.append('}');
        return sb.toString();
    }

    public String getCustomer() {
        return customer;
    }

    public Wafer setCustomer(String customer) {
        this.customer = customer;
        return this;
    }

    public String getSubCustomer() {
        return subCustomer;
    }

    public Wafer setSubCustomer(String subCustomer) {
        this.subCustomer = subCustomer;
        return this;
    }

    public TestArea getTestArea() {
        return testArea;
    }

    public Wafer setTestArea(TestArea testArea) {
        this.testArea = testArea;
        return this;
    }

    public String getFactory() {
        return factory;
    }

    public Wafer setFactory(String factory) {
        this.factory = factory;
        return this;
    }

    public String getFactorySite() {
        return factorySite;
    }

    public Wafer setFactorySite(String factorySite) {
        this.factorySite = factorySite;
        return this;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public Wafer setDeviceId(String deviceId) {
        this.deviceId = deviceId;
        return this;
    }

    public String getLotId() {
        return lotId;
    }

    public Wafer setLotId(String lotId) {
        this.lotId = lotId;
        return this;
    }

    public String getWaferNo() {
        return waferNo;
    }

    public Wafer setWaferNo(String waferNo) {
        this.waferNo = waferNo;
        return this;
    }

    public String getTestStage() {
        return testStage;
    }

    public Wafer setTestStage(String testStage) {
        this.testStage = testStage;
        return this;
    }

    public FileCategory getFileCategory() {
        return fileCategory;
    }

    public Wafer setFileCategory(FileCategory fileCategory) {
        this.fileCategory = fileCategory;
        return this;
    }

    public LotType getLotType() {
        return lotType;
    }

    public Wafer setLotType(LotType lotType) {
        this.lotType = lotType;
        return this;
    }
}
