package com.guwave.onedata.dataware.source.agent.manual.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.FileLoadException;
import com.guwave.onedata.dataware.common.model.manual.ManualCalculateFinishVo;
import com.guwave.onedata.dataware.common.model.message.ManualWarehousingFinishMessage;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualMessage;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualCalculateTaskRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualFileInfoRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.ManualMessageRepository;
import com.guwave.onedata.dataware.source.agent.common.sink.impl.KafkaSink;
import com.guwave.onedata.dataware.source.agent.common.util.FileSystemUtil;
import com.guwave.onedata.dataware.source.agent.common.util.FileUtil;
import com.guwave.onedata.dataware.source.agent.manual.util.RawDataCommonUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.tika.Tika;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.util.*;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

@Component
public class ManualUploadService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ManualUploadService.class);

    @Value("${spring.kafka.manualFinishTopic}")
    private String manualFinishTopic;
    @Value("${spring.handler.file.readPath}")
    private String readPath;


    @Autowired
    private ManualCalculateTaskRepository manualCalculateTaskRepository;
    @Autowired
    private ManualMessageRepository manualMessageRepository;
    @Autowired
    private ManualFileInfoRepository manualFileInfoRepository;
    @Autowired
    private KafkaSink kafkaSink;
    @Autowired
    private Map<String, FileSystemUtil> fileSystemUtilMap;
    @Autowired
    private List<ParseFileHandler> parseFileHandlers;
    @Value("${spring.runMode.standaloneThreshold}")
    private Long standaloneThreshold;

    private static final Map<ManualFileSystem, String> MANUAL_FILE_SYSTEM_MAP = new HashMap<ManualFileSystem, String>() {{
        put(ManualFileSystem.HDFS, "hdfsUtil");
        put(ManualFileSystem.LOCAL, "localfsUtil");
        put(ManualFileSystem.MINIO, "minIOUtil");
    }};

    @ScheduleSwitch
    @Scheduled(fixedDelayString = "${spring.scheduler.polling.milliseconds}")
    public void parseFile() {
        if (!RawDataCommonUtil.canRunTask()) {
            return;
        }

        List<ManualCalculateTask> manualCalculateTasks = manualCalculateTaskRepository.findAllByDwLayerAndProcessStatusOrderByPriorityDescIdAsc(DwLayer.ODS, ProcessStatus.CREATE, Pageable.ofSize(1));
        if (manualCalculateTasks.isEmpty()) {
            LOGGER.info("不存在需要解析的ods任务");
            return;
        }

        ManualCalculateTask manualCalculateTask = manualCalculateTasks.get(0);
        int updateCnt = manualCalculateTaskRepository.updateProcessStatusProcessingFromCreate(manualCalculateTask.getId(), new Date());
        if (updateCnt == 0) {
            LOGGER.info("{} 已经被处理", manualCalculateTask.getId());
            return;
        }

        manualCalculateTask.setProcessStatus(ProcessStatus.PROCESSING);
        if (manualCalculateTask.getFileCategory() == FileCategory.RAW_DATA) {
            RawDataCommonUtil.runTask(() -> doTask(manualCalculateTask));
        } else {
            doTask(manualCalculateTask);
        }
    }

    private void doTask(ManualCalculateTask manualCalculateTask) {
        File localFileDir = new File(readPath, UUID.randomUUID().toString());
        LOGGER.info("任务 {} 开始 删除目录 {}", manualCalculateTask.getId(), localFileDir);
        FileUtils.deleteQuietly(localFileDir);

        LOGGER.info("parseFile开始执行 manualCalculateTaskId：{}", manualCalculateTask.getId());
        ManualMessage manualMessage = manualMessageRepository.findById(manualCalculateTask.getMessageId()).get();
        ManualFileInfo manualFileInfo = manualFileInfoRepository.findByFileId(manualCalculateTask.getFileId());
        FileSystemUtil fileSystemUtil = fileSystemUtilMap.get(MANUAL_FILE_SYSTEM_MAP.get(manualFileInfo.getManualFileSystem()));
        try {
            FileUtils.forceMkdir(localFileDir);

            // 下载待解析的文件到本地
            File localFile = new File(localFileDir, manualFileInfo.getFilePath().substring(manualFileInfo.getFilePath().lastIndexOf(SLASH) + 1));
            LOGGER.info("下载不删除原文件: {} --> {}", manualFileInfo.getFilePath(), localFile.getAbsolutePath());
            fileSystemUtil.downloadToLocal(false, manualFileInfo.getFilePath(), localFile.getAbsolutePath());

            // 下载脚本文件
            File scriptFile = null;
            if (StringUtils.isNotBlank(manualFileInfo.getScriptExecuteFileName())) {
                scriptFile = new File(localFileDir, manualFileInfo.getScriptPath().substring(manualFileInfo.getScriptPath().lastIndexOf(SLASH) + 1));
                LOGGER.info("下载不删除原文件: {} --> {}", manualFileInfo.getScriptPath(), scriptFile.getAbsolutePath());
                fileSystemUtil.downloadToLocal(false, manualFileInfo.getScriptPath(), scriptFile.getAbsolutePath());
                Tika tika = new Tika();
                if (tika.detect(scriptFile).equalsIgnoreCase("application/zip")) {
                    File scriptDir = new File(localFileDir, UUID.randomUUID().toString());
                    LOGGER.info("解压文件 {} 到 {}", scriptFile.getAbsolutePath(), scriptDir.getAbsolutePath());
                    fileSystemUtil.unCompress(scriptDir.getAbsolutePath(), scriptFile.getAbsolutePath());
                    scriptFile = new File(scriptDir, manualFileInfo.getScriptExecuteFileName());
                }
            }

            // 处理文件
            Optional<ParseFileHandler> parseFileHandlerOptional = parseFileHandlers.stream().filter(handler -> handler.support(manualFileInfo.getFileCategory(), manualFileInfo.getParseRule())).findFirst();
            if (!parseFileHandlerOptional.isPresent()) {
                throw new FileLoadException(FileLoadExceptionInfo.NOT_SUPPORT_FILE, FileLoadExceptionInfo.NOT_SUPPORT_FILE.getMessage(), null);
            }

            // 根据实际的file_category以及文件类型决定是否解压
            if (getDecompressFlag(manualFileInfo, localFile)) {
                String filePath = localFile.getAbsolutePath().endsWith(POINT + FileType.ZIP.getType()) ? localFile.getAbsolutePath() : localFile.getAbsolutePath() + POINT + FileType.ZIP.getType();
                LOGGER.info("开始解压：{}", filePath);
                fileSystemUtil.unCompress(localFileDir.getAbsolutePath(), filePath);
                localFile = new File(localFileDir, FileUtil.removeFileSuffix(localFile.getName(), FileType.ZIP));
            }

            // 预解析一遍文件确定文件真正的file_category
            manualFileInfo.setFileCategory(parseFileHandlerOptional.get().calRealFileCategory(localFile, scriptFile, manualFileInfo));
            if (manualFileInfo.getFileCategory() == FileCategory.WAT) {
                manualFileInfo.setTestArea(TestArea.WAT);
            } else if (manualFileInfo.getTestArea() == TestArea.WAT) {
                manualFileInfo.setTestArea(TestArea.NA);
            }
            manualCalculateTask
                    .setFileCategory(manualFileInfo.getFileCategory())
                    .setTestArea(manualFileInfo.getTestArea());

            parseFileHandlerOptional = parseFileHandlers.stream().filter(handler -> handler.support(manualFileInfo.getFileCategory(), manualFileInfo.getParseRule())).findFirst();
            if (!parseFileHandlerOptional.isPresent()) {
                throw new FileLoadException(FileLoadExceptionInfo.NOT_SUPPORT_FILE, FileLoadExceptionInfo.NOT_SUPPORT_FILE.getMessage(), null);
            }

            // 正式解析文件
            parseFileHandlerOptional.get().dealFile(manualCalculateTask, localFile, manualFileInfo, scriptFile);
            if (!parseFileHandlerOptional.get().hasNext()) {
                return;
            }

            // 保存填充后的manualFileInfo
            Date date = new Date();
            manualFileInfo.setUpdateTime(date);
            manualFileInfoRepository.save(manualFileInfo);

            // 发送ods进度
            ManualWarehousingFinishMessage manualWarehousingFinishMessage = new ManualWarehousingFinishMessage()
                    .setManualType(manualMessage.getManualType())
                    .setManualCalculateFinishVo(new ManualCalculateFinishVo().setFileId(manualCalculateTask.getFileId()).setFileName(manualCalculateTask.getFileName()).setDwLayer(DwLayer.ODS))
                    .setProcessStatus(ProcessStatus.PROCESSING)
                    .setComment(manualFileInfo.getComment());
            String manualFinishMessageStr = JSON.toJSONString(manualWarehousingFinishMessage);
            kafkaSink.send(manualFinishTopic, manualCalculateTask.getFileId() + EMPTY, manualFinishMessageStr);

            // 结束ods任务
            manualCalculateTask
                    .setTestArea(manualFileInfo.getTestArea())
                    .setFactory(manualFileInfo.getFactory())
                    .setFactorySite(manualFileInfo.getFactorySite())
                    .setDeviceId(manualFileInfo.getDeviceId())
                    .setLotId(manualFileInfo.getLotId())
                    .setWaferNo(manualFileInfo.getWaferNo())
                    .setLotType(manualFileInfo.getLotType())
                    .setTestStage(manualFileInfo.getTestStage())
                    .setProcessStatus(ProcessStatus.SUCCESS)
                    .setUpdateTime(date);

            // 生成dwd任务
            ManualCalculateTask nextManualCalculateTask = new ManualCalculateTask();
            BeanUtils.copyProperties(manualCalculateTask, nextManualCalculateTask);
            nextManualCalculateTask
                    .setId(null)
                    .setDwLayer(DwLayer.DWD)
                    .setProcessStatus(ProcessStatus.CREATE)
                    .setCreateTime(date)
                    .setUpdateTime(date)
                    .setRunMode(manualFileInfo.getTestItemDataCount() < standaloneThreshold ? RunMode.STANDALONE : RunMode.DISTRIBUTED);

            manualCalculateTaskRepository.saveAll(Lists.newArrayList(manualCalculateTask, nextManualCalculateTask));
            LOGGER.info("parseFile执行成功 manualCalculateTaskId：{}", manualCalculateTask.getId());
        } catch (Exception e) {
            LOGGER.error("parseFile执行失败 manualCalculateTaskId：{}", manualCalculateTask.getId(), e);
            manualFileInfoRepository.save(manualFileInfo.setUpdateTime(new Date()));

            FileLoadException exception = e instanceof FileLoadException ? (FileLoadException) e : new FileLoadException(FileLoadExceptionInfo.OTHER_EXCEPTION, ExceptionUtils.getStackTrace(e), null).updateExceptionMessage(e.getMessage());
            manualMessage
                    .setProcessStatus(ProcessStatus.FAIL)
                    .setExceptionType(exception.getExceptionType())
                    .setExceptionMessage(exception.getExceptionMessage())
                    .setErrorMessage(ExceptionUtils.getStackTrace(exception));

            ManualWarehousingFinishMessage manualWarehousingFinishMessage = new ManualWarehousingFinishMessage()
                    .setManualType(manualMessage.getManualType())
                    .setManualCalculateFinishVo(new ManualCalculateFinishVo().setFileId(manualCalculateTask.getFileId()).setFileName(manualCalculateTask.getFileName()).setDwLayer(manualCalculateTask.getDwLayer()))
                    .setProcessStatus(manualMessage.getProcessStatus())
                    .setExceptionType(manualMessage.getExceptionType())
                    .setExceptionMessage(manualMessage.getExceptionMessage())
                    .setComment(manualFileInfo.getComment());
            String manualFinishMessageStr = JSON.toJSONString(manualWarehousingFinishMessage);

            Date date = new Date();
            manualMessage
                    .setManualFinishMessage(manualFinishMessageStr)
                    .setUpdateTime(date);
            manualCalculateTask
                    .setProcessStatus(manualMessage.getProcessStatus())
                    .setUpdateTime(date);

            manualCalculateTaskRepository.save(manualCalculateTask);
            manualMessageRepository.save(manualMessage);
            // 发送结束消息
            kafkaSink.send(manualFinishTopic, manualMessage.getManualType().getType(), manualMessage.getManualFinishMessage());
        } finally {
            // 删除临时目录
            LOGGER.info("任务 {} 结束 删除目录 {}", manualCalculateTask.getId(), localFileDir);
            FileUtils.deleteQuietly(localFileDir);
        }
    }

    /**
     * 判断是否需要解压
     *
     * @param manualFileInfo manualFileInfo
     * @param localFile      localFile
     * @return decompressFlag
     * @throws IOException IOException
     */
    private boolean getDecompressFlag(ManualFileInfo manualFileInfo, File localFile) throws IOException {
        // 检测文件类型是否为zip并且是STDF文件
        Tika tika = new Tika();
        if (tika.detect(localFile).equalsIgnoreCase("application/zip") && manualFileInfo.getFileCategory() == FileCategory.STDF) {
            // 如果文件名不是以zip结尾，先改为.zip结尾的文件
            if (!localFile.getAbsolutePath().endsWith(POINT + FileType.ZIP.getType())) {
                LOGGER.info("文件不是以.zip结尾的文件，先进行重命名");
                File newLocalFile = new File(localFile.getAbsolutePath() + POINT + FileType.ZIP.getType());
                localFile.renameTo(newLocalFile);
            }
            return true;
        }
        return false;
    }
}