package com.guwave.onedata.dataware.source.agent.wat.mock;

import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.model.wat.WatCommon;
import com.guwave.onedata.dataware.common.model.wat.dim.*;
import com.guwave.onedata.dataware.common.model.wat.dwd.DwdWatDieDetail;
import com.guwave.onedata.dataware.common.model.wat.dwd.DwdWatTestItemDetail;
import com.guwave.onedata.dataware.common.model.wat.ods.OdsWat;
import com.guwave.onedata.dataware.source.agent.common.util.HdfsUtil;
import com.guwave.onedata.dataware.source.agent.common.util.WatCommonUtil;
import com.guwave.onedata.dataware.source.agent.wat.fetcher.WatHandler;
import com.guwave.onedata.dataware.source.agent.wat.sink.HdfsSink;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.hdfs.DistributedFileSystem;
import org.springframework.beans.BeanUtils;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

import static com.guwave.onedata.dataware.common.contant.Constant.*;

public class HdfsParquetMock {
    public static void main(String[] args) throws Exception {
        FileSystem fileSystem = null;
        // 加载默认配置文件
        Configuration conf = new Configuration();
        conf.set("fs.hdfs.impl", DistributedFileSystem.class.getName());
        try {
            // 获得hdfs文件系统
            fileSystem = FileSystem.newInstance(URI.create("hdfs://riot11.guwave.com:8020"), conf, "glory");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        HdfsUtil hdfsUtil = new HdfsUtil(fileSystem);

        WatCommon watCommon = new WatCommon()
                .setCustomer("customer")
                .setFactory("factory")
                .setFab("fab")
                .setLotId("lotId")
                .setWaferId("waferId")
                .setStartTime(123L)
                .setEndTime(456L)
                .setDeviceId("deviceId")
                .setType(ODS_WAT)
                .setDwLayer(DwLayer.ODS.getLayer())
                .setTs(System.currentTimeMillis())
                .setFactorySite("factorySite")
                .setFabSite("fabSite")
                .setUploadType(UploadType.AUTO.getType())
                .setFileName("fileName")
                .setFileType(FileType.CSV.getType())
                .setFileId(111L)
                .setLotType(LotType.PRODUCTION.getType())
                .setTestArea(TestArea.WAT.getArea())
                .setTestStage("testStage")
                .setWaferNo("12")
                .setSubCustomer("subCustomer");

        Random random = new Random();
        ArrayList<OdsWat> odsWats = new ArrayList<>();
        for (int i = 0; i < 1000000; i++) {
            OdsWat odsWat = new OdsWat();
            BeanUtils.copyProperties(watCommon, odsWat);
            odsWat
                    .setReticleX(random.nextInt())
                    .setReticleY(random.nextInt())
                    .setSiteId(random.nextInt() + "")
                    .setWfFlat(random.nextInt() + "")
                    .setTestProgram(random.nextInt() + "")
                    .setTestTemperature(random.nextDouble() + "")
                    .setTesterName(random.nextInt() + "")
                    .setOperatorName(random.nextInt() + "")
                    .setHiLimit(random.nextDouble())
                    .setLoLimit(random.nextDouble())
                    .setTarget(random.nextDouble())
                    .setUnits(random.nextInt() + "")
                    .setTestValue(random.nextDouble())
                    .setId(random.nextInt() + "")
                    .setTestNum(random.nextLong())
                    .setIsFirstTest(random.nextInt())
                    .setIsFinalTest(random.nextInt())
                    .setIsDupFirstTest(random.nextInt())
                    .setIsDupFinalTest(random.nextInt())
                    .setProcess(random.nextInt() + "");
            odsWats.add(odsWat);
        }

        WatHandler watHandler = new WatHandler();
        List<DwdWatTestItemDetail> testItemDetails = odsWats
                .stream()
                .flatMap(item -> {
                    Long testNum = item.getTestNum();
                    String testTxt = item.getTestTxt();
                    String testItem = EMPTY;
                    if (testNum != null && StringUtils.isNotBlank(testTxt)) {
                        testItem = testNum + ":" + testTxt;
                    } else if (testNum != null) {
                        testItem = String.valueOf(testNum);
                    } else {
                        testItem = testTxt;
                    }
                    ArrayList<DwdWatTestItemDetail> dwdWatTestItemDetails = new ArrayList<>();

                    boolean fillDataFail = true;
                    if (fillDataFail) {
                        // 没有补数据时
                        DwdWatTestItemDetail vo = WatCommonUtil.buildDwdWatTestItemDetail(item, testNum, testTxt, testItem);
                        dwdWatTestItemDetails.add(vo);
                    }
                    return dwdWatTestItemDetails.stream();
                }).collect(Collectors.toList());

        List<DwdWatDieDetail> dieDetails = testItemDetails
                .stream()
                .map(item -> {
                    DwdWatDieDetail vo = new DwdWatDieDetail();
                    vo
                            .setSiteId(item.getSiteId())
                            .setWaferMargin(item.getWaferMargin())
                            .setReticleRow(item.getReticleRow())
                            .setReticleColumn(item.getReticleColumn())
                            .setReticleRowCenterOffset(item.getReticleRowCenterOffset())
                            .setReticleColumnCenterOffset(item.getReticleColumnCenterOffset())
                            .setCenterX(item.getCenterX())
                            .setCenterY(item.getCenterY())
                            .setCenterOffsetX(item.getCenterOffsetX())
                            .setCenterOffsetY(item.getCenterOffsetY())
                            .setCenterReticleX(item.getCenterReticleX())
                            .setCenterReticleY(item.getCenterReticleY())
                            .setCenterReticleOffsetX(item.getCenterReticleOffsetX())
                            .setCenterReticleOffsetY(item.getCenterReticleOffsetY())
                            .setWfUnits(item.getWfUnits())
                            .setDieHeight(item.getDieHeight())
                            .setDieWidth(item.getDieWidth())
                            .setWaferSize(item.getWaferSize())
                            .setWfFlat(item.getWfFlat())
                            .setDieCnt(item.getDieCnt())
                            .setPosX(item.getPosX())
                            .setPosY(item.getPosY())
                            .setOriginalWaferSize(item.getOriginalWaferSize())
                            .setOriginalWaferMargin(item.getOriginalWaferMargin())
                            .setOriginalWfUnits(item.getOriginalWfUnits())
                            .setOriginalWfFlat(item.getOriginalWfFlat())
                            .setOriginalPosX(item.getOriginalPosX())
                            .setOriginalPosY(item.getOriginalPosY())
                            .setOriginalDieWidth(item.getOriginalDieWidth())
                            .setOriginalDieHeight(item.getOriginalDieHeight())
                            .setOriginalReticleRow(item.getOriginalReticleRow())
                            .setOriginalReticleColumn(item.getOriginalReticleColumn())
                            .setOriginalReticleRowCenterOffset(item.getOriginalReticleRowCenterOffset())
                            .setOriginalReticleColumnCenterOffset(item.getOriginalReticleColumnCenterOffset())
                            .setOriginalCenterX(item.getOriginalCenterX())
                            .setOriginalCenterY(item.getOriginalCenterY())
                            .setOriginalCenterReticleX(item.getOriginalCenterReticleX())
                            .setOriginalCenterReticleY(item.getOriginalCenterReticleY())
                            .setOriginalCenterOffsetX(item.getOriginalCenterOffsetX())
                            .setOriginalCenterOffsetY(item.getOriginalCenterOffsetY())
                            .setOriginalCenterReticleOffsetX(item.getOriginalCenterReticleOffsetX())
                            .setOriginalCenterReticleOffsetY(item.getOriginalCenterReticleOffsetY())
                            .setxCoord(item.getxCoord())
                            .setyCoord(item.getyCoord())
                            .setDieX(item.getDieX())
                            .setDieY(item.getDieY())
                            .setReticleX(item.getReticleX())
                            .setReticleY(item.getReticleY())
                            .setReticleTX(item.getReticleTX())
                            .setReticleTY(item.getReticleTY())
                            .setWaferLotId(item.getWaferLotId())
                            .setProcess(item.getProcess())
                            .setCustomer(item.getCustomer())
                            .setSubCustomer(item.getSubCustomer())
                            .setUploadType(item.getUploadType())
                            .setFileId(item.getFileId())
                            .setFileName(item.getFileName())
                            .setFileType(item.getFileType())
                            .setLotType(item.getLotType())
                            .setTestArea(item.getTestArea())
                            .setFactory(item.getFactory())
                            .setFactorySite(item.getFactorySite())
                            .setFab(item.getFab())
                            .setFabSite(item.getFabSite())
                            .setDeviceId(item.getDeviceId())
                            .setLotId(item.getLotId())
                            .setWaferId(item.getWaferId())
                            .setFabwfId(item.getFabwfId())
                            .setStartTime(item.getStartTime())
                            .setEndTime(item.getEndTime())
                            .setWaferNo(item.getWaferNo());
                    return vo;
                }).distinct().collect(Collectors.toList());
        dieDetails.forEach(t -> t.setId(EMPTY));

        List<DimWatTestItem> testItems = testItemDetails
                .stream()
                .map(item -> {
                    DimWatTestItem vo = new DimWatTestItem();
                    vo
                            .setTestNum(item.getTestNum())
                            .setTestTxt(item.getTestTxt())
                            .setTestItem(item.getTestItem())
                            .setTestProgram(item.getTestProgram())
                            .setHiLimit(item.getHiLimit())
                            .setLoLimit(item.getLoLimit())
                            .setOriginHiLimit(item.getOriginHiLimit())
                            .setOriginLoLimit(item.getOriginLoLimit())
                            .setUnits(item.getUnits())
                            .setTarget(item.getTarget())
                            .setOriginUnits(item.getOriginUnits())
                            .setWaferLotId(item.getWaferLotId())
                            .setCustomer(item.getCustomer())
                            .setSubCustomer(item.getSubCustomer())
                            .setUploadType(item.getUploadType())
                            .setFileId(item.getFileId())
                            .setFileName(item.getFileName())
                            .setFileType(item.getFileType())
                            .setLotType(item.getLotType())
                            .setTestArea(item.getTestArea())
                            .setFactory(item.getFactory())
                            .setFactorySite(item.getFactorySite())
                            .setFab(item.getFab())
                            .setFabSite(item.getFabSite())
                            .setDeviceId(item.getDeviceId())
                            .setLotId(item.getLotId())
                            .setWaferId(item.getWaferId())
                            .setFabwfId(item.getFabwfId())
                            .setStartTime(item.getStartTime())
                            .setEndTime(item.getEndTime())
                            .setWaferNo(item.getWaferNo());
                    return vo;
                }).distinct().collect(Collectors.toList());

        List<DimWatWafer> wafers = testItemDetails
                .stream()
                .map(item -> {
                    DimWatWafer vo = new DimWatWafer();
                    vo
                            .setCustomer(item.getCustomer())
                            .setSubCustomer(item.getSubCustomer())
                            .setUploadType(item.getUploadType())
                            .setFileId(item.getFileId())
                            .setFileName(item.getFileName())
                            .setFileType(item.getFileType())
                            .setLotType(item.getLotType())
                            .setTestArea(item.getTestArea())
                            .setFactory(item.getFactory())
                            .setFactorySite(item.getFactorySite())
                            .setFab(item.getFab())
                            .setFabSite(item.getFabSite())
                            .setDeviceId(item.getDeviceId())
                            .setLotId(item.getLotId())
                            .setWaferId(item.getWaferId())
                            .setFabwfId(item.getFabwfId())
                            .setStartTime(item.getStartTime())
                            .setEndTime(item.getEndTime())
                            .setWaferNo(item.getWaferNo());
                    return vo;
                }).distinct().collect(Collectors.toList());

        List<DimWatWaferBin> watWaferBins = testItemDetails
                .stream()
                .map(item -> {
                    DimWatWaferBin vo = new DimWatWaferBin();
                    vo
                            .setWaferLotId(item.getWaferLotId())
                            .setTestProgram(item.getTestProgram())
                            .setCustomer(item.getCustomer())
                            .setSubCustomer(item.getSubCustomer())
                            .setUploadType(item.getUploadType())
                            .setFileId(item.getFileId())
                            .setFileName(item.getFileName())
                            .setFactory(item.getFactory())
                            .setFactorySite(item.getFactorySite())
                            .setFab(item.getFab())
                            .setFabSite(item.getFabSite())
                            .setTestArea(item.getTestArea())
                            .setTestStage(item.getTestStage())
                            .setLotType(item.getLotType())
                            .setDeviceId(item.getDeviceId())
                            .setLotId(item.getLotId())
                            .setWaferId(item.getWaferId())
                            .setFabwfId(item.getFabwfId())
                            .setWaferNo(item.getWaferNo());

                    return vo;
                }).distinct().collect(Collectors.toList());

        List<DimWatTestProgramSite> watTestProgramSites = testItemDetails
                .stream()
                .map(item -> {
                    DimWatTestProgramSite vo = new DimWatTestProgramSite();
                    vo
                            .setTestProgram(item.getTestProgram())
                            .setSite(DIM_TEST_PROGRAM_NOT_NULL_DEFAULT)
                            .setSiteKey(DIM_TEST_PROGRAM_NOT_NULL_DEFAULT.toString())
                            .setVersion(DIM_TEST_PROGRAM_VERSION_MAX - item.getStartTime() / 1000)
                            .setCustomer(item.getCustomer())
                            .setSubCustomer(item.getSubCustomer())
                            .setUploadType(item.getUploadType())
                            .setFactory(item.getFactory())
                            .setFactorySite(item.getFactorySite())
                            .setFab(item.getFab())
                            .setFabSite(item.getFabSite())
                            .setTestArea(item.getTestArea())
                            .setTestStage(item.getTestStage())
                            .setDeviceId(item.getDeviceId());
                    return vo;
                }).distinct().collect(Collectors.toList());

        List<DimWatTestProgramBin> watTestProgramBins = testItemDetails
                .stream()
                .map(item -> {
                    DimWatTestProgramBin vo = new DimWatTestProgramBin();
                    vo
                            .setTestProgram(item.getTestProgram())
                            .setHbinNum(DIM_TEST_PROGRAM_NOT_NULL_DEFAULT)
                            .setHbinNumKey(DIM_TEST_PROGRAM_NOT_NULL_DEFAULT.toString())
                            .setSbinNum(DIM_TEST_PROGRAM_NOT_NULL_DEFAULT)
                            .setSbinNumKey(DIM_TEST_PROGRAM_NOT_NULL_DEFAULT.toString())
                            .setVersion(DIM_TEST_PROGRAM_VERSION_MAX - item.getStartTime() / 1000)
                            .setCustomer(item.getCustomer())
                            .setSubCustomer(item.getSubCustomer())
                            .setUploadType(item.getUploadType())
                            .setFactory(item.getFactory())
                            .setFactorySite(item.getFactorySite())
                            .setFab(item.getFab())
                            .setFabSite(item.getFabSite())
                            .setTestArea(item.getTestArea())
                            .setTestStage(item.getTestStage())
                            .setDeviceId(item.getDeviceId());
                    return vo;
                }).distinct().collect(Collectors.toList());

        List<DimWatTestProgramTestItem> watTestProgramTestItems = testItemDetails
                .stream()
                .map(item -> {
                    DimWatTestProgramTestItem vo = new DimWatTestProgramTestItem();
                    vo
                            .setTestProgram(item.getTestProgram())
                            .setTestNum(item.getTestNum() == null ? DIM_TEST_PROGRAM_NOT_NULL_DEFAULT : item.getTestNum())
                            .setTestNumKey((item.getTestNum() == null ? DIM_TEST_PROGRAM_NOT_NULL_DEFAULT : item.getTestNum()).toString())
                            .setTestTxt(item.getTestTxt())
                            .setTestItem(item.getTestItem())
                            .setTestitemType(item.getTestitemType())
                            .setOriginHiLimit(item.getOriginHiLimit())
                            .setOriginLoLimit(item.getOriginLoLimit())
                            .setTarget(item.getTarget())
                            .setOriginUnits(item.getOriginUnits())
                            .setLoLimit(item.getLoLimit())
                            .setHiLimit(item.getHiLimit())
                            .setUnits(item.getUnits())
                            .setVersion(DIM_TEST_PROGRAM_VERSION_MAX - item.getStartTime() / 1000)
                            .setCustomer(item.getCustomer())
                            .setSubCustomer(item.getSubCustomer())
                            .setUploadType(item.getUploadType())
                            .setFactory(item.getFactory())
                            .setFactorySite(item.getFactorySite())
                            .setFab(item.getFab())
                            .setFabSite(item.getFabSite())
                            .setTestArea(item.getTestArea())
                            .setTestStage(item.getTestStage())
                            .setDeviceId(item.getDeviceId());
                    return vo;
                }).distinct().collect(Collectors.toList());

        HdfsSink hdfsSink = new HdfsSink(1, hdfsUtil, odsWats.get(0), "/test/cz/wat1.csv", "/user/glory/data/onedata/dataware/ods/result/{FILE_CATEGORY}/{TYPE}/customer={CUSTOMER}/test_area={TEST_AREA}/factory={FACTORY}/lot_id={LOT_ID}/", "/user/glory/data/onedata/dataware/{DW_LAYER}/result/{TYPE}/customer={CUSTOMER}/test_area={TEST_AREA}/factory={FACTORY}/lot_id={LOT_ID}/");

        Thread thread1 = new Thread(() -> {
            try {
                hdfsSink.send(ODS_WAT, odsWats);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        thread1.start();

        Thread thread2 = new Thread(() -> {
            try {
                hdfsSink.send(DWD_TEST_ITEM_DETAIL, testItemDetails);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        thread2.start();

        Thread thread3 = new Thread(() -> {
            try {
                hdfsSink.send(DWD_DIE_DETAIL, dieDetails);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        thread3.start();


        Thread thread4 = new Thread(() -> {
            try {
                hdfsSink.send(DIM_TEST_ITEM, testItems);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        thread4.start();

        Thread thread5 = new Thread(() -> {
            try {
                hdfsSink.send(DIM_LOT_WAFER, wafers);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        thread5.start();


        Thread thread6 = new Thread(() -> {
            try {
                hdfsSink.send(DIM_LOT_WAFER_BIN, watWaferBins);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        thread6.start();


        Thread thread7 = new Thread(() -> {
            try {
                hdfsSink.send(DIM_TEST_PROGRAM_SITE, watTestProgramSites);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        thread7.start();

        Thread thread8 = new Thread(() -> {
            try {
                hdfsSink.send(DIM_TEST_PROGRAM_BIN, watTestProgramBins);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        thread8.start();


        Thread thread9 = new Thread(() -> {
            try {
                hdfsSink.send(DIM_TEST_PROGRAM_TEST_ITEM, watTestProgramTestItems);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        thread9.start();

        thread1.join();
        thread2.join();
        thread3.join();
        thread4.join();
        thread5.join();
        thread6.join();
        thread7.join();
        thread8.join();
        thread9.join();

        hdfsSink.afterSend();
        hdfsSink.close();
    }
}
