package com.guwave.onedata.dataware.source.agent.manual.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.common.exception.WrongValidHeadException;
import com.guwave.onedata.dataware.common.model.stdf.Mir;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.DtrParseRule;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualCalculateTask;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.ManualFileInfo;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.DtrParseRuleRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.EcidRuleRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LotRelationSyncRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.UidRuleRepository;
import com.guwave.onedata.dataware.parser.stdf.model.ClearRule;
import com.guwave.onedata.dataware.parser.stdf.model.PrrLocation;
import com.guwave.onedata.dataware.parser.stdf.util.StdfMultiThreadParseUtil;
import com.guwave.onedata.dataware.parser.stdf.visitor.Visitor;
import com.guwave.onedata.dataware.source.agent.common.sink.ck.CkSink;
import com.guwave.onedata.dataware.source.agent.common.util.FileSystemUtil;
import com.guwave.onedata.dataware.source.agent.common.visitor.MultiThreadVisitor;
import com.guwave.onedata.dataware.source.agent.manual.service.ParseFileHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.guwave.onedata.dataware.common.contant.Constant.EMPTY;

@Component
public class StdfParseHandler implements ParseFileHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(StdfParseHandler.class);


    private static final List<ParseRule> NEED_MERGE_TEST_TXT_LIST = Lists.newArrayList(ParseRule.J750);
    private static final List<ParseRule> NOT_NEED_LOOP_TEST_TXT = Lists.newArrayList(ParseRule.NONE_LOOP);


    @Value("${spring.handler.sink.coreNum}")
    private Integer coreNum;
    @Value("${spring.hdfs.odsHdfsTemplatePath}")
    private String odsHdfsTemplatePath;
    @Value("${spring.handler.sink.batchSize}")
    private Integer batchSize;
    @Value("${spring.smt8.testTxt.rule}")
    private String smt8TestTxtRule;
    @Value("${spring.handler.sink.maxTextDatSize}")
    private Integer maxTextDatSize;
    @Autowired
    private Map<String, FileSystemUtil> fileSystemUtilMap;
    @Autowired
    private Map<String, CkSink> ckSinkMap;
    @Autowired
    private EcidRuleRepository ecidRuleRepository;
    @Autowired
    private UidRuleRepository uidRuleRepository;
    @Autowired
    private LotRelationSyncRepository lotRelationSyncRepository;
    @Autowired
    private DtrParseRuleRepository dtrParseRuleRepository;

    private static final Map<ManualFileSystem, String> MANUAL_FILE_SYSTEM_MAP = new HashMap<ManualFileSystem, String>() {{
        put(ManualFileSystem.HDFS, "hdfsUtil");
        put(ManualFileSystem.LOCAL, "localfsUtil");
        put(ManualFileSystem.MINIO, "minIOUtil");
    }};

    @Override
    public Boolean support(FileCategory fileCategory, ParseRule parseRule) {
        return fileCategory == FileCategory.STDF;
    }

    @Override
    public void dealFile(ManualCalculateTask manualCalculateTask, File localFile, ManualFileInfo manualFileInfo, File scriptFile) {
        // 解析文件到ods
        int allThreadCnt = StdfMultiThreadParseUtil.calculateThredCnt(localFile);
        List<Visitor> visitors;
        try {
            visitors = parseStdfFile(manualCalculateTask, localFile, manualFileInfo, allThreadCnt);
        } catch (WrongValidHeadException wrongValidHeadException) {
            LOGGER.info("{} 分 {} 片解析,head校验不正确,尝试使用1个线程解析", localFile.getAbsolutePath(), allThreadCnt, wrongValidHeadException);
            allThreadCnt = 1;
            visitors = parseStdfFile(manualCalculateTask, localFile, manualFileInfo, allThreadCnt);
        }

        // 填充manual_file_info
        defaultFillManualFileInfo(manualFileInfo, visitors, localFile);
    }

    private List<Visitor> parseStdfFile(ManualCalculateTask manualCalculateTask, File localFile, ManualFileInfo manualFileInfo, int allThreadCnt) {
        FileSystemUtil fileSystemUtil = fileSystemUtilMap.get(MANUAL_FILE_SYSTEM_MAP.get(manualFileInfo.getManualFileSystem()));
        List<Visitor> visitors = new ArrayList<>();
        for (int i = 1; i <= allThreadCnt; i++) {
            visitors.add(new MultiThreadVisitor(i, fileSystemUtil, odsHdfsTemplatePath, localFile, defaultFillFileMainDataConsumer(manualFileInfo), allThreadCnt, batchSize, ckSinkMap, ecidRuleRepository, uidRuleRepository, lotRelationSyncRepository, manualFileInfo.getParameterType(), manualFileInfo.getTestParameter(), manualFileInfo.getScript()));
        }
        JSONObject parseSetting = StringUtils.isNotBlank(manualFileInfo.getParseSetting()) ? JSON.parseObject(manualFileInfo.getParseSetting()) : new JSONObject();

        Map<StdfFieldType, List<ClearRule>> clearRules = new HashMap<>();
        if (ParseRule.CTDR == manualFileInfo.getParseRule()) {
            ClearRule clearRule = new ClearRule(null, EMPTY);
            clearRules.put(StdfFieldType.CTDR, Lists.newArrayList(clearRule));
        }
        StdfMultiThreadParseUtil.MetaData multiThreadMetaData = new StdfMultiThreadParseUtil.MetaData()
                .setFile(localFile)
                .setVisitors(visitors)
                .setTestAreaFunction((mir, prrs) -> this.predictTestArea(mir, prrs, manualCalculateTask, fileSystemUtil))
                .setNeedMergeTestTxt(NEED_MERGE_TEST_TXT_LIST.contains(manualFileInfo.getParseRule()))
                .setNotNeedLoopTestTxt(NOT_NEED_LOOP_TEST_TXT.contains(manualFileInfo.getParseRule()))
                .setClearRules(clearRules)
                .setMaxTextDatSize(maxTextDatSize)
                .setSiteNumFilterFlag(false)
                .setIgnoreDamagedTouchdownFlag(true)
                .setPtrFirstTxtRegex(parseSetting.getString(ParseSettingType.FIRST_TXT_REGEX.getType()))
                .setClearTestTxtRegex(parseSetting.getObject(ParseSettingType.CLEAR_TXT_REGEX.getType(), Map.class))
                .setTestItemFromFields(parseSetting.getObject(ParseSettingType.TEST_ITEM_FROM_FIELDS.getType(), Map.class))
                .setClearVectNamRegex(parseSetting.getString(ParseSettingType.CLEAR_VECT_NAM_REGEX.getType()))
                .setFailPinParseFlag(parseSetting.getBooleanValue(ParseSettingType.FAIL_PIN_PARSE.getType()))
                .setSmt8TestTxtRule(smt8TestTxtRule);
        try {
            StdfMultiThreadParseUtil.parse(multiThreadMetaData);
        } finally {
            if (CollectionUtils.isNotEmpty(multiThreadMetaData.getComments())) {
                manualFileInfo.setComment(String.join(Constant.ENTER, multiThreadMetaData.getComments()));
            }
            visitors.forEach(t -> {
                MultiThreadVisitor visitor = (MultiThreadVisitor) t;
                visitor.close();
            });
        }
        return visitors;
    }

    /**
     * 推测TestArea
     *
     * @param mir                 Mir
     * @param prrs                Prrs
     * @param manualCalculateTask manualCalculateTask
     * @return {@link TestArea}
     */
    private TestArea predictTestArea(Mir mir, List<PrrLocation> prrs, ManualCalculateTask manualCalculateTask, FileSystemUtil fileSystemUtil) {
        TestArea resultArea = null;
        if (manualCalculateTask.getTestArea() != null) {
            resultArea = manualCalculateTask.getTestArea();
        } else {
            String testCod = mir.getTestCod();
            HashSet<TestArea> blackAreas = Sets.newHashSet(TestArea.WAT);
            List<TestArea> testAreas = Stream.of(TestArea.values())
                    .filter(item -> !blackAreas.contains(item))
                    .collect(Collectors.toList());
            for (TestArea item : testAreas) {
                if (testCod.toUpperCase().startsWith(item.getArea())) {
                    resultArea = item;
                    break;
                }
            }
        }
        if (resultArea == null) {
            HashSet<Integer> blackCoords = Sets.newHashSet(1024, -1024, 32768, -32768);
            resultArea = prrs.stream().map(t -> t.getPrr().getxCoord()).filter(x -> !blackCoords.contains(x)).distinct().count() > 1 ? TestArea.CP : TestArea.NA;
        }

        // 删除此文件对应的ods数据
        deleteOdsFile(odsHdfsTemplatePath, manualCalculateTask, resultArea, fileSystemUtil);

        return resultArea;
    }
}
