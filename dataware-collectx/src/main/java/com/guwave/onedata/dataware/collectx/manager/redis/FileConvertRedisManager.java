package com.guwave.onedata.dataware.collectx.manager.redis;

import com.guwave.onedata.dataware.common.contant.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class FileConvertRedisManager {

    private static Long lockExpireTime;

    private static RedisTemplate<String, String> redisTemplate;

    @Autowired
    private void setRedisTemplate(RedisTemplate redisTemplate) {
        FileConvertRedisManager.redisTemplate = redisTemplate;
    }

    @Autowired
    private void setLockExpireTime(@Value("${spring.handler.lockExpireTime}") Long lockExpireTime) {
        FileConvertRedisManager.lockExpireTime = lockExpireTime;
    }


    public void clearAll() {
        clearLockZipSplitConvert();
    }

    public static void clearKey(String key) {
        key = key + Constant.MULTIPLICATION_SIGN;
        Set<String> keys = redisTemplate.keys(key);
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }
        keys.forEach(t -> {
            log.info("删除redis key：{}", t);
            redisTemplate.delete(t);
        });
    }

    public static boolean lockZipSplitConvert(String remoteOriginalFilePath) {
        String key = BusinessRedisKeys.ZIP_SPLIT_REMOTE_ORIGINAL_FILE_PATH + remoteOriginalFilePath;
        return Boolean.TRUE.equals(redisTemplate.boundValueOps(key).setIfAbsent(remoteOriginalFilePath, lockExpireTime, TimeUnit.SECONDS));
    }

    public static void unLockZipSplitConvert(String remoteOriginalFilePath) {
        String key = BusinessRedisKeys.ZIP_SPLIT_REMOTE_ORIGINAL_FILE_PATH + remoteOriginalFilePath;
        redisTemplate.delete(key);
    }

    public static void clearLockZipSplitConvert() {
        clearKey(BusinessRedisKeys.ZIP_SPLIT_REMOTE_ORIGINAL_FILE_PATH);
    }
}
