package com.guwave.onedata.dataware.quality.job;

import com.guwave.onedata.dataware.common.annotation.ScheduleSwitch;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.QualityRule;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.QualityRuleRepository;
import com.guwave.onedata.dataware.quality.service.QualityCheckService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DailyJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(DailyJob.class);

    @Autowired
    private QualityCheckService qualityCheckService;
    @Autowired
    private QualityRuleRepository qualityRuleRepository;

    @ScheduleSwitch
    @Scheduled(cron = "${spring.scheduler.timer.dailyTask}")
    public void pullTask() {
        try {
            List<QualityRule> qualityRules = qualityRuleRepository.findAllByExecuteTimeTypeAndStatusAndDeleteFlag(1, 1, 0);
            qualityCheckService.qualityCheck(qualityRules, true);
        } catch (Exception e) {
            LOGGER.error("DailyJob 异常：", e);
        }
    }
}
