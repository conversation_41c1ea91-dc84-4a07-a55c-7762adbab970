package com.guwave.onedata.dataware.source.agent.common.configuration;

import com.guwave.onedata.dataware.common.contant.Constant;
import com.guwave.onedata.dataware.source.agent.common.vo.FileProcessProgress;
import io.minio.MinioClient;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.LocalFileSystem;
import org.apache.hadoop.hdfs.DistributedFileSystem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.net.URI;

/**
 * Copyright (C), 2022, guwave
 *
 * <AUTHOR>
 * @version 0.0.1
 * 2022-02-28 11:20:52
 */
@org.springframework.context.annotation.Configuration
public class SourceAgentCommonConfiguration {

    private static final Logger LOGGER = LoggerFactory.getLogger(SourceAgentCommonConfiguration.class);


    @Value("${spring.handler.file.hdfsMode}")
    private String hdfsMode;
    @Value("${spring.handler.file.hdfsUrl}")
    private String hdfsUrl;
    @Value("${spring.handler.file.hdfsUser}")
    private String hdfsUser;
    @Value("${spring.minio.endpoint}")
    private String endpoint;
    @Value("${spring.minio.accessKey}")
    private String accessKey;
    @Value("${spring.minio.secretKey}")
    private String secretKey;

    @Bean("redisTemplate")
    public RedisTemplate<String, FileProcessProgress> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, FileProcessProgress> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        template.setKeySerializer(RedisSerializer.string());
        template.setValueSerializer(new Jackson2JsonRedisSerializer<>(FileProcessProgress.class));

        template.setHashKeySerializer(RedisSerializer.string());
        template.setHashValueSerializer(RedisSerializer.string());
        return template;
    }

    @Bean("fileSystem")
    public FileSystem fileSystem() {
        // 加载默认配置文件
        org.apache.hadoop.conf.Configuration conf = generateHdfsConfig();
        if (Constant.HDFS_MODE_HA.equals(hdfsMode)) {
            return genarateFileSystem(URI.create(conf.get("fs.defaultFS")), conf);
        } else {
            conf.set("dfs.replication", "1");
            return genarateFileSystem(URI.create(this.hdfsUrl), conf);
        }
    }

    @Bean("minioClient")
    public MinioClient minioClient() {
        MinioClient minioClient = MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .build();
        return minioClient;
    }

    public org.apache.hadoop.conf.Configuration generateHdfsConfig() {
        org.apache.hadoop.conf.Configuration conf = new org.apache.hadoop.conf.Configuration();
        conf.set("fs.hdfs.impl", DistributedFileSystem.class.getName());
        if (Constant.HDFS_MODE_HA.equals(hdfsMode)) {
            String[] hdfsUrls = hdfsUrl.split(Constant.COMMA);
            if (hdfsUrls.length != 2) {
                throw new RuntimeException("HA模式下 handler.file.hdfsUrl 需要配置两个hdfs地址！");
            }
            // 默认文件系统的名称
            conf.set("fs.defaultFS", "hdfs://mycluster");
            // namenode 集群的名字
            conf.set("dfs.nameservices", "mycluster");
            // mycluster 下有两个 NameNode，逻辑地址分别是 nn1，nn2
            conf.set("dfs.ha.namenodes.mycluster", "nn1,nn2");
            // nn1 的 http 通信地址
            conf.set("dfs.namenode.rpc-address.mycluster.nn1", hdfsUrls[0]);
            // nn2 的 http 通信地址
            conf.set("dfs.namenode.rpc-address.mycluster.nn2", hdfsUrls[1]);
            // 配置读取失败自动切换的实现方式
            conf.set("dfs.client.failover.proxy.provider.mycluster", "org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider");
        }
        return conf;
    }

    public org.apache.hadoop.conf.Configuration generateLocalConfig() {
        org.apache.hadoop.conf.Configuration conf = new org.apache.hadoop.conf.Configuration();
        conf.set("fs.hdfs.impl", DistributedFileSystem.class.getName());
        conf.set("fs.file.impl", LocalFileSystem.class.getName());
        return conf;
    }

    public org.apache.hadoop.conf.Configuration generateMinIOConfig() {
        org.apache.hadoop.conf.Configuration conf = new org.apache.hadoop.conf.Configuration();
        conf.set("fs.s3a.impl", org.apache.hadoop.fs.s3a.S3AFileSystem.class.getName());
        conf.set("fs.s3a.endpoint", endpoint);
        conf.set("fs.s3a.access.key", accessKey);
        conf.set("fs.s3a.secret.key", secretKey);
        conf.set("fs.s3a.path.style.access", "true");
        return conf;
    }

    private FileSystem genarateFileSystem(URI uri, org.apache.hadoop.conf.Configuration conf) {
        try {
            // 获得hdfs文件系统
            return FileSystem.newInstance(uri, conf, this.hdfsUser);
        } catch (Exception e) {
            LOGGER.error("创建FileSystem失败", e);
            throw new RuntimeException(e);
        }
    }
}
