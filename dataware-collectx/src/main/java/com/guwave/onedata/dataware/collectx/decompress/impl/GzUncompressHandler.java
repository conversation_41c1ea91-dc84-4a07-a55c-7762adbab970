package com.guwave.onedata.dataware.collectx.decompress.impl;

import com.guwave.onedata.dataware.collectx.decompress.UnCompressHandler;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.zip.GZIPInputStream;

public class GzUncompressHandler implements UnCompressHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GzUncompressHandler.class);

    @Override
    public void unCompress(File file, File targetDir) throws Exception {
        try (FileInputStream fin = new FileInputStream(file);
             FileInputStream fin2 = new FileInputStream(file);
             BufferedInputStream in = new BufferedInputStream(fin);
             GZIPInputStream gzIn = new GZIPInputStream(in);
             GzipCompressorInputStream gzipCompressorInputStream = new GzipCompressorInputStream(fin2)
        ) {
            String fileName = file.getName();
            String dropSuffix = fileName.substring(0, fileName.lastIndexOf('.'));
            String outFileName;
            try {
                String metaFileName = gzipCompressorInputStream.getMetaData().getFilename();
                outFileName = metaFileName != null ? metaFileName : dropSuffix;
            } catch (Exception ignored) {
                LOGGER.warn("GZIP获取MetaData文件名失败，删除后缀作为输出文件名：{}", dropSuffix);
                outFileName = dropSuffix;
            }
            File outFile = new File(targetDir, outFileName);
            try (FileOutputStream out = new FileOutputStream(outFile)) {
                final byte[] buffer = new byte[1024 * 5];
                int n;
                while (-1 != (n = gzIn.read(buffer))) {
                    out.write(buffer, 0, n);
                }
                out.flush();
            } catch (Exception e) {
                LOGGER.error("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
                throw e;
            }
        } catch (Exception e) {
            LOGGER.error("解压失败：{} --> {}", file.getAbsolutePath(), targetDir, e);
            throw e;
        }
    }
}
